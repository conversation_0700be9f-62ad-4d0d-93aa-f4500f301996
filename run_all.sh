#!/bin/bash

# Directory containing the Python scripts
SCRIPT_DIR="./scripts"

# Environment variable specifying the file to execute
# If FILE_TO_EXECUTE is not set, all files will be executed
export APP_LOG_DIR=$(pwd)
export PYTHONPATH=$PYTHONPATH:$(pwd)

echo "All logs will be save into $APP_LOG_DIR/app.log"

genera_error_message=""

if [ -z "$FILE_TO_EXECUTE" ]; then
    echo "No FILE_TO_EXECUTE environment variable set. Running all Python files."
    PYTHON_FILES=$(ls $SCRIPT_DIR/*.py | grep -v proxy_setup 2>/dev/null)
else
    PYTHON_FILES="$SCRIPT_DIR/$FILE_TO_EXECUTE"
    if [ ! -f "$PYTHON_FILES" ]; then
        genera_error_message="Specified file $FILE_TO_EXECUTE does not exist in $SCRIPT_DIR."
    fi
fi

# Check if there are any Python files
if [ -z "$PYTHON_FILES" ]; then
    genera_error_message="No Python files found in $SCRIPT_DIR."
fi

all_success_result=""
all_failed_result=""
time_cost_in_seconds=0

if [ -z "$genera_error_message" ]; then
    # Run each Python file
    for FILE in $PYTHON_FILES; do
        # Skip proxy_setup.py file
        FILE_NAME=$(basename "$FILE")
        if [ "$FILE_NAME" = "proxy_setup.py" ]; then
            echo "Skipping $FILE"
            continue
        fi

        # Record start time
        start_time=$(date +%s)
        echo "$(date) - Running $FILE"
        python "$FILE"

        # Record end time
        end_time=$(date +%s)

        # Calculate the time taken for this script
        time_taken=$((end_time - start_time))
        time_cost_in_seconds=$((time_cost_in_seconds + time_taken))

        # Check if log file exists and has content
        if [ -s "$APP_LOG_DIR/app.log" ]; then
            last_line_of_output=$(tail -1 "$APP_LOG_DIR/app.log")
            my_result="${FILE_NAME}:::${last_line_of_output}__EOF__"
            
            # Check if the last line contains '===new_record==='
            if echo "$last_line_of_output" | grep -q '===new_record==='; then
                all_success_result="${all_success_result}${my_result}"
            else
                all_failed_result="${all_failed_result}${my_result}"
            fi
        else
            # Handle case where log file doesn't exist or is empty
            all_failed_result="${all_failed_result}${FILE_NAME}:::Log file:$APP_LOG_DIR/app.log empty or not found.__EOF__"
        fi
    done
else
    all_failed_result="$genera_error_message"
fi


echo -e "all_success_result: ${all_success_result}"
echo -e "all_failed_result: ${all_failed_result}"
echo -e "time_cost_in_seconds: ${time_cost_in_seconds}s"

python ./send_feishu_notification.py --all_failed_result="$all_failed_result" --all_success_result="$all_success_result" --time_cost_in_seconds=$time_cost_in_seconds

# echo -e "`date` 运行七鱼话务语音转文字的任务："
# python ./scripts/qiyu_call_audio_recognize.py

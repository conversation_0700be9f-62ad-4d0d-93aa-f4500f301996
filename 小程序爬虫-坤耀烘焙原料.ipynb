{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "*************, headers:{'uniacid': '2595', 'appType': 'mini'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "THREAD_CNT = int(os.environ.get('THREAD_CNT', 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "hints={'odps.sql.hive.compatible':True,'odps.sql.type.system.odps2':True}\n", "def get_odps_sql_result_as_df(sql):\n", "    instance=odps.execute_sql(sql, hints=hints)\n", "    instance.wait_for_success()\n", "    pd_df=None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={'uniacid':'2595','appType':'mini',}\n", "brand_name='坤耀烘焙原料'\n", "competitor_name_en='kun<PERSON><PERSON><PERSON><PERSON>'\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["牛奶类 9\n", "淡奶油 8\n", "黄油 7\n", "面粉类 11\n", "奶酪芝士马苏 8\n", "馅料 7\n", "糖制品 9\n", "肉松系列产品 3\n", "香肠肉制品 6\n", "慕斯/冰淇淋 9\n", "奶粉 1\n", "沙拉酱调味酱 7\n", "冷冻果茸果品 10\n", "咖啡椰乳奶 3\n", "中餐调味料 1\n", "烘焙配料 7\n", "干果蜜饯 3\n", "果蔬预拌粉 2\n", "食品保鲜脱氧剂 7\n", "咖啡饮品类 9\n", "工具包装 3\n", "运费差价 1\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>typePid</th>\n", "      <th>aloneType</th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>icon</th>\n", "      <th>price</th>\n", "      <th>salesNum</th>\n", "      <th>stock</th>\n", "      <th>minNum</th>\n", "      <th>maxNum</th>\n", "      <th>...</th>\n", "      <th>isSpecs</th>\n", "      <th>isAttr</th>\n", "      <th>isMaterial</th>\n", "      <th>salesType</th>\n", "      <th>startTime</th>\n", "      <th>endTime</th>\n", "      <th>weekDay</th>\n", "      <th>weekSalesType</th>\n", "      <th>activityGoodData</th>\n", "      <th>vipPrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>199390</td>\n", "      <td>进口蒙牛纯牛奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>98</td>\n", "      <td>5</td>\n", "      <td>95</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>98.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>199646</td>\n", "      <td>国产蒙牛牛奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>50</td>\n", "      <td>0</td>\n", "      <td>999</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>199658</td>\n", "      <td>国产蒙牛牛奶（航天版包装）</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>50</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>199666</td>\n", "      <td>辉山牛奶（烘焙专用）</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>70</td>\n", "      <td>0</td>\n", "      <td>100</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>70.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>216047</td>\n", "      <td>牡纯纯牛奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>78</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1679068800</td>\n", "      <td>1681660800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>78.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>205422</td>\n", "      <td>每日鲜语咖啡大师奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>180</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678579200</td>\n", "      <td>1681171200</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>180.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>205429</td>\n", "      <td>蒙牛现代牧场鲜牛奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>108</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678579200</td>\n", "      <td>1681171200</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>108.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>472121</td>\n", "      <td>庄园纯牛奶1L*8盒/箱</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>56</td>\n", "      <td>0</td>\n", "      <td>1000</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1695484800</td>\n", "      <td>1698076800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>56.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>359118</td>\n", "      <td>归一牛奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>78</td>\n", "      <td>0</td>\n", "      <td>1000</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1686240000</td>\n", "      <td>1688832000</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>78.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>46513</td>\n", "      <td>2</td>\n", "      <td>199677</td>\n", "      <td>安佳淡奶油</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>490</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>490.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 24 columns</p>\n", "</div>"], "text/plain": ["  typePid aloneType      id           name  \\\n", "0   46469         2  199390        进口蒙牛纯牛奶   \n", "1   46469         2  199646         国产蒙牛牛奶   \n", "2   46469         2  199658  国产蒙牛牛奶（航天版包装）   \n", "3   46469         2  199666     辉山牛奶（烘焙专用）   \n", "4   46469         2  216047          牡纯纯牛奶   \n", "5   46469         2  205422      每日鲜语咖啡大师奶   \n", "6   46469         2  205429      蒙牛现代牧场鲜牛奶   \n", "7   46469         2  472121   庄园纯牛奶1L*8盒/箱   \n", "8   46469         2  359118           归一牛奶   \n", "9   46513         2  199677          安佳淡奶油   \n", "\n", "                                                icon price salesNum stock  \\\n", "0  https://static.saas.qxepay.com/yb_wm/2595/2023...    98        5    95   \n", "1  https://static.saas.qxepay.com/yb_wm/2595/2023...    50        0   999   \n", "2  https://static.saas.qxepay.com/yb_wm/2595/2023...    50        0     0   \n", "3  https://static.saas.qxepay.com/yb_wm/2595/2023...    70        0   100   \n", "4  https://static.saas.qxepay.com/yb_wm/2595/2023...    78        0     0   \n", "5  https://static.saas.qxepay.com/yb_wm/2595/2023...   180        0     0   \n", "6  https://static.saas.qxepay.com/yb_wm/2595/2023...   108        0     0   \n", "7  https://static.saas.qxepay.com/yb_wm/2595/2023...    56        0  1000   \n", "8  https://static.saas.qxepay.com/yb_wm/2595/2023...    78        0  1000   \n", "9  https://static.saas.qxepay.com/yb_wm/2595/2023...   490        4     6   \n", "\n", "  minNum maxNum  ... isSpecs isAttr isMaterial salesType   startTime  \\\n", "0      5      0  ...       2      2          2         1  1678204800   \n", "1     10      0  ...       2      2          2         1  1678204800   \n", "2      1      0  ...       2      2          2         1  1678204800   \n", "3      5      0  ...       2      2          2         1  1678204800   \n", "4      1      0  ...       2      2          2         1  1679068800   \n", "5      1      0  ...       2      2          2         1  1678579200   \n", "6      1      0  ...       2      2          2         1  1678579200   \n", "7      1      0  ...       2      2          2         1  1695484800   \n", "8      1      0  ...       2      2          2         1  1686240000   \n", "9      1      0  ...       2      2          2         1  1678204800   \n", "\n", "      endTime weekDay weekSalesType  \\\n", "0  1680796800      []             1   \n", "1  1680796800      []             1   \n", "2  1680796800      []             1   \n", "3  1680796800      []             1   \n", "4  1681660800      []             1   \n", "5  1681171200      []             1   \n", "6  1681171200      []             1   \n", "7  1698076800      []             1   \n", "8  1688832000      []             1   \n", "9  1680796800      []             1   \n", "\n", "                                    activityGoodData vipPrice  \n", "0  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     98.0  \n", "1  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     50.0  \n", "2  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     50.0  \n", "3  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     70.0  \n", "4  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     78.0  \n", "5  {'id': 0, 'type': 0, 'money': 0, 'activityMone...    180.0  \n", "6  {'id': 0, 'type': 0, 'money': 0, 'activityMone...    108.0  \n", "7  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     56.0  \n", "8  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     78.0  \n", "9  {'id': 0, 'type': 0, 'money': 0, 'activityMone...    490.0  \n", "\n", "[10 rows x 24 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["url='https://saas.qxepay.com/index.php/channelApi/good/get-product-list?storeId=2653&lat=26.113972&lng=119.320571'\n", "\n", "category_and_goods=requests.get(url=url, headers=headers).json()['data']['data']\n", "goods=[]\n", "for data in category_and_goods:\n", "    print(data['name'], len(data['goods']))\n", "    goods.extend(data['goods'])\n", "\n", "product_list_all_df=pd.DataFrame(goods)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["131\n"]}], "source": ["print(len(goods))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "123.96.138.212:37732\n", "112.195.46.74:39365\n", "58.219.85.216:47852\n", "117.69.11.218:40779\n", "221.227.100.223:31348\n", "42.57.151.133:30477\n", "121.205.212.185:32450\n", "122.230.38.114:44814\n", "175.155.181.82:40974\n", "222.190.163.46:32568\n", "['123.96.138.212:37732', '112.195.46.74:39365', '58.219.85.216:47852', '117.69.11.218:40779', '221.227.100.223:31348', '42.57.151.133:30477', '121.205.212.185:32450', '122.230.38.114:44814', '175.155.181.82:40974', '222.190.163.46:32568']\n", "成功写入odps:summerfarm_ds.spider_kunyaohongbei_product_result_df, partition_spec:ds=20240222,competitor_name=kunyaohongbei, attemp:0\n", "sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_kunyaohongbei_product_result_df\n", "                             where ds>='20240123' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240222</td>\n", "      <td>kun<PERSON><PERSON><PERSON>bei</td>\n", "      <td>131</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ds competitor_name  recods\n", "0  20240222   kunyaohongbei     131"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "product_list_all_df['competitor']=brand_name\n", "all_products_df=product_list_all_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
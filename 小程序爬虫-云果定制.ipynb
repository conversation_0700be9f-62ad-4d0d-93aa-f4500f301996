{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "*************, headers:{'uniacid': '2595', 'appType': 'mini', 'Referer': 'https://servicewechat.com/wx92c8f2cd458916b5/36/page-frame.html'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "THREAD_CNT = int(os.environ.get('THREAD_CNT', 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "hints={'odps.sql.hive.compatible':True,'odps.sql.type.system.odps2':True}\n", "def get_odps_sql_result_as_df(sql):\n", "    instance=odps.execute_sql(sql, hints=hints)\n", "    instance.wait_for_success()\n", "    pd_df=None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={'uniacid':'2595','appType':'mini','Referer':'https://servicewechat.com/wx92c8f2cd458916b5/36/page-frame.html',}\n", "brand_name='云果定制'\n", "competitor_name_en='yunguodingzhi'\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'Data': {'user_name': '蜜菓', 'access_token': '4b49efec9a8f440f8fc8552092491fb7', 'decimal_length': 2, 'buy_length': 2, 'Role': 'member', 'SecondRole': 0, 'UserId': 7578, 'IsSubMember': <PERSON>als<PERSON>, 'AllYSMoney': 0.0, 'Balance': 0.0, 'IsOutDate': Fals<PERSON>, 'CanUseMoney': 0.0, 'IsHaveAgreementPrice': 0}, 'StatusCode': 0, 'Message': '成功'}\n"]}], "source": ["login={\n", "    \"username\": \"蜜菓\",\n", "    \"pwd\": \"123456\",\n", "    \"module_id\": \"5A612570-3E60-496F-AF51-9E680FE9CD9E\",\n", "    \"access_token\": \"\",\n", "    \"role\": \"member\",\n", "    \"check_url_id\": \"\"\n", "}\n", "\n", "token=requests.post('https://ytgydhy.mdydt.net/api/Login/Login', json=login).json()\n", "print(token)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'CategoryId': 1, 'ParentCategoryId': 0, 'TopCategoryId': 1, 'Name': '草莓类', 'SKUPrefix': None, 'DisplaySequence': 1, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Depth': 1, 'Path': '1', 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'HasChildren': False, 'TypeID': None, 'IsEnable': True, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 2, 'ParentCategoryId': 0, 'TopCategoryId': 2, 'Name': '瓜类', 'SKUPrefix': None, 'DisplaySequence': 2, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Depth': 1, 'Path': '2', 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'HasChildren': False, 'TypeID': None, 'IsEnable': True, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 3, 'ParentCategoryId': 0, 'TopCategoryId': 3, 'Name': '进口水果', 'SKUPrefix': None, 'DisplaySequence': 3, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Depth': 1, 'Path': '3', 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'HasChildren': False, 'TypeID': None, 'IsEnable': True, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 4, 'ParentCategoryId': 0, 'TopCategoryId': 4, 'Name': '蔬菜类', 'SKUPrefix': None, 'DisplaySequence': 4, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Depth': 1, 'Path': '4', 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'HasChildren': False, 'TypeID': None, 'IsEnable': True, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 5, 'ParentCategoryId': 0, 'TopCategoryId': 5, 'Name': '国产水果', 'SKUPrefix': None, 'DisplaySequence': 5, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Depth': 1, 'Path': '5', 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'HasChildren': False, 'TypeID': None, 'IsEnable': True, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 6, 'ParentCategoryId': 0, 'TopCategoryId': 6, 'Name': '芒果类', 'SKUPrefix': None, 'DisplaySequence': 6, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Depth': 1, 'Path': '6', 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'HasChildren': False, 'TypeID': None, 'IsEnable': True, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 7, 'ParentCategoryId': 0, 'TopCategoryId': 7, 'Name': '柠檬金桔猕猴桃百香果', 'SKUPrefix': None, 'DisplaySequence': 7, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Depth': 1, 'Path': '7', 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'HasChildren': False, 'TypeID': None, 'IsEnable': True, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 35, 'ParentCategoryId': 0, 'TopCategoryId': 35, 'Name': '自助餐类', 'SKUPrefix': None, 'DisplaySequence': 7, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Depth': 1, 'Path': '35', 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'HasChildren': False, 'TypeID': None, 'IsEnable': True, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 8, 'ParentCategoryId': 0, 'TopCategoryId': 8, 'Name': '副食品', 'SKUPrefix': None, 'DisplaySequence': 8, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Depth': 1, 'Path': '8', 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'HasChildren': False, 'TypeID': None, 'IsEnable': True, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 10, 'ParentCategoryId': 0, 'TopCategoryId': 10, 'Name': '精品菜类', 'SKUPrefix': None, 'DisplaySequence': 10, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Depth': 1, 'Path': '10', 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'HasChildren': False, 'TypeID': None, 'IsEnable': True, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}]\n"]}], "source": ["body={\n", "  \"app_type\": \"1\",\n", "  \"access_token\": token['Data']['access_token'],\n", "  \"role\": \"member\",\n", "  \"check_url_id\": \"5432ef495f6147e1b726ea9b663441fa\"\n", "}\n", "\n", "category_list=requests.post('https://ytgydhy.mdydt.net/api/ShopAPI/Product/GetCategorysList', json=body).json()['Data']['categorys_list_stage']\n", "print(category_list)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["草莓类, 商品数:3, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '01006', 'ProductId': 6, 'ProductName': '本地草莓大果', 'RankPrice': 7.7, 'SkuID': '6_0', 'Standard': '一件15斤', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_6fe2fa04-1b69-45e8-aa27-94a73d63b9e8.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_6fe2fa04-1b69-45e8-aa27-94a73d63b9e8.jpg', 'Type': '', 'UnitId': 20, 'UnitName': '斤', 'VirtualStock': '5468.51', 'ptypeUnits': [{'ProductCode': '01006', 'UnitId': 20, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.1, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '01006', 'UnitId': 761, 'Uname': '斤（毛重）', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 15.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '01006', 'UnitId': 21, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.1, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "瓜类, 商品数:16, 商品:{'EnableMOQ': True, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '02077', 'ProductId': 436, 'ProductName': '无籽西瓜（毛重带箱）', 'RankPrice': 3.1, 'SkuID': '436_0', 'Standard': '', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_53b56020-2577-4881-95f2-38382e2eda86.png', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_53b56020-2577-4881-95f2-38382e2eda86.png', 'Type': '', 'UnitId': 1157, 'UnitName': '斤', 'VirtualStock': '30157.27', 'ptypeUnits': [{'ProductCode': '02077', 'UnitId': 1157, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 30.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '02077', 'UnitId': 1158, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 15.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "进口水果, 商品数:11, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '03003', 'ProductId': 37, 'ProductName': '进口西柚', 'RankPrice': 5.0, 'SkuID': '37_0', 'Standard': '1个约0.6斤，1件50个 1份/6个', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_ce5968a5-3168-4016-8fbd-ead9434558d8.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_ce5968a5-3168-4016-8fbd-ead9434558d8.jpg', 'Type': '', 'UnitId': 90, 'UnitName': '个', 'VirtualStock': '6334.21', 'ptypeUnits': [{'ProductCode': '03003', 'UnitId': 90, 'Uname': '个', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '03003', 'UnitId': 92, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.5, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '03003', 'UnitId': 656, 'Uname': '份', 'CarCount': 0.0, 'Keyid': None, 'URate': 3.6, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '03003', 'UnitId': 93, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 4.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '03003', 'UnitId': 835, 'Uname': '件', 'CarCount': 0.0, 'Keyid': None, 'URate': 50.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "蔬菜类, 商品数:17, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '04004', 'ProductId': 64, 'ProductName': '日本乳瓜', 'RankPrice': 6.1, 'SkuID': '64_0', 'Standard': '', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_46edf3d9-2c7d-42fd-a38a-4db03c606142.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_46edf3d9-2c7d-42fd-a38a-4db03c606142.jpg', 'Type': '', 'UnitId': 152, 'UnitName': '斤', 'VirtualStock': '235.43', 'ptypeUnits': [{'ProductCode': '04004', 'UnitId': 152, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '04004', 'UnitId': 153, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '04004', 'UnitId': 154, 'Uname': '件', 'CarCount': 0.0, 'Keyid': None, 'URate': 19.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "国产水果, 商品数:42, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '05003', 'ProductId': 114, 'ProductName': '雪梨', 'RankPrice': 3.3, 'SkuID': '114_0', 'Standard': '约0.4斤/个', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_094d25ad-5812-42cf-bab4-570ca4f62dcb.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_094d25ad-5812-42cf-bab4-570ca4f62dcb.jpg', 'Type': '', 'UnitId': 250, 'UnitName': '斤', 'VirtualStock': '499877.9', 'ptypeUnits': [{'ProductCode': '05003', 'UnitId': 250, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05003', 'UnitId': 251, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05003', 'UnitId': 252, 'Uname': '件', 'CarCount': 0.0, 'Keyid': None, 'URate': 33.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "芒果类, 商品数:4, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '06001', 'ProductId': 186, 'ProductName': '小台农', 'RankPrice': 7.4, 'SkuID': '186_0', 'Standard': '1件带框38斤左右', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_7d8e6a13-525c-4d68-9c67-6f89f05cbaaa.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_7d8e6a13-525c-4d68-9c67-6f89f05cbaaa.jpg', 'Type': '', 'UnitId': 407, 'UnitName': '斤', 'VirtualStock': '38444.8', 'ptypeUnits': [{'ProductCode': '06001', 'UnitId': 407, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '06001', 'UnitId': 1106, 'Uname': '斤（毛重）', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 38.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '06001', 'UnitId': 408, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '06001', 'UnitId': 663, 'Uname': '份', 'CarCount': 0.0, 'Keyid': None, 'URate': 8.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "柠檬金桔猕猴桃百香果, 商品数:11, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '07001', 'ProductId': 198, 'ProductName': '黄柠檬', 'RankPrice': 3.1, 'SkuID': '198_0', 'Standard': '约0.2斤/个', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_f0f7dd86-10bb-4905-88ca-1e7620d56737.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_f0f7dd86-10bb-4905-88ca-1e7620d56737.jpg', 'Type': '', 'UnitId': 432, 'UnitName': '斤', 'VirtualStock': '48606.79', 'ptypeUnits': [{'ProductCode': '07001', 'UnitId': 432, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.5, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '07001', 'UnitId': 434, 'Uname': '个', 'CarCount': 0.0, 'Keyid': None, 'URate': 0.2, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '07001', 'UnitId': 861, 'Uname': '斤（毛重）', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '07001', 'UnitId': 433, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '07001', 'UnitId': 666, 'Uname': '份', 'CarCount': 0.0, 'Keyid': None, 'URate': 5.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "自助餐类, 商品数:8, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '05032', 'ProductId': 143, 'ProductName': '芭乐', 'RankPrice': 4.4, 'SkuID': '143_0', 'Standard': '一个约0.4斤', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_bd10bb56-6f63-4391-9568-a5d5196e680a.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_bd10bb56-6f63-4391-9568-a5d5196e680a.jpg', 'Type': '', 'UnitId': 321, 'UnitName': '斤', 'VirtualStock': '4018.57', 'ptypeUnits': [{'ProductCode': '05032', 'UnitId': 321, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05032', 'UnitId': 722, 'Uname': '斤（毛重）', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05032', 'UnitId': 751, 'Uname': '500克/份', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05032', 'UnitId': 723, 'Uname': '公斤（毛重)', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05032', 'UnitId': 322, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "副食品, 商品数:2, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '08002', 'ProductId': 212, 'ProductName': '鸡蛋', 'RankPrice': 7.0, 'SkuID': '212_0', 'Standard': '一板大概4斤左右', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_b3d6d185-f410-4ebc-903a-cc651ef5b91b.png', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_b3d6d185-f410-4ebc-903a-cc651ef5b91b.png', 'Type': '', 'UnitId': 462, 'UnitName': '斤', 'VirtualStock': '41928.8564', 'ptypeUnits': [{'ProductCode': '08002', 'UnitId': 462, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '08002', 'UnitId': 463, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "精品菜类, 商品数:11, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '11002', 'ProductId': 217, 'ProductName': '薄荷叶', 'RankPrice': 13.2, 'SkuID': '217_0', 'Standard': '', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_eed4dd9f-a29b-499d-aa43-1f712422a9f6.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_eed4dd9f-a29b-499d-aa43-1f712422a9f6.jpg', 'Type': '', 'UnitId': 471, 'UnitName': '斤', 'VirtualStock': '498.69', 'ptypeUnits': [{'ProductCode': '11002', 'UnitId': 471, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.1, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '11002', 'UnitId': 472, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.1, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EnableMOQ</th>\n", "      <th>EnableNoStock</th>\n", "      <th>HasSKU</th>\n", "      <th>MarketPrice</th>\n", "      <th>ProductCode</th>\n", "      <th>ProductId</th>\n", "      <th>ProductName</th>\n", "      <th>RankPrice</th>\n", "      <th>SkuID</th>\n", "      <th>Standard</th>\n", "      <th>...</th>\n", "      <th>ProductIsCanBuy</th>\n", "      <th>PermitNo</th>\n", "      <th>PermitNoPeriod</th>\n", "      <th>TypeStatus</th>\n", "      <th>UsefulLifeMonth</th>\n", "      <th>StorageCondition</th>\n", "      <th>TradeMark</th>\n", "      <th>SubUnitName</th>\n", "      <th>SubUnitStock</th>\n", "      <th>PromotionInfoList</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01006</td>\n", "      <td>6</td>\n", "      <td>本地草莓大果</td>\n", "      <td>7.7</td>\n", "      <td>6_0</td>\n", "      <td>一件15斤</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01007</td>\n", "      <td>7</td>\n", "      <td>本地草莓中果</td>\n", "      <td>5.5</td>\n", "      <td>7_0</td>\n", "      <td>毛重14-17斤/件</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01010</td>\n", "      <td>10</td>\n", "      <td>丹东红颜草莓（4*5)）</td>\n", "      <td>13.5</td>\n", "      <td>10_0</td>\n", "      <td>20颗一盒</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02077</td>\n", "      <td>436</td>\n", "      <td>无籽西瓜（毛重带箱）</td>\n", "      <td>3.1</td>\n", "      <td>436_0</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02001</td>\n", "      <td>15</td>\n", "      <td>无籽西瓜</td>\n", "      <td>4.0</td>\n", "      <td>15_0</td>\n", "      <td>特大 一件27-30斤</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02003</td>\n", "      <td>17</td>\n", "      <td>黑美人</td>\n", "      <td>2.8</td>\n", "      <td>17_0</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02079</td>\n", "      <td>438</td>\n", "      <td>美都麒麟瓜（毛重带箱）</td>\n", "      <td>6.1</td>\n", "      <td>438_0</td>\n", "      <td>个别有籽</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02008</td>\n", "      <td>22</td>\n", "      <td>黄皮哈密瓜</td>\n", "      <td>5.0</td>\n", "      <td>22_0</td>\n", "      <td>约3.6斤/个</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02010</td>\n", "      <td>24</td>\n", "      <td>木瓜</td>\n", "      <td>3.9</td>\n", "      <td>24_0</td>\n", "      <td>0.8斤/个</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02012</td>\n", "      <td>26</td>\n", "      <td>玫珑瓜</td>\n", "      <td>14.3</td>\n", "      <td>26_0</td>\n", "      <td>约2.7斤/个</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 35 columns</p>\n", "</div>"], "text/plain": ["   EnableMOQ  EnableNoStock  HasSKU  MarketPrice ProductCode  ProductId  \\\n", "0      False           True   False          0.0       01006          6   \n", "1      False           True   False          0.0       01007          7   \n", "2      False           True   False          0.0       01010         10   \n", "3       True           True   False          0.0       02077        436   \n", "4       True           True   False          0.0       02001         15   \n", "5      False           True   False          0.0       02003         17   \n", "6      False           True   False          0.0       02079        438   \n", "7      False           True   False          0.0       02008         22   \n", "8      False           True   False          0.0       02010         24   \n", "9       True           True   False          0.0       02012         26   \n", "\n", "    ProductName  RankPrice  SkuID     Standard  ... ProductIsCanBuy PermitNo  \\\n", "0        本地草莓大果        7.7    6_0        一件15斤  ...            True            \n", "1        本地草莓中果        5.5    7_0   毛重14-17斤/件  ...            True            \n", "2  丹东红颜草莓（4*5)）       13.5   10_0        20颗一盒  ...            True            \n", "3    无籽西瓜（毛重带箱）        3.1  436_0               ...            True            \n", "4          无籽西瓜        4.0   15_0  特大 一件27-30斤  ...            True            \n", "5           黑美人        2.8   17_0               ...            True            \n", "6   美都麒麟瓜（毛重带箱）        6.1  438_0         个别有籽  ...            True            \n", "7         黄皮哈密瓜        5.0   22_0      约3.6斤/个  ...            True            \n", "8            木瓜        3.9   24_0       0.8斤/个  ...            True            \n", "9           玫珑瓜       14.3   26_0      约2.7斤/个  ...            True            \n", "\n", "  PermitNoPeriod TypeStatus  UsefulLifeMonth StorageCondition TradeMark  \\\n", "0                                          0                              \n", "1                                          0                              \n", "2                                          0                              \n", "3                                          0                              \n", "4                                          0                              \n", "5                                          0                              \n", "6                                          0                              \n", "7                                          0                              \n", "8                                          0                              \n", "9                                          0                              \n", "\n", "  SubUnitName SubUnitStock PromotionInfoList  \n", "0                      0.0              None  \n", "1                      0.0              None  \n", "2                      0.0              None  \n", "3                      0.0              None  \n", "4                      0.0              None  \n", "5                      0.0              None  \n", "6                      0.0              None  \n", "7                      0.0              None  \n", "8                      0.0              None  \n", "9                      0.0              None  \n", "\n", "[10 rows x 35 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["params={\n", "    \"flag\": \"0\",\n", "    \"standard\": \"\",\n", "    \"app_type\": \"1\",\n", "    \"key_words\": \"\",\n", "    \"request_source\": \"products_product-list\",\n", "    \"category_id\": \"2\",\n", "    \"max_sale_price\": \"\",\n", "    \"min_sale_price\": \"\",\n", "    \"is_count_down\": \"false\",\n", "    \"is_group_buy\": \"false\",\n", "    \"onlyinstock\": \"\",\n", "    \"tagids\": \"\",\n", "    \"activity_id\": \"\",\n", "    \"brands\": \"\",\n", "    \"sort_order_by\": \"DisplaySequence\",\n", "    \"sort_order\": \"Asc\",\n", "    \"page_index\": \"1\",\n", "    \"page_size\": \"100\",\n", "    \"access_token\": token['Data']['access_token'],\n", "    \"role\": \"member\",\n", "    \"check_url_id\": \"5432ef495f6147e1b726ea9b663441fa\"\n", "}\n", "\n", "product_list_all=[]\n", "for category in category_list:\n", "    name = category['Name']\n", "    CategoryId = category['CategoryId']\n", "    params[\"category_id\"]=CategoryId\n", "    product_list=requests.post('https://ytgydhy.mdydt.net/api/ShopAPI/ProductInfos/GetProductsList', json=params).json()['Data']['ProducetList']\n", "    print(f'{name}, 商品数:{len(product_list)}, 商品:{product_list[0]}')\n", "    product_list_all.extend(product_list)\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "175.175.70.177:34489\n", "117.69.232.59:44930\n", "112.194.88.24:31881\n", "115.219.4.47:49847\n", "223.241.1.146:36783\n", "119.132.75.77:44266\n", "115.219.2.84:30459\n", "60.18.16.181:43945\n", "183.154.55.120:45194\n", "42.59.117.109:34463\n", "['175.175.70.177:34489', '117.69.232.59:44930', '112.194.88.24:31881', '115.219.4.47:49847', '223.241.1.146:36783', '119.132.75.77:44266', '115.219.2.84:30459', '60.18.16.181:43945', '183.154.55.120:45194', '42.59.117.109:34463']\n", "成功写入odps:summerfarm_ds.spider_yunguodingzhi_product_result_df, partition_spec:ds=20240223,competitor_name=yunguodingzhi, attemp:0\n", "sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_yunguodingzhi_product_result_df\n", "                             where ds>='20240124' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240223</td>\n", "      <td>yunguodingzhi</td>\n", "      <td>125</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ds competitor_name  recods\n", "0  20240223   yunguodingzhi     125"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "product_list_all_df['competitor']=brand_name\n", "all_products_df=product_list_all_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
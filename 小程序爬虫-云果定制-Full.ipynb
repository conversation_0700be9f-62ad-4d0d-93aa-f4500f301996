{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 17:58:39, date_of_now:2024-03-06, brand_name:云果定制, headers:{'uniacid': '2595', 'appType': 'mini', 'Referer': 'https://servicewechat.com/wx92c8f2cd458916b5/36/page-frame.html'}\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/云果定制/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "\n", "headers={'uniacid':'2595','appType':'mini','Referer':'https://servicewechat.com/wx92c8f2cd458916b5/36/page-frame.html',}\n", "brand_name='云果定制'\n", "competitor_name_en='yunguodingzhi'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, headers:{headers}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["***************:41661\n", "*************:32811\n", "*************:46646\n", "************:46159\n", "***************:35275\n", "***************:30123\n", "**************:42254\n", "*************:41352\n", "**************:31134\n", "*************:44796\n", "['***************:41661', '*************:32811', '*************:46646', '************:46159', '***************:35275', '***************:30123', '**************:42254', '*************:41352', '**************:31134', '*************:44796']\n"]}], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def post_remote_data_with_proxy(url, json):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, json=json, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["login获取token"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: http://18258841203:8gTcEKLs@***************:30123\n", "{'Data': {'user_name': '蜜菓', 'access_token': '1376ef1871b540daa220ced031c63a0d', 'decimal_length': 2, 'buy_length': 2, 'Role': 'member', 'SecondRole': 0, 'UserId': 7578, 'IsSubMember': <PERSON><PERSON><PERSON>, 'AllYSMoney': 0.0, 'Balance': 0.0, 'IsOutDate': Fals<PERSON>, 'CanUseMoney': 0.0, 'IsHaveAgreementPrice': 0}, 'StatusCode': 0, 'Message': '成功'}\n"]}], "source": ["login={\n", "    \"username\": \"蜜菓\",\n", "    \"pwd\": \"123456\",\n", "    \"module_id\": \"5A612570-3E60-496F-AF51-9E680FE9CD9E\",\n", "    \"access_token\": \"\",\n", "    \"role\": \"member\",\n", "    \"check_url_id\": \"\"\n", "}\n", "\n", "token=json.loads(post_remote_data_with_proxy('https://ytgydhy.mdydt.net/api/Login/Login', json=login).text)\n", "print(token)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["获取分类列表"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: http://18258841203:8gTcEKLs@***************:30123\n", "[{'CategoryId': 1, 'Name': '草莓类', 'DisplaySequence': 1, 'ParentCategoryId': 0, 'Depth': 1, 'Path': '1', 'HasChildren': False, 'IsEnable': True, 'TopCategoryId': 1, 'SKUPrefix': None, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'TypeID': None, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 2, 'Name': '瓜类', 'DisplaySequence': 2, 'ParentCategoryId': 0, 'Depth': 1, 'Path': '2', 'HasChildren': False, 'IsEnable': True, 'TopCategoryId': 2, 'SKUPrefix': None, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'TypeID': None, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 3, 'Name': '进口水果', 'DisplaySequence': 3, 'ParentCategoryId': 0, 'Depth': 1, 'Path': '3', 'HasChildren': False, 'IsEnable': True, 'TopCategoryId': 3, 'SKUPrefix': None, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'TypeID': None, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 4, 'Name': '蔬菜类', 'DisplaySequence': 4, 'ParentCategoryId': 0, 'Depth': 1, 'Path': '4', 'HasChildren': False, 'IsEnable': True, 'TopCategoryId': 4, 'SKUPrefix': None, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'TypeID': None, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 5, 'Name': '国产水果', 'DisplaySequence': 5, 'ParentCategoryId': 0, 'Depth': 1, 'Path': '5', 'HasChildren': False, 'IsEnable': True, 'TopCategoryId': 5, 'SKUPrefix': None, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'TypeID': None, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 6, 'Name': '芒果类', 'DisplaySequence': 6, 'ParentCategoryId': 0, 'Depth': 1, 'Path': '6', 'HasChildren': False, 'IsEnable': True, 'TopCategoryId': 6, 'SKUPrefix': None, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'TypeID': None, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 7, 'Name': '柠檬金桔猕猴桃百香果', 'DisplaySequence': 7, 'ParentCategoryId': 0, 'Depth': 1, 'Path': '7', 'HasChildren': False, 'IsEnable': True, 'TopCategoryId': 7, 'SKUPrefix': None, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'TypeID': None, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 35, 'Name': '自助餐类', 'DisplaySequence': 7, 'ParentCategoryId': 0, 'Depth': 1, 'Path': '35', 'HasChildren': False, 'IsEnable': True, 'TopCategoryId': 35, 'SKUPrefix': None, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'TypeID': None, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 8, 'Name': '副食品', 'DisplaySequence': 8, 'ParentCategoryId': 0, 'Depth': 1, 'Path': '8', 'HasChildren': False, 'IsEnable': True, 'TopCategoryId': 8, 'SKUPrefix': None, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'TypeID': None, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}, {'CategoryId': 10, 'Name': '精品菜类', 'DisplaySequence': 10, 'ParentCategoryId': 0, 'Depth': 1, 'Path': '10', 'HasChildren': False, 'IsEnable': True, 'TopCategoryId': 10, 'SKUPrefix': None, 'MetaDescription': None, 'MetaTitle': None, 'MetaKeywords': None, 'Notes1': None, 'Notes2': None, 'Notes3': None, 'Notes4': None, 'Notes5': None, 'Icon': None, 'RewriteName': None, 'AssociatedProductType': None, 'Theme': None, 'TypeID': None, 'IsCheck': False, 'CategoryInfoList': None, 'EnableShowMember': 0}]\n"]}], "source": ["body={\n", "  \"app_type\": \"1\",\n", "  \"access_token\": token['Data']['access_token'],\n", "  \"role\": \"member\",\n", "  \"check_url_id\": \"5432ef495f6147e1b726ea9b663441fa\"\n", "}\n", "\n", "category_list=json.loads(post_remote_data_with_proxy('https://ytgydhy.mdydt.net/api/ShopAPI/Product/GetCategorysList', json=body).text)['Data']['categorys_list_stage']\n", "print(category_list)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: http://18258841203:8gTcEKLs@*************:41352\n", "草莓类, 商品数:5, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '01006', 'ProductId': 6, 'ProductName': '本地草莓大果', 'RankPrice': 6.1, 'SkuID': '6_0', 'Standard': '一件15斤', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_6fe2fa04-1b69-45e8-aa27-94a73d63b9e8.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_6fe2fa04-1b69-45e8-aa27-94a73d63b9e8.jpg', 'Type': '', 'UnitId': 20, 'UnitName': '斤', 'VirtualStock': '6153.84', 'ptypeUnits': [{'ProductCode': '01006', 'UnitId': 20, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.1, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '01006', 'UnitId': 761, 'Uname': '斤（毛重）', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 15.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '01006', 'UnitId': 21, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.1, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "Using proxy: *************************************************\n", "瓜类, 商品数:16, 商品:{'EnableMOQ': True, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '02077', 'ProductId': 436, 'ProductName': '无籽西瓜（毛重带箱）', 'RankPrice': 1.9, 'SkuID': '436_0', 'Standard': '', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_53b56020-2577-4881-95f2-38382e2eda86.png', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_53b56020-2577-4881-95f2-38382e2eda86.png', 'Type': '', 'UnitId': 1157, 'UnitName': '斤', 'VirtualStock': '30115.95', 'ptypeUnits': [{'ProductCode': '02077', 'UnitId': 1157, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 30.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '02077', 'UnitId': 1158, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 15.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "Using proxy: http://18258841203:8gTcEKLs@*************:41352\n", "进口水果, 商品数:11, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '03003', 'ProductId': 37, 'ProductName': '进口西柚', 'RankPrice': 5.0, 'SkuID': '37_0', 'Standard': '1个约0.6斤，1件50个 1份/6个', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_ce5968a5-3168-4016-8fbd-ead9434558d8.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_ce5968a5-3168-4016-8fbd-ead9434558d8.jpg', 'Type': '', 'UnitId': 90, 'UnitName': '个', 'VirtualStock': '5513.89', 'ptypeUnits': [{'ProductCode': '03003', 'UnitId': 90, 'Uname': '个', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '03003', 'UnitId': 92, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.5, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '03003', 'UnitId': 656, 'Uname': '份', 'CarCount': 0.0, 'Keyid': None, 'URate': 3.6, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '03003', 'UnitId': 93, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 4.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '03003', 'UnitId': 835, 'Uname': '件', 'CarCount': 0.0, 'Keyid': None, 'URate': 50.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "Using proxy: ***********************************************\n", "蔬菜类, 商品数:17, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '04004', 'ProductId': 64, 'ProductName': '日本乳瓜', 'RankPrice': 6.1, 'SkuID': '64_0', 'Standard': '', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_46edf3d9-2c7d-42fd-a38a-4db03c606142.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_46edf3d9-2c7d-42fd-a38a-4db03c606142.jpg', 'Type': '', 'UnitId': 152, 'UnitName': '斤', 'VirtualStock': '235.43', 'ptypeUnits': [{'ProductCode': '04004', 'UnitId': 152, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '04004', 'UnitId': 153, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '04004', 'UnitId': 154, 'Uname': '件', 'CarCount': 0.0, 'Keyid': None, 'URate': 19.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "Using proxy: ***********************************************\n", "国产水果, 商品数:43, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '05003', 'ProductId': 114, 'ProductName': '雪梨', 'RankPrice': 2.8, 'SkuID': '114_0', 'Standard': '约0.4斤/个', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_094d25ad-5812-42cf-bab4-570ca4f62dcb.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_094d25ad-5812-42cf-bab4-570ca4f62dcb.jpg', 'Type': '', 'UnitId': 250, 'UnitName': '斤', 'VirtualStock': '499813.9', 'ptypeUnits': [{'ProductCode': '05003', 'UnitId': 250, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05003', 'UnitId': 251, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05003', 'UnitId': 252, 'Uname': '件', 'CarCount': 0.0, 'Keyid': None, 'URate': 33.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "Using proxy: *************************************************\n", "芒果类, 商品数:4, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '06001', 'ProductId': 186, 'ProductName': '小台农', 'RankPrice': 5.3, 'SkuID': '186_0', 'Standard': '1件带框38斤左右', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_7d8e6a13-525c-4d68-9c67-6f89f05cbaaa.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_7d8e6a13-525c-4d68-9c67-6f89f05cbaaa.jpg', 'Type': '', 'UnitId': 407, 'UnitName': '斤', 'VirtualStock': '36601.28', 'ptypeUnits': [{'ProductCode': '06001', 'UnitId': 407, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '06001', 'UnitId': 1106, 'Uname': '斤（毛重）', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 38.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '06001', 'UnitId': 408, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '06001', 'UnitId': 663, 'Uname': '份', 'CarCount': 0.0, 'Keyid': None, 'URate': 8.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "Using proxy: ************************************************\n", "柠檬金桔猕猴桃百香果, 商品数:11, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '07001', 'ProductId': 198, 'ProductName': '黄柠檬', 'RankPrice': 3.1, 'SkuID': '198_0', 'Standard': '约0.2斤/个', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_f0f7dd86-10bb-4905-88ca-1e7620d56737.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_f0f7dd86-10bb-4905-88ca-1e7620d56737.jpg', 'Type': '', 'UnitId': 432, 'UnitName': '斤', 'VirtualStock': '48340.16', 'ptypeUnits': [{'ProductCode': '07001', 'UnitId': 432, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.5, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '07001', 'UnitId': 434, 'Uname': '个', 'CarCount': 0.0, 'Keyid': None, 'URate': 0.2, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '07001', 'UnitId': 861, 'Uname': '斤（毛重）', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '07001', 'UnitId': 433, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '07001', 'UnitId': 666, 'Uname': '份', 'CarCount': 0.0, 'Keyid': None, 'URate': 5.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "Using proxy: ***********************************************\n", "自助餐类, 商品数:8, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '05032', 'ProductId': 143, 'ProductName': '芭乐', 'RankPrice': 4.4, 'SkuID': '143_0', 'Standard': '一个约0.4斤', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_bd10bb56-6f63-4391-9568-a5d5196e680a.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_bd10bb56-6f63-4391-9568-a5d5196e680a.jpg', 'Type': '', 'UnitId': 321, 'UnitName': '斤', 'VirtualStock': '4018.57', 'ptypeUnits': [{'ProductCode': '05032', 'UnitId': 321, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05032', 'UnitId': 722, 'Uname': '斤（毛重）', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05032', 'UnitId': 751, 'Uname': '500克/份', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05032', 'UnitId': 723, 'Uname': '公斤（毛重)', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '05032', 'UnitId': 322, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 0, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "Using proxy: ***********************************************\n", "副食品, 商品数:2, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '08002', 'ProductId': 212, 'ProductName': '鸡蛋', 'RankPrice': 7.0, 'SkuID': '212_0', 'Standard': '一板大概4斤左右', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_b3d6d185-f410-4ebc-903a-cc651ef5b91b.png', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_b3d6d185-f410-4ebc-903a-cc651ef5b91b.png', 'Type': '', 'UnitId': 462, 'UnitName': '斤', 'VirtualStock': '40722.6764', 'ptypeUnits': [{'ProductCode': '08002', 'UnitId': 462, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '08002', 'UnitId': 463, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 1.0, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n", "Using proxy: ************************************************\n", "精品菜类, 商品数:11, 商品:{'EnableMOQ': False, 'EnableNoStock': True, 'HasSKU': False, 'MarketPrice': 0.0, 'ProductCode': '11002', 'ProductId': 217, 'ProductName': '薄荷叶', 'RankPrice': 13.2, 'SkuID': '217_0', 'Standard': '', 'Stock': None, 'ThumbnailUrl220': '/Storage/master/product/thumbs220/220_eed4dd9f-a29b-499d-aa43-1f712422a9f6.jpg', 'ThumbnailUrl310': '/Storage/master/product/thumbs310/310_eed4dd9f-a29b-499d-aa43-1f712422a9f6.jpg', 'Type': '', 'UnitId': 471, 'UnitName': '斤', 'VirtualStock': '498.88', 'ptypeUnits': [{'ProductCode': '11002', 'UnitId': 471, 'Uname': '斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 1.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.1, 'GjpBaseUnit': 1, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 1, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}, {'ProductCode': '11002', 'UnitId': 472, 'Uname': '公斤', 'CarCount': 0.0, 'Keyid': None, 'URate': 2.0, 'IsDefaultUnit': 1, 'ProductBarCode': None, 'MOQ': 0.1, 'GjpBaseUnit': 0, 'SalePrice': 0.0, 'MarketPrice': 0.0, 'Stock': 0.0, 'VirtualStock': 0.0, 'OccupyStock': 0.0, 'ShowSalePrice': 0.0, 'ShowUnit': 0, 'HasBuyCount': 0.0, 'Purchase': None, 'PurchaseCanBuy': None}], 'Brand': '其他品牌', 'ProArea': '', 'proareaName': '', 'UsefulLifeDay': 0, 'IsCountDown': False, 'IsGroupBuy': False, 'IsCanBuy': False, 'ProductIsCanBuy': True, 'PermitNo': '', 'PermitNoPeriod': '', 'TypeStatus': '', 'UsefulLifeMonth': '0', 'StorageCondition': '', 'TradeMark': '', 'SubUnitName': '', 'SubUnitStock': 0.0, 'PromotionInfoList': None}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EnableMOQ</th>\n", "      <th>EnableNoStock</th>\n", "      <th>HasSKU</th>\n", "      <th>MarketPrice</th>\n", "      <th>ProductCode</th>\n", "      <th>ProductId</th>\n", "      <th>ProductName</th>\n", "      <th>RankPrice</th>\n", "      <th>SkuID</th>\n", "      <th>Standard</th>\n", "      <th>...</th>\n", "      <th>ProductIsCanBuy</th>\n", "      <th>PermitNo</th>\n", "      <th>PermitNoPeriod</th>\n", "      <th>TypeStatus</th>\n", "      <th>UsefulLifeMonth</th>\n", "      <th>StorageCondition</th>\n", "      <th>TradeMark</th>\n", "      <th>SubUnitName</th>\n", "      <th>SubUnitStock</th>\n", "      <th>PromotionInfoList</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01006</td>\n", "      <td>6</td>\n", "      <td>本地草莓大果</td>\n", "      <td>6.1</td>\n", "      <td>6_0</td>\n", "      <td>一件15斤</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01007</td>\n", "      <td>7</td>\n", "      <td>本地草莓中果</td>\n", "      <td>4.4</td>\n", "      <td>7_0</td>\n", "      <td>毛重14-17斤/件</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01010</td>\n", "      <td>10</td>\n", "      <td>丹东红颜草莓（4*5)）</td>\n", "      <td>9.9</td>\n", "      <td>10_0</td>\n", "      <td>20颗一盒</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01013</td>\n", "      <td>13</td>\n", "      <td>丹东十合一草莓</td>\n", "      <td>35.0</td>\n", "      <td>13_0</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01016</td>\n", "      <td>254</td>\n", "      <td>红颜板装草莓（3斤装）</td>\n", "      <td>49.5</td>\n", "      <td>254_0</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02077</td>\n", "      <td>436</td>\n", "      <td>无籽西瓜（毛重带箱）</td>\n", "      <td>1.9</td>\n", "      <td>436_0</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02003</td>\n", "      <td>17</td>\n", "      <td>黑美人</td>\n", "      <td>2.8</td>\n", "      <td>17_0</td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02001</td>\n", "      <td>15</td>\n", "      <td>无籽西瓜</td>\n", "      <td>2.2</td>\n", "      <td>15_0</td>\n", "      <td>特大 一件27-30斤</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02079</td>\n", "      <td>438</td>\n", "      <td>美都麒麟瓜（毛重带箱）</td>\n", "      <td>6.1</td>\n", "      <td>438_0</td>\n", "      <td>个别有籽</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>02008</td>\n", "      <td>22</td>\n", "      <td>黄皮哈密瓜</td>\n", "      <td>5.0</td>\n", "      <td>22_0</td>\n", "      <td>约3.6斤/个</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 35 columns</p>\n", "</div>"], "text/plain": ["   EnableMOQ  EnableNoStock  HasSKU  MarketPrice ProductCode  ProductId  \\\n", "0      False           True   False          0.0       01006          6   \n", "1      False           True   False          0.0       01007          7   \n", "2      False           True   False          0.0       01010         10   \n", "3      False           True   False          0.0       01013         13   \n", "4      False           True   False          0.0       01016        254   \n", "5       True           True   False          0.0       02077        436   \n", "6      False           True   False          0.0       02003         17   \n", "7       True           True   False          0.0       02001         15   \n", "8      False           True   False          0.0       02079        438   \n", "9      False           True   False          0.0       02008         22   \n", "\n", "    ProductName  RankPrice  SkuID     Standard  ... ProductIsCanBuy PermitNo  \\\n", "0        本地草莓大果        6.1    6_0        一件15斤  ...            True            \n", "1        本地草莓中果        4.4    7_0   毛重14-17斤/件  ...            True            \n", "2  丹东红颜草莓（4*5)）        9.9   10_0        20颗一盒  ...            True            \n", "3       丹东十合一草莓       35.0   13_0               ...            True            \n", "4   红颜板装草莓（3斤装）       49.5  254_0               ...            True            \n", "5    无籽西瓜（毛重带箱）        1.9  436_0               ...            True            \n", "6           黑美人        2.8   17_0               ...            True            \n", "7          无籽西瓜        2.2   15_0  特大 一件27-30斤  ...            True            \n", "8   美都麒麟瓜（毛重带箱）        6.1  438_0         个别有籽  ...            True            \n", "9         黄皮哈密瓜        5.0   22_0      约3.6斤/个  ...            True            \n", "\n", "  PermitNoPeriod TypeStatus  UsefulLifeMonth StorageCondition TradeMark  \\\n", "0                                          0                              \n", "1                                          0                              \n", "2                                          0                              \n", "3                                          0                              \n", "4                                          0                              \n", "5                                          0                              \n", "6                                          0                              \n", "7                                          0                              \n", "8                                          0                              \n", "9                                          0                              \n", "\n", "  SubUnitName SubUnitStock PromotionInfoList  \n", "0                      0.0              None  \n", "1                      0.0              None  \n", "2                      0.0              None  \n", "3                      0.0              None  \n", "4                      0.0              None  \n", "5                      0.0              None  \n", "6                      0.0              None  \n", "7                      0.0              None  \n", "8                      0.0              None  \n", "9                      0.0              None  \n", "\n", "[10 rows x 35 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["params={\n", "    \"flag\": \"0\",\n", "    \"standard\": \"\",\n", "    \"app_type\": \"1\",\n", "    \"key_words\": \"\",\n", "    \"request_source\": \"products_product-list\",\n", "    \"category_id\": \"2\",\n", "    \"max_sale_price\": \"\",\n", "    \"min_sale_price\": \"\",\n", "    \"is_count_down\": \"false\",\n", "    \"is_group_buy\": \"false\",\n", "    \"onlyinstock\": \"\",\n", "    \"tagids\": \"\",\n", "    \"activity_id\": \"\",\n", "    \"brands\": \"\",\n", "    \"sort_order_by\": \"DisplaySequence\",\n", "    \"sort_order\": \"Asc\",\n", "    \"page_index\": \"1\",\n", "    \"page_size\": \"100\",\n", "    \"access_token\": token['Data']['access_token'],\n", "    \"role\": \"member\",\n", "    \"check_url_id\": \"5432ef495f6147e1b726ea9b663441fa\"\n", "}\n", "\n", "product_list_all=[]\n", "for category in category_list:\n", "    name = category['Name']\n", "    CategoryId = category['CategoryId']\n", "    params[\"category_id\"]=CategoryId\n", "    product_list=json.loads(post_remote_data_with_proxy('https://ytgydhy.mdydt.net/api/ShopAPI/ProductInfos/GetProductsList', json=params).text)['Data']['ProducetList']\n", "    print(f'{name}, 商品数:{len(product_list)}, 商品:{product_list[0]}')\n", "    product_list_all.extend(product_list)\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EnableMOQ</th>\n", "      <th>EnableNoStock</th>\n", "      <th>HasSKU</th>\n", "      <th>MarketPrice</th>\n", "      <th>ProductCode</th>\n", "      <th>ProductId</th>\n", "      <th>ProductName</th>\n", "      <th>RankPrice</th>\n", "      <th>SkuID</th>\n", "      <th>Standard</th>\n", "      <th>...</th>\n", "      <th>ProductIsCanBuy</th>\n", "      <th>PermitNo</th>\n", "      <th>PermitNoPeriod</th>\n", "      <th>TypeStatus</th>\n", "      <th>UsefulLifeMonth</th>\n", "      <th>StorageCondition</th>\n", "      <th>TradeMark</th>\n", "      <th>SubUnitName</th>\n", "      <th>SubUnitStock</th>\n", "      <th>PromotionInfoList</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01006</td>\n", "      <td>6</td>\n", "      <td>本地草莓大果</td>\n", "      <td>6.1</td>\n", "      <td>6_0</td>\n", "      <td>一件15斤</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01007</td>\n", "      <td>7</td>\n", "      <td>本地草莓中果</td>\n", "      <td>4.4</td>\n", "      <td>7_0</td>\n", "      <td>毛重14-17斤/件</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0.0</td>\n", "      <td>01010</td>\n", "      <td>10</td>\n", "      <td>丹东红颜草莓（4*5)）</td>\n", "      <td>9.9</td>\n", "      <td>10_0</td>\n", "      <td>20颗一盒</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 35 columns</p>\n", "</div>"], "text/plain": ["   EnableMOQ  EnableNoStock  HasSKU  MarketPrice ProductCode  ProductId  \\\n", "0      False           True   False          0.0       01006          6   \n", "1      False           True   False          0.0       01007          7   \n", "2      False           True   False          0.0       01010         10   \n", "\n", "    ProductName  RankPrice SkuID    Standard  ... ProductIsCanBuy PermitNo  \\\n", "0        本地草莓大果        6.1   6_0       一件15斤  ...            True            \n", "1        本地草莓中果        4.4   7_0  毛重14-17斤/件  ...            True            \n", "2  丹东红颜草莓（4*5)）        9.9  10_0       20颗一盒  ...            True            \n", "\n", "  PermitNoPeriod TypeStatus  UsefulLifeMonth StorageCondition TradeMark  \\\n", "0                                          0                              \n", "1                                          0                              \n", "2                                          0                              \n", "\n", "  SubUnitName SubUnitStock PromotionInfoList  \n", "0                      0.0              None  \n", "1                      0.0              None  \n", "2                      0.0              None  \n", "\n", "[3 rows x 35 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(product_list_all_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:本地草莓大果, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:636.4679336547852ms\n", "inputText:本地草莓中果, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:613.9049530029297ms\n", "inputText:丹东红颜草莓（4*5)）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:538.0153656005859ms\n", "inputText:丹东十合一草莓, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:711.0068798065186ms\n", "inputText:红颜板装草莓（3斤装）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:749.6259212493896ms\n", "inputText:无籽西瓜（毛重带箱）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:549.8132705688477ms\n", "inputText:黑美人, usage:{\"prompt_tokens\": 3, \"total_tokens\": 3}, time cost:679.5666217803955ms\n", "inputText:无籽西瓜, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:650.5551338195801ms\n", "inputText:美都麒麟瓜（毛重带箱）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:955.5835723876953ms\n", "inputText:黄皮哈密瓜, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:564.988374710083ms\n", "inputText:木瓜, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:543.0219173431396ms\n", "inputText:玫珑瓜, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:661.9260311126709ms\n", "inputText:特小凤, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:719.0971374511719ms\n", "inputText:白地瓜, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:580.1506042480469ms\n", "inputText:玉菇香瓜, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:552.8805255889893ms\n", "inputText:哈密瓜, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:585.1719379425049ms\n", "inputText:玉菇甜瓜, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:633.3363056182861ms\n", "inputText:晓蜜哈密瓜（小果）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:547.6646423339844ms\n", "inputText:黑美人（毛重）, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:585.1471424102783ms\n", "inputText:哈密瓜小果（毛重）, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:647.416353225708ms\n", "inputText:玉菇甜瓜（毛重）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:586.6265296936035ms\n", "inputText:进口西柚, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:544.3129539489746ms\n", "inputText:牛油果中果, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:598.7958908081055ms\n", "inputText:泰国龙眼, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:655.4780006408691ms\n", "inputText:车厘子(ld), usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:636.5609169006348ms\n", "inputText:树莓, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:726.1455059051514ms\n", "inputText:蓝莓, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:594.1805839538574ms\n", "inputText:红心火龙果, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:566.6875839233398ms\n", "inputText:佳农凤梨7-8, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:702.6269435882568ms\n", "inputText:车厘子3J, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:563.9026165008545ms\n", "inputText:龙眼, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:557.9473972320557ms\n", "inputText:泰国椰皇（40头）, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:669.5024967193604ms\n", "inputText:日本乳瓜, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:535.6423854827881ms\n", "inputText:菜椒, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:583.3899974822998ms\n", "inputText:红彩椒, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:521.2366580963135ms\n", "inputText:黄彩椒, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:663.254976272583ms\n", "inputText:红萝卜, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:739.9609088897705ms\n", "inputText:欧芹, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:620.2938556671143ms\n", "inputText:菠菜, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:616.631031036377ms\n", "inputText:老南瓜, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:616.5459156036377ms\n", "inputText:黄瓜, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:535.0959300994873ms\n", "inputText:春菜, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:702.5318145751953ms\n", "inputText:西红柿, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:646.8567848205566ms\n", "inputText:西生菜, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:633.3441734313965ms\n", "inputText:洋葱, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:533.6675643920898ms\n", "inputText:紫包菜, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:495.4214096069336ms\n", "inputText:小葱, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:499.2070198059082ms\n", "inputText:山东蜜薯, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:499.1590976715088ms\n", "inputText:羽衣甘蓝, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:521.6798782348633ms\n", "inputText:雪梨, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:495.52345275878906ms\n", "inputText:皇冠梨, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:649.7337818145752ms\n", "inputText:砀山梨, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:583.5840702056885ms\n", "inputText:赣南脐橙90-100, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:602.2193431854248ms\n", "inputText:本地香梨, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:634.0382099151611ms\n", "inputText:新疆香梨, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:613.5697364807129ms\n", "inputText:巨峰葡萄, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:633.450984954834ms\n", "inputText:夏黑葡萄, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:865.7629489898682ms\n", "inputText:香印青提, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:841.3064479827881ms\n", "inputText:菠萝, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:606.4572334289551ms\n", "inputText:小可爱, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:600.031852722168ms\n", "inputText:香蕉, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:619.4784641265869ms\n", "inputText:火龙果, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:562.5536441802979ms\n", "inputText:红心芭乐, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:569.8668956756592ms\n", "inputText:人参果, usage:{\"prompt_tokens\": 3, \"total_tokens\": 3}, time cost:518.4199810028076ms\n", "inputText:樱桃, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:532.4664115905762ms\n", "inputText:桑葚, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:551.520824432373ms\n", "inputText:青橄榄, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:727.567195892334ms\n", "inputText:菠萝蜜肉, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:638.6885643005371ms\n", "inputText:去皮马蹄, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:622.6284503936768ms\n", "inputText:小金橘, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:599.8189449310303ms\n", "inputText:杨桃, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:532.421350479126ms\n", "inputText:砂糖橘, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:601.9244194030762ms\n", "inputText:青枣, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:699.9664306640625ms\n", "inputText:芦柑, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:551.1023998260498ms\n", "inputText:本地西柚, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:577.4478912353516ms\n", "inputText:赣南脐橙70-80, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:583.0821990966797ms\n", "inputText:翠香猕猴桃, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:534.4192981719971ms\n", "inputText:赣南脐橙80-90, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:783.1263542175293ms\n", "inputText:苹果80-90, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:557.7788352966309ms\n", "inputText:苹果90-100, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:702.4977207183838ms\n", "inputText:本地红提, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:736.8016242980957ms\n", "inputText:佳农椰青, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:703.5853862762451ms\n", "inputText:耙耙柑, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:833.33420753479ms\n", "inputText:青木瓜, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:555.4020404815674ms\n", "inputText:榨汁苹果（毛重）, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:587.8152847290039ms\n", "inputText:榨汁榨汁翠冠梨（毛重）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:582.486629486084ms\n", "inputText:永定红柿子（毛重）, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:674.9305725097656ms\n", "inputText:七月黄柿子（毛重）, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:576.890230178833ms\n", "inputText:翠香奇峰奇异果大果, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:574.141263961792ms\n", "inputText:泰国椰皇（36头）, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:608.4504127502441ms\n", "inputText:秘鲁青提, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:620.5651760101318ms\n", "inputText:龙眼., usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:599.9512672424316ms\n", "inputText:小台农, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:1234.8182201385498ms\n", "inputText:青芒, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:660.84885597229ms\n", "inputText:水仙芒, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:578.8054466247559ms\n", "inputText:小台农二级, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:645.2634334564209ms\n", "inputText:黄柠檬, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:707.5088024139404ms\n", "inputText:广东香水柠檬二级果, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:670.6111431121826ms\n", "inputText:长香水柠檬, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:710.3865146636963ms\n", "inputText:广东香水柠檬, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:748.2993602752686ms\n", "inputText:百香果, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:554.6815395355225ms\n", "inputText:百香果次果, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:524.9791145324707ms\n", "inputText:猕猴桃, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:527.571439743042ms\n", "inputText:小青桔, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:644.1142559051514ms\n", "inputText:青柠檬, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:599.6260643005371ms\n", "inputText:广东香水一级半, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:607.8367233276367ms\n", "inputText:猕猴桃., usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:704.0202617645264ms\n", "inputText:芭乐, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:569.1931247711182ms\n", "inputText:红李子, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:511.58857345581055ms\n", "inputText:白柚子, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:597.1341133117676ms\n", "inputText:红柚子, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:527.1849632263184ms\n", "inputText:冬枣, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:633.6567401885986ms\n", "inputText:哈密瓜（毛重带箱）, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:612.3321056365967ms\n", "inputText:小可爱., usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:633.8508129119873ms\n", "inputText:脆锅巴零食, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:628.8328170776367ms\n", "inputText:鸡蛋, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:668.0207252502441ms\n", "inputText:洁净蛋, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:533.6332321166992ms\n", "inputText:薄荷叶, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:525.2995491027832ms\n", "inputText:三色堇, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:530.2107334136963ms\n", "inputText:迷迭香, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:569.2892074584961ms\n", "inputText:柠檬叶, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:676.4383316040039ms\n", "inputText:香茅草, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:824.9413967132568ms\n", "inputText:罗马生菜, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:519.8945999145508ms\n", "inputText:红叶生菜, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:512.0372772216797ms\n", "inputText:手指萝卜, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:904.7379493713379ms\n", "inputText:樱桃萝卜, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:504.63032722473145ms\n", "inputText:黄苦菊, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:574.7125148773193ms\n", "inputText:酸膜叶, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:662.254810333252ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['ProductName'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
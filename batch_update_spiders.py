#!/usr/bin/env python3
"""
批量更新爬虫脚本，使用新的SpiderResultReporter类替换原有的===new_record===输出方式
"""

import os
import re
import glob
from pathlib import Path

def update_spider_file(file_path):
    """更新单个爬虫文件"""
    print(f"正在处理: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经更新过
    if 'create_spider_reporter' in content and 'spider_reporter.report_' in content:
        print(f"  跳过 {file_path} - 已经更新过")
        return False
    
    # 检查是否包含===new_record===
    if '===new_record===' not in content:
        print(f"  跳过 {file_path} - 不包含===new_record===")
        return False
    
    original_content = content
    
    # 1. 添加import
    if 'from proxy_setup import' in content:
        # 找到proxy_setup的import语句
        import_pattern = r'from proxy_setup import \((.*?)\)'
        match = re.search(import_pattern, content, re.DOTALL)
        if match:
            imports = match.group(1)
            if 'create_spider_reporter' not in imports:
                # 添加create_spider_reporter到import列表
                new_imports = imports.rstrip() + ',\n    create_spider_reporter,\n'
                content = content.replace(match.group(1), new_imports)
    
    # 2. 提取品牌名称和文件名
    file_name = os.path.basename(file_path)
    
    # 尝试从代码中提取brand_name
    brand_pattern = r'brand_name\s*=\s*["\']([^"\']+)["\']'
    brand_match = re.search(brand_pattern, content)
    brand_name = brand_match.group(1) if brand_match else file_name.replace('_spider.py', '').replace('.py', '')
    
    # 3. 添加spider_reporter创建代码
    # 在brand_name定义后添加
    if brand_match:
        insert_pos = brand_match.end()
        reporter_code = f'\n\n# 创建爬虫结果报告器\nspider_reporter = create_spider_reporter("{file_name}", brand_name)'
        content = content[:insert_pos] + reporter_code + content[insert_pos:]
    
    # 4. 替换===new_record===输出
    # 查找所有的===new_record===模式
    record_patterns = [
        r'logging\.info\(f"===new_record===([^"]+)"\)',
        r'logging\.info\(f\'===new_record===([^\']+)\'\)',
        r'print\(f"===new_record===([^"]+)"\)',
        r'print\(f\'===new_record===([^\']+)\'\)'
    ]
    
    for pattern in record_patterns:
        matches = list(re.finditer(pattern, content))
        for match in reversed(matches):  # 从后往前替换，避免位置偏移
            full_match = match.group(0)
            record_info = match.group(1)
            
            # 解析商品数量
            count_pattern = r'商品数:(\{[^}]+\}|\w+)'
            count_match = re.search(count_pattern, record_info)
            if count_match:
                count_var = count_match.group(1)
                if count_var.startswith('{') and count_var.endswith('}'):
                    count_var = count_var[1:-1]  # 移除大括号
                
                # 生成新的报告代码
                new_code = f'''# 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count={count_var},
        additional_info={{
            "odps_table": table_name if 'table_name' in locals() else "unknown",
            "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }}
    )'''
                
                content = content.replace(full_match, new_code)
    
    # 5. 处理失败情况的输出
    # 查找写入ODPS失败的日志
    failure_patterns = [
        r'logging\.info\(f"([^"]*), 写入ODPS失败"\)',
        r'logging\.info\(f\'([^\']*), 写入ODPS失败\'\)',
        r'print\(f"([^"]*), 写入ODPS失败"\)',
        r'print\(f\'([^\']*), 写入ODPS失败\'\)'
    ]
    
    for pattern in failure_patterns:
        matches = list(re.finditer(pattern, content))
        for match in reversed(matches):
            full_match = match.group(0)
            brand_info = match.group(1)
            
            new_code = f'''# 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={{
            "odps_table": table_name if 'table_name' in locals() else "unknown",
            "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }}
    )'''
            
            content = content.replace(full_match, new_code)
    
    # 检查是否有实际修改
    if content != original_content:
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ 已更新 {file_path}")
        return True
    else:
        print(f"  ⚠️  {file_path} - 没有找到可更新的模式")
        return False

def main():
    """主函数"""
    scripts_dir = Path(__file__).parent / "scripts"
    
    # 获取所有Python爬虫文件
    spider_files = list(scripts_dir.glob("*_spider.py"))
    spider_files.extend(scripts_dir.glob("*spider*.py"))
    
    # 排除已经手动更新的文件
    exclude_files = {
        "baijian_spider.py",
        "beidian_spider.py", 
        "youzan_spider.py",
        "proxy_setup.py",
        "proxy_setup2.py"
    }
    
    spider_files = [f for f in spider_files if f.name not in exclude_files]
    
    print(f"找到 {len(spider_files)} 个爬虫文件需要更新")
    
    updated_count = 0
    for file_path in spider_files:
        if update_spider_file(file_path):
            updated_count += 1
    
    print(f"\n总结: 成功更新了 {updated_count} 个文件")

if __name__ == "__main__":
    main()

{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "1708917467235, headers:{'token': '5319aeee-58ed-4f97-a28f-91da3c4f7575', 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "THREAD_CNT = int(os.environ.get('THREAD_CNT', 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "hints={'odps.sql.hive.compatible':True,'odps.sql.type.system.odps2':True}\n", "def get_odps_sql_result_as_df(sql):\n", "    instance=odps.execute_sql(sql, hints=hints)\n", "    instance.wait_for_success()\n", "    pd_df=None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={'token':'5319aeee-58ed-4f97-a28f-91da3c4f7575',\n", "         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "         'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}\n", "brand_name='答音云仓'\n", "competitor_name_en='dayinyuncang'\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 先登录\n", "\n", "获取token并保存"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sessionid: rilkv5d8yfacwltwfj9sif8yc512fbht\n", "response cookie:{'sessionid': 'rilkv5d8yfacwltwfj9sif8yc512fbht'}\n", "{'code': 0, 'msg': '登录成功', 'data': {'user_id': 3003009}} 登录成功\n"]}], "source": ["# 登录\n", "from urllib.parse import unquote\n", "\n", "url='https://bshop.guanmai.cn/login'\n", "login_response=requests.post(url, headers=headers, data={'username':'17729941198', 'password':'aa123456'})\n", "\n", "\n", "after_login_cookie={}\n", "# Print all the cookies set by the server\n", "for cookie in login_response.cookies:\n", "    print(f'{cookie.name}: {cookie.value}')\n", "    after_login_cookie[cookie.name]=cookie.value\n", "\n", "\n", "print(f\"response cookie:{after_login_cookie}\")\n", "print(login_response.json(),unquote(login_response.json()['msg']))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<!doctype html><html><head><meta charset=\"UTF-8\"/><meta name=\"format-detection\" content=\"telephone=n\n", "new sessionid:rilkv5d8yfacwltwfj9sif8yc512fbht\n"]}], "source": ["after_login_cookie.update({'cms_key':'cygyl','group_id':'3252'})\n", "after_login_cookie\n", "\n", "sessionid=after_login_cookie['sessionid']\n", "\n", "url = 'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp=1706286340876'\n", "\n", "headers = {\n", "    'authority': 'bshop.guanmai.cn',\n", "    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "    'accept-language': 'en-US,en;q=0.9',\n", "    'cookie': f'cms_key=cygyl; group_id=3252; sessionid={sessionid}; gr_user_id=62c026d8-a829-40c7-823f-d7e38bf255d6; 9beedda875b5420f_gr_session_id=2a97577a-00ae-45a7-8392-4cf0d0fde7cb; 9beedda875b5420f_gr_session_id_sent_vst=2a97577a-00ae-45a7-8392-4cf0d0fde7cb',\n", "    'referer': 'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp=1706286034360',\n", "    'sec-fetch-dest': 'document',\n", "    'sec-fetch-mode': 'navigate',\n", "    'sec-fetch-site': 'same-origin',\n", "    'sec-fetch-user': '?1',\n", "    'upgrade-insecure-requests': '1',\n", "    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'\n", "}\n", "\n", "response = requests.get(url, headers=headers)\n", "\n", "print(response.text[0:100])\n", "\n", "print(f\"new sessionid:{sessionid}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取一级类目列表"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'limit': 99, 'page': 1, 'type': 1}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>image</th>\n", "      <th>weigh</th>\n", "      <th>type_text</th>\n", "      <th>flag_text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230407/3...</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230407/2...</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20231209/2...</td>\n", "      <td>4</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230601/e...</td>\n", "      <td>74</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230601/5...</td>\n", "      <td>118</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>141</td>\n", "      <td>其他</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230601/b...</td>\n", "      <td>128</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230407/e...</td>\n", "      <td>141</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  name                                              image  weigh  \\\n", "0   92  鲜果鸡蛋  https://bd.dayinyuncang.com/uploads/20230407/3...      1   \n", "1   18   乳制品  https://bd.dayinyuncang.com/uploads/20230407/2...      2   \n", "2   17  烘焙辅料  https://bd.dayinyuncang.com/uploads/20231209/2...      4   \n", "3  119  甜品奶茶  https://bd.dayinyuncang.com/uploads/20230601/e...     74   \n", "4  111   巧克力  https://bd.dayinyuncang.com/uploads/20230601/5...    118   \n", "5  141    其他  https://bd.dayinyuncang.com/uploads/20230601/b...    128   \n", "6   74  包装用品  https://bd.dayinyuncang.com/uploads/20230407/e...    141   \n", "\n", "  type_text flag_text  \n", "0      None      None  \n", "1      None      None  \n", "2      None      None  \n", "3      None      None  \n", "4      None      None  \n", "5      None      None  \n", "6      None      None  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取一级类目列表\n", "\n", "import urllib.parse\n", "import json\n", "\n", "# Convert the dictionary into a JSON object\n", "json_obj = {'limit':99, 'page':1, 'type':1}\n", "\n", "print(json_obj)\n", "\n", "url='https://bd.dayinyuncang.com/api/product/category'\n", "\n", "categoryList=requests.post(url, \n", "                        #    headers=headers,\n", "                            data=json_obj).json()['data']['data']\n", "category_list_df=pd.DataFrame(categoryList)\n", "category_list_df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["secondCategoryList:[{'id': 93, 'name': '芒果', 'image': '', 'weigh': 93, 'type_text': None, 'flag_text': None}, {'id': 94, 'name': '火龙果', 'image': '', 'weigh': 94, 'type_text': None, 'flag_text': None}, {'id': 95, 'name': '草莓｜蓝莓', 'image': '', 'weigh': 95, 'type_text': None, 'flag_text': None}, {'id': 96, 'name': '瓜类｜香蕉', 'image': '', 'weigh': 96, 'type_text': None, 'flag_text': None}, {'id': 97, 'name': '番茄｜桃子', 'image': '', 'weigh': 97, 'type_text': None, 'flag_text': None}, {'id': 101, 'name': '鸡蛋｜石榴', 'image': '', 'weigh': 98, 'type_text': None, 'flag_text': None}, {'id': 164, 'name': '葡提｜椰子', 'image': '', 'weigh': 99, 'type_text': None, 'flag_text': None}, {'id': 168, 'name': '应季水果', 'image': '', 'weigh': 100, 'type_text': None, 'flag_text': None}, {'id': 100, 'name': '猕猴桃', 'image': '', 'weigh': 101, 'type_text': None, 'flag_text': None}, {'id': 98, 'name': '橘子｜柠檬', 'image': '', 'weigh': 120, 'type_text': None, 'flag_text': None}, {'id': 120, 'name': '梨｜苹果', 'image': '', 'weigh': 164, 'type_text': None, 'flag_text': None}, {'id': 99, 'name': '凤梨｜菠萝', 'image': '', 'weigh': 166, 'type_text': None, 'flag_text': None}, {'id': 159, 'name': '牛油果', 'image': '', 'weigh': 168, 'type_text': None, 'flag_text': None}, {'id': 166, 'name': '水果制品', 'image': '', 'weigh': 170, 'type_text': None, 'flag_text': None}]\n", "secondCategoryList:[{'id': 102, 'name': '淡奶油', 'image': '', 'weigh': 102, 'type_text': None, 'flag_text': None}, {'id': 167, 'name': '散装奶油', 'image': '', 'weigh': 103, 'type_text': None, 'flag_text': None}, {'id': 103, 'name': '纯牛奶', 'image': '', 'weigh': 104, 'type_text': None, 'flag_text': None}, {'id': 104, 'name': '奶酪I芝士', 'image': '', 'weigh': 105, 'type_text': None, 'flag_text': None}, {'id': 105, 'name': '黄油', 'image': '', 'weigh': 107, 'type_text': None, 'flag_text': None}, {'id': 107, 'name': '奶粉', 'image': '', 'weigh': 167, 'type_text': None, 'flag_text': None}]\n", "secondCategoryList:[{'id': 108, 'name': '夹心馅料', 'image': '', 'weigh': 1, 'type_text': None, 'flag_text': None}, {'id': 158, 'name': '罐头', 'image': '', 'weigh': 1, 'type_text': None, 'flag_text': None}, {'id': 169, 'name': '饼干', 'image': '', 'weigh': 1, 'type_text': None, 'flag_text': None}, {'id': 136, 'name': '冻品原料', 'image': '', 'weigh': 44, 'type_text': None, 'flag_text': None}, {'id': 109, 'name': '砂糖', 'image': '', 'weigh': 108, 'type_text': None, 'flag_text': None}, {'id': 44, 'name': '面粉丨淀粉', 'image': '', 'weigh': 109, 'type_text': None, 'flag_text': None}, {'id': 122, 'name': '肉松', 'image': '', 'weigh': 122, 'type_text': None, 'flag_text': None}, {'id': 123, 'name': '调味酒', 'image': '', 'weigh': 123, 'type_text': None, 'flag_text': None}, {'id': 127, 'name': '添加剂', 'image': '', 'weigh': 127, 'type_text': None, 'flag_text': None}, {'id': 132, 'name': '油类', 'image': '', 'weigh': 132, 'type_text': None, 'flag_text': None}, {'id': 165, 'name': '坚果干货', 'image': '', 'weigh': 165, 'type_text': None, 'flag_text': None}]\n", "secondCategoryList:[{'id': 124, 'name': '沙拉酱', 'image': '', 'weigh': 124, 'type_text': None, 'flag_text': None}, {'id': 125, 'name': '果酱', 'image': '', 'weigh': 125, 'type_text': None, 'flag_text': None}, {'id': 131, 'name': '果汁原料', 'image': '', 'weigh': 131, 'type_text': None, 'flag_text': None}, {'id': 133, 'name': '罐头', 'image': '', 'weigh': 133, 'type_text': None, 'flag_text': None}, {'id': 137, 'name': '雪媚娘', 'image': '', 'weigh': 137, 'type_text': None, 'flag_text': None}, {'id': 138, 'name': '椰蓉', 'image': '', 'weigh': 138, 'type_text': None, 'flag_text': None}]\n", "secondCategoryList:[{'id': 139, 'name': '常规品', 'image': '', 'weigh': 112, 'type_text': None, 'flag_text': None}, {'id': 113, 'name': '装饰', 'image': '', 'weigh': 113, 'type_text': None, 'flag_text': None}, {'id': 112, 'name': '可可豆', 'image': '', 'weigh': 114, 'type_text': None, 'flag_text': None}, {'id': 114, 'name': '饼干', 'image': '', 'weigh': 116, 'type_text': None, 'flag_text': None}, {'id': 116, 'name': '寿桃', 'image': '', 'weigh': 135, 'type_text': None, 'flag_text': None}, {'id': 135, 'name': '装饰用碎', 'image': '', 'weigh': 139, 'type_text': None, 'flag_text': None}, {'id': 140, 'name': '立体类', 'image': '', 'weigh': 140, 'type_text': None, 'flag_text': None}]\n", "secondCategoryList:[{'id': 142, 'name': '餐具', 'image': '', 'weigh': 142, 'type_text': None, 'flag_text': None}, {'id': 143, 'name': '工具', 'image': '', 'weigh': 143, 'type_text': None, 'flag_text': None}]\n", "secondCategoryList:[{'id': 110, 'name': '刀叉盘子', 'image': '', 'weigh': 1, 'type_text': None, 'flag_text': None}, {'id': 77, 'name': '蛋糕盒', 'image': '', 'weigh': 2, 'type_text': None, 'flag_text': None}, {'id': 78, 'name': '丝带', 'image': '', 'weigh': 74, 'type_text': None, 'flag_text': None}, {'id': 80, 'name': '生日帽', 'image': '', 'weigh': 80, 'type_text': None, 'flag_text': None}]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>image</th>\n", "      <th>weigh</th>\n", "      <th>type_text</th>\n", "      <th>flag_text</th>\n", "      <th>first_category_id</th>\n", "      <th>first_category_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>93</td>\n", "      <td>芒果</td>\n", "      <td></td>\n", "      <td>93</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>94</td>\n", "      <td>火龙果</td>\n", "      <td></td>\n", "      <td>94</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>95</td>\n", "      <td>草莓｜蓝莓</td>\n", "      <td></td>\n", "      <td>95</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>96</td>\n", "      <td>瓜类｜香蕉</td>\n", "      <td></td>\n", "      <td>96</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>97</td>\n", "      <td>番茄｜桃子</td>\n", "      <td></td>\n", "      <td>97</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>101</td>\n", "      <td>鸡蛋｜石榴</td>\n", "      <td></td>\n", "      <td>98</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>164</td>\n", "      <td>葡提｜椰子</td>\n", "      <td></td>\n", "      <td>99</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>168</td>\n", "      <td>应季水果</td>\n", "      <td></td>\n", "      <td>100</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>100</td>\n", "      <td>猕猴桃</td>\n", "      <td></td>\n", "      <td>101</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>98</td>\n", "      <td>橘子｜柠檬</td>\n", "      <td></td>\n", "      <td>120</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>120</td>\n", "      <td>梨｜苹果</td>\n", "      <td></td>\n", "      <td>164</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>99</td>\n", "      <td>凤梨｜菠萝</td>\n", "      <td></td>\n", "      <td>166</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>159</td>\n", "      <td>牛油果</td>\n", "      <td></td>\n", "      <td>168</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>166</td>\n", "      <td>水果制品</td>\n", "      <td></td>\n", "      <td>170</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>102</td>\n", "      <td>淡奶油</td>\n", "      <td></td>\n", "      <td>102</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>167</td>\n", "      <td>散装奶油</td>\n", "      <td></td>\n", "      <td>103</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>103</td>\n", "      <td>纯牛奶</td>\n", "      <td></td>\n", "      <td>104</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>104</td>\n", "      <td>奶酪I芝士</td>\n", "      <td></td>\n", "      <td>105</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>105</td>\n", "      <td>黄油</td>\n", "      <td></td>\n", "      <td>107</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>107</td>\n", "      <td>奶粉</td>\n", "      <td></td>\n", "      <td>167</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>108</td>\n", "      <td>夹心馅料</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>158</td>\n", "      <td>罐头</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>169</td>\n", "      <td>饼干</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>136</td>\n", "      <td>冻品原料</td>\n", "      <td></td>\n", "      <td>44</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>109</td>\n", "      <td>砂糖</td>\n", "      <td></td>\n", "      <td>108</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>44</td>\n", "      <td>面粉丨淀粉</td>\n", "      <td></td>\n", "      <td>109</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>122</td>\n", "      <td>肉松</td>\n", "      <td></td>\n", "      <td>122</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>123</td>\n", "      <td>调味酒</td>\n", "      <td></td>\n", "      <td>123</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>127</td>\n", "      <td>添加剂</td>\n", "      <td></td>\n", "      <td>127</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>132</td>\n", "      <td>油类</td>\n", "      <td></td>\n", "      <td>132</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>165</td>\n", "      <td>坚果干货</td>\n", "      <td></td>\n", "      <td>165</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>124</td>\n", "      <td>沙拉酱</td>\n", "      <td></td>\n", "      <td>124</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>125</td>\n", "      <td>果酱</td>\n", "      <td></td>\n", "      <td>125</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>131</td>\n", "      <td>果汁原料</td>\n", "      <td></td>\n", "      <td>131</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>133</td>\n", "      <td>罐头</td>\n", "      <td></td>\n", "      <td>133</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>137</td>\n", "      <td>雪媚娘</td>\n", "      <td></td>\n", "      <td>137</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>138</td>\n", "      <td>椰蓉</td>\n", "      <td></td>\n", "      <td>138</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>139</td>\n", "      <td>常规品</td>\n", "      <td></td>\n", "      <td>112</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>113</td>\n", "      <td>装饰</td>\n", "      <td></td>\n", "      <td>113</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>112</td>\n", "      <td>可可豆</td>\n", "      <td></td>\n", "      <td>114</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>114</td>\n", "      <td>饼干</td>\n", "      <td></td>\n", "      <td>116</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>116</td>\n", "      <td>寿桃</td>\n", "      <td></td>\n", "      <td>135</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>135</td>\n", "      <td>装饰用碎</td>\n", "      <td></td>\n", "      <td>139</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>140</td>\n", "      <td>立体类</td>\n", "      <td></td>\n", "      <td>140</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>142</td>\n", "      <td>餐具</td>\n", "      <td></td>\n", "      <td>142</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>141</td>\n", "      <td>其他</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>143</td>\n", "      <td>工具</td>\n", "      <td></td>\n", "      <td>143</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>141</td>\n", "      <td>其他</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>110</td>\n", "      <td>刀叉盘子</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>77</td>\n", "      <td>蛋糕盒</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>78</td>\n", "      <td>丝带</td>\n", "      <td></td>\n", "      <td>74</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>80</td>\n", "      <td>生日帽</td>\n", "      <td></td>\n", "      <td>80</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     id   name image  weigh type_text flag_text  first_category_id  \\\n", "0    93     芒果           93      None      None                 92   \n", "1    94    火龙果           94      None      None                 92   \n", "2    95  草莓｜蓝莓           95      None      None                 92   \n", "3    96  瓜类｜香蕉           96      None      None                 92   \n", "4    97  番茄｜桃子           97      None      None                 92   \n", "5   101  鸡蛋｜石榴           98      None      None                 92   \n", "6   164  葡提｜椰子           99      None      None                 92   \n", "7   168   应季水果          100      None      None                 92   \n", "8   100    猕猴桃          101      None      None                 92   \n", "9    98  橘子｜柠檬          120      None      None                 92   \n", "10  120   梨｜苹果          164      None      None                 92   \n", "11   99  凤梨｜菠萝          166      None      None                 92   \n", "12  159    牛油果          168      None      None                 92   \n", "13  166   水果制品          170      None      None                 92   \n", "14  102    淡奶油          102      None      None                 18   \n", "15  167   散装奶油          103      None      None                 18   \n", "16  103    纯牛奶          104      None      None                 18   \n", "17  104  奶酪I芝士          105      None      None                 18   \n", "18  105     黄油          107      None      None                 18   \n", "19  107     奶粉          167      None      None                 18   \n", "20  108   夹心馅料            1      None      None                 17   \n", "21  158     罐头            1      None      None                 17   \n", "22  169     饼干            1      None      None                 17   \n", "23  136   冻品原料           44      None      None                 17   \n", "24  109     砂糖          108      None      None                 17   \n", "25   44  面粉丨淀粉          109      None      None                 17   \n", "26  122     肉松          122      None      None                 17   \n", "27  123    调味酒          123      None      None                 17   \n", "28  127    添加剂          127      None      None                 17   \n", "29  132     油类          132      None      None                 17   \n", "30  165   坚果干货          165      None      None                 17   \n", "31  124    沙拉酱          124      None      None                119   \n", "32  125     果酱          125      None      None                119   \n", "33  131   果汁原料          131      None      None                119   \n", "34  133     罐头          133      None      None                119   \n", "35  137    雪媚娘          137      None      None                119   \n", "36  138     椰蓉          138      None      None                119   \n", "37  139    常规品          112      None      None                111   \n", "38  113     装饰          113      None      None                111   \n", "39  112    可可豆          114      None      None                111   \n", "40  114     饼干          116      None      None                111   \n", "41  116     寿桃          135      None      None                111   \n", "42  135   装饰用碎          139      None      None                111   \n", "43  140    立体类          140      None      None                111   \n", "44  142     餐具          142      None      None                141   \n", "45  143     工具          143      None      None                141   \n", "46  110   刀叉盘子            1      None      None                 74   \n", "47   77    蛋糕盒            2      None      None                 74   \n", "48   78     丝带           74      None      None                 74   \n", "49   80    生日帽           80      None      None                 74   \n", "\n", "   first_category_name  \n", "0                 鲜果鸡蛋  \n", "1                 鲜果鸡蛋  \n", "2                 鲜果鸡蛋  \n", "3                 鲜果鸡蛋  \n", "4                 鲜果鸡蛋  \n", "5                 鲜果鸡蛋  \n", "6                 鲜果鸡蛋  \n", "7                 鲜果鸡蛋  \n", "8                 鲜果鸡蛋  \n", "9                 鲜果鸡蛋  \n", "10                鲜果鸡蛋  \n", "11                鲜果鸡蛋  \n", "12                鲜果鸡蛋  \n", "13                鲜果鸡蛋  \n", "14                 乳制品  \n", "15                 乳制品  \n", "16                 乳制品  \n", "17                 乳制品  \n", "18                 乳制品  \n", "19                 乳制品  \n", "20                烘焙辅料  \n", "21                烘焙辅料  \n", "22                烘焙辅料  \n", "23                烘焙辅料  \n", "24                烘焙辅料  \n", "25                烘焙辅料  \n", "26                烘焙辅料  \n", "27                烘焙辅料  \n", "28                烘焙辅料  \n", "29                烘焙辅料  \n", "30                烘焙辅料  \n", "31                甜品奶茶  \n", "32                甜品奶茶  \n", "33                甜品奶茶  \n", "34                甜品奶茶  \n", "35                甜品奶茶  \n", "36                甜品奶茶  \n", "37                 巧克力  \n", "38                 巧克力  \n", "39                 巧克力  \n", "40                 巧克力  \n", "41                 巧克力  \n", "42                 巧克力  \n", "43                 巧克力  \n", "44                  其他  \n", "45                  其他  \n", "46                包装用品  \n", "47                包装用品  \n", "48                包装用品  \n", "49                包装用品  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["all_second_cate_list=[]\n", "def get_second_category(firstCate={\"id\":18, \"name\":\"乳制品\"}):\n", "    reanmed_first_cate={\"first_category_id\":firstCate['id'], \"first_category_name\":firstCate['name']}\n", "    json_obj = {'limit':99, 'page':1, 'type':1, \"pid\": firstCate['id']}\n", "    url='https://bd.dayinyuncang.com/api/product/category'\n", "\n", "    secondCategoryList=requests.post(url, data=json_obj).json()['data']['data']\n", "    print(f'secondCategoryList:{secondCategoryList}')\n", "    for cate in secondCategoryList:\n", "        cate.update(reanmed_first_cate)\n", "    all_second_cate_list.extend(secondCategoryList)\n", "    return secondCategoryList\n", "\n", "for first in categoryList:\n", "    get_second_category(firstCate=first)\n", "\n", "all_second_cate_list_df=pd.DataFrame(all_second_cate_list)\n", "all_second_cate_list_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["芒果 商品数量：9\n", "火龙果 商品数量：8\n", "草莓｜蓝莓 商品数量：15\n", "瓜类｜香蕉 商品数量：13\n", "番茄｜桃子 商品数量：7\n", "鸡蛋｜石榴 商品数量：4\n", "葡提｜椰子 商品数量：12\n", "应季水果 商品数量：7\n", "猕猴桃 商品数量：6\n", "橘子｜柠檬 商品数量：13\n", "梨｜苹果 商品数量：6\n", "凤梨｜菠萝 商品数量：6\n", "牛油果 商品数量：2\n", "水果制品 商品数量：24\n", "淡奶油 商品数量：12\n", "散装奶油 商品数量：11\n", "纯牛奶 商品数量：13\n", "奶酪I芝士 商品数量：5\n", "黄油 商品数量：5\n", "奶粉 商品数量：1\n", "夹心馅料 商品数量：16\n", "罐头 商品数量：6\n", "饼干 商品数量：5\n", "冻品原料 商品数量：4\n", "砂糖 商品数量：6\n", "面粉丨淀粉 商品数量：13\n", "肉松 商品数量：9\n", "调味酒 商品数量：9\n", "添加剂 商品数量：8\n", "油类 商品数量：5\n", "坚果干货 商品数量：1\n", "沙拉酱 商品数量：6\n", "果酱 商品数量：8\n", "果汁原料 商品数量：4\n", "罐头 商品数量：6\n", "雪媚娘 商品数量：1\n", "椰蓉 商品数量：1\n", "常规品 商品数量：15\n", "装饰 商品数量：15\n", "可可豆 商品数量：17\n", "饼干 商品数量：4\n", "寿桃 商品数量：11\n", "装饰用碎 商品数量：7\n", "立体类 商品数量：26\n", "餐具 商品数量：5\n", "工具 商品数量：3\n", "刀叉盘子 商品数量：3\n", "蛋糕盒 商品数量：8\n", "没有商品！！丝带\n", "没有商品！！生日帽\n"]}], "source": ["import pandas as pd\n", "pid_list_url='https://bd.dayinyuncang.com/api/product/list'\n", "params={'category_id':102,\n", "'limit':50,\n", "'page':1,\n", "'new_order':1,\n", "'price_order':None,}\n", "\n", "all_products=[]\n", "\n", "for cate in all_second_cate_list:\n", "    params[\"category_id\"]=cate[\"id\"]\n", "    cate_obj={\"first_category_id\":cate[\"first_category_id\"],\"first_category_name\":cate[\"first_category_name\"],'second_category_id':cate[\"id\"],'second_category_name':cate[\"name\"]}\n", "    products=requests.post(pid_list_url, data=params).json()['data']['data']\n", "    for product in products:\n", "        product.update(cate_obj)\n", "    if len(products)>0:\n", "        all_products.extend(products)\n", "        print(f\"{cate['name']} 商品数量：{len(products)}\")\n", "    else:\n", "        print(f\"没有商品！！{cate['name']}\")\n", "\n", "all_products_df=pd.DataFrame(all_products)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>cover_images</th>\n", "      <th>current_price</th>\n", "      <th>original_price</th>\n", "      <th>new_user_price</th>\n", "      <th>old_user_price</th>\n", "      <th>count</th>\n", "      <th>tags</th>\n", "      <th>s<PERSON><PERSON><PERSON></th>\n", "      <th>description</th>\n", "      <th>stock_order</th>\n", "      <th>price_type</th>\n", "      <th>stock</th>\n", "      <th>first_category_id</th>\n", "      <th>first_category_name</th>\n", "      <th>second_category_id</th>\n", "      <th>second_category_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>722</td>\n", "      <td>越南金煌芒400g+（10-10.5斤）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230821/2...</td>\n", "      <td>60.00</td>\n", "      <td>60.00</td>\n", "      <td>60.00</td>\n", "      <td>60.00</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>[{'name': '重量', 'list': ['10-10.5斤']}]</td>\n", "      <td>果肉细腻 香甜爽口</td>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "      <td>93</td>\n", "      <td>芒果</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>628</td>\n", "      <td>【净重】越南大青芒果/单果400g+（10-10.5斤）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230417/7...</td>\n", "      <td>59.88</td>\n", "      <td>59.88</td>\n", "      <td>59.88</td>\n", "      <td>59.88</td>\n", "      <td>1</td>\n", "      <td>热销</td>\n", "      <td>[{'name': '斤', 'list': ['10-10.5斤']}]</td>\n", "      <td>二级果/口感香甜</td>\n", "      <td>98534</td>\n", "      <td>2</td>\n", "      <td>98534</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "      <td>93</td>\n", "      <td>芒果</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>456</td>\n", "      <td>【净重】越南大青芒果/单果500g+（10-10.5斤）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230417/7...</td>\n", "      <td>69.88</td>\n", "      <td>69.88</td>\n", "      <td>69.88</td>\n", "      <td>69.88</td>\n", "      <td>1</td>\n", "      <td>热销</td>\n", "      <td>[{'name': '重量', 'list': ['10-10.5斤']}]</td>\n", "      <td>一级果/口感香甜</td>\n", "      <td>9590</td>\n", "      <td>2</td>\n", "      <td>9590</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "      <td>93</td>\n", "      <td>芒果</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>455</td>\n", "      <td>【净重】大台芒果150g+（10-10.5斤）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230530/b...</td>\n", "      <td>80.00</td>\n", "      <td>80.00</td>\n", "      <td>80.00</td>\n", "      <td>80.00</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>[{'name': '重量', 'list': ['10-10.5斤']}]</td>\n", "      <td>大果150g+ /一级</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "      <td>93</td>\n", "      <td>芒果</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>419</td>\n", "      <td>越南大青芒整件（58-60斤）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230417/7...</td>\n", "      <td>258.00</td>\n", "      <td>258.00</td>\n", "      <td>258.00</td>\n", "      <td>258.00</td>\n", "      <td>1</td>\n", "      <td>热销</td>\n", "      <td>[{'name': '斤', 'list': ['58-60斤']}]</td>\n", "      <td>口感香甜 烘焙优选</td>\n", "      <td>7956</td>\n", "      <td>2</td>\n", "      <td>7956</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "      <td>93</td>\n", "      <td>芒果</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>319</th>\n", "      <td>597</td>\n", "      <td>【半透明】8寸蛋糕盒单层26cm（50*1件）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230519/a...</td>\n", "      <td>150.00</td>\n", "      <td>150.00</td>\n", "      <td>150.00</td>\n", "      <td>150.00</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>[{'name': '数量', 'list': ['8寸单层50个']}]</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "      <td>77</td>\n", "      <td>蛋糕盒</td>\n", "    </tr>\n", "    <tr>\n", "      <th>320</th>\n", "      <td>596</td>\n", "      <td>【半透明】8寸蛋糕盒三层26cm（50*1件）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230519/a...</td>\n", "      <td>215.00</td>\n", "      <td>215.00</td>\n", "      <td>215.00</td>\n", "      <td>215.00</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>[{'name': '数量', 'list': ['50个（加高）']}]</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "      <td>77</td>\n", "      <td>蛋糕盒</td>\n", "    </tr>\n", "    <tr>\n", "      <th>321</th>\n", "      <td>432</td>\n", "      <td>【半透明】6寸蛋糕盒三层21cm（100*1件）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230519/a...</td>\n", "      <td>310.00</td>\n", "      <td>310.00</td>\n", "      <td>310.00</td>\n", "      <td>310.00</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>[{'name': '数量', 'list': ['100个（加高）']}]</td>\n", "      <td>加高三层盒</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "      <td>77</td>\n", "      <td>蛋糕盒</td>\n", "    </tr>\n", "    <tr>\n", "      <th>322</th>\n", "      <td>692</td>\n", "      <td>【半透明蓝色】6寸蛋糕盒单层22cm（100*1件）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230519/a...</td>\n", "      <td>220.00</td>\n", "      <td>220.00</td>\n", "      <td>220.00</td>\n", "      <td>220.00</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>[{'name': '数量', 'list': ['22cm单层100个']}]</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "      <td>77</td>\n", "      <td>蛋糕盒</td>\n", "    </tr>\n", "    <tr>\n", "      <th>323</th>\n", "      <td>430</td>\n", "      <td>【半透明白色】6寸蛋糕盒单层22cm（100*1件）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20231105/4...</td>\n", "      <td>220.00</td>\n", "      <td>220.00</td>\n", "      <td>220.00</td>\n", "      <td>220.00</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>[{'name': '数量', 'list': ['6寸单层100个']}]</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "      <td>77</td>\n", "      <td>蛋糕盒</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>324 rows × 18 columns</p>\n", "</div>"], "text/plain": ["      id                          name  \\\n", "0    722          越南金煌芒400g+（10-10.5斤）   \n", "1    628  【净重】越南大青芒果/单果400g+（10-10.5斤）   \n", "2    456  【净重】越南大青芒果/单果500g+（10-10.5斤）   \n", "3    455       【净重】大台芒果150g+（10-10.5斤）   \n", "4    419               越南大青芒整件（58-60斤）   \n", "..   ...                           ...   \n", "319  597       【半透明】8寸蛋糕盒单层26cm（50*1件）   \n", "320  596       【半透明】8寸蛋糕盒三层26cm（50*1件）   \n", "321  432      【半透明】6寸蛋糕盒三层21cm（100*1件）   \n", "322  692    【半透明蓝色】6寸蛋糕盒单层22cm（100*1件）   \n", "323  430    【半透明白色】6寸蛋糕盒单层22cm（100*1件）   \n", "\n", "                                          cover_images current_price  \\\n", "0    https://bd.dayinyuncang.com/uploads/20230821/2...         60.00   \n", "1    https://bd.dayinyuncang.com/uploads/20230417/7...         59.88   \n", "2    https://bd.dayinyuncang.com/uploads/20230417/7...         69.88   \n", "3    https://bd.dayinyuncang.com/uploads/20230530/b...         80.00   \n", "4    https://bd.dayinyuncang.com/uploads/20230417/7...        258.00   \n", "..                                                 ...           ...   \n", "319  https://bd.dayinyuncang.com/uploads/20230519/a...        150.00   \n", "320  https://bd.dayinyuncang.com/uploads/20230519/a...        215.00   \n", "321  https://bd.dayinyuncang.com/uploads/20230519/a...        310.00   \n", "322  https://bd.dayinyuncang.com/uploads/20230519/a...        220.00   \n", "323  https://bd.dayinyuncang.com/uploads/20231105/4...        220.00   \n", "\n", "    original_price new_user_price old_user_price  count tags  \\\n", "0            60.00          60.00          60.00      1        \n", "1            59.88          59.88          59.88      1   热销   \n", "2            69.88          69.88          69.88      1   热销   \n", "3            80.00          80.00          80.00      1        \n", "4           258.00         258.00         258.00      1   热销   \n", "..             ...            ...            ...    ...  ...   \n", "319         150.00         150.00         150.00      1        \n", "320         215.00         215.00         215.00      1        \n", "321         310.00         310.00         310.00      1        \n", "322         220.00         220.00         220.00      1        \n", "323         220.00         220.00         220.00      1        \n", "\n", "                                      sku<PERSON>son  description stock_order  \\\n", "0      [{'name': '重量', 'list': ['10-10.5斤']}]    果肉细腻 香甜爽口           5   \n", "1       [{'name': '斤', 'list': ['10-10.5斤']}]     二级果/口感香甜       98534   \n", "2      [{'name': '重量', 'list': ['10-10.5斤']}]     一级果/口感香甜        9590   \n", "3      [{'name': '重量', 'list': ['10-10.5斤']}]  大果150g+ /一级           2   \n", "4         [{'name': '斤', 'list': ['58-60斤']}]    口感香甜 烘焙优选        7956   \n", "..                                        ...          ...         ...   \n", "319     [{'name': '数量', 'list': ['8寸单层50个']}]                        4   \n", "320     [{'name': '数量', 'list': ['50个（加高）']}]                        2   \n", "321    [{'name': '数量', 'list': ['100个（加高）']}]        加高三层盒           3   \n", "322  [{'name': '数量', 'list': ['22cm单层100个']}]                        0   \n", "323    [{'name': '数量', 'list': ['6寸单层100个']}]                        0   \n", "\n", "     price_type  stock  first_category_id first_category_name  \\\n", "0             2      5                 92                鲜果鸡蛋   \n", "1             2  98534                 92                鲜果鸡蛋   \n", "2             2   9590                 92                鲜果鸡蛋   \n", "3             2      2                 92                鲜果鸡蛋   \n", "4             2   7956                 92                鲜果鸡蛋   \n", "..          ...    ...                ...                 ...   \n", "319           2      4                 74                包装用品   \n", "320           2      2                 74                包装用品   \n", "321           2      3                 74                包装用品   \n", "322           2      0                 74                包装用品   \n", "323           2      0                 74                包装用品   \n", "\n", "     second_category_id second_category_name  \n", "0                    93                   芒果  \n", "1                    93                   芒果  \n", "2                    93                   芒果  \n", "3                    93                   芒果  \n", "4                    93                   芒果  \n", "..                  ...                  ...  \n", "319                  77                  蛋糕盒  \n", "320                  77                  蛋糕盒  \n", "321                  77                  蛋糕盒  \n", "322                  77                  蛋糕盒  \n", "323                  77                  蛋糕盒  \n", "\n", "[324 rows x 18 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["all_products_df"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["all_products_df.to_csv(f\"./data/答音云仓/所有商品-{time_of_now}.csv\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "60.176.237.247:49786\n", "***************:31164\n", "*************:43639\n", "**************:49480\n", "**************:41879\n", "***************:36768\n", "**************:34455\n", "************:30425\n", "***************:45312\n", "**************:49926\n", "['60.176.237.247:49786', '***************:31164', '*************:43639', '**************:49480', '**************:41879', '***************:36768', '**************:34455', '************:30425', '***************:45312', '**************:49926']\n", "成功写入odps:summerfarm_ds.spider_dayinyuncang_product_result_df, partition_spec:ds=20240226,competitor_name=dayinyuncang, attemp:0\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps \n", "# 写入odps\n", "all_products_df['competitor']=brand_name\n", "all_products_df=all_products_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = 'summerfarm_ds.spider_dayinyuncang_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sql:\n", "select ds,name,id,competitor,count(*) as recods,avg(cast(stock as int)) as avg_stock\n", "                          from summerfarm_ds.spider_dayinyuncang_product_result_df \n", "                          where ds>='20240115'\n", "                          and name like '%铁塔%'\n", "                          group by ds,competitor,name ,id\n", "                          order by ds desc,name,competitor\n", "                          limit 500\n", "columns:Index(['ds', 'name', 'id', 'competitor', 'recods', 'avg_stock'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>name</th>\n", "      <th>id</th>\n", "      <th>competitor</th>\n", "      <th>recods</th>\n", "      <th>avg_stock</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240226</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20240226</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20240226</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20240226</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*12盒/1箱）</td>\n", "      <td>190</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20240226</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>20240225</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>8.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>20240225</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>20240224</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>20240224</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>20240224</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>20240224</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>20240223</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>20240223</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>20240223</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>20240223</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>20240222</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>20240222</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>20240222</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>20240222</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>20240221</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>20240221</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>20240221</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>20240221</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>20240220</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>20240220</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>20240220</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>20240220</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>20240219</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>20240219</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>20240219</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>20240219</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>20240218</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>20240218</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>20240218</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>20240218</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>20240208</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>20240208</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>20240208</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>20240208</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*12盒/1箱）</td>\n", "      <td>190</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>20240208</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>11.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>20240207</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>20240207</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>20240207</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>20240207</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*12盒/1箱）</td>\n", "      <td>190</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>20240207</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>20240206</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>20240206</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>20240206</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>20240206</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*12盒/1箱）</td>\n", "      <td>190</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>20240206</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>20240205</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）</td>\n", "      <td>279</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>20240205</td>\n", "      <td>爱乐薇（紫铁塔）淡奶油（1L*1瓶）</td>\n", "      <td>560</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>13.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>20240205</td>\n", "      <td>爱乐薇（铁塔）黄油（1KG*1块）</td>\n", "      <td>267</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>10.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>20240205</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*12盒/1箱）</td>\n", "      <td>190</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>20240205</td>\n", "      <td>爱尔薇 铁塔淡奶油（1L*1瓶）</td>\n", "      <td>561</td>\n", "      <td>答音云仓</td>\n", "      <td>1</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          ds                    name   id competitor  recods  avg_stock\n", "0   20240226  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        0.0\n", "1   20240226      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1        0.0\n", "2   20240226       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "3   20240226    爱尔薇 铁塔淡奶油（1L*12盒/1箱）  190       答音云仓       1        0.0\n", "4   20240226        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1        0.0\n", "5   20240225      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1        8.0\n", "6   20240225       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "7   20240224  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        1.0\n", "8   20240224      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1        9.0\n", "9   20240224       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "10  20240224        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1        0.0\n", "11  20240223  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        1.0\n", "12  20240223      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1        9.0\n", "13  20240223       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "14  20240223        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1        0.0\n", "15  20240222  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        1.0\n", "16  20240222      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1        9.0\n", "17  20240222       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "18  20240222        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1        0.0\n", "19  20240221  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        2.0\n", "20  20240221      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1        9.0\n", "21  20240221       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "22  20240221        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1        0.0\n", "23  20240220  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        2.0\n", "24  20240220      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1        9.0\n", "25  20240220       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "26  20240220        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1       10.0\n", "27  20240219  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        3.0\n", "28  20240219      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1        9.0\n", "29  20240219       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "30  20240219        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1       10.0\n", "31  20240218  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        3.0\n", "32  20240218      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1        9.0\n", "33  20240218       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "34  20240218        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1       10.0\n", "35  20240208  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        9.0\n", "36  20240208      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1       12.0\n", "37  20240208       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "38  20240208    爱尔薇 铁塔淡奶油（1L*12盒/1箱）  190       答音云仓       1        4.0\n", "39  20240208        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1       11.0\n", "40  20240207  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        9.0\n", "41  20240207      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1       12.0\n", "42  20240207       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "43  20240207    爱尔薇 铁塔淡奶油（1L*12盒/1箱）  190       答音云仓       1        4.0\n", "44  20240207        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1       12.0\n", "45  20240206  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        9.0\n", "46  20240206      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1       12.0\n", "47  20240206       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "48  20240206    爱尔薇 铁塔淡奶油（1L*12盒/1箱）  190       答音云仓       1        5.0\n", "49  20240206        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1       12.0\n", "50  20240205  爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）  279       答音云仓       1        9.0\n", "51  20240205      爱乐薇（紫铁塔）淡奶油（1L*1瓶）  560       答音云仓       1       13.0\n", "52  20240205       爱乐薇（铁塔）黄油（1KG*1块）  267       答音云仓       1       10.0\n", "53  20240205    爱尔薇 铁塔淡奶油（1L*12盒/1箱）  190       答音云仓       1        5.0\n", "54  20240205        爱尔薇 铁塔淡奶油（1L*1瓶）  561       答音云仓       1       12.0"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "\n", "days_15=(datetime.now()-<PERSON><PERSON><PERSON>(15)).strftime('%Y%m%d')\n", "\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,name,id,competitor,count(*) as recods,avg(cast(stock as int)) as avg_stock\n", "                          from summerfarm_ds.spider_dayinyuncang_product_result_df \n", "                          where ds>='20240115'\n", "                          and name like '%铁塔%'\n", "                          group by ds,competitor,name ,id\n", "                          order by ds desc,name,competitor\n", "                          limit 500\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "import time\n", "import os\n", "import pandasql\n", "from IPython.core.display import HTML\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "cache_file_path = '/Users/<USER>/Documents/github/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=os.environ['AZURE_API_KEY_XM']\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "# print(getEmbeddingsFromAzure(\"越南大青芒\"))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 使用代理"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["************:36384\n", "************:36588\n", "***************:39505\n", "*************:45291\n", "*************:38241\n", "***************:39103\n", "***************:33085\n", "*************:50359\n", "*************:39206\n", "*************:33787\n", "['************:36384', '************:36588', '***************:39505', '*************:45291', '*************:38241', '***************:39103', '***************:33085', '*************:50359', '*************:39206', '*************:33787']\n"]}], "source": ["def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "import requests\n", "import random\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url, max_retries=3):\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://***********:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response.text\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-01-10 15:19:53, date_of_now:2024-01-10, brand_name:优享鲜焙\n"]}], "source": ["import requests\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "brand_name=\"优享鲜焙\"\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 爬取类目树"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ***********************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"rP1ae2bqTU\",\"attributes\":\"\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"type\":\"config\",\"title\":\"全部\",\"category\":[],\"is_global_setting\":\"0\",\"risk_type\":0,\"risk_alias\":\"rP1ae2bqTU\"},{\"component\":\"dc-search\",\"type\":\"search\",\"placeholder\":\"搜索商品\",\"hotWords\":[],\"position\":0,\"showMode\":0,\"showScan\":false,\"borderRadius\":8,\"textAlign\":\"left\",\"height\":40,\"color\":\"#999\",\"bgColor\":\"#f9f9f9\",\"borderColor\":\"#fff\",\"zIndex\":110,\"uuid\":\"\",\"showSearchComponent\":1,\"minHeightOpt\":null,\"risk_type\":0,\"risk_alias\":\"rP1ae2bqTU\"},{\"show_method\":\"3\",\"background_color\":\"#fff\",\"color\":\"#000\",\"count\":4,\"image_fill_style\":\"2\",\"sub_entry\":[{\"link_type\":\"image_ad_selection\",\"link_url\":\"https://shop128990586.youzan.com/wscshop/showcase/feature?alias=pDXPeDPwe0\",\"alias\":\"pDXPeDPwe0\",\"template_id\":1,\"link_title\":\"乳制品\",\"type\":\"image_ad_selection\",\"title\":\"乳制品\",\"link_id\":109941947},{\"link_type\":\"image_ad_selection\",\"link_url\":\"https://shop128990586.youzan.com/wscshop/showcase/feature?alias=UzTKrwjJk6\",\"alias\":\"UzTKrwjJk6\",\"template_id\":1,\"link_title\":\"面粉\",\"type\":\"image_ad_selection\",\"title\":\"面粉\",\"link_id\":109941976},{\"link_type\":\"image_ad_selection\",\"link_url\":\"https://shop128990586.youzan.com/wscshop/showcase/feature?alias=46Mefp2whm\",\"alias\":\"46Mefp2whm\",\"template_id\":1,\"link_title\":\"烘焙辅料\",\"type\":\"image_ad_selection\",\"title\":\"烘焙辅料\",\"link_id\":109941914},{\"link_type\":\"image_ad_selection\",\"link_url\":\"https://shop128990586.youzan.com/wscshop/showcase/feature?alias=WFRaD5burC\",\"alias\":\"WFRaD5burC\",\"template_id\":1,\"link_title\":\"成品半成品\",\"type\":\"image_ad_selection\",\"title\":\"半成品\",\"link_id\":109941897},{\"image_url\":\"\",\"link_title\":\"水吧咖啡\",\"image_width\":0,\"type\":\"image_ad_selection\",\"title\":\"水吧咖啡\",\"image_thumb_url\":\"\",\"link_id\":109283392,\"link_type\":\"image_ad_selection\",\"image_height\":0,\"link_url\":\"https://shop128990586.youzan.com/wscshop/showcase/feature?alias=2Tt4PpcJNF\",\"alias\":\"2Tt4PpcJNF\",\"template_id\":1,\"image_id\":0},{\"image_url\":\"\",\"link_title\":\"肉制品\",\"image_width\":0,\"type\":\"image_ad_selection\",\"title\":\"肉制品\",\"image_thumb_url\":\"\",\"link_id\":109283359,\"link_type\":\"image_ad_selection\",\"image_height\":0,\"link_url\":\"https://shop128990586.youzan.com/wscshop/showcase/feature?alias=VAEzhSy1bY\",\"alias\":\"VAEzhSy1bY\",\"template_id\":1,\"image_id\":0},{\"image_url\":\"\",\"link_title\":\"西餐辅料\",\"image_width\":0,\"type\":\"image_ad_selection\",\"title\":\"西餐辅料\",\"image_thumb_url\":\"\",\"link_id\":109942005,\"link_type\":\"image_ad_selection\",\"image_height\":0,\"link_url\":\"https://shop128990586.youzan.com/wscshop/showcase/feature?alias=9Zjs781tnl\",\"alias\":\"9Zjs781tnl\",\"template_id\":1,\"image_id\":0},{\"image_url\":\"\",\"link_title\":\"饮料酒水\",\"image_width\":0,\"type\":\"image_ad_selection\",\"title\":\"酒水饮料\",\"image_thumb_url\":\"\",\"link_id\":109941992,\"link_type\":\"image_ad_selection\",\"image_height\":0,\"link_url\":\"https://shop128990586.youzan.com/wscshop/showcase/feature?alias=CS1qHErlPv\",\"alias\":\"CS1qHErlPv\",\"template_id\":1,\"image_id\":0},{\"image_url\":\"\",\"link_title\":\"冷冻蛋糕\",\"image_width\":0,\"type\":\"image_ad_selection\",\"title\":\"冷冻蛋糕\",\"image_thumb_url\":\"\",\"link_id\":109941942,\"link_type\":\"image_ad_selection\",\"image_height\":0,\"link_url\":\"https://shop128990586.youzan.com/wscshop/showcase/feature?alias=sJPKIxXuN8\",\"alias\":\"sJPKIxXuN8\",\"template_id\":1,\"image_id\":0}],\"slide_setting\":\"1\",\"type\":\"top_nav\",\"risk_type\":0,\"risk_alias\":\"rP1ae2bqTU\"}],\"createdTime\":1678342455000,\"goodsNum\":0,\"id\":109947067,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":128798418,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":1,\"templateId\":83,\"title\":\"全部\",\"updateTime\":1679383455000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"kdtId\":128798418,\"lockStatus\":0,\"shopName\":\"优享鲜焙\",\"shopRole\":0,\"shopTopic\":0,\"shopType\":0},\"themeAndColors\":{\"type\":1,\"colors\":{\"general\":\"#ff5e15\",\"main-bg\":\"#ff5e15\",\"main-bg-gradient\":\"#ff5e15\",\"main-text\":\"#ffffff\",\"vice-bg\":\"#FF9300\",\"vice-text\":\"#ffffff\",\"icon\":\"#ff5e15\",\"price\":\"#ff5e15\",\"tag-text\":\"#ff5e15\",\"tag-bg\":\"#FFEDE6\",\"start-bg\":\"#FF8C20\",\"end-bg\":\"#FF4300\",\"ump-main-bg\":\"#ff5e15\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"#FF9300\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#ff5e15\",\"ump-price\":\"#ff5e15\",\"ump-tag-text\":\"#ff5e15\",\"ump-tag-bg\":\"#FFEDE6\",\"ump-coupon-bg\":\"#FFF6F2\",\"ump-border\":\"#FFDCCC\",\"ump-start-bg\":\"#FF8C20\",\"ump-end-bg\":\"#FF4300\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州贝克丹士食品有限公司\",\"area\":\"临平区\",\"business\":\"37\",\"businessName\":\"蛋糕烘焙\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"章总\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2023-02-15 16:31:05\",\"intro\":\"\",\"kdtId\":128798418,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2023/05/08/FgBP0hlojSfYgPoZY_NfK88Jbn_5.jpg\",\"province\":\"浙江省\",\"shopId\":96190213,\"shopName\":\"优享鲜焙\",\"shopType\":0},\"shopConfig\":{\"sold_out_goods_flag\":\"\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "search\n", "top_nav\n", "[{'link_type': 'image_ad_selection', 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=pDXPeDPwe0', 'alias': 'pDXPeDPwe0', 'template_id': 1, 'link_title': '乳制品', 'type': 'image_ad_selection', 'title': '乳制品', 'link_id': 109941947}, {'link_type': 'image_ad_selection', 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=UzTKrwjJk6', 'alias': 'UzTKrwjJk6', 'template_id': 1, 'link_title': '面粉', 'type': 'image_ad_selection', 'title': '面粉', 'link_id': 109941976}, {'link_type': 'image_ad_selection', 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=46Mefp2whm', 'alias': '46Mefp2whm', 'template_id': 1, 'link_title': '烘焙辅料', 'type': 'image_ad_selection', 'title': '烘焙辅料', 'link_id': 109941914}, {'link_type': 'image_ad_selection', 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=WFRaD5burC', 'alias': 'WFRaD5burC', 'template_id': 1, 'link_title': '成品半成品', 'type': 'image_ad_selection', 'title': '半成品', 'link_id': 109941897}, {'image_url': '', 'link_title': '水吧咖啡', 'image_width': 0, 'type': 'image_ad_selection', 'title': '水吧咖啡', 'image_thumb_url': '', 'link_id': 109283392, 'link_type': 'image_ad_selection', 'image_height': 0, 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=2Tt4PpcJNF', 'alias': '2Tt4PpcJNF', 'template_id': 1, 'image_id': 0}, {'image_url': '', 'link_title': '肉制品', 'image_width': 0, 'type': 'image_ad_selection', 'title': '肉制品', 'image_thumb_url': '', 'link_id': 109283359, 'link_type': 'image_ad_selection', 'image_height': 0, 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=VAEzhSy1bY', 'alias': 'VAEzhSy1bY', 'template_id': 1, 'image_id': 0}, {'image_url': '', 'link_title': '西餐辅料', 'image_width': 0, 'type': 'image_ad_selection', 'title': '西餐辅料', 'image_thumb_url': '', 'link_id': 109942005, 'link_type': 'image_ad_selection', 'image_height': 0, 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=9Zjs781tnl', 'alias': '9Zjs781tnl', 'template_id': 1, 'image_id': 0}, {'image_url': '', 'link_title': '饮料酒水', 'image_width': 0, 'type': 'image_ad_selection', 'title': '酒水饮料', 'image_thumb_url': '', 'link_id': 109941992, 'link_type': 'image_ad_selection', 'image_height': 0, 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=CS1qHErlPv', 'alias': 'CS1qHErlPv', 'template_id': 1, 'image_id': 0}, {'image_url': '', 'link_title': '冷冻蛋糕', 'image_width': 0, 'type': 'image_ad_selection', 'title': '冷冻蛋糕', 'image_thumb_url': '', 'link_id': 109941942, 'link_type': 'image_ad_selection', 'image_height': 0, 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=sJPKIxXuN8', 'alias': 'sJPKIxXuN8', 'template_id': 1, 'image_id': 0}]\n"]}], "source": ["app_and_kdt_id='app_id=wx3e2e3761ccf9bcae&kdt_id=128798418'\n", "cate_list=get_remote_data_with_proxy('https://h5.youzan.com/wscdeco/feature-detail.json?alias=rP1ae2bqTU&stage=16&tee_route=pages/tab/one/index&check_multi_store=1&close_chainstore_webview_limit=true&check_old_home=1&version_control={%22use_native_feature_page%22:1,%22feature_page_path%22:%22pages/tab/one/index%22}&'+app_and_kdt_id+'&access_token=13eabc4486859e4f06582d141f4c83')\n", "print(cate_list)\n", "cate_list=json.loads(cate_list)\n", "\n", "root_cate_df=None\n", "root_cate_list=[]\n", "for component in cate_list['data']['components']:\n", "    print(component['type'])\n", "    if component['type']=='top_nav':\n", "        print(component['sub_entry'])\n", "        root_cate_list=component['sub_entry']\n", "        root_cate_df = pd.DataFrame(root_cate_list)\n", "\n", "root_cate_df\n", "pd.set_option('display.max_colwidth', None)\n", "root_cate_df.to_csv(f'./data/{brand_name}/{brand_name}一级类目(顶部).csv',index=False)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: *************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"pDXPeDPwe0\",\"attributes\":\"{}\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"乳制品\",\"category\":[],\"uuid\":\"dae78ca8-60da-43cd-b578-bbf2c3d73b6c\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"pDXPeDPwe0\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"global\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"pDXPeDPwe0\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"稀奶油\",\"alias\":\"3evyaq1wm3tde\",\"number\":15,\"goodsNumber\":100},{\"loading\":true,\"title\":\"植脂制品\",\"alias\":\"35zn8algiars2\",\"number\":8,\"goodsNumber\":100},{\"loading\":true,\"title\":\"牛奶\",\"alias\":\"3f5rhrn1vnob6\",\"number\":7,\"goodsNumber\":100},{\"loading\":true,\"title\":\"奶酪丨芝士\",\"alias\":\"2xje176nz2vwi\",\"number\":13,\"goodsNumber\":100},{\"loading\":true,\"title\":\"黄油\",\"alias\":\"2g1xluqfiumtu\",\"number\":4,\"goodsNumber\":100},{\"loading\":true,\"title\":\"炼乳\",\"alias\":\"3es6uc8echvjm\",\"number\":2,\"goodsNumber\":100},{\"loading\":true,\"title\":\"奶粉\",\"alias\":\"275p4wxf0jo82\",\"number\":1,\"goodsNumber\":100}],\"uuid\":\"8471cc9f-83f8-4687-8db9-bf3fde765839\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"circle\",\"imgOpt\":{\"fill\":\"contain\",\"corner\":\"circle\",\"radius\":8,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":500,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":500,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":{\"fontSize\":12,\"delLine\":true,\"tagGap\":2,\"color\":\"#c8c9cc\"},\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"128798418\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"slideType\":\"tag_slide\",\"risk_type\":0,\"risk_alias\":\"pDXPeDPwe0\"}],\"createdTime\":1678333575000,\"goodsNum\":0,\"id\":109941947,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":128798418,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":1,\"templateId\":1,\"title\":\"乳制品\",\"updateTime\":1687398000000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"kdtId\":128798418,\"lockStatus\":0,\"shopName\":\"优享鲜焙\",\"shopRole\":0,\"shopTopic\":0,\"shopType\":0},\"themeAndColors\":{\"type\":1,\"colors\":{\"general\":\"#ff5e15\",\"main-bg\":\"#ff5e15\",\"main-bg-gradient\":\"#ff5e15\",\"main-text\":\"#ffffff\",\"vice-bg\":\"#FF9300\",\"vice-text\":\"#ffffff\",\"icon\":\"#ff5e15\",\"price\":\"#ff5e15\",\"tag-text\":\"#ff5e15\",\"tag-bg\":\"#FFEDE6\",\"start-bg\":\"#FF8C20\",\"end-bg\":\"#FF4300\",\"ump-main-bg\":\"#ff5e15\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"#FF9300\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#ff5e15\",\"ump-price\":\"#ff5e15\",\"ump-tag-text\":\"#ff5e15\",\"ump-tag-bg\":\"#FFEDE6\",\"ump-coupon-bg\":\"#FFF6F2\",\"ump-border\":\"#FFDCCC\",\"ump-start-bg\":\"#FF8C20\",\"ump-end-bg\":\"#FF4300\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州贝克丹士食品有限公司\",\"area\":\"临平区\",\"business\":\"37\",\"businessName\":\"蛋糕烘焙\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"章总\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2023-02-15 16:31:05\",\"intro\":\"\",\"kdtId\":128798418,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2023/05/08/FgBP0hlojSfYgPoZY_NfK88Jbn_5.jpg\",\"province\":\"浙江省\",\"shopId\":96190213,\"shopName\":\"优享鲜焙\",\"shopType\":0},\"shopConfig\":{\"sold_out_goods_flag\":\"\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "tag_list_left\n", "[{'loading': True, 'title': '稀奶油', 'alias': '3evyaq1wm3tde', 'number': 15, 'goodsNumber': 100}, {'loading': True, 'title': '植脂制品', 'alias': '35zn8algiars2', 'number': 8, 'goodsNumber': 100}, {'loading': True, 'title': '牛奶', 'alias': '3f5rhrn1vnob6', 'number': 7, 'goodsNumber': 100}, {'loading': True, 'title': '奶酪丨芝士', 'alias': '2xje176nz2vwi', 'number': 13, 'goodsNumber': 100}, {'loading': True, 'title': '黄油', 'alias': '2g1xluqfiumtu', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '炼乳', 'alias': '3es6uc8echvjm', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '奶粉', 'alias': '275p4wxf0jo82', 'number': 1, 'goodsNumber': 100}]\n", "Using proxy: *************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"UzTKrwjJk6\",\"attributes\":\"{}\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"面粉\",\"category\":[],\"uuid\":\"8439149a-7312-442d-8dc3-8b58dadaa31d\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"UzTKrwjJk6\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"global\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"UzTKrwjJk6\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"王后面粉\",\"alias\":\"35vxc6grkbhg2\",\"number\":6,\"goodsNumber\":10},{\"loading\":true,\"title\":\"伯爵\",\"alias\":\"2x9jmeofdl3c2\",\"number\":7,\"goodsNumber\":10},{\"loading\":true,\"title\":\"美玫\",\"alias\":\"2g4fujjsvqxz6\",\"number\":2,\"goodsNumber\":10},{\"loading\":true,\"title\":\"金像\",\"alias\":\"35vxuqpjwqceq\",\"number\":6,\"goodsNumber\":10},{\"loading\":true,\"title\":\"昭和\",\"alias\":\"3f22abujrj31u\",\"number\":3,\"goodsNumber\":10},{\"loading\":true,\"title\":\"柔风\",\"alias\":\"2fulsl5psnc5e\",\"number\":4,\"goodsNumber\":10},{\"loading\":true,\"title\":\"中粮\",\"alias\":\"2fy8qoxgsuqf6\",\"number\":3,\"goodsNumber\":10},{\"loading\":true,\"title\":\"日清\",\"alias\":\"2fs30tpccd6bm\",\"number\":3,\"goodsNumber\":10},{\"loading\":true,\"title\":\"冠军系列\",\"alias\":\"3epr6tn745ouq\",\"number\":3,\"goodsNumber\":10},{\"loading\":true,\"title\":\"樱皇\",\"alias\":\"27383oifjlrle\",\"number\":1,\"goodsNumber\":10}],\"uuid\":\"900ae29f-0171-4300-927a-a24730fd20fc\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"circle\",\"imgOpt\":{\"fill\":\"contain\",\"corner\":\"circle\",\"radius\":8,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":500,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":500,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":{\"fontSize\":12,\"delLine\":true,\"tagGap\":2,\"color\":\"#c8c9cc\"},\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"128798418\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"slideType\":\"tag_slide\",\"risk_type\":0,\"risk_alias\":\"UzTKrwjJk6\"}],\"createdTime\":1678333538000,\"goodsNum\":0,\"id\":109941976,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":128798418,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":1,\"templateId\":1,\"title\":\"面粉\",\"updateTime\":1693185947000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"kdtId\":128798418,\"lockStatus\":0,\"shopName\":\"优享鲜焙\",\"shopRole\":0,\"shopTopic\":0,\"shopType\":0},\"themeAndColors\":{\"type\":1,\"colors\":{\"general\":\"#ff5e15\",\"main-bg\":\"#ff5e15\",\"main-bg-gradient\":\"#ff5e15\",\"main-text\":\"#ffffff\",\"vice-bg\":\"#FF9300\",\"vice-text\":\"#ffffff\",\"icon\":\"#ff5e15\",\"price\":\"#ff5e15\",\"tag-text\":\"#ff5e15\",\"tag-bg\":\"#FFEDE6\",\"start-bg\":\"#FF8C20\",\"end-bg\":\"#FF4300\",\"ump-main-bg\":\"#ff5e15\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"#FF9300\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#ff5e15\",\"ump-price\":\"#ff5e15\",\"ump-tag-text\":\"#ff5e15\",\"ump-tag-bg\":\"#FFEDE6\",\"ump-coupon-bg\":\"#FFF6F2\",\"ump-border\":\"#FFDCCC\",\"ump-start-bg\":\"#FF8C20\",\"ump-end-bg\":\"#FF4300\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州贝克丹士食品有限公司\",\"area\":\"临平区\",\"business\":\"37\",\"businessName\":\"蛋糕烘焙\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"章总\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2023-02-15 16:31:05\",\"intro\":\"\",\"kdtId\":128798418,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2023/05/08/FgBP0hlojSfYgPoZY_NfK88Jbn_5.jpg\",\"province\":\"浙江省\",\"shopId\":96190213,\"shopName\":\"优享鲜焙\",\"shopType\":0},\"shopConfig\":{\"sold_out_goods_flag\":\"\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "tag_list_left\n", "[{'loading': True, 'title': '王后面粉', 'alias': '35vxc6grkbhg2', 'number': 6, 'goodsNumber': 10}, {'loading': True, 'title': '伯爵', 'alias': '2x9jmeofdl3c2', 'number': 7, 'goodsNumber': 10}, {'loading': True, 'title': '美玫', 'alias': '2g4fujjsvqxz6', 'number': 2, 'goodsNumber': 10}, {'loading': True, 'title': '金像', 'alias': '35vxuqpjwqceq', 'number': 6, 'goodsNumber': 10}, {'loading': True, 'title': '昭和', 'alias': '3f22abujrj31u', 'number': 3, 'goodsNumber': 10}, {'loading': True, 'title': '柔风', 'alias': '2fulsl5psnc5e', 'number': 4, 'goodsNumber': 10}, {'loading': True, 'title': '中粮', 'alias': '2fy8qoxgsuqf6', 'number': 3, 'goodsNumber': 10}, {'loading': True, 'title': '日清', 'alias': '2fs30tpccd6bm', 'number': 3, 'goodsNumber': 10}, {'loading': True, 'title': '冠军系列', 'alias': '3epr6tn745ouq', 'number': 3, 'goodsNumber': 10}, {'loading': True, 'title': '樱皇', 'alias': '27383oifjlrle', 'number': 1, 'goodsNumber': 10}]\n", "Using proxy: *************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"46Mefp2whm\",\"attributes\":\"{}\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"烘焙辅料\",\"category\":[],\"uuid\":\"128b6288-d60d-4c54-9c0f-20575c7fa426\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"46Mefp2whm\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FrY3u9JfTSz81VMr9R0_maaqINgn.png\"},\"show\":0,\"title\":\"首页\",\"key\":\"home\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"global\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"46Mefp2whm\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"鸡蛋\",\"alias\":\"3evxflcpwinz6\",\"number\":1,\"goodsNumber\":10},{\"loading\":true,\"title\":\"水果\",\"alias\":\"3nogceh0afk4y\",\"number\":10,\"goodsNumber\":10},{\"loading\":true,\"title\":\"糖\",\"alias\":\"1y4hkj6gyfy9u\",\"number\":9,\"goodsNumber\":99},{\"loading\":true,\"title\":\"添加剂丨酵母丨改良剂\",\"alias\":\"1yi32zqoemn9u\",\"number\":16,\"goodsNumber\":99},{\"loading\":true,\"title\":\"烘焙用油\",\"alias\":\"3f0u4bgudymya\",\"number\":8,\"goodsNumber\":30},{\"loading\":true,\"title\":\"可可制品\",\"alias\":\"274hb5m2w4lhe\",\"number\":12,\"goodsNumber\":99},{\"loading\":true,\"title\":\"果泥果酱\",\"alias\":\"26x27j2aqiulu\",\"number\":8,\"goodsNumber\":99},{\"loading\":true,\"title\":\"沙拉酱\",\"alias\":\"2fqvw6kac1zea\",\"number\":5,\"goodsNumber\":99},{\"loading\":true,\"title\":\"调制酒\",\"alias\":\"2on46ld3qkr9u\",\"number\":2,\"goodsNumber\":99},{\"loading\":true,\"title\":\"食用馅料\",\"alias\":\"2ftd3ksy2b2tu\",\"number\":10,\"goodsNumber\":99},{\"loading\":true,\"title\":\"预拌粉\",\"alias\":\"2x8ba423d5zr6\",\"number\":9,\"goodsNumber\":30},{\"loading\":true,\"title\":\"其他辅料\",\"alias\":\"2g1ygvxphjpjm\",\"number\":30,\"goodsNumber\":30},{\"loading\":true,\"title\":\"器具包材\",\"alias\":\"2okn5i71gkf5u\",\"number\":3,\"goodsNumber\":10}],\"uuid\":\"cbdf2cdf-e95f-4476-b8f0-fd2a1dd6a7d7\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"circle\",\"imgOpt\":{\"fill\":\"contain\",\"corner\":\"circle\",\"radius\":8,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":500,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":500,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":{\"fontSize\":12,\"delLine\":true,\"tagGap\":2,\"color\":\"#c8c9cc\"},\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"128798418\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"slideType\":\"tag_slide\",\"risk_type\":0,\"risk_alias\":\"46Mefp2whm\"}],\"createdTime\":1678333656000,\"goodsNum\":0,\"id\":109941914,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":128798418,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":1,\"templateId\":1,\"title\":\"烘焙辅料\",\"updateTime\":1698111365000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"kdtId\":128798418,\"lockStatus\":0,\"shopName\":\"优享鲜焙\",\"shopRole\":0,\"shopTopic\":0,\"shopType\":0},\"themeAndColors\":{\"type\":1,\"colors\":{\"general\":\"#ff5e15\",\"main-bg\":\"#ff5e15\",\"main-bg-gradient\":\"#ff5e15\",\"main-text\":\"#ffffff\",\"vice-bg\":\"#FF9300\",\"vice-text\":\"#ffffff\",\"icon\":\"#ff5e15\",\"price\":\"#ff5e15\",\"tag-text\":\"#ff5e15\",\"tag-bg\":\"#FFEDE6\",\"start-bg\":\"#FF8C20\",\"end-bg\":\"#FF4300\",\"ump-main-bg\":\"#ff5e15\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"#FF9300\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#ff5e15\",\"ump-price\":\"#ff5e15\",\"ump-tag-text\":\"#ff5e15\",\"ump-tag-bg\":\"#FFEDE6\",\"ump-coupon-bg\":\"#FFF6F2\",\"ump-border\":\"#FFDCCC\",\"ump-start-bg\":\"#FF8C20\",\"ump-end-bg\":\"#FF4300\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州贝克丹士食品有限公司\",\"area\":\"临平区\",\"business\":\"37\",\"businessName\":\"蛋糕烘焙\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"章总\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2023-02-15 16:31:05\",\"intro\":\"\",\"kdtId\":128798418,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2023/05/08/FgBP0hlojSfYgPoZY_NfK88Jbn_5.jpg\",\"province\":\"浙江省\",\"shopId\":96190213,\"shopName\":\"优享鲜焙\",\"shopType\":0},\"shopConfig\":{\"sold_out_goods_flag\":\"\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "tag_list_left\n", "[{'loading': True, 'title': '鸡蛋', 'alias': '3evxflcpwinz6', 'number': 1, 'goodsNumber': 10}, {'loading': True, 'title': '水果', 'alias': '3nogceh0afk4y', 'number': 10, 'goodsNumber': 10}, {'loading': True, 'title': '糖', 'alias': '1y4hkj6gyfy9u', 'number': 9, 'goodsNumber': 99}, {'loading': True, 'title': '添加剂丨酵母丨改良剂', 'alias': '1yi32zqoemn9u', 'number': 16, 'goodsNumber': 99}, {'loading': True, 'title': '烘焙用油', 'alias': '3f0u4bgudymya', 'number': 8, 'goodsNumber': 30}, {'loading': True, 'title': '可可制品', 'alias': '274hb5m2w4lhe', 'number': 12, 'goodsNumber': 99}, {'loading': True, 'title': '果泥果酱', 'alias': '26x27j2aqiulu', 'number': 8, 'goodsNumber': 99}, {'loading': True, 'title': '沙拉酱', 'alias': '2fqvw6kac1zea', 'number': 5, 'goodsNumber': 99}, {'loading': True, 'title': '调制酒', 'alias': '2on46ld3qkr9u', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '食用馅料', 'alias': '2ftd3ksy2b2tu', 'number': 10, 'goodsNumber': 99}, {'loading': True, 'title': '预拌粉', 'alias': '2x8ba423d5zr6', 'number': 9, 'goodsNumber': 30}, {'loading': True, 'title': '其他辅料', 'alias': '2g1ygvxphjpjm', 'number': 30, 'goodsNumber': 30}, {'loading': True, 'title': '器具包材', 'alias': '2okn5i71gkf5u', 'number': 3, 'goodsNumber': 10}]\n", "Using proxy: **********************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"WFRaD5burC\",\"attributes\":\"{}\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"成品半成品\",\"category\":[],\"uuid\":\"a2d24fca-e7f5-493d-9582-5548cc669755\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"WFRaD5burC\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FrY3u9JfTSz81VMr9R0_maaqINgn.png\"},\"show\":0,\"title\":\"首页\",\"key\":\"home\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"global\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"WFRaD5burC\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"鑫万来\",\"alias\":\"1ylqk64ovwauq\",\"number\":17,\"goodsNumber\":99},{\"loading\":true,\"title\":\"高贝\",\"alias\":\"3eyeapnbfb5xe\",\"number\":11,\"goodsNumber\":99},{\"loading\":true,\"title\":\"一烤得\",\"alias\":\"2oehlic861vtu\",\"number\":7,\"goodsNumber\":10},{\"loading\":true,\"title\":\"饼干\",\"alias\":\"3nvwns0ueqdpu\",\"number\":1,\"goodsNumber\":10},{\"loading\":true,\"title\":\"奥昆\",\"alias\":\"2xbzg7ii68c6q\",\"number\":5,\"goodsNumber\":10},{\"loading\":true,\"title\":\"新迪嘉禾\",\"alias\":\"3niafzf37l7rm\",\"number\":3,\"goodsNumber\":10},{\"loading\":true,\"title\":\"月饼\",\"alias\":\"3nks6h5l1opr6\",\"number\":0,\"goodsNumber\":10}],\"uuid\":\"f97c08b1-6e06-4a8e-998e-a345b8604609\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"circle\",\"imgOpt\":{\"fill\":\"contain\",\"corner\":\"circle\",\"radius\":8,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":500,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":500,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":{\"fontSize\":12,\"delLine\":true,\"tagGap\":2,\"color\":\"#c8c9cc\"},\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"128798418\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"slideType\":\"tag_slide\",\"risk_type\":0,\"risk_alias\":\"WFRaD5burC\"}],\"createdTime\":1678333742000,\"goodsNum\":0,\"id\":109941897,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":128798418,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":1,\"templateId\":1,\"title\":\"成品半成品\",\"updateTime\":1697699598000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"kdtId\":128798418,\"lockStatus\":0,\"shopName\":\"优享鲜焙\",\"shopRole\":0,\"shopTopic\":0,\"shopType\":0},\"themeAndColors\":{\"type\":1,\"colors\":{\"general\":\"#ff5e15\",\"main-bg\":\"#ff5e15\",\"main-bg-gradient\":\"#ff5e15\",\"main-text\":\"#ffffff\",\"vice-bg\":\"#FF9300\",\"vice-text\":\"#ffffff\",\"icon\":\"#ff5e15\",\"price\":\"#ff5e15\",\"tag-text\":\"#ff5e15\",\"tag-bg\":\"#FFEDE6\",\"start-bg\":\"#FF8C20\",\"end-bg\":\"#FF4300\",\"ump-main-bg\":\"#ff5e15\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"#FF9300\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#ff5e15\",\"ump-price\":\"#ff5e15\",\"ump-tag-text\":\"#ff5e15\",\"ump-tag-bg\":\"#FFEDE6\",\"ump-coupon-bg\":\"#FFF6F2\",\"ump-border\":\"#FFDCCC\",\"ump-start-bg\":\"#FF8C20\",\"ump-end-bg\":\"#FF4300\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州贝克丹士食品有限公司\",\"area\":\"临平区\",\"business\":\"37\",\"businessName\":\"蛋糕烘焙\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"章总\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2023-02-15 16:31:05\",\"intro\":\"\",\"kdtId\":128798418,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2023/05/08/FgBP0hlojSfYgPoZY_NfK88Jbn_5.jpg\",\"province\":\"浙江省\",\"shopId\":96190213,\"shopName\":\"优享鲜焙\",\"shopType\":0},\"shopConfig\":{\"sold_out_goods_flag\":\"\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "tag_list_left\n", "[{'loading': True, 'title': '鑫万来', 'alias': '1ylqk64ovwauq', 'number': 17, 'goodsNumber': 99}, {'loading': True, 'title': '高贝', 'alias': '3eyeapnbfb5xe', 'number': 11, 'goodsNumber': 99}, {'loading': True, 'title': '一烤得', 'alias': '2oehlic861vtu', 'number': 7, 'goodsNumber': 10}, {'loading': True, 'title': '饼干', 'alias': '3nvwns0ueqdpu', 'number': 1, 'goodsNumber': 10}, {'loading': True, 'title': '奥昆', 'alias': '2xbzg7ii68c6q', 'number': 5, 'goodsNumber': 10}, {'loading': True, 'title': '新迪嘉禾', 'alias': '3niafzf37l7rm', 'number': 3, 'goodsNumber': 10}, {'loading': True, 'title': '月饼', 'alias': '3nks6h5l1opr6', 'number': 0, 'goodsNumber': 10}]\n", "Using proxy: http://***********:8gTcEKLs@*************:45291\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"2Tt4PpcJNF\",\"attributes\":\"{}\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"水吧咖啡\",\"category\":[],\"uuid\":\"49ded391-24a6-4f6f-b751-f1a3f18fcdcf\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"2Tt4PpcJNF\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"global\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"2Tt4PpcJNF\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"常温牛奶\",\"alias\":\"2fzi4ivgqfdua\",\"number\":1,\"goodsNumber\":99},{\"loading\":true,\"title\":\"鲜牛乳\",\"alias\":\"364jx8lgabozm\",\"number\":1,\"goodsNumber\":99},{\"loading\":true,\"title\":\"炼乳\",\"alias\":\"3es6uc8echvjm\",\"number\":2,\"goodsNumber\":10},{\"loading\":true,\"title\":\"奶粉\",\"alias\":\"275p4wxf0jo82\",\"number\":1,\"goodsNumber\":10},{\"loading\":true,\"title\":\"奶酪丨芝士\",\"alias\":\"2xje176nz2vwi\",\"number\":10,\"goodsNumber\":10},{\"loading\":true,\"title\":\"稀奶油\",\"alias\":\"3evyaq1wm3tde\",\"number\":10,\"goodsNumber\":10},{\"loading\":true,\"title\":\"植脂制品\",\"alias\":\"35zn8algiars2\",\"number\":8,\"goodsNumber\":10}],\"uuid\":\"0a7520d2-7c43-45b7-aa78-ac0378f8d61f\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"circle\",\"imgOpt\":{\"fill\":\"contain\",\"corner\":\"circle\",\"radius\":8,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":500,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":500,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":{\"fontSize\":12,\"delLine\":true,\"tagGap\":2,\"color\":\"#c8c9cc\"},\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"128798418\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"slideType\":\"tag_slide\",\"risk_type\":0,\"risk_alias\":\"2Tt4PpcJNF\"}],\"createdTime\":1678333778000,\"goodsNum\":0,\"id\":109283392,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":128798418,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":1,\"templateId\":1,\"title\":\"水吧咖啡\",\"updateTime\":1687398052000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"kdtId\":128798418,\"lockStatus\":0,\"shopName\":\"优享鲜焙\",\"shopRole\":0,\"shopTopic\":0,\"shopType\":0},\"themeAndColors\":{\"type\":1,\"colors\":{\"general\":\"#ff5e15\",\"main-bg\":\"#ff5e15\",\"main-bg-gradient\":\"#ff5e15\",\"main-text\":\"#ffffff\",\"vice-bg\":\"#FF9300\",\"vice-text\":\"#ffffff\",\"icon\":\"#ff5e15\",\"price\":\"#ff5e15\",\"tag-text\":\"#ff5e15\",\"tag-bg\":\"#FFEDE6\",\"start-bg\":\"#FF8C20\",\"end-bg\":\"#FF4300\",\"ump-main-bg\":\"#ff5e15\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"#FF9300\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#ff5e15\",\"ump-price\":\"#ff5e15\",\"ump-tag-text\":\"#ff5e15\",\"ump-tag-bg\":\"#FFEDE6\",\"ump-coupon-bg\":\"#FFF6F2\",\"ump-border\":\"#FFDCCC\",\"ump-start-bg\":\"#FF8C20\",\"ump-end-bg\":\"#FF4300\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州贝克丹士食品有限公司\",\"area\":\"临平区\",\"business\":\"37\",\"businessName\":\"蛋糕烘焙\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"章总\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2023-02-15 16:31:05\",\"intro\":\"\",\"kdtId\":128798418,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2023/05/08/FgBP0hlojSfYgPoZY_NfK88Jbn_5.jpg\",\"province\":\"浙江省\",\"shopId\":96190213,\"shopName\":\"优享鲜焙\",\"shopType\":0},\"shopConfig\":{\"sold_out_goods_flag\":\"\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "tag_list_left\n", "[{'loading': True, 'title': '常温牛奶', 'alias': '2fzi4ivgqfdua', 'number': 1, 'goodsNumber': 99}, {'loading': True, 'title': '鲜牛乳', 'alias': '364jx8lgabozm', 'number': 1, 'goodsNumber': 99}, {'loading': True, 'title': '炼乳', 'alias': '3es6uc8echvjm', 'number': 2, 'goodsNumber': 10}, {'loading': True, 'title': '奶粉', 'alias': '275p4wxf0jo82', 'number': 1, 'goodsNumber': 10}, {'loading': True, 'title': '奶酪丨芝士', 'alias': '2xje176nz2vwi', 'number': 10, 'goodsNumber': 10}, {'loading': True, 'title': '稀奶油', 'alias': '3evyaq1wm3tde', 'number': 10, 'goodsNumber': 10}, {'loading': True, 'title': '植脂制品', 'alias': '35zn8algiars2', 'number': 8, 'goodsNumber': 10}]\n", "Using proxy: http://***********:8gTcEKLs@*************:50359\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"VAEzhSy1bY\",\"attributes\":\"{}\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"肉制品\",\"category\":[],\"uuid\":\"9e8c522f-0dae-4d17-bb99-c002af3a3ae6\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"VAEzhSy1bY\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"global\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"VAEzhSy1bY\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"圣农\",\"alias\":\"2oi6mu1gafqf6\",\"number\":9,\"goodsNumber\":100},{\"loading\":true,\"title\":\"台宏\",\"alias\":\"3es7j5u020xtu\",\"number\":5,\"goodsNumber\":10},{\"loading\":true,\"title\":\"至大\",\"alias\":\"1y86fbta7xvky\",\"number\":4,\"goodsNumber\":10}],\"uuid\":\"83874757-6fca-4ee5-bed7-3296e8b704f2\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"circle\",\"imgOpt\":{\"fill\":\"contain\",\"corner\":\"circle\",\"radius\":8,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":500,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":500,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":{\"fontSize\":12,\"delLine\":true,\"tagGap\":2,\"color\":\"#c8c9cc\"},\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"128798418\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"slideType\":\"tag_slide\",\"risk_type\":0,\"risk_alias\":\"VAEzhSy1bY\"}],\"createdTime\":1678333812000,\"goodsNum\":0,\"id\":109283359,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":128798418,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":1,\"templateId\":1,\"title\":\"肉制品\",\"updateTime\":1687398066000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"kdtId\":128798418,\"lockStatus\":0,\"shopName\":\"优享鲜焙\",\"shopRole\":0,\"shopTopic\":0,\"shopType\":0},\"themeAndColors\":{\"type\":1,\"colors\":{\"general\":\"#ff5e15\",\"main-bg\":\"#ff5e15\",\"main-bg-gradient\":\"#ff5e15\",\"main-text\":\"#ffffff\",\"vice-bg\":\"#FF9300\",\"vice-text\":\"#ffffff\",\"icon\":\"#ff5e15\",\"price\":\"#ff5e15\",\"tag-text\":\"#ff5e15\",\"tag-bg\":\"#FFEDE6\",\"start-bg\":\"#FF8C20\",\"end-bg\":\"#FF4300\",\"ump-main-bg\":\"#ff5e15\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"#FF9300\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#ff5e15\",\"ump-price\":\"#ff5e15\",\"ump-tag-text\":\"#ff5e15\",\"ump-tag-bg\":\"#FFEDE6\",\"ump-coupon-bg\":\"#FFF6F2\",\"ump-border\":\"#FFDCCC\",\"ump-start-bg\":\"#FF8C20\",\"ump-end-bg\":\"#FF4300\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州贝克丹士食品有限公司\",\"area\":\"临平区\",\"business\":\"37\",\"businessName\":\"蛋糕烘焙\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"章总\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2023-02-15 16:31:05\",\"intro\":\"\",\"kdtId\":128798418,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2023/05/08/FgBP0hlojSfYgPoZY_NfK88Jbn_5.jpg\",\"province\":\"浙江省\",\"shopId\":96190213,\"shopName\":\"优享鲜焙\",\"shopType\":0},\"shopConfig\":{\"sold_out_goods_flag\":\"\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "tag_list_left\n", "[{'loading': True, 'title': '圣农', 'alias': '2oi6mu1gafqf6', 'number': 9, 'goodsNumber': 100}, {'loading': True, 'title': '台宏', 'alias': '3es7j5u020xtu', 'number': 5, 'goodsNumber': 10}, {'loading': True, 'title': '至大', 'alias': '1y86fbta7xvky', 'number': 4, 'goodsNumber': 10}]\n", "Using proxy: *************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"9Zjs781tnl\",\"attributes\":\"{}\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"西餐辅料\",\"category\":[],\"uuid\":\"2c2e6225-5c40-47b6-9bad-dc016e9f9988\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"9Zjs781tnl\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"global\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"9Zjs781tnl\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"番茄制品\",\"alias\":\"2ojf5aibvmhhu\",\"number\":2,\"goodsNumber\":100},{\"loading\":true,\"title\":\"肉制品\",\"alias\":\"36c08jhcjo79u\",\"number\":5,\"goodsNumber\":100},{\"loading\":true,\"title\":\"冷冻蛋糕\",\"alias\":\"2fzgwsub6i23m\",\"number\":30,\"goodsNumber\":30},{\"loading\":true,\"title\":\"炼乳\",\"alias\":\"3es6uc8echvjm\",\"number\":2,\"goodsNumber\":10},{\"loading\":true,\"title\":\"黄油\",\"alias\":\"2g1xluqfiumtu\",\"number\":4,\"goodsNumber\":10},{\"loading\":true,\"title\":\"奶酪丨芝士\",\"alias\":\"2xje176nz2vwi\",\"number\":13,\"goodsNumber\":30},{\"loading\":true,\"title\":\"稀奶油\",\"alias\":\"3evyaq1wm3tde\",\"number\":10,\"goodsNumber\":10},{\"loading\":true,\"title\":\"瑞士卷\",\"alias\":\"1y9fmuok8ur6a\",\"number\":6,\"goodsNumber\":10},{\"loading\":true,\"title\":\"歌德\",\"alias\":\"27bv7jmznlnk2\",\"number\":5,\"goodsNumber\":10},{\"loading\":true,\"title\":\"沙拉酱\",\"alias\":\"2fqvw6kac1zea\",\"number\":5,\"goodsNumber\":10}],\"uuid\":\"9da8b58a-7023-459c-907c-7c4da0bd9a1e\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"circle\",\"imgOpt\":{\"fill\":\"contain\",\"corner\":\"circle\",\"radius\":8,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":500,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":500,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":{\"fontSize\":12,\"delLine\":true,\"tagGap\":2,\"color\":\"#c8c9cc\"},\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"128798418\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"slideType\":\"tag_slide\",\"risk_type\":0,\"risk_alias\":\"9Zjs781tnl\"}],\"createdTime\":1678333848000,\"goodsNum\":0,\"id\":109942005,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":128798418,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":1,\"templateId\":1,\"title\":\"西餐辅料\",\"updateTime\":1687398081000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"kdtId\":128798418,\"lockStatus\":0,\"shopName\":\"优享鲜焙\",\"shopRole\":0,\"shopTopic\":0,\"shopType\":0},\"themeAndColors\":{\"type\":1,\"colors\":{\"general\":\"#ff5e15\",\"main-bg\":\"#ff5e15\",\"main-bg-gradient\":\"#ff5e15\",\"main-text\":\"#ffffff\",\"vice-bg\":\"#FF9300\",\"vice-text\":\"#ffffff\",\"icon\":\"#ff5e15\",\"price\":\"#ff5e15\",\"tag-text\":\"#ff5e15\",\"tag-bg\":\"#FFEDE6\",\"start-bg\":\"#FF8C20\",\"end-bg\":\"#FF4300\",\"ump-main-bg\":\"#ff5e15\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"#FF9300\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#ff5e15\",\"ump-price\":\"#ff5e15\",\"ump-tag-text\":\"#ff5e15\",\"ump-tag-bg\":\"#FFEDE6\",\"ump-coupon-bg\":\"#FFF6F2\",\"ump-border\":\"#FFDCCC\",\"ump-start-bg\":\"#FF8C20\",\"ump-end-bg\":\"#FF4300\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州贝克丹士食品有限公司\",\"area\":\"临平区\",\"business\":\"37\",\"businessName\":\"蛋糕烘焙\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"章总\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2023-02-15 16:31:05\",\"intro\":\"\",\"kdtId\":128798418,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2023/05/08/FgBP0hlojSfYgPoZY_NfK88Jbn_5.jpg\",\"province\":\"浙江省\",\"shopId\":96190213,\"shopName\":\"优享鲜焙\",\"shopType\":0},\"shopConfig\":{\"sold_out_goods_flag\":\"\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "tag_list_left\n", "[{'loading': True, 'title': '番茄制品', 'alias': '2ojf5aibvmhhu', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '肉制品', 'alias': '36c08jhcjo79u', 'number': 5, 'goodsNumber': 100}, {'loading': True, 'title': '冷冻蛋糕', 'alias': '2fzgwsub6i23m', 'number': 30, 'goodsNumber': 30}, {'loading': True, 'title': '炼乳', 'alias': '3es6uc8echvjm', 'number': 2, 'goodsNumber': 10}, {'loading': True, 'title': '黄油', 'alias': '2g1xluqfiumtu', 'number': 4, 'goodsNumber': 10}, {'loading': True, 'title': '奶酪丨芝士', 'alias': '2xje176nz2vwi', 'number': 13, 'goodsNumber': 30}, {'loading': True, 'title': '稀奶油', 'alias': '3evyaq1wm3tde', 'number': 10, 'goodsNumber': 10}, {'loading': True, 'title': '瑞士卷', 'alias': '1y9fmuok8ur6a', 'number': 6, 'goodsNumber': 10}, {'loading': True, 'title': '歌德', 'alias': '27bv7jmznlnk2', 'number': 5, 'goodsNumber': 10}, {'loading': True, 'title': '沙拉酱', 'alias': '2fqvw6kac1zea', 'number': 5, 'goodsNumber': 10}]\n", "Using proxy: *************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"CS1qHErlPv\",\"attributes\":\"{}\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"饮料酒水\",\"category\":[],\"uuid\":\"95c506e2-c6ef-4df9-9c73-eee63a71486a\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"CS1qHErlPv\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"global\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"CS1qHErlPv\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"酒水\",\"alias\":\"279csfa7t6fma\",\"number\":2,\"goodsNumber\":100},{\"loading\":true,\"title\":\"饮料果汁\",\"alias\":\"1y88i8cza25eq\",\"number\":6,\"goodsNumber\":100}],\"uuid\":\"0fe9be3c-26a1-4716-9c15-486252e9b6dc\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"circle\",\"imgOpt\":{\"fill\":\"contain\",\"corner\":\"circle\",\"radius\":8,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":500,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":500,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":{\"fontSize\":12,\"delLine\":true,\"tagGap\":2,\"color\":\"#c8c9cc\"},\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"128798418\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"slideType\":\"tag_slide\",\"risk_type\":0,\"risk_alias\":\"CS1qHErlPv\"}],\"createdTime\":1678333884000,\"goodsNum\":0,\"id\":109941992,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":128798418,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":1,\"templateId\":1,\"title\":\"饮料酒水\",\"updateTime\":1687398102000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"kdtId\":128798418,\"lockStatus\":0,\"shopName\":\"优享鲜焙\",\"shopRole\":0,\"shopTopic\":0,\"shopType\":0},\"themeAndColors\":{\"type\":1,\"colors\":{\"general\":\"#ff5e15\",\"main-bg\":\"#ff5e15\",\"main-bg-gradient\":\"#ff5e15\",\"main-text\":\"#ffffff\",\"vice-bg\":\"#FF9300\",\"vice-text\":\"#ffffff\",\"icon\":\"#ff5e15\",\"price\":\"#ff5e15\",\"tag-text\":\"#ff5e15\",\"tag-bg\":\"#FFEDE6\",\"start-bg\":\"#FF8C20\",\"end-bg\":\"#FF4300\",\"ump-main-bg\":\"#ff5e15\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"#FF9300\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#ff5e15\",\"ump-price\":\"#ff5e15\",\"ump-tag-text\":\"#ff5e15\",\"ump-tag-bg\":\"#FFEDE6\",\"ump-coupon-bg\":\"#FFF6F2\",\"ump-border\":\"#FFDCCC\",\"ump-start-bg\":\"#FF8C20\",\"ump-end-bg\":\"#FF4300\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州贝克丹士食品有限公司\",\"area\":\"临平区\",\"business\":\"37\",\"businessName\":\"蛋糕烘焙\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"章总\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2023-02-15 16:31:05\",\"intro\":\"\",\"kdtId\":128798418,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2023/05/08/FgBP0hlojSfYgPoZY_NfK88Jbn_5.jpg\",\"province\":\"浙江省\",\"shopId\":96190213,\"shopName\":\"优享鲜焙\",\"shopType\":0},\"shopConfig\":{\"sold_out_goods_flag\":\"\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "tag_list_left\n", "[{'loading': True, 'title': '酒水', 'alias': '279csfa7t6fma', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '饮料果汁', 'alias': '1y88i8cza25eq', 'number': 6, 'goodsNumber': 100}]\n", "Using proxy: ***********************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"sJPKIxXuN8\",\"attributes\":\"{}\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"冷冻蛋糕\",\"category\":[],\"uuid\":\"70209a2d-d159-4261-aca8-ce7548511f14\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"sJPKIxXuN8\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FrY3u9JfTSz81VMr9R0_maaqINgn.png\"},\"show\":0,\"title\":\"首页\",\"key\":\"home\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"global\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"sJPKIxXuN8\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"瑞士卷\",\"alias\":\"1y9fmuok8ur6a\",\"number\":6,\"goodsNumber\":100},{\"loading\":true,\"title\":\"冷冻慕斯\",\"alias\":\"1y4he9saystaq\",\"number\":7,\"goodsNumber\":100},{\"loading\":true,\"title\":\"歌德\",\"alias\":\"27bv7jmznlnk2\",\"number\":5,\"goodsNumber\":100},{\"loading\":true,\"title\":\"百可利\",\"alias\":\"2xltuwiu08geq\",\"number\":15,\"goodsNumber\":100},{\"loading\":true,\"title\":\"普利欧\",\"alias\":\"3enb0rpro4fya\",\"number\":4,\"goodsNumber\":100},{\"loading\":true,\"title\":\"魔客\",\"alias\":\"1yjb9cdzf8ftu\",\"number\":40,\"goodsNumber\":100},{\"loading\":true,\"title\":\"千层\",\"alias\":\"3er0edeo06tya\",\"number\":1,\"goodsNumber\":10},{\"loading\":true,\"title\":\"元泉\",\"alias\":\"2okopkwnjf6iq\",\"number\":4,\"goodsNumber\":10}],\"uuid\":\"7cf606ac-d4dc-4400-9cca-3bccc7cf48f2\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"circle\",\"imgOpt\":{\"fill\":\"contain\",\"corner\":\"circle\",\"radius\":8,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":500,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":500,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":{\"fontSize\":12,\"delLine\":true,\"tagGap\":2,\"color\":\"#c8c9cc\"},\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"128798418\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"slideType\":\"tag_slide\",\"risk_type\":0,\"risk_alias\":\"sJPKIxXuN8\"}],\"createdTime\":1678333913000,\"goodsNum\":0,\"id\":109941942,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":128798418,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":1,\"templateId\":1,\"title\":\"冷冻蛋糕\",\"updateTime\":1704177255000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"kdtId\":128798418,\"lockStatus\":0,\"shopName\":\"优享鲜焙\",\"shopRole\":0,\"shopTopic\":0,\"shopType\":0},\"themeAndColors\":{\"type\":1,\"colors\":{\"general\":\"#ff5e15\",\"main-bg\":\"#ff5e15\",\"main-bg-gradient\":\"#ff5e15\",\"main-text\":\"#ffffff\",\"vice-bg\":\"#FF9300\",\"vice-text\":\"#ffffff\",\"icon\":\"#ff5e15\",\"price\":\"#ff5e15\",\"tag-text\":\"#ff5e15\",\"tag-bg\":\"#FFEDE6\",\"start-bg\":\"#FF8C20\",\"end-bg\":\"#FF4300\",\"ump-main-bg\":\"#ff5e15\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"#FF9300\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#ff5e15\",\"ump-price\":\"#ff5e15\",\"ump-tag-text\":\"#ff5e15\",\"ump-tag-bg\":\"#FFEDE6\",\"ump-coupon-bg\":\"#FFF6F2\",\"ump-border\":\"#FFDCCC\",\"ump-start-bg\":\"#FF8C20\",\"ump-end-bg\":\"#FF4300\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州贝克丹士食品有限公司\",\"area\":\"临平区\",\"business\":\"37\",\"businessName\":\"蛋糕烘焙\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"章总\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2023-02-15 16:31:05\",\"intro\":\"\",\"kdtId\":128798418,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2023/05/08/FgBP0hlojSfYgPoZY_NfK88Jbn_5.jpg\",\"province\":\"浙江省\",\"shopId\":96190213,\"shopName\":\"优享鲜焙\",\"shopType\":0},\"shopConfig\":{\"sold_out_goods_flag\":\"\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "tag_list_left\n", "[{'loading': True, 'title': '瑞士卷', 'alias': '1y9fmuok8ur6a', 'number': 6, 'goodsNumber': 100}, {'loading': True, 'title': '冷冻慕斯', 'alias': '1y4he9saystaq', 'number': 7, 'goodsNumber': 100}, {'loading': True, 'title': '歌德', 'alias': '27bv7jmznlnk2', 'number': 5, 'goodsNumber': 100}, {'loading': True, 'title': '百可利', 'alias': '2xltuwiu08geq', 'number': 15, 'goodsNumber': 100}, {'loading': True, 'title': '普利欧', 'alias': '3enb0rpro4fya', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '魔客', 'alias': '1yjb9cdzf8ftu', 'number': 40, 'goodsNumber': 100}, {'loading': True, 'title': '千层', 'alias': '3er0edeo06tya', 'number': 1, 'goodsNumber': 10}, {'loading': True, 'title': '元泉', 'alias': '2okopkwnjf6iq', 'number': 4, 'goodsNumber': 10}]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>root_cate</th>\n", "      <th>sub_cate_list</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'link_type': 'image_ad_selection', 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=pDXPeDPwe0', 'alias': 'pDXPeDPwe0', 'template_id': 1, 'link_title': '乳制品', 'type': 'image_ad_selection', 'title': '乳制品', 'link_id': 109941947}</td>\n", "      <td>[{'loading': True, 'title': '稀奶油', 'alias': '3evyaq1wm3tde', 'number': 15, 'goodsNumber': 100}, {'loading': True, 'title': '植脂制品', 'alias': '35zn8algiars2', 'number': 8, 'goodsNumber': 100}, {'loading': True, 'title': '牛奶', 'alias': '3f5rhrn1vnob6', 'number': 7, 'goodsNumber': 100}, {'loading': True, 'title': '奶酪丨芝士', 'alias': '2xje176nz2vwi', 'number': 13, 'goodsNumber': 100}, {'loading': True, 'title': '黄油', 'alias': '2g1xluqfiumtu', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '炼乳', 'alias': '3es6uc8echvjm', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '奶粉', 'alias': '275p4wxf0jo82', 'number': 1, 'goodsNumber': 100}]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                                                                                                                                                                                                         root_cate  \\\n", "0  {'link_type': 'image_ad_selection', 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=pDXPeDPwe0', 'alias': 'pDXPeDPwe0', 'template_id': 1, 'link_title': '乳制品', 'type': 'image_ad_selection', 'title': '乳制品', 'link_id': 109941947}   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         sub_cate_list  \n", "0  [{'loading': True, 'title': '稀奶油', 'alias': '3evyaq1wm3tde', 'number': 15, 'goodsNumber': 100}, {'loading': True, 'title': '植脂制品', 'alias': '35zn8algiars2', 'number': 8, 'goodsNumber': 100}, {'loading': True, 'title': '牛奶', 'alias': '3f5rhrn1vnob6', 'number': 7, 'goodsNumber': 100}, {'loading': True, 'title': '奶酪丨芝士', 'alias': '2xje176nz2vwi', 'number': 13, 'goodsNumber': 100}, {'loading': True, 'title': '黄油', 'alias': '2g1xluqfiumtu', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '炼乳', 'alias': '3es6uc8echvjm', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '奶粉', 'alias': '275p4wxf0jo82', 'number': 1, 'goodsNumber': 100}]  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_sub_cate_list_by_root_cate(root_cate):\n", "    root_cate_alias='pDXPeDPwe0'\n", "    root_cate_alias=root_cate['alias']\n", "    get_second_cate_url=f'https://h5.youzan.com/wscdeco/feature-detail.json?app_id=wx3e2e3761ccf9bcae&kdt_id=128798418&&alias={root_cate_alias}&check_chainstore=true&stage=16&check_multi_store=1&close_chainstore_webview_limit=true&check_old_home=1'\n", "    second_cate_list=get_remote_data_with_proxy(get_second_cate_url)\n", "    print(second_cate_list)\n", "\n", "    tag_list_left=None\n", "    for component in json.loads(second_cate_list)['data']['components']:\n", "        print(component['type'])\n", "        if component['type']=='tag_list_left':\n", "            tag_list_left=component['tags']\n", "            break\n", "\n", "    print(tag_list_left)\n", "    return tag_list_left\n", "\n", "\n", "all_sub_cate_list=[]\n", "for root_cate in root_cate_list:\n", "    sub_cate_list=get_sub_cate_list_by_root_cate(root_cate)\n", "    all_sub_cate_list.append({\"root_cate\": root_cate, \"sub_cate_list\": sub_cate_list})\n", "\n", "all_sub_cate_list_df=pd.DataFrame(all_sub_cate_list)\n", "all_sub_cate_list_df.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据类目ID获取类目的商品"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'root_cate': {'link_type': 'image_ad_selection', 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=pDXPeDPwe0', 'alias': 'pDXPeDPwe0', 'template_id': 1, 'link_title': '乳制品', 'type': 'image_ad_selection', 'title': '乳制品', 'link_id': 109941947}, 'sub_cate_list': [{'loading': True, 'title': '稀奶油', 'alias': '3evyaq1wm3tde', 'number': 15, 'goodsNumber': 100}, {'loading': True, 'title': '植脂制品', 'alias': '35zn8algiars2', 'number': 8, 'goodsNumber': 100}, {'loading': True, 'title': '牛奶', 'alias': '3f5rhrn1vnob6', 'number': 7, 'goodsNumber': 100}, {'loading': True, 'title': '奶酪丨芝士', 'alias': '2xje176nz2vwi', 'number': 13, 'goodsNumber': 100}, {'loading': True, 'title': '黄油', 'alias': '2g1xluqfiumtu', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '炼乳', 'alias': '3es6uc8echvjm', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '奶粉', 'alias': '275p4wxf0jo82', 'number': 1, 'goodsNumber': 100}]}\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=3evyaq1wm3tde&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: **********************************************\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=35zn8algiars2&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: ***********************************************\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=3f5rhrn1vnob6&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: *************************************************\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=2xje176nz2vwi&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: *************************************************\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=2g1xluqfiumtu&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: ***********************************************\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=3es6uc8echvjm&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: *************************************************\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=275p4wxf0jo82&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: *************************************************\n", "{'root_cate': {'link_type': 'image_ad_selection', 'link_url': 'https://shop128990586.youzan.com/wscshop/showcase/feature?alias=UzTKrwjJk6', 'alias': 'UzTKrwjJk6', 'template_id': 1, 'link_title': '面粉', 'type': 'image_ad_selection', 'title': '面粉', 'link_id': 109941976}, 'sub_cate_list': [{'loading': True, 'title': '王后面粉', 'alias': '35vxc6grkbhg2', 'number': 6, 'goodsNumber': 10}, {'loading': True, 'title': '伯爵', 'alias': '2x9jmeofdl3c2', 'number': 7, 'goodsNumber': 10}, {'loading': True, 'title': '美玫', 'alias': '2g4fujjsvqxz6', 'number': 2, 'goodsNumber': 10}, {'loading': True, 'title': '金像', 'alias': '35vxuqpjwqceq', 'number': 6, 'goodsNumber': 10}, {'loading': True, 'title': '昭和', 'alias': '3f22abujrj31u', 'number': 3, 'goodsNumber': 10}, {'loading': True, 'title': '柔风', 'alias': '2fulsl5psnc5e', 'number': 4, 'goodsNumber': 10}, {'loading': True, 'title': '中粮', 'alias': '2fy8qoxgsuqf6', 'number': 3, 'goodsNumber': 10}, {'loading': True, 'title': '日清', 'alias': '2fs30tpccd6bm', 'number': 3, 'goodsNumber': 10}, {'loading': True, 'title': '冠军系列', 'alias': '3epr6tn745ouq', 'number': 3, 'goodsNumber': 10}, {'loading': True, 'title': '樱皇', 'alias': '27383oifjlrle', 'number': 1, 'goodsNumber': 10}]}\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=35vxc6grkbhg2&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: *************************************************\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=2x9jmeofdl3c2&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: ***********************************************\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=2g4fujjsvqxz6&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: *************************************************\n", "优享鲜焙, URL:https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=35vxuqpjwqceq&json=1&offlineId=0&kdt_id=128798418&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\n", "Using proxy: ***********************************************\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 11\u001b[0m\n\u001b[1;32m      9\u001b[0m url\u001b[38;5;241m=\u001b[39m\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttps://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00malias\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m&json=1&offlineId=0&kdt_id=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mkdt_id\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mbrand_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, URL:\u001b[39m\u001b[38;5;132;01m{\u001b[39;00murl\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 11\u001b[0m cate_product_list\u001b[38;5;241m=\u001b[39m\u001b[43mget_remote_data_with_proxy\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     12\u001b[0m cate_product_list\u001b[38;5;241m=\u001b[39mjson\u001b[38;5;241m.\u001b[39mloads(cate_product_list)\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m product \u001b[38;5;129;01min\u001b[39;00m cate_product_list[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m'\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlist\u001b[39m\u001b[38;5;124m'\u001b[39m]:\n", "Cell \u001b[0;32mIn[2], line 21\u001b[0m, in \u001b[0;36mget_remote_data_with_proxy\u001b[0;34m(url, max_retries)\u001b[0m\n\u001b[1;32m     19\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(max_retries):\n\u001b[1;32m     20\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 21\u001b[0m         response \u001b[38;5;241m=\u001b[39m \u001b[43mrequests\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m30\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     22\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m200\u001b[39m:\n\u001b[1;32m     23\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m response\u001b[38;5;241m.\u001b[39mtext\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/requests/api.py:73\u001b[0m, in \u001b[0;36mget\u001b[0;34m(url, params, **kwargs)\u001b[0m\n\u001b[1;32m     62\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mget\u001b[39m(url, params\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m     63\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a GET request.\u001b[39;00m\n\u001b[1;32m     64\u001b[0m \n\u001b[1;32m     65\u001b[0m \u001b[38;5;124;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     70\u001b[0m \u001b[38;5;124;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[1;32m     71\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m---> 73\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mget\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/requests/api.py:59\u001b[0m, in \u001b[0;36mrequest\u001b[0;34m(method, url, **kwargs)\u001b[0m\n\u001b[1;32m     55\u001b[0m \u001b[38;5;66;03m# By using the 'with' statement we are sure the session is closed, thus we\u001b[39;00m\n\u001b[1;32m     56\u001b[0m \u001b[38;5;66;03m# avoid leaving sockets open which can trigger a ResourceWarning in some\u001b[39;00m\n\u001b[1;32m     57\u001b[0m \u001b[38;5;66;03m# cases, and look like a memory leak in others.\u001b[39;00m\n\u001b[1;32m     58\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m sessions\u001b[38;5;241m.\u001b[39mSession() \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[0;32m---> 59\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msession\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/requests/sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[1;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[1;32m    587\u001b[0m }\n\u001b[1;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/requests/sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    700\u001b[0m start \u001b[38;5;241m=\u001b[39m preferred_clock()\n\u001b[1;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[0;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43madapter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[1;32m    706\u001b[0m elapsed \u001b[38;5;241m=\u001b[39m preferred_clock() \u001b[38;5;241m-\u001b[39m start\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/requests/adapters.py:486\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    483\u001b[0m     timeout \u001b[38;5;241m=\u001b[39m TimeoutSauce(connect\u001b[38;5;241m=\u001b[39mtimeout, read\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[1;32m    485\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 486\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    487\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    488\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    489\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    490\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    491\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    492\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    493\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    494\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    495\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    496\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    497\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    498\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    500\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m    501\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(err, request\u001b[38;5;241m=\u001b[39mrequest)\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/urllib3/connectionpool.py:715\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[1;32m    712\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_proxy(conn)\n\u001b[1;32m    714\u001b[0m \u001b[38;5;66;03m# Make the request on the httplib connection object.\u001b[39;00m\n\u001b[0;32m--> 715\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    716\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    717\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    718\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    719\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    720\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    721\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    722\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    723\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    725\u001b[0m \u001b[38;5;66;03m# If we're going to release the connection in ``finally:``, then\u001b[39;00m\n\u001b[1;32m    726\u001b[0m \u001b[38;5;66;03m# the response doesn't need to know about the connection. Otherwise\u001b[39;00m\n\u001b[1;32m    727\u001b[0m \u001b[38;5;66;03m# it will also try to release it and we'll have a double-release\u001b[39;00m\n\u001b[1;32m    728\u001b[0m \u001b[38;5;66;03m# mess.\u001b[39;00m\n\u001b[1;32m    729\u001b[0m response_conn \u001b[38;5;241m=\u001b[39m conn \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m release_conn \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/urllib3/connectionpool.py:404\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    402\u001b[0m \u001b[38;5;66;03m# Trigger any extra validation we need to do.\u001b[39;00m\n\u001b[1;32m    403\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 404\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    405\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    406\u001b[0m     \u001b[38;5;66;03m# Py2 raises this as a BaseSSLError, Py3 raises it as socket timeout.\u001b[39;00m\n\u001b[1;32m    407\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_raise_timeout(err\u001b[38;5;241m=\u001b[39me, url\u001b[38;5;241m=\u001b[39murl, timeout_value\u001b[38;5;241m=\u001b[39mconn\u001b[38;5;241m.\u001b[39mtimeout)\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/urllib3/connectionpool.py:1058\u001b[0m, in \u001b[0;36mHTTPSConnectionPool._validate_conn\u001b[0;34m(self, conn)\u001b[0m\n\u001b[1;32m   1056\u001b[0m \u001b[38;5;66;03m# Force connect early to allow us to validate the connection.\u001b[39;00m\n\u001b[1;32m   1057\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(conn, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msock\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m):  \u001b[38;5;66;03m# AppE<PERSON>ine might not have  `.sock`\u001b[39;00m\n\u001b[0;32m-> 1058\u001b[0m     \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1060\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m conn\u001b[38;5;241m.\u001b[39mis_verified:\n\u001b[1;32m   1061\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m   1062\u001b[0m         (\n\u001b[1;32m   1063\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnverified HTTPS request is being made to host \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1068\u001b[0m         InsecureRequestWarning,\n\u001b[1;32m   1069\u001b[0m     )\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/urllib3/connection.py:419\u001b[0m, in \u001b[0;36mHTTPSConnection.connect\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    410\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[1;32m    411\u001b[0m     \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mca_certs\n\u001b[1;32m    412\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mca_cert_dir\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    415\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(context, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mload_default_certs\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    416\u001b[0m ):\n\u001b[1;32m    417\u001b[0m     context\u001b[38;5;241m.\u001b[39mload_default_certs()\n\u001b[0;32m--> 419\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock \u001b[38;5;241m=\u001b[39m \u001b[43mssl_wrap_socket\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    420\u001b[0m \u001b[43m    \u001b[49m\u001b[43msock\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    421\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkeyfile\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkey_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    422\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcertfile\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcert_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    423\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkey_password\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkey_password\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    424\u001b[0m \u001b[43m    \u001b[49m\u001b[43mca_certs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mca_certs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    425\u001b[0m \u001b[43m    \u001b[49m\u001b[43mca_cert_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mca_cert_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    426\u001b[0m \u001b[43m    \u001b[49m\u001b[43mca_cert_data\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mca_cert_data\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    427\u001b[0m \u001b[43m    \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_hostname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    428\u001b[0m \u001b[43m    \u001b[49m\u001b[43mssl_context\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    429\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtls_in_tls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtls_in_tls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    430\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    432\u001b[0m \u001b[38;5;66;03m# If we're using all defaults and the connection\u001b[39;00m\n\u001b[1;32m    433\u001b[0m \u001b[38;5;66;03m# is TLSv1 or TLSv1.1 we throw a DeprecationWarning\u001b[39;00m\n\u001b[1;32m    434\u001b[0m \u001b[38;5;66;03m# for the host.\u001b[39;00m\n\u001b[1;32m    435\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[1;32m    436\u001b[0m     default_ssl_context\n\u001b[1;32m    437\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mssl_version \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    438\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mversion\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    439\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock\u001b[38;5;241m.\u001b[39mversion() \u001b[38;5;129;01min\u001b[39;00m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTLSv1\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTLSv1.1\u001b[39m\u001b[38;5;124m\"\u001b[39m}\n\u001b[1;32m    440\u001b[0m ):\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/urllib3/util/ssl_.py:449\u001b[0m, in \u001b[0;36mssl_wrap_socket\u001b[0;34m(sock, keyfile, certfile, cert_reqs, ca_certs, server_hostname, ssl_version, ciphers, ssl_context, ca_cert_dir, key_password, ca_cert_data, tls_in_tls)\u001b[0m\n\u001b[1;32m    437\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m    438\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAn HTTPS request has been made, but the SNI (Server Name \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    439\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIndication) extension to TLS is not available on this platform. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    445\u001b[0m         SNIMissingWarning,\n\u001b[1;32m    446\u001b[0m     )\n\u001b[1;32m    448\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m send_sni:\n\u001b[0;32m--> 449\u001b[0m     ssl_sock \u001b[38;5;241m=\u001b[39m \u001b[43m_ssl_wrap_socket_impl\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    450\u001b[0m \u001b[43m        \u001b[49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtls_in_tls\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_hostname\u001b[49m\n\u001b[1;32m    451\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    452\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    453\u001b[0m     ssl_sock \u001b[38;5;241m=\u001b[39m _ssl_wrap_socket_impl(sock, context, tls_in_tls)\n", "File \u001b[0;32m~/Documents/work@sf/codes/spiderman/.venv/lib/python3.10/site-packages/urllib3/util/ssl_.py:493\u001b[0m, in \u001b[0;36m_ssl_wrap_socket_impl\u001b[0;34m(sock, ssl_context, tls_in_tls, server_hostname)\u001b[0m\n\u001b[1;32m    490\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m SSLTransport(sock, ssl_context, server_hostname)\n\u001b[1;32m    492\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m server_hostname:\n\u001b[0;32m--> 493\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mssl_context\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwrap_socket\u001b[49m\u001b[43m(\u001b[49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_hostname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    494\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    495\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m ssl_context\u001b[38;5;241m.\u001b[39mwrap_socket(sock)\n", "File \u001b[0;32m/opt/homebrew/Cellar/python@3.10/3.10.10/Frameworks/Python.framework/Versions/3.10/lib/python3.10/ssl.py:513\u001b[0m, in \u001b[0;36mSSLContext.wrap_socket\u001b[0;34m(self, sock, server_side, do_handshake_on_connect, suppress_ragged_eofs, server_hostname, session)\u001b[0m\n\u001b[1;32m    507\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrap_socket\u001b[39m(\u001b[38;5;28mself\u001b[39m, sock, server_side\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    508\u001b[0m                 do_handshake_on_connect\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[1;32m    509\u001b[0m                 suppress_ragged_eofs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[1;32m    510\u001b[0m                 server_hostname\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, session\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m):\n\u001b[1;32m    511\u001b[0m     \u001b[38;5;66;03m# SSLSocket class handles server_hostname encoding before it calls\u001b[39;00m\n\u001b[1;32m    512\u001b[0m     \u001b[38;5;66;03m# ctx._wrap_socket()\u001b[39;00m\n\u001b[0;32m--> 513\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msslsocket_class\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_create\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    514\u001b[0m \u001b[43m        \u001b[49m\u001b[43msock\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    515\u001b[0m \u001b[43m        \u001b[49m\u001b[43mserver_side\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_side\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    516\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdo_handshake_on_connect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdo_handshake_on_connect\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    517\u001b[0m \u001b[43m        \u001b[49m\u001b[43msuppress_ragged_eofs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msuppress_ragged_eofs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    518\u001b[0m \u001b[43m        \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_hostname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    519\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcontext\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    520\u001b[0m \u001b[43m        \u001b[49m\u001b[43msession\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msession\u001b[49m\n\u001b[1;32m    521\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/homebrew/Cellar/python@3.10/3.10.10/Frameworks/Python.framework/Versions/3.10/lib/python3.10/ssl.py:1071\u001b[0m, in \u001b[0;36mSSLSocket._create\u001b[0;34m(cls, sock, server_side, do_handshake_on_connect, suppress_ragged_eofs, server_hostname, context, session)\u001b[0m\n\u001b[1;32m   1068\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m timeout \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0.0\u001b[39m:\n\u001b[1;32m   1069\u001b[0m             \u001b[38;5;66;03m# non-blocking\u001b[39;00m\n\u001b[1;32m   1070\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdo_handshake_on_connect should not be specified for non-blocking sockets\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m-> 1071\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdo_handshake\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1072\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (\u001b[38;5;167;01mOSError\u001b[39;00m, \u001b[38;5;167;01mValueError\u001b[39;00m):\n\u001b[1;32m   1073\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclose()\n", "File \u001b[0;32m/opt/homebrew/Cellar/python@3.10/3.10.10/Frameworks/Python.framework/Versions/3.10/lib/python3.10/ssl.py:1342\u001b[0m, in \u001b[0;36mSSLSocket.do_handshake\u001b[0;34m(self, block)\u001b[0m\n\u001b[1;32m   1340\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m timeout \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0.0\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m block:\n\u001b[1;32m   1341\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msettimeout(\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[0;32m-> 1342\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sslobj\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdo_handshake\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1343\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[1;32m   1344\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msettimeout(timeout)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["kdt_id=128798418\n", "all_products=[]\n", "for cate in all_sub_cate_list:\n", "    print(cate)\n", "    cate_info=cate['root_cate']\n", "    sub_cate_list=cate['sub_cate_list']\n", "    for tag in sub_cate_list:\n", "        alias=tag['alias']\n", "        url=f'https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?app_id=wx3e2e3761ccf9bcae&page=1&alias={alias}&json=1&offlineId=0&kdt_id={kdt_id}&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1'\n", "        print(f\"{brand_name}, URL:{url}\")\n", "        cate_product_list=get_remote_data_with_proxy(url)\n", "        cate_product_list=json.loads(cate_product_list)\n", "        for product in cate_product_list['data']['list']:\n", "            product['类目名']=cate_info['title']\n", "            product['二级类目名']=tag['title']\n", "            product['类目link']=cate_info['link_url']\n", "            all_products.append(product)\n", "\n", "print(all_products[0])\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>一级类目</th>\n", "      <th>二级类目</th>\n", "      <th>alias</th>\n", "      <th>link</th>\n", "      <th>数据获取时间</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>乳制品</td>\n", "      <td></td>\n", "      <td>pDXPeDPwe0</td>\n", "      <td>https://shop128990586.youzan.com/wscshop/showcase/feature?alias=pDXPeDPwe0</td>\n", "      <td>2024-01-10 14:17:24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>乳制品</td>\n", "      <td>稀奶油</td>\n", "      <td>3evyaq1wm3tde</td>\n", "      <td></td>\n", "      <td>2024-01-10 14:17:24</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  一级类目 二级类目          alias  \\\n", "0  乳制品          pDXPeDPwe0   \n", "1  乳制品  稀奶油  3evyaq1wm3tde   \n", "\n", "                                                                         link  \\\n", "0  https://shop128990586.youzan.com/wscshop/showcase/feature?alias=pDXPeDPwe0   \n", "1                                                                               \n", "\n", "                数据获取时间  \n", "0  2024-01-10 14:17:24  \n", "1  2024-01-10 14:17:24  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["cate_list_to_save=[]\n", "for root_cate in all_sub_cate_list:\n", "    # print(root_cate)\n", "    cate_list_to_save.append({\"一级类目\":root_cate['root_cate']['title'],\n", "                              \"二级类目\":\"\", \n", "                              \"alias\":root_cate['root_cate']['alias'],\n", "                              \"link\":root_cate['root_cate']['link_url'],\n", "                              \"数据获取时间\":time_of_now})\n", "    for sub_cate in root_cate['sub_cate_list']:\n", "        cate_list_to_save.append({\"一级类目\":root_cate['root_cate']['title'],\n", "                                  \"二级类目\":sub_cate['title'], \n", "                                  \"alias\":sub_cate['alias'], \n", "                                  \"link\":'',\n", "                                  \"数据获取时间\":time_of_now})\n", "\n", "cate_list_to_save_df=pd.DataFrame(cate_list_to_save)\n", "cate_list_to_save_df.to_csv(f\"./data/{brand_name}/{brand_name}-全部类目树.csv\", index=False)\n", "\n", "cate_list_to_save_df.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取商品SKU信息并清洗"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ***********************************************\n", "[{'skuId': 24786677786, 'skuStock': 47, 'skuSpec': '1L*12盒/箱', 'skuPrice': 33500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24614788212, 'skuStock': 1, 'skuSpec': '1L*12盒/箱', 'skuPrice': 51300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24344486315, 'skuStock': 4, 'skuSpec': '1kg*12盒/箱', 'skuPrice': 34800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23995295655, 'skuStock': 14, 'skuSpec': '980g*12盒/箱', 'skuPrice': 28500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13506839883, 'skuStock': 6, 'skuSpec': '1L*12盒/箱', 'skuPrice': 36500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13462722506, 'skuStock': 50, 'skuSpec': '1L*12盒/箱', 'skuPrice': 60300}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13462703063, 'skuStock': 283, 'skuSpec': '1L*12盒/箱', 'skuPrice': 58500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13883615035, 'skuStock': 2, 'skuSpec': '1kg*12盒/箱', 'skuPrice': 30500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13460701920, 'skuStock': 8, 'skuSpec': '1L*6盒/箱', 'skuPrice': 19100}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13456402593, 'skuStock': 69, 'skuSpec': '1L*12盒/箱', 'skuPrice': 47400}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24710883940, 'skuStock': 0, 'skuSpec': '980g*12盒/箱', 'skuPrice': 15000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24610793644, 'skuStock': 0, 'skuSpec': '980g*12盒/箱', 'skuPrice': 19800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24440783959, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 46700}]\n", "Using proxy: **********************************************\n", "[{'skuId': 13462639180, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 34000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13456402646, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 52200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14653113564, 'skuStock': 10, 'skuSpec': '980g*12盒/箱', 'skuPrice': 22000}]\n", "Using proxy: **********************************************\n", "[{'skuId': 13518611535, 'skuStock': 94, 'skuSpec': '907ml*12盒/箱', 'skuPrice': 24000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13462807675, 'skuStock': 14, 'skuSpec': '907g*12盒/箱', 'skuPrice': 27900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13462741455, 'skuStock': 7, 'skuSpec': '907g*12盒/箱', 'skuPrice': 22000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462741190, 'skuStock': 5, 'skuSpec': '907G*12盒/箱', 'skuPrice': 24000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13883631280, 'skuStock': 30, 'skuSpec': '907g*12盒/箱', 'skuPrice': 21900}]\n", "Using proxy: **********************************************\n", "[{'skuId': 14188129636, 'skuStock': 0, 'skuSpec': '907克*12支/箱', 'skuPrice': 20500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13710326961, 'skuStock': 0, 'skuSpec': '907g*12盒/箱', 'skuPrice': 21000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 25086995050, 'skuStock': 6, 'skuSpec': '1L*12盒/箱', 'skuPrice': 7100}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14176303006, 'skuStock': 223, 'skuSpec': '1L*12盒/箱', 'skuPrice': 7000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24281893744, 'skuStock': 95, 'skuSpec': '1L*12盒/箱', 'skuPrice': 7000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24042077590, 'skuStock': 301, 'skuSpec': '1L*6盒/箱', 'skuPrice': 4800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24717195556, 'skuStock': 0, 'skuSpec': '200ml*24盒/箱', 'skuPrice': 4200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24530694076, 'skuStock': 0, 'skuSpec': '950ml*12盒/箱', 'skuPrice': 16800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24509194120, 'skuStock': 0, 'skuSpec': '1L*盒', 'skuPrice': 2600}]\n", "Using proxy: **********************************************\n", "[{'skuId': 25454887348, 'skuStock': 50, 'skuSpec': '500g*6盒/箱', 'skuPrice': 21600}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 25433277537, 'skuStock': 2, 'skuSpec': '20kg/箱', 'skuPrice': 81000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14597429989, 'skuStock': 36, 'skuSpec': '1kg*盒', 'skuPrice': 9000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23826492150, 'skuStock': 34, 'skuSpec': '黄片80片/包', 'skuPrice': 5800}, {'skuId': 23826492151, 'skuStock': 47, 'skuSpec': '白片80片/包', 'skuPrice': 5800}]\n", "Using proxy: **********************************************\n", "[{'skuId': 13475632796, 'skuStock': 28, 'skuSpec': '2kg*1盒', 'skuPrice': 7200}, {'skuId': 13475632797, 'skuStock': 0, 'skuSpec': '2kg*5盒/箱', 'skuPrice': 36000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13475618228, 'skuStock': 9, 'skuSpec': '5kg*包', 'skuPrice': 24000}, {'skuId': 13475618229, 'skuStock': 2, 'skuSpec': '5kg*4包/箱', 'skuPrice': 95600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23745599155, 'skuStock': 4, 'skuSpec': '橙片', 'skuPrice': 7300}, {'skuId': 23745599156, 'skuStock': 26, 'skuSpec': '白片', 'skuPrice': 6500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23745589447, 'skuStock': 0, 'skuSpec': '橙片', 'skuPrice': 69800}, {'skuId': 23745589448, 'skuStock': 1, 'skuSpec': '白片', 'skuPrice': 61500}]\n", "Using proxy: **********************************************\n", "[{'skuId': 23745592743, 'skuStock': 39, 'skuSpec': '1kg*1盒', 'skuPrice': 5900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13475501095, 'skuStock': 19, 'skuSpec': '2kg*1盒', 'skuPrice': 11800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13475509315, 'skuStock': 214, 'skuSpec': '3kg*1袋', 'skuPrice': 14500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23745481145, 'skuStock': 27, 'skuSpec': '3kg*1袋', 'skuPrice': 15000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13883625447, 'skuStock': 0, 'skuSpec': '500g*6盒/箱', 'skuPrice': 23600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23732187871, 'skuStock': 47, 'skuSpec': '25KG*箱', 'skuPrice': 88000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462836833, 'skuStock': 21, 'skuSpec': '5KG*块', 'skuPrice': 30300}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23732186206, 'skuStock': 0, 'skuSpec': '25KG*箱', 'skuPrice': 137000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462825755, 'skuStock': 0, 'skuSpec': '25KG*箱', 'skuPrice': 133000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13709029511, 'skuStock': 6, 'skuSpec': '5kg*1罐', 'skuPrice': 9500}, {'skuId': 13709029512, 'skuStock': 1, 'skuSpec': '5kg*4罐/箱', 'skuPrice': 38000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13709022909, 'skuStock': 18, 'skuSpec': '5kg*1桶', 'skuPrice': 6300}, {'skuId': 13709022910, 'skuStock': 1, 'skuSpec': '5kg*4桶/箱', 'skuPrice': 25200}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13467231253, 'skuStock': 49, 'skuSpec': '25kg*1袋', 'skuPrice': 75000}]\n", "Using proxy: **********************************************\n", "[{'skuId': 25156293966, 'skuStock': 5, 'skuSpec': '25kg/包', 'skuPrice': 28000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14184740077, 'skuStock': 185, 'skuSpec': '25kg*1包', 'skuPrice': 15000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13801233787, 'skuStock': 39, 'skuSpec': '25KG*1袋', 'skuPrice': 15500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13801236063, 'skuStock': 13, 'skuSpec': '25KG*1袋', 'skuPrice': 17800}, {'skuId': 13801236062, 'skuStock': 0, 'skuSpec': '25KG*1袋', 'skuPrice': 18000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13801226476, 'skuStock': 53, 'skuSpec': '25KG*1袋', 'skuPrice': 17600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13801238078, 'skuStock': 409, 'skuSpec': '25KG*1袋', 'skuPrice': 16500}]\n", "Using proxy: **********************************************\n", "[{'skuId': 24097281120, 'skuStock': 3, 'skuSpec': '25KG*1包', 'skuPrice': 39500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24097275129, 'skuStock': 5, 'skuSpec': '25KG*1包', 'skuPrice': 52000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13461001833, 'skuStock': 3, 'skuSpec': '25kg', 'skuPrice': 39000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24097280324, 'skuStock': 3, 'skuSpec': '25KG*1包', 'skuPrice': 28500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24097279671, 'skuStock': 78, 'skuSpec': '25KG*1包', 'skuPrice': 27200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13429246836, 'skuStock': 106, 'skuSpec': '25kg', 'skuPrice': 26800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24097282710, 'skuStock': 4, 'skuSpec': '25KG*1包', 'skuPrice': 38800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13462547862, 'skuStock': 41, 'skuSpec': '25kg', 'skuPrice': 14500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462610006, 'skuStock': 30, 'skuSpec': '25kg', 'skuPrice': 12200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25432488967, 'skuStock': 6, 'skuSpec': '25kg/包', 'skuPrice': 14700}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462614706, 'skuStock': 44, 'skuSpec': '25kg', 'skuPrice': 13300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462607192, 'skuStock': 17, 'skuSpec': '22.7kg', 'skuPrice': 18500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13462531154, 'skuStock': 43, 'skuSpec': '25kg', 'skuPrice': 14300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23731880070, 'skuStock': 23, 'skuSpec': '25kg', 'skuPrice': 14600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23731876737, 'skuStock': 1, 'skuSpec': '25kg', 'skuPrice': 16500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24097279405, 'skuStock': 5, 'skuSpec': '25KG*1包', 'skuPrice': 30500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24097278985, 'skuStock': 2, 'skuSpec': '25KG*1包', 'skuPrice': 29000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24097275119, 'skuStock': 54, 'skuSpec': '25KG*1包', 'skuPrice': 29000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24097199736, 'skuStock': 15, 'skuSpec': '25KG*1包', 'skuPrice': 22000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24097194799, 'skuStock': 79, 'skuSpec': '25KG*1包', 'skuPrice': 22000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24097198936, 'skuStock': 298, 'skuSpec': '25KG*1包', 'skuPrice': 22000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24692283778, 'skuStock': 0, 'skuSpec': '25kg/包', 'skuPrice': 18500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13514625218, 'skuStock': 46, 'skuSpec': '25kg', 'skuPrice': 11600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23782494545, 'skuStock': 46, 'skuSpec': '25kg', 'skuPrice': 12400}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13514622863, 'skuStock': 0, 'skuSpec': '25kg', 'skuPrice': 16500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13502128100, 'skuStock': 5, 'skuSpec': '25kg', 'skuPrice': 33300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13502123208, 'skuStock': 1, 'skuSpec': '25kg', 'skuPrice': 32600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23768885331, 'skuStock': 0, 'skuSpec': '25kg', 'skuPrice': 32800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13752933153, 'skuStock': 8, 'skuSpec': '25kg/包', 'skuPrice': 43000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13752913881, 'skuStock': 9, 'skuSpec': '25kg/包', 'skuPrice': 54000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13752930807, 'skuStock': 0, 'skuSpec': '22.5kg/包', 'skuPrice': 43000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24752680461, 'skuStock': 23, 'skuSpec': '25kg/包', 'skuPrice': 30800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14355525893, 'skuStock': 6, 'skuSpec': '约27斤 /筐(含押金15元-可退）', 'skuPrice': 15000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14596228430, 'skuStock': 20, 'skuSpec': '水仙芒（11.5斤-12.5斤左右）', 'skuPrice': 11300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25338695154, 'skuStock': 7, 'skuSpec': '秘鲁蓝莓 125G*12盒/一级', 'skuPrice': 16800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14562516560, 'skuStock': 99, 'skuSpec': '红颜草莓24粒', 'skuPrice': 1300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14562502039, 'skuStock': 97, 'skuSpec': '红颜草莓20粒', 'skuPrice': 1300}]\n", "Using proxy: **********************************************\n", "[{'skuId': 14562445794, 'skuStock': 100, 'skuSpec': '红颜草莓15粒', 'skuPrice': 1300}]\n", "Using proxy: **********************************************\n", "[{'skuId': 14559234897, 'skuStock': 30, 'skuSpec': '一板3.8到4斤装左右', 'skuPrice': 6000}]\n", "Using proxy: **********************************************\n", "[{'skuId': 24968899170, 'skuStock': 20, 'skuSpec': '越南进口金煌芒  毛重15.6-16.6', 'skuPrice': 14000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24968875799, 'skuStock': 10, 'skuSpec': '红心火龙果（毛重30-32斤）', 'skuPrice': 13000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14356743826, 'skuStock': 40, 'skuSpec': '越南进口青芒 毛重15.5-16.5/一级果', 'skuPrice': 12300}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24836195675, 'skuStock': 30, 'skuSpec': '越南红心火龙果-13-15斤（小规格）', 'skuPrice': 8200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14339746047, 'skuStock': 37, 'skuSpec': '125G*2盒', 'skuPrice': 2900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24531181767, 'skuStock': 20, 'skuSpec': '毛重32斤/二级/标准规格', 'skuPrice': 12200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14321846620, 'skuStock': 1, 'skuSpec': '25kg/包', 'skuPrice': 23000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23845389912, 'skuStock': 37, 'skuSpec': '2kg*1包', 'skuPrice': 2150}, {'skuId': 23845389913, 'skuStock': 2, 'skuSpec': '2kg*10包/箱', 'skuPrice': 21500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13572325813, 'skuStock': 6, 'skuSpec': '25kg*1包', 'skuPrice': 23500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13495847876, 'skuStock': 12, 'skuSpec': '50kg*1袋', 'skuPrice': 42500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23763987358, 'skuStock': 3, 'skuSpec': '50kg*1袋', 'skuPrice': 44000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13495323669, 'skuStock': 4, 'skuSpec': '1kg*1袋', 'skuPrice': 6300}]\n", "Using proxy: **********************************************\n", "[{'skuId': 23743395201, 'skuStock': 4, 'skuSpec': '13.62kg*1桶', 'skuPrice': 22800}]\n", "Using proxy: **********************************************\n", "[{'skuId': 23998691948, 'skuStock': 56, 'skuSpec': '30kg', 'skuPrice': 27000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23998689777, 'skuStock': 153, 'skuSpec': '30kg', 'skuPrice': 27700}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23972480619, 'skuStock': 6, 'skuSpec': '5kg*1桶', 'skuPrice': 12600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23864988903, 'skuStock': 194, 'skuSpec': '300g*1袋', 'skuPrice': 1300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23782683714, 'skuStock': 98, 'skuSpec': '500*1包', 'skuPrice': 2700}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13473900643, 'skuStock': 2, 'skuSpec': '5kg*1桶', 'skuPrice': 14000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23743578244, 'skuStock': 23, 'skuSpec': '1kg*1袋', 'skuPrice': 3000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23743498571, 'skuStock': 41, 'skuSpec': '1kg*1袋', 'skuPrice': 3800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23743385918, 'skuStock': 17, 'skuSpec': '1kg*1桶', 'skuPrice': 5300}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13473509610, 'skuStock': 26, 'skuSpec': '1.4kg*1桶', 'skuPrice': 3300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13473443255, 'skuStock': 27, 'skuSpec': '1kg*1桶', 'skuPrice': 3800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13473444209, 'skuStock': 19, 'skuSpec': '1kg*1袋', 'skuPrice': 12000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23742786261, 'skuStock': 6, 'skuSpec': '2.7kg*1桶', 'skuPrice': 6200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13467744206, 'skuStock': 47, 'skuSpec': '500g*1袋', 'skuPrice': 2500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14647919649, 'skuStock': 0, 'skuSpec': '1kg', 'skuPrice': 3800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23782796221, 'skuStock': 0, 'skuSpec': '500g*1袋', 'skuPrice': 2150}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23763892056, 'skuStock': 0, 'skuSpec': '3.25kg*1罐', 'skuPrice': 5500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23743492818, 'skuStock': 0, 'skuSpec': '4kg*1桶', 'skuPrice': 13500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25278194167, 'skuStock': 30, 'skuSpec': '5L*桶', 'skuPrice': 9500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24173484231, 'skuStock': 2, 'skuSpec': '20KG', 'skuPrice': 45500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24089587178, 'skuStock': 11, 'skuSpec': '1kg*10片/箱', 'skuPrice': 26000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23874286918, 'skuStock': 8, 'skuSpec': '16kg*1桶', 'skuPrice': 33500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13590717335, 'skuStock': 16, 'skuSpec': '10kg/箱', 'skuPrice': 23000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23736079745, 'skuStock': 2, 'skuSpec': '5L*1桶', 'skuPrice': 9000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23735778169, 'skuStock': 12, 'skuSpec': '16KG', 'skuPrice': 35000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13463830419, 'skuStock': 57, 'skuSpec': '20KG', 'skuPrice': 41800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25381679442, 'skuStock': 19, 'skuSpec': '1kg*袋', 'skuPrice': 7500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13495414395, 'skuStock': 12, 'skuSpec': '623g*1桶', 'skuPrice': 2500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23758884719, 'skuStock': 45, 'skuSpec': '1kg*1块', 'skuPrice': 3800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23758879065, 'skuStock': 12, 'skuSpec': '1kg*1块', 'skuPrice': 3800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23758878540, 'skuStock': 2, 'skuSpec': '1kg*1块', 'skuPrice': 3800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13487439129, 'skuStock': 21, 'skuSpec': '1kg*1块', 'skuPrice': 3800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13487436448, 'skuStock': 27, 'skuSpec': '500g*1盒', 'skuPrice': 3500}, {'skuId': 13487436449, 'skuStock': 1, 'skuSpec': '500g*8盒/箱', 'skuPrice': 28000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23758875800, 'skuStock': 9, 'skuSpec': '500g*1盒', 'skuPrice': 3500}, {'skuId': 23758875801, 'skuStock': 0, 'skuSpec': '500g*8盒/箱', 'skuPrice': 28000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13475636761, 'skuStock': 3, 'skuSpec': '1kg*1袋', 'skuPrice': 5600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23743482274, 'skuStock': 0, 'skuSpec': '防潮', 'skuPrice': 9500}, {'skuId': 23743482275, 'skuStock': 5, 'skuSpec': '深黑', 'skuPrice': 8000}, {'skuId': 23743482276, 'skuStock': 17, 'skuSpec': '高脂', 'skuPrice': 6000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13487445781, 'skuStock': 0, 'skuSpec': '1kg*1块', 'skuPrice': 3800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23758789776, 'skuStock': 0, 'skuSpec': '1kg*1袋', 'skuPrice': 5600}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 25278496092, 'skuStock': 20, 'skuSpec': '1kg*袋', 'skuPrice': 3100}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24701880655, 'skuStock': 3, 'skuSpec': '2.5kg', 'skuPrice': 4100}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14243006672, 'skuStock': 10, 'skuSpec': '3kg/桶', 'skuPrice': 10500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14117030825, 'skuStock': 5, 'skuSpec': '1kg*1盒', 'skuPrice': 7500}, {'skuId': 14117030826, 'skuStock': 0, 'skuSpec': '1kg*1盒', 'skuPrice': 10800}, {'skuId': 14117030827, 'skuStock': 0, 'skuSpec': '1kg*1盒', 'skuPrice': 7500}, {'skuId': 14117030828, 'skuStock': 7, 'skuSpec': '1kg*1盒', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13495840389, 'skuStock': 2, 'skuSpec': '3kg*1桶', 'skuPrice': 3200}, {'skuId': 13495840390, 'skuStock': 1, 'skuSpec': '3kg*1桶', 'skuPrice': 3500}, {'skuId': 13495840391, 'skuStock': 1, 'skuSpec': '3kg*1桶', 'skuPrice': 4000}, {'skuId': 13495840392, 'skuStock': 5, 'skuSpec': '3kg*1桶', 'skuPrice': 3000}, {'skuId': 13495840393, 'skuStock': 3, 'skuSpec': '3kg*1桶', 'skuPrice': 3500}, {'skuId': 13495840394, 'skuStock': 3, 'skuSpec': '3kg*1桶', 'skuPrice': 3200}, {'skuId': 13495840395, 'skuStock': 4, 'skuSpec': '3kg*1桶', 'skuPrice': 3000}, {'skuId': 13495840396, 'skuStock': 0, 'skuSpec': '3kg*1桶', 'skuPrice': 3000}, {'skuId': 13495840397, 'skuStock': 17, 'skuSpec': '3kg*1桶', 'skuPrice': 3500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13495811098, 'skuStock': 7, 'skuSpec': '5kg*1桶', 'skuPrice': 22000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14365516057, 'skuStock': 0, 'skuSpec': '3kg/桶', 'skuPrice': 6000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13954816011, 'skuStock': 0, 'skuSpec': '5kg*1桶', 'skuPrice': 20000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13599120681, 'skuStock': 42, 'skuSpec': '1kg*1袋', 'skuPrice': 1500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23874196534, 'skuStock': 94, 'skuSpec': '900g*1袋', 'skuPrice': 1500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13599041748, 'skuStock': 14, 'skuSpec': '900g*1袋', 'skuPrice': 1500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23764079363, 'skuStock': 3, 'skuSpec': '1kg*1袋', 'skuPrice': 1700}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23743488804, 'skuStock': 9, 'skuSpec': '1kg*1袋', 'skuPrice': 1500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13496141005, 'skuStock': 4, 'skuSpec': '700ml*1瓶', 'skuPrice': 10500}, {'skuId': 13496141006, 'skuStock': 0, 'skuSpec': '700ml*12瓶/箱', 'skuPrice': 117600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13496130313, 'skuStock': 0, 'skuSpec': '750ml*6瓶/箱', 'skuPrice': 8000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24544878487, 'skuStock': 3, 'skuSpec': '5kg*4袋/箱', 'skuPrice': 23500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14173649011, 'skuStock': 5, 'skuSpec': '410g*24罐/箱', 'skuPrice': 8000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24272698562, 'skuStock': 1, 'skuSpec': '500g*20袋/箱', 'skuPrice': 15500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24271194886, 'skuStock': 38, 'skuSpec': '5kg*包', 'skuPrice': 6300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13687909017, 'skuStock': 32, 'skuSpec': '5kg*1袋', 'skuPrice': 6300}]\n", "Using proxy: **********************************************\n", "[{'skuId': 13687844497, 'skuStock': 19, 'skuSpec': '5kg*1袋', 'skuPrice': 6500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23972898634, 'skuStock': 4, 'skuSpec': '奶酪味', 'skuPrice': 1800}, {'skuId': 23972898635, 'skuStock': 0, 'skuSpec': '蛋奶味', 'skuPrice': 2100}, {'skuId': 23972898636, 'skuStock': 2, 'skuSpec': '酸奶味', 'skuPrice': 2100}, {'skuId': 23972898637, 'skuStock': 0, 'skuSpec': '巧克力味', 'skuPrice': 2100}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13639300530, 'skuStock': 2, 'skuSpec': '5kg*4袋/箱', 'skuPrice': 43200}, {'skuId': 13639300531, 'skuStock': 13, 'skuSpec': '5kg*1袋', 'skuPrice': 10800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23913977496, 'skuStock': 1, 'skuSpec': '3kg*6袋/箱', 'skuPrice': 25200}, {'skuId': 23913977497, 'skuStock': 1, 'skuSpec': '3kg*1袋', 'skuPrice': 4200}]\n", "Using proxy: **********************************************\n", "[{'skuId': 14266746876, 'skuStock': 0, 'skuSpec': '5kg*4袋/箱', 'skuPrice': 36500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24536793833, 'skuStock': 7, 'skuSpec': '1kg*1袋', 'skuPrice': 3300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13662006956, 'skuStock': 10, 'skuSpec': '5kg*2袋/ 箱', 'skuPrice': 29000}, {'skuId': 13662006957, 'skuStock': 15, 'skuSpec': '5kg/袋', 'skuPrice': 14500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13661949897, 'skuStock': 3, 'skuSpec': '5kg*2袋/ 箱', 'skuPrice': 28000}, {'skuId': 13661949898, 'skuStock': 6, 'skuSpec': '5kg/袋', 'skuPrice': 14000}]\n", "Using proxy: **********************************************\n", "[{'skuId': 23900078778, 'skuStock': 28, 'skuSpec': '2.5kg*1袋', 'skuPrice': 6000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13598906452, 'skuStock': 11, 'skuSpec': '2.5kg*1袋', 'skuPrice': 7000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13598911710, 'skuStock': 19, 'skuSpec': '5kg*1袋', 'skuPrice': 9200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24790675306, 'skuStock': 0, 'skuSpec': '5kg*袋', 'skuPrice': 8800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14316514937, 'skuStock': 0, 'skuSpec': '5kg*袋', 'skuPrice': 10500}]\n", "Using proxy: **********************************************\n", "[{'skuId': 14054441310, 'skuStock': 0, 'skuSpec': '1kg*1袋', 'skuPrice': 2800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14593026966, 'skuStock': 6, 'skuSpec': '1KG*1块', 'skuPrice': 3000}, {'skuId': 14593026967, 'skuStock': 4, 'skuSpec': '1kg*10块/箱', 'skuPrice': 28800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25278179108, 'skuStock': 27, 'skuSpec': '400g*袋', 'skuPrice': 1600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14310012296, 'skuStock': 22, 'skuSpec': '3kg*1包', 'skuPrice': 4000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24540895776, 'skuStock': 99, 'skuSpec': '1kg*1袋', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24457881998, 'skuStock': 4, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 16800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14096541343, 'skuStock': 10, 'skuSpec': '3kg/1袋', 'skuPrice': 21000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14067141278, 'skuStock': 8, 'skuSpec': '900g*12袋/箱', 'skuPrice': 21600}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14042110954, 'skuStock': 14, 'skuSpec': '20颗/袋', 'skuPrice': 2500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24314077157, 'skuStock': 3, 'skuSpec': '500g*40袋/箱', 'skuPrice': 4800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24263377044, 'skuStock': 34, 'skuSpec': '420g*1盒', 'skuPrice': 750}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24251596419, 'skuStock': 25, 'skuSpec': '2.5kg*1袋', 'skuPrice': 12000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24194791782, 'skuStock': 12, 'skuSpec': '1kg', 'skuPrice': 7500}]\n", "Using proxy: **********************************************\n", "[{'skuId': 13890132868, 'skuStock': 45, 'skuSpec': '1kg', 'skuPrice': 2500}]\n", "Using proxy: **********************************************\n", "[{'skuId': 24173488897, 'skuStock': 10, 'skuSpec': '3kg*1袋', 'skuPrice': 5500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13711707457, 'skuStock': 914, 'skuSpec': '1kg', 'skuPrice': 2500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13687437199, 'skuStock': 4, 'skuSpec': '1kg*1袋', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13687444010, 'skuStock': 1, 'skuSpec': '1kg*1袋', 'skuPrice': 2600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13687421430, 'skuStock': 12, 'skuSpec': '1kg*1袋', 'skuPrice': 4800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13687330869, 'skuStock': 33, 'skuSpec': '1kg*1袋', 'skuPrice': 6500}, {'skuId': 13687330868, 'skuStock': 5, 'skuSpec': '1kg*1袋', 'skuPrice': 7000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24148188107, 'skuStock': 1, 'skuSpec': '40g*5片*48盒/箱', 'skuPrice': 33600}, {'skuId': 24148188104, 'skuStock': 2, 'skuSpec': '40g*5片*48盒/箱', 'skuPrice': 33600}, {'skuId': 24148188105, 'skuStock': 8, 'skuSpec': '40g*5片*48盒/箱', 'skuPrice': 33600}, {'skuId': 24148188106, 'skuStock': 0, 'skuSpec': '40g*5片*48盒/箱', 'skuPrice': 33600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23887693015, 'skuStock': 1, 'skuSpec': '0.97kg*12盒/箱', 'skuPrice': 34000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13599131320, 'skuStock': 5, 'skuSpec': '2.5kg*1罐', 'skuPrice': 7300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13572906531, 'skuStock': 11, 'skuSpec': '10kg/箱', 'skuPrice': 15300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23846079192, 'skuStock': 5, 'skuSpec': '10kg/箱', 'skuPrice': 23500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24120197190, 'skuStock': 2, 'skuSpec': '220g*5*45盒/箱', 'skuPrice': 44200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23763995902, 'skuStock': 25, 'skuSpec': '500g*1袋', 'skuPrice': 850}]\n", "Using proxy: **********************************************\n", "[{'skuId': 23763895980, 'skuStock': 21, 'skuSpec': '85g*1瓶', 'skuPrice': 1900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13495441306, 'skuStock': 2, 'skuSpec': '3kg*1桶', 'skuPrice': 4700}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13495433960, 'skuStock': 22, 'skuSpec': '3kg*1桶', 'skuPrice': 3390}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13473903205, 'skuStock': 33, 'skuSpec': '0.45kg*1瓶', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23743480352, 'skuStock': 150, 'skuSpec': '400g*1袋', 'skuPrice': 1800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23742787858, 'skuStock': 14, 'skuSpec': '500g*1袋', 'skuPrice': 16000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14614446468, 'skuStock': 0, 'skuSpec': '5kg*桶', 'skuPrice': 13800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24313893728, 'skuStock': 0, 'skuSpec': '2kg*1袋', 'skuPrice': 14100}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13986505695, 'skuStock': 0, 'skuSpec': '5kg/桶', 'skuPrice': 3600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13981929747, 'skuStock': 0, 'skuSpec': '2.5kg*1袋', 'skuPrice': 16200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13962232345, 'skuStock': 0, 'skuSpec': '1kg／包', 'skuPrice': 5300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13711427343, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 31000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23846076336, 'skuStock': 0, 'skuSpec': '公斤', 'skuPrice': 5500}, {'skuId': 24444477146, 'skuStock': 0, 'skuSpec': '11.34kg/箱', 'skuPrice': 61800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13473436338, 'skuStock': 0, 'skuSpec': '1kg*1盒', 'skuPrice': 14300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24359177081, 'skuStock': 8, 'skuSpec': '25只/1盒-0', 'skuPrice': 900}, {'skuId': 24359177082, 'skuStock': 0, 'skuSpec': '25只/1盒-1', 'skuPrice': 900}, {'skuId': 24359177083, 'skuStock': 3, 'skuSpec': '25只/1盒-2', 'skuPrice': 900}, {'skuId': 24359177084, 'skuStock': 8, 'skuSpec': '25只/1盒-3', 'skuPrice': 900}, {'skuId': 24359177085, 'skuStock': 8, 'skuSpec': '25只/1盒-4', 'skuPrice': 900}, {'skuId': 24359177086, 'skuStock': 8, 'skuSpec': '25只/1盒-5', 'skuPrice': 900}, {'skuId': 24359177087, 'skuStock': 6, 'skuSpec': '25只/1盒-6', 'skuPrice': 900}, {'skuId': 24359177088, 'skuStock': 8, 'skuSpec': '25只/1盒-7', 'skuPrice': 900}, {'skuId': 24359177089, 'skuStock': 4, 'skuSpec': '25只/1盒-8', 'skuPrice': 900}, {'skuId': 24359177090, 'skuStock': 7, 'skuSpec': '25只/1盒-9', 'skuPrice': 900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14043104723, 'skuStock': 2, 'skuSpec': '300套/箱', 'skuPrice': 36000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13743639944, 'skuStock': 235, 'skuSpec': '100只/包', 'skuPrice': 1000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25449286253, 'skuStock': 50, 'skuSpec': '20g*300只/箱', 'skuPrice': 11500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14648642315, 'skuStock': 3, 'skuSpec': '110g*20只/包', 'skuPrice': 6200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14197644527, 'skuStock': 2, 'skuSpec': '25g*24只*10袋/箱', 'skuPrice': 17200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13611121724, 'skuStock': 4, 'skuSpec': '1.8kg*4袋/箱', 'skuPrice': 28000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13611110705, 'skuStock': 2, 'skuSpec': '40g*192个/箱', 'skuPrice': 30000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13610435351, 'skuStock': 29, 'skuSpec': '25g*1袋/24个', 'skuPrice': 2250}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13495223108, 'skuStock': 5, 'skuSpec': '600g*10袋/箱', 'skuPrice': 17500}]\n", "Using proxy: **********************************************\n", "[{'skuId': 13495208546, 'skuStock': 12, 'skuSpec': '24g*300只/箱', 'skuPrice': 18000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13495207413, 'skuStock': 3, 'skuSpec': '55g*180只/箱', 'skuPrice': 27000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13495209575, 'skuStock': 2, 'skuSpec': '30g*240只/箱', 'skuPrice': 21000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24545792179, 'skuStock': 2, 'skuSpec': '160g*60只/箱', 'skuPrice': 25500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13495143000, 'skuStock': 1, 'skuSpec': '110g*100只/箱（1箱）', 'skuPrice': 30500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13495134609, 'skuStock': 1, 'skuSpec': '30g*18只*6包/箱（1箱）', 'skuPrice': 14000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23886796912, 'skuStock': 6, 'skuSpec': '200g*1袋/30个', 'skuPrice': 4600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23763575345, 'skuStock': 0, 'skuSpec': '120g*60根/箱', 'skuPrice': 16000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23763494409, 'skuStock': 0, 'skuSpec': '300g*42只/箱', 'skuPrice': 24500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23763490289, 'skuStock': 0, 'skuSpec': '50g*90只/箱', 'skuPrice': 14500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24135285580, 'skuStock': 5, 'skuSpec': '30g*108只/箱', 'skuPrice': 12500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13611042958, 'skuStock': 4, 'skuSpec': '40g*210只/箱', 'skuPrice': 17200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13883729635, 'skuStock': 1, 'skuSpec': '55g*72只/箱', 'skuPrice': 18000}, {'skuId': 13883729634, 'skuStock': 5, 'skuSpec': '55g*72只/箱', 'skuPrice': 18000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13571129362, 'skuStock': 2, 'skuSpec': '50g*75只/箱', 'skuPrice': 14600}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13571134455, 'skuStock': 2, 'skuSpec': '11g*560只/箱', 'skuPrice': 15500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13571129494, 'skuStock': 1, 'skuSpec': '21g*300只/箱', 'skuPrice': 13800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23843987218, 'skuStock': 7, 'skuSpec': '45g*150只/箱', 'skuPrice': 16500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23843984865, 'skuStock': 11, 'skuSpec': '400g*200只/箱', 'skuPrice': 29000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23774079672, 'skuStock': 6, 'skuSpec': '50g*75只/箱', 'skuPrice': 13000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25449397987, 'skuStock': 0, 'skuSpec': '25g*320只/箱', 'skuPrice': 18600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13571142989, 'skuStock': 0, 'skuSpec': '135g*72只/箱', 'skuPrice': 19000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14245511154, 'skuStock': 1, 'skuSpec': '40g*28个*7盘/箱', 'skuPrice': 23500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24035391477, 'skuStock': 1, 'skuSpec': '50g*23个*7盘/箱', 'skuPrice': 26000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13745336104, 'skuStock': 5, 'skuSpec': '45g*32个*7盘/箱', 'skuPrice': 35800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23888780851, 'skuStock': 3, 'skuSpec': '12块*19袋/箱', 'skuPrice': 36000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13745349518, 'skuStock': 0, 'skuSpec': '45g*32个*7盘/箱', 'skuPrice': 42300}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24035382506, 'skuStock': 0, 'skuSpec': '40g*23个*7盘/箱', 'skuPrice': 29000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23888597331, 'skuStock': 0, 'skuSpec': '50g*12个*15包/箱', 'skuPrice': 36000}, {'skuId': 23888597332, 'skuStock': 0, 'skuSpec': '50g*12个/包', 'skuPrice': 2500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14152509646, 'skuStock': 0, 'skuSpec': '400g*24袋/箱', 'skuPrice': 42600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14188127669, 'skuStock': 0, 'skuSpec': '15个*5袋/箱', 'skuPrice': 11500}, {'skuId': 14188127670, 'skuStock': 1, 'skuSpec': '15个*5袋/箱', 'skuPrice': 12500}, {'skuId': 14188127671, 'skuStock': 0, 'skuSpec': '15个*5袋/箱', 'skuPrice': 13500}]\n", "Using proxy: **********************************************\n", "[{'skuId': 14227839389, 'skuStock': 0, 'skuSpec': '600g*1袋', 'skuPrice': 1500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14188131008, 'skuStock': 0, 'skuSpec': '12个*2托/层*3层/箱', 'skuPrice': 13500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14188130912, 'skuStock': 0, 'skuSpec': '24个*5托/箱', 'skuPrice': 13000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24535776298, 'skuStock': 0, 'skuSpec': '600g*10袋/箱', 'skuPrice': 12300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14247927044, 'skuStock': 2, 'skuSpec': '430*14袋/箱', 'skuPrice': 22300}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14247928886, 'skuStock': 1, 'skuSpec': '500g*15只/箱', 'skuPrice': 24500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14247919155, 'skuStock': 3, 'skuSpec': '590g*14只/箱', 'skuPrice': 25500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24042077590, 'skuStock': 301, 'skuSpec': '1L*6盒/箱', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24530694076, 'skuStock': 0, 'skuSpec': '950ml*12盒/箱', 'skuPrice': 16800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13709029511, 'skuStock': 6, 'skuSpec': '5kg*1罐', 'skuPrice': 9500}, {'skuId': 13709029512, 'skuStock': 1, 'skuSpec': '5kg*4罐/箱', 'skuPrice': 38000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13709022909, 'skuStock': 18, 'skuSpec': '5kg*1桶', 'skuPrice': 6300}, {'skuId': 13709022910, 'skuStock': 1, 'skuSpec': '5kg*4桶/箱', 'skuPrice': 25200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13467231253, 'skuStock': 49, 'skuSpec': '25kg*1袋', 'skuPrice': 75000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 25454887348, 'skuStock': 50, 'skuSpec': '500g*6盒/箱', 'skuPrice': 21600}]\n", "Using proxy: **********************************************\n", "[{'skuId': 25433277537, 'skuStock': 2, 'skuSpec': '20kg/箱', 'skuPrice': 81000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14597429989, 'skuStock': 36, 'skuSpec': '1kg*盒', 'skuPrice': 9000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23826492150, 'skuStock': 34, 'skuSpec': '黄片80片/包', 'skuPrice': 5800}, {'skuId': 23826492151, 'skuStock': 47, 'skuSpec': '白片80片/包', 'skuPrice': 5800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13475632796, 'skuStock': 28, 'skuSpec': '2kg*1盒', 'skuPrice': 7200}, {'skuId': 13475632797, 'skuStock': 0, 'skuSpec': '2kg*5盒/箱', 'skuPrice': 36000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13475618228, 'skuStock': 9, 'skuSpec': '5kg*包', 'skuPrice': 24000}, {'skuId': 13475618229, 'skuStock': 2, 'skuSpec': '5kg*4包/箱', 'skuPrice': 95600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23745599155, 'skuStock': 4, 'skuSpec': '橙片', 'skuPrice': 7300}, {'skuId': 23745599156, 'skuStock': 26, 'skuSpec': '白片', 'skuPrice': 6500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23745589447, 'skuStock': 0, 'skuSpec': '橙片', 'skuPrice': 69800}, {'skuId': 23745589448, 'skuStock': 1, 'skuSpec': '白片', 'skuPrice': 61500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23745592743, 'skuStock': 39, 'skuSpec': '1kg*1盒', 'skuPrice': 5900}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13475501095, 'skuStock': 19, 'skuSpec': '2kg*1盒', 'skuPrice': 11800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13475509315, 'skuStock': 214, 'skuSpec': '3kg*1袋', 'skuPrice': 14500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23745481145, 'skuStock': 27, 'skuSpec': '3kg*1袋', 'skuPrice': 15000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13883625447, 'skuStock': 0, 'skuSpec': '500g*6盒/箱', 'skuPrice': 23600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24786677786, 'skuStock': 47, 'skuSpec': '1L*12盒/箱', 'skuPrice': 33500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24614788212, 'skuStock': 1, 'skuSpec': '1L*12盒/箱', 'skuPrice': 51300}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24344486315, 'skuStock': 4, 'skuSpec': '1kg*12盒/箱', 'skuPrice': 34800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23995295655, 'skuStock': 14, 'skuSpec': '980g*12盒/箱', 'skuPrice': 28500}]\n", "Using proxy: **********************************************\n", "[{'skuId': 13506839883, 'skuStock': 6, 'skuSpec': '1L*12盒/箱', 'skuPrice': 36500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462722506, 'skuStock': 50, 'skuSpec': '1L*12盒/箱', 'skuPrice': 60300}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13462703063, 'skuStock': 283, 'skuSpec': '1L*12盒/箱', 'skuPrice': 58500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13883615035, 'skuStock': 2, 'skuSpec': '1kg*12盒/箱', 'skuPrice': 30500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13460701920, 'skuStock': 8, 'skuSpec': '1L*6盒/箱', 'skuPrice': 19100}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13456402593, 'skuStock': 69, 'skuSpec': '1L*12盒/箱', 'skuPrice': 47400}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24710883940, 'skuStock': 0, 'skuSpec': '980g*12盒/箱', 'skuPrice': 15000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24610793644, 'skuStock': 0, 'skuSpec': '980g*12盒/箱', 'skuPrice': 19800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24440783959, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 46700}]\n", "Using proxy: **********************************************\n", "[{'skuId': 13462639180, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 34000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13456402646, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 52200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14653113564, 'skuStock': 10, 'skuSpec': '980g*12盒/箱', 'skuPrice': 22000}]\n", "Using proxy: **********************************************\n", "[{'skuId': 13518611535, 'skuStock': 94, 'skuSpec': '907ml*12盒/箱', 'skuPrice': 24000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13462807675, 'skuStock': 14, 'skuSpec': '907g*12盒/箱', 'skuPrice': 27900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462741455, 'skuStock': 7, 'skuSpec': '907g*12盒/箱', 'skuPrice': 22000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462741190, 'skuStock': 5, 'skuSpec': '907G*12盒/箱', 'skuPrice': 24000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13883631280, 'skuStock': 30, 'skuSpec': '907g*12盒/箱', 'skuPrice': 21900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14188129636, 'skuStock': 0, 'skuSpec': '907克*12支/箱', 'skuPrice': 20500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13710326961, 'skuStock': 0, 'skuSpec': '907g*12盒/箱', 'skuPrice': 21000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23758898984, 'skuStock': 2, 'skuSpec': '1kg*1袋', 'skuPrice': 3000}, {'skuId': 23758898985, 'skuStock': 34, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 29500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13487536044, 'skuStock': 120, 'skuSpec': '1kg*1袋', 'skuPrice': 2600}, {'skuId': 13487536045, 'skuStock': 22, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 23500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13932803318, 'skuStock': 84, 'skuSpec': '1.1kg*1袋', 'skuPrice': 3300}, {'skuId': 13932803317, 'skuStock': 47, 'skuSpec': '1.1kg*8袋/箱', 'skuPrice': 26500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 13487528319, 'skuStock': 40, 'skuSpec': '1kg*1袋', 'skuPrice': 2400}, {'skuId': 13487528320, 'skuStock': 2, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 21500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13487520723, 'skuStock': 27, 'skuSpec': '2kg*1袋', 'skuPrice': 4200}, {'skuId': 13487520724, 'skuStock': 59, 'skuSpec': '2kg*4袋/箱', 'skuPrice': 15000}]\n", "Using proxy: **********************************************\n", "[{'skuId': 23758890014, 'skuStock': 10, 'skuSpec': '1kg*1袋', 'skuPrice': 3500}, {'skuId': 23758890015, 'skuStock': 61, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 35000}]\n", "Using proxy: **********************************************\n", "[{'skuId': 23758886087, 'skuStock': 22, 'skuSpec': '1kg*1袋', 'skuPrice': 3000}, {'skuId': 23758886088, 'skuStock': 1, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 31500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23758885177, 'skuStock': 10, 'skuSpec': '1kg*1袋', 'skuPrice': 3200}, {'skuId': 23758885178, 'skuStock': 0, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 31000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13487503244, 'skuStock': 10, 'skuSpec': '1kg*1袋', 'skuPrice': 3500}, {'skuId': 13487503245, 'skuStock': 16, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 34500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14182602152, 'skuStock': 6, 'skuSpec': '1kg/袋', 'skuPrice': 3700}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14173640979, 'skuStock': 1, 'skuSpec': '2kg*6袋/箱', 'skuPrice': 31000}, {'skuId': 24507985026, 'skuStock': 6, 'skuSpec': '2kg*1袋', 'skuPrice': 5200}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24027599864, 'skuStock': 1, 'skuSpec': '500g*24袋/箱', 'skuPrice': 57000}, {'skuId': 24027599865, 'skuStock': 6, 'skuSpec': '500g*1袋', 'skuPrice': 2400}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13738131216, 'skuStock': 1, 'skuSpec': '1.5kg*8袋/箱', 'skuPrice': 36500}, {'skuId': 13738131217, 'skuStock': 0, 'skuSpec': '1.5kg*1袋', 'skuPrice': 4600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24027579193, 'skuStock': 0, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 41500}, {'skuId': 24027579194, 'skuStock': 7, 'skuSpec': '1kg*1袋', 'skuPrice': 3500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25416092935, 'skuStock': 4, 'skuSpec': '130g/袋', 'skuPrice': 5700}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14614505865, 'skuStock': 2, 'skuSpec': '1kg*袋', 'skuPrice': 4800}]\n", "Using proxy: **********************************************\n", "[{'skuId': 24906290647, 'skuStock': 17, 'skuSpec': '1.26kg*袋', 'skuPrice': 5300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24239798106, 'skuStock': 69, 'skuSpec': '1kg*1袋', 'skuPrice': 4200}, {'skuId': 24239798107, 'skuStock': 6, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 50000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13495441306, 'skuStock': 2, 'skuSpec': '3kg*1桶', 'skuPrice': 4700}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13495433960, 'skuStock': 22, 'skuSpec': '3kg*1桶', 'skuPrice': 3390}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24906290647, 'skuStock': 17, 'skuSpec': '1.26kg*袋', 'skuPrice': 5300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13487528319, 'skuStock': 40, 'skuSpec': '1kg*1袋', 'skuPrice': 2400}, {'skuId': 13487528320, 'skuStock': 2, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 21500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23758890014, 'skuStock': 10, 'skuSpec': '1kg*1袋', 'skuPrice': 3500}, {'skuId': 23758890015, 'skuStock': 61, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 35000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 23758886087, 'skuStock': 22, 'skuSpec': '1kg*1袋', 'skuPrice': 3000}, {'skuId': 23758886088, 'skuStock': 1, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 31500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13487503244, 'skuStock': 10, 'skuSpec': '1kg*1袋', 'skuPrice': 3500}, {'skuId': 13487503245, 'skuStock': 16, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 34500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25452883297, 'skuStock': 13, 'skuSpec': '80g*8个*盒', 'skuPrice': 10300}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14652112980, 'skuStock': 12, 'skuSpec': '90g*8个*盒', 'skuPrice': 10300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25452797330, 'skuStock': 13, 'skuSpec': '85g*8个*盒', 'skuPrice': 10300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25452795061, 'skuStock': 31, 'skuSpec': '80g*5个*盒', 'skuPrice': 5200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14652115932, 'skuStock': 13, 'skuSpec': '90g*8个*盒', 'skuPrice': 10000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14650323468, 'skuStock': 49, 'skuSpec': '30个*3盒/箱', 'skuPrice': 14600}]\n", "Using proxy: **********************************************\n", "[{'skuId': 14650325559, 'skuStock': 45, 'skuSpec': '12个*4盒/箱', 'skuPrice': 13300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25449384576, 'skuStock': 295, 'skuSpec': '（85g*10个）*1组', 'skuPrice': 6000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25449378094, 'skuStock': 584, 'skuSpec': '450克*盒（6寸）', 'skuPrice': 3990}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 25449381215, 'skuStock': 10, 'skuSpec': '（200g*4寸*4盒）*1组', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14597806843, 'skuStock': 16, 'skuSpec': '100g*5片*盒', 'skuPrice': 5200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25344092468, 'skuStock': 15, 'skuSpec': '100g*5片*盒', 'skuPrice': 4600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14596718500, 'skuStock': 32, 'skuSpec': '90g*5片*盒', 'skuPrice': 5800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25343998885, 'skuStock': 13, 'skuSpec': '80g*8个*盒', 'skuPrice': 8900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25343977497, 'skuStock': 5, 'skuSpec': '125g*6个*盒', 'skuPrice': 8000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25204690102, 'skuStock': 9, 'skuSpec': '100g*10片/盒', 'skuPrice': 9500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14423900118, 'skuStock': 62, 'skuSpec': '200g/4寸/4盒', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14423906957, 'skuStock': 10, 'skuSpec': '（200g*4寸*4盒）*1组', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14387225698, 'skuStock': 18, 'skuSpec': '95g*5片/盒', 'skuPrice': 4500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24929879785, 'skuStock': 20, 'skuSpec': '95g*5片/盒', 'skuPrice': 4800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14358740049, 'skuStock': 5, 'skuSpec': '130g*9个/盒', 'skuPrice': 11500}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14358748233, 'skuStock': 8, 'skuSpec': '130g*9个/盒', 'skuPrice': 14300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24810285420, 'skuStock': 9, 'skuSpec': '40g*16个/盒', 'skuPrice': 11520}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24802788696, 'skuStock': 15, 'skuSpec': '100g*6个/盒', 'skuPrice': 8000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24802789627, 'skuStock': 19, 'skuSpec': '80g*9个/盒', 'skuPrice': 8800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24802782981, 'skuStock': 14, 'skuSpec': '95g*6个/盒', 'skuPrice': 7600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14322621259, 'skuStock': 16, 'skuSpec': '32g*16个/盒', 'skuPrice': 11520}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24802692477, 'skuStock': 17, 'skuSpec': '100g*6个/盒', 'skuPrice': 8000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24802691337, 'skuStock': 8, 'skuSpec': '110g*6个/盒', 'skuPrice': 8000}]\n", "Using proxy: **********************************************\n", "[{'skuId': 24802692709, 'skuStock': 6, 'skuSpec': '100g*6个/盒', 'skuPrice': 8000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14322600190, 'skuStock': 23, 'skuSpec': '100g*6个/盒', 'skuPrice': 8000}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14322606239, 'skuStock': 8, 'skuSpec': '40g*16个/盒', 'skuPrice': 11520}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24802688643, 'skuStock': 10, 'skuSpec': '40g*16个/盒', 'skuPrice': 11520}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24802690268, 'skuStock': 27, 'skuSpec': '95g*6个/盒', 'skuPrice': 7600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14322600882, 'skuStock': 14, 'skuSpec': '650g*16个/盒', 'skuPrice': 11520}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24802599579, 'skuStock': 22, 'skuSpec': '40g*16个/盒', 'skuPrice': 11520}]\n", "Using proxy: **********************************************\n", "[{'skuId': 24802598972, 'skuStock': 15, 'skuSpec': '70g*9个/盒', 'skuPrice': 12150}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24802594376, 'skuStock': 1, 'skuSpec': '40g*16个/盒', 'skuPrice': 11520}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14322542700, 'skuStock': 22, 'skuSpec': '36g*16个/盒', 'skuPrice': 11520}]\n", "Using proxy: **********************************************\n", "[{'skuId': 14322406359, 'skuStock': 14, 'skuSpec': '70g*9个/盒', 'skuPrice': 10300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24802279877, 'skuStock': 11, 'skuSpec': '80g*8个*盒', 'skuPrice': 8900}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14322347849, 'skuStock': 15, 'skuSpec': '75g*9个/盒', 'skuPrice': 12600}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 24801082091, 'skuStock': 5, 'skuSpec': '100g*6个/盒', 'skuPrice': 8000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14321736019, 'skuStock': 19, 'skuSpec': '640g*1盒/16个', 'skuPrice': 11520}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14321731354, 'skuStock': 4, 'skuSpec': '160g*8盒/组', 'skuPrice': 10400}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24800992306, 'skuStock': 3, 'skuSpec': '160g*8盒/组', 'skuPrice': 10400}]\n", "Using proxy: **********************************************\n", "[{'skuId': 14321731034, 'skuStock': 12, 'skuSpec': '640g*1盒/16个', 'skuPrice': 11520}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24757583367, 'skuStock': 16, 'skuSpec': '（200g*4寸*4盒）*1组', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14288446351, 'skuStock': 10, 'skuSpec': '75g*14片*1盒', 'skuPrice': 6500}]\n", "Using proxy: **********************************************\n", "[{'skuId': 14285445613, 'skuStock': 17, 'skuSpec': '8寸/10切/盒', 'skuPrice': 6500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24729675279, 'skuStock': 35, 'skuSpec': '8寸/10切/盒', 'skuPrice': 6500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14188646097, 'skuStock': 50, 'skuSpec': '（200g*4寸*4盒）*1组', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14188645244, 'skuStock': 4, 'skuSpec': '（200g*4寸*4盒）*1组', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14188649218, 'skuStock': 33, 'skuSpec': '（200g*4寸*4盒）*1组', 'skuPrice': 4800}]\n", "Using proxy: ***********************************************\n", "[{'skuId': 14185447730, 'skuStock': 0, 'skuSpec': '（100g*5罐）*1组', 'skuPrice': 4500}, {'skuId': 14185447731, 'skuStock': 2, 'skuSpec': '（100g*5罐）*1组', 'skuPrice': 4500}, {'skuId': 14185447732, 'skuStock': 4, 'skuSpec': '（100g*5罐）*1组', 'skuPrice': 4500}, {'skuId': 14185447733, 'skuStock': 0, 'skuSpec': '（100g*5罐）*1组', 'skuPrice': 4500}, {'skuId': 14185447734, 'skuStock': 7, 'skuSpec': '（100g*5罐）*1组', 'skuPrice': 4500}, {'skuId': 14185447735, 'skuStock': 0, 'skuSpec': '（100g*5罐）*1组', 'skuPrice': 4500}, {'skuId': 14185447736, 'skuStock': 5, 'skuSpec': '（100g*5罐）*1组', 'skuPrice': 5500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24250985366, 'skuStock': 81, 'skuSpec': '（200g*4寸*4盒）*1组', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n"]}, {"ename": "KeyError", "evalue": "'goodsData'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[9], line 57\u001b[0m\n\u001b[1;32m     55\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m product_details:\n\u001b[1;32m     56\u001b[0m     product[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mproduct_details\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m=\u001b[39mproduct_details[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdata\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m---> 57\u001b[0m     \u001b[43mparsed_sku_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mproduct\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[9], line 5\u001b[0m, in \u001b[0;36mparsed_sku_data\u001b[0;34m(product)\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mparsed_sku_data\u001b[39m(product):\n\u001b[0;32m----> 5\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[43mproduct\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mproduct_details\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mgoodsData\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mskuInfo\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m      7\u001b[0m     \u001b[38;5;66;03m# Extracting SKU information\u001b[39;00m\n\u001b[1;32m      8\u001b[0m     skus \u001b[38;5;241m=\u001b[39m data\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mskus\u001b[39m\u001b[38;5;124m'\u001b[39m, [])\n", "\u001b[0;31mKeyError\u001b[0m: 'goodsData'"]}], "source": ["import json\n", "\n", "# Parse the JSON data\n", "def parsed_sku_data(product):\n", "    data = product['product_details']['goodsData']['skuInfo']\n", "\n", "    # Extracting SKU information\n", "    skus = data.get('skus', [])\n", "    sku_prices = data.get('skuPrices', [])\n", "    sku_stocks = data.get('skuStocks', [])\n", "    sku_specs = {str(item['id']): item['name'] for prop in data.get('props', []) for item in prop.get('v', [])}\n", "\n", "    # Creating a list to store extracted SKU information\n", "    extracted_skus = []\n", "\n", "    # Iterating through each SKU entry\n", "    for sku in skus:\n", "        sku_id = sku.get('skuId')\n", "        sku_stock = next((stock['stockNum'] for stock in sku_stocks if stock['skuId'] == sku_id), None)\n", "        sku_spec_id = sku.get('s1')\n", "        sku_spec = sku_specs.get(sku_spec_id)\n", "        sku_price = next((price['price'] for price in sku_prices if price['skuId'] == sku_id), None)\n", "\n", "        # Creating a dictionary for each SKU\n", "        extracted_sku = {\n", "            'skuId': sku_id,\n", "            'skuStock': sku_stock,\n", "            'skuSpec': sku_spec,\n", "            'skuPrice': sku_price\n", "        }\n", "        extracted_skus.append(extracted_sku)\n", "\n", "    # Printing the extracted SKU information\n", "    if len(extracted_skus) <=0:\n", "        extracted_skus.append({\n", "            'skuId': product['alias'],\n", "            'skuStock': 10000,\n", "            'skuSpec': '无子SKU, 见标题',\n", "            'skuPrice': 0\n", "        })\n", "    print(extracted_skus)\n", "    product['sku_obj_list']=extracted_skus\n", "\n", "def get_product_detail(alias='2frx68v0712dq06'):\n", "    detail_url=f'https://h5.m.youzan.com/wscgoods/tee-app/detail.json?{app_and_kdt_id}&bizEnv=retail&mpVersion=3.113.15&alias={alias}&slg=tagGoodList-default%2COpBottom%2C11927391377%2CabTraceId&banner_id=f.103346086~tag_list_left.1~0~Y1qPtmOd&oid=0&scene=1089&ump_alias=&ump_type=&activityId=&activityType=&subKdtId=0&fullPresaleSupportCart=true&platform=weixin&client=weapp&isGoodsWeappNative=1&withoutSkuDirectOrder=1'\n", "    product_detail=get_remote_data_with_proxy(detail_url)\n", "    return json.loads(product_detail)\n", "\n", "\n", "for product in all_products:\n", "    if 'product_details' in product:\n", "        print(\"已经有'product_details'了\")\n", "        continue\n", "    product_details=get_product_detail(product['alias'])\n", "    if \"data\" in product_details:\n", "        product['product_details']=product_details[\"data\"]\n", "        parsed_sku_data(product)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 清洗商品数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_products_clean=[]\n", "for product in all_products:\n", "    for item_tag in ['tagsOpt', 'oPriceOpt', 'priceOpt', 'imgOpt', 'titleOpt', 'subTitleOpt', 'extraInfo', 'goodsPreloadOpt', 'actionOpt', 'extOpt']:\n", "        product[item_tag] = product['itemCardOpt'][item_tag]\n", "    clean_product={}\n", "    clean_product['title']=product['titleOpt']['title']\n", "    clean_product['类目名']=product['类目名']\n", "    clean_product['二级类目名']=product['二级类目名']\n", "    clean_product['price']=product['priceOpt']['price']\n", "        # print(product['oPriceOpt'],product['url'],product)\n", "    if 'price' in product['oPriceOpt']:\n", "        clean_product['origin_price']=product['oPriceOpt']['price']\n", "    else:\n", "        clean_product['origin_price']=\"NotFound\"\n", "    clean_product['img']=product['imgOpt']['src']\n", "    clean_product['url']=product['url']\n", "    clean_product['alias']=product['alias']\n", "    clean_product['id']=product['id']\n", "    clean_product['类目link']=product['类目link']\n", "    clean_product['skuInfo']=product['sku_obj_list']\n", "    sku_info=clean_product['skuInfo']\n", "    if len(sku_info) <=0:\n", "        sku_info=[{\n", "            \"skuId\": clean_product['alias'],\n", "            \"skuStock\": 10000,\n", "            \"skuSpec\": '无子SKU,见标题',\n", "            \"skuPrice\": clean_product['price']\n", "        }]\n", "    clean_product['skuInfo'] = sku_info\n", "    clean_product['数据获取时间']=time_of_now\n", "    all_products_clean.append(clean_product)\n", "\n", "df_cate_list=pd.DataFrame(all_products)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品列表-原始数据-{date_of_now}.csv', index=False)\n", "df_cate_list=pd.DataFrame(all_products_clean)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品列表-清洗后数据-{date_of_now}.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 将SKU List展开，以SKU维度进行存储和embedding"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_exploded = df_cate_list.explode('skuInfo')\n", "\n", "df_exploded['skuId'] = df_exploded['skuInfo'].apply(lambda x: x['skuId'])\n", "df_exploded['skuStock'] = df_exploded['skuInfo'].apply(lambda x: x['skuStock'])\n", "df_exploded['skuSpec'] = df_exploded['skuInfo'].apply(lambda x: x['skuSpec'])\n", "df_exploded['skuPrice'] = df_exploded['skuInfo'].apply(lambda x: x['skuPrice']/100.00)\n", "\n", "# Dropping the original 'skuInfo' column\n", "df_exploded.drop(columns=['skuInfo'], inplace=True)\n", "df_exploded.drop_duplicates(subset=['alias','skuId'], inplace=True)\n", "df_exploded[df_exploded['alias']=='2xecd73d11s5agv']\n", "df_exploded.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-{date_of_now}.csv', index=False)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就可以结束了，可以写入ODPS了"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_exploded['title_embedding']=df_exploded['title'].apply(getEmbeddingsFromAzure)\n", "df_exploded.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False)\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xianmu_path_to_csv='./data/鲜沐/xianmu_sku_with_name_and_price_with_embedding.csv'\n", "xianmu_df=pd.read_csv(xianmu_path_to_csv)\n", "area_name='杭州'\n", "xianmu_df=xianmu_df[xianmu_df['area_name']==area_name]\n", "xianmu_dairy_sku_list=['N001S01R005','N001S01R002','L001S01R001','N001H01Y003','Q001L01S001',\n", "                       '60553221161','2400337515','15103217314','15300532428','605874603137',\n", "                       '15178473420','1233884862','464633265','607188000000','D009H20T012']\n", "xianmu_order_category_sku_list=['15487486557','T001S01H001','3816076886','6151406635','78083116121','831273000000',\n", "                                '3803673132','15487540817','L001A01A001','300231','3834605103','5116666618','83065074266',\n", "                                '791251702788','802382247651','800878814020','827584118207','15418784088','1884053024','16331003164','100803']\n", "xianmu_fruit_sku_list=['6472','6416','16738467013','5432522553','5432522553','14042','555812873340','16788463466','5432522553','16788463466',\n", "                       '611184462','5432522553','145515','14042','555812873340','16788463466','17120703381','5432522406','17320871038','28482']\n", "combined_list = xianmu_dairy_sku_list + xianmu_order_category_sku_list + xianmu_fruit_sku_list\n", "xianmu_analytic_df=xianmu_df[xianmu_df['sku'].isin(combined_list)]\n", "\n", "xianmu_analytic_df[['sku','area_name','administrative_area','area_sku_price','sku_full_name_with_weight']].head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 基于Embedding的搜索匹配算法"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_cate_list=df_exploded.drop_duplicates(subset=['alias','skuId','数据获取时间'])\n", "print(df_cate_list.columns)\n", "print(xianmu_analytic_df.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "\n", "def dotProduct(a = [], b = []):\n", "    return sum(val * b[idx] for idx, val in enumerate(a))\n", "\n", "def magnitude(vector):\n", "    return math.sqrt(sum(val * val for val in vector))\n", "\n", "def cosine_similarity(a=[], b=[]):\n", "    if a is None or b is None:\n", "        return 0.0;\n", "    dotProductValue = dotProduct(a, b)\n", "    magnitudesA = magnitude(a)\n", "    magnitudesB = magnitude(b)\n", "\n", "    return dotProductValue / (magnitudesA * magnitudesB)\n", "\n", "\n", "def match_xianmu_to_this(embedding='[]', topK = 10, df_to_search = df_cate_list):\n", "    embedding=json.loads(embedding)\n", "    searching_index=0\n", "    embedding_scores=[]\n", "    for (index,row) in df_to_search.iterrows():\n", "        title_embedding = row['title_embedding']\n", "        if title_embedding is None:\n", "            print(f\"{row['title']} has no embedding.\")\n", "            continue\n", "        # print(f\"embedding:{len(embedding)}, topK:{topK}, title_embedding:{len(title_embedding)}\")\n", "        # print(embedding)\n", "        searching_index = searching_index+1\n", "        price = row['skuPrice']\n", "        if price<=0:\n", "            price=row['price']\n", "        embedding_scores.append({'sku':row['skuId'], \n", "                                'title':row['title']+\", \"+row['skuSpec'],\n", "                                '单位成本':row['单位成本'],\n", "                                '类目':f\"{row['类目名']}->{row['二级类目名']}\",\n", "                                'price':price,\n", "                                'url':row['url'],\n", "                                '数据获取时间':row['数据获取时间'],\n", "                                'img':row['img'],\n", "                                'score':cosine_similarity(embedding,title_embedding)})\n", "    embedding_scores.sort(key=lambda x: x['score'], reverse=True)\n", "    return embedding_scores[:topK]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 调用Anthropic接口进行水果单价计算"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT\n", "import os\n", "import socket\n", "\n", "def get_local_ip():\n", "    try:\n", "        # Create a socket object\n", "        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)\n", "        sock.connect((\"*******\", 80))  # Connecting to a known external server (Google's DNS server)\n", "\n", "        # Get the local IP address connected to the external server\n", "        local_ip = sock.getsockname()[0]\n", "        return local_ip\n", "    except socket.error as e:\n", "        return f\"Error: {e}\"\n", "\n", "# Get and print your local IP\n", "local_ip = get_local_ip()\n", "print(f\"Your local IP address is: {local_ip}\")\n", "\n", "\n", "ANTHROPIC_API_KEY=os.environ['ANTHROPIC_API_KEY']\n", "\n", "proxies=f'http://{local_ip}:8001'\n", "print(proxies)\n", "\n", "anthropic = Anthropic(\n", "    api_key=ANTHROPIC_API_KEY,\n", "    proxies=proxies\n", ")\n", "\n", "\n", "def call_claude_api_to_get_price_by_unit(product_name_with_price):\n", "    prompt=f\"\"\"你是一个聪明的采购商，在采购水果时，总是能够计算出每个商品报价中的单位价格，比如3.2斤橙子总价¥18，则单位成本为¥18/3.2=¥5.625(斤)。\n", "用户会给你一个商品的报价单，请你用JSON的格式输出该商品的单位价格。\n", "{HUMAN_PROMPT}请计算该商品的单位价格：16788463466AC, AC广东粗皮香水柠檬 净重5-5.1斤/一级/（大果）, ¥56.88\n", "{AI_PROMPT}{{\"商品ID\":\"16788463466AC\",\"商品名称\":\"AC广东粗皮香水柠檬 净重5-5.1斤/一级/（大果）\",\"商品规格\":\"净重5-5.1斤/一级\",\"单位成本\":\"11.376\",\"总价\":\"56.88\"}}\n", "{HUMAN_PROMPT}很好，很准确。请继续计算商品的价格：17120703381AB, 丹东红颜草莓ABC 净重3-3.3斤/一级/10g以上, ¥79.5\n", "{AI_PROMPT}{{\"商品ID\":\"17120703381AB\",\"商品名称\":\"丹东红颜草莓ABC 净重3-3.3斤/一级/10g以上\",\"商品规格\":\"净重3-3.3斤/一级/10g以上\",\"单位成本\":\"26.5\",\"总价\":\"79.5\"}}\n", "{HUMAN_PROMPT}很好，非常准确，你使用了最低净重，这很重要！请继续计算商品的价格：{product_name_with_price}\n", "{AI_PROMPT}\"\"\"\n", "    # print(prompt)\n", "    completion = anthropic.completions.create(\n", "        model=\"claude-2.1\",\n", "        max_tokens_to_sample=300,\n", "        prompt=prompt,\n", "        temperature=0.1,\n", "    )\n", "    # print(completion.completion)\n", "    return completion.completion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "def get_brand_unit_price(row):\n", "    if row['二级类目名'] !='水果':\n", "        return {'单位成本':row['price']}\n", "    prompt=f'{row[\"alias\"]}, {row[\"title\"]} {row[\"skuSpec\"]}, ¥{row[\"skuPrice\"]}'\n", "    unit_price=json.loads(call_claude_api_to_get_price_by_unit(prompt))\n", "    print(unit_price)\n", "    return unit_price\n", "\n", "def get_xianmu_unit_price(row):\n", "    if row['root_category'] !='新鲜水果':\n", "        return {'单位成本':row['area_sku_price']}\n", "    prompt=f'{row[\"sku\"]}, {row[\"sku_full_name_with_weight\"]}, ¥{row[\"area_sku_price\"]}'\n", "    unit_price=json.loads(call_claude_api_to_get_price_by_unit(prompt))\n", "    print(unit_price)\n", "    return unit_price\n", "# xianmu_analytic_df.head(1)\n", "xianmu_analytic_df['单位成本']=xianmu_analytic_df.apply(get_xianmu_unit_price, axis=1)\n", "# xianmu_analytic_df[xianmu_analytic_df['root_category']=='新鲜水果'][['单位成本','sku_full_name_with_weight','sku','area_sku_price']]\n", "\n", "# {'商品ID': '2frx68v0712dq06', '商品名称': '99红颜草莓丹东/江苏产地随机发 24颗/盒', '商品规格': '24颗/盒', '单位成本': '1.02', '总价': '24.50'}\n", "\n", "print(df_cate_list.columns)\n", "df_cate_list.groupby('二级类目名')['title'].count()\n", "df_cate_list['单位成本']=df_cate_list.apply(get_brand_unit_price, axis=1)\n", "df_cate_list[df_cate_list['二级类目名']=='水果'][['单位成本','title','alias','price']]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 转成HTML展示"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.core.display import HTML\n", "\n", "print(cosine_similarity([1,2,3],[4,5,6]))\n", "xianmu_analytic_df.loc[:, 'matched_top5'] = xianmu_analytic_df['sku_full_name_with_weight_embedding'].apply(match_xianmu_to_this, topK=5)\n", "\n", "# Assuming 'df' is your DataFrame\n", "css = \"\"\"\n", "<link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css\" integrity=\"sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\" crossorigin=\"anonymous\">\n", "<style type=\\\"text/css\\\">\n", "table {\n", "color: #333;\n", "font-family: unset;\n", "font-size: 12px;\n", "line-height: 1.5;\n", "width: 1024px;\n", "border-collapse:\n", "collapse; \n", "border-spacing: 0;\n", "}\n", "\n", "tr{\n", "border-bottom: 1px solid #C1C3D1;\n", "}\n", "\n", "tr:nth-child(even) {\n", "background-color: #F8F8F8;\n", "}\n", "\n", "td, th {\n", "border: 1px solid transparent; /* No more visible border */\n", "height: 30px;\n", "}\n", "\n", "th {\n", "background-color: #DFDFDF; /* Darken header a bit */\n", "font-weight: bolder;\n", "min-width: 100px;\n", "text-align: center;\n", "}\n", "\n", "td {\n", "background-color: #FAFAFA;\n", "text-align: center;\n", "}\n", "\n", "ol li{\n", "text-align: left;\n", "}\n", ".brand-container{\n", "display:flex;\n", "}\n", ".xianmu-container, .brand-sku{\n", "padding:0 10px;\n", "margin-right:5px\n", "border:solid 1px #efefef;\n", "}\n", ".xianmu-container{\n", "width:200px;\n", "}\n", ".brand-sku{\n", "width:180px;\n", "background-color:#fff;\n", "}\n", ".match-score{\n", "font-size:smaller;\n", "}\n", ".matched-very-well{\n", "color:lightgreen;\n", "}\n", ".higher-price{\n", "color:purple;\n", "}\n", ".lower-price{\n", "color:lightblue;\n", "font-weight:bolder;\n", "}\n", ".unit-price{color:red;}\n", "</style>\n", "\"\"\"\n", "\n", "def display_xianmu_html(row):\n", "    unit_price=row['单位成本']\n", "    if '商品规格' in unit_price:\n", "        unit_price='单价:¥'+unit_price['单位成本']+'<br>规格:'+unit_price['商品规格']\n", "        unit_price=f\"\"\"<span class='unit-price'>{unit_price}</span><br>\"\"\"\n", "    else:\n", "        unit_price=\"\"\n", "    img = row[\"img\"]\n", "    content= f\"\"\"<div class=\"xianmu-container\"><img width=\"80\" src=\"{img}\"><br>售价:¥{row[\"area_sku_price\"]}, 平均成本价:¥{row['avg_cost']}<br>{unit_price}<span>{row['sku']}, {row[\"sku_full_name_with_weight\"]}</span></div>\"\"\"\n", "    return content\n", "\n", "# Assuming 'df' is your DataFrame and 'url' and 'title' are your columns\n", "def display_brand_html(row_of_df):\n", "    # {'商品ID': '2frx68v0712dq06', '商品名称': '99红颜草莓丹东/江苏产地随机发 24颗/盒', '商品规格': '24颗/盒', '单位成本': '1.02', '总价': '24.50'}\n", "    matched_top=row_of_df['matched_top5']\n", "    content_list=[]\n", "    is_top_one=True\n", "    for row in matched_top:\n", "        unit_price=row['单位成本']\n", "        if '商品规格' in unit_price:\n", "            unit_price='单价:¥'+unit_price['单位成本']+'<br>规格:'+unit_price['商品规格']\n", "            unit_price=f\"\"\"<span class='unit-price'>{unit_price}</span><br>\"\"\"\n", "        else:\n", "            unit_price=\"\"\n", "        price_diff=row_of_df['area_sku_price']-float(row[\"price\"])\n", "        is_higher_price_class=\"higher-price\"\n", "        if price_diff<=0:\n", "            is_higher_price_class=\"lower-price\"\n", "        is_top_one=False\n", "        score_class=\"match-score\"\n", "        if row['score']>0.9:\n", "            score_class=\"match-score matched-very-well\"\n", "        img = row[\"img\"]\n", "        content= f\"\"\"<div class=\"brand-sku\"><img width=\"80\" src=\"{img}\"><br><span class=\"{is_higher_price_class}\">¥{row[\"price\"]}, 差价:¥{price_diff}</span><br>{unit_price}<span class=\"{score_class}\">匹配分数:{round(row['score'], 4)}</span><br/><a target=\"_blank\" href=\"{row[\"url\"]}\">{row[\"title\"]}</a><br>ID:{row[\"sku\"]}</div>\"\"\"\n", "        content_list.append(content)\n", "    content_list=\"\".join(content_list)\n", "    return f'<div class=\"brand-container\">{content_list}</div>'\n", "\n", "\n", "xianmu_sku_column_name=f'鲜沐SKU:{area_name}'\n", "xianmu_analytic_df[xianmu_sku_column_name]=xianmu_analytic_df.apply(display_xianmu_html, axis=1)\n", "brand_column_name=f'{brand_name}SKU:{time_of_now}'\n", "xianmu_analytic_df[brand_column_name]=xianmu_analytic_df.apply(display_brand_html, axis=1)\n", "xianmu_analytic_df=xianmu_analytic_df.sort_values(['root_category','sku_full_name'])\n", "\n", "html_content=css+xianmu_analytic_df[[xianmu_sku_column_name,brand_column_name]].to_html(escape=False, index=False, classes='table dataframe')\n", "html_content=f'<html><head><meta charset=\"UTF-8\"><meta name=\"title\" content=\"{brand_name}和鲜沐的比较-{date_of_now}\"></head><body>{html_content}</body></html>'\n", "display(HTML(html_content))\n", "\n", "# 保存HTML到本地文件：\n", "with open(f'./data/{brand_name}/{brand_name}和鲜沐的比较-{date_of_now}.html', 'w') as f:\n", "    f.write(html_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取过去7天的DF，进行价格变动监控"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "# Calculate the date 7 days ago\n", "seven_days_ago = datetime.now() - <PERSON><PERSON><PERSON>(days=7)\n", "\n", "# Create a set to store date strings\n", "date_set = []\n", "\n", "# Generate date strings starting from 7 days ago until today\n", "current_date = datetime.now()\n", "while seven_days_ago <= current_date:\n", "    date_string = current_date.strftime(\"%Y-%m-%d\")  # Format date as string\n", "    date_set.append(date_string)\n", "    current_date -= <PERSON><PERSON><PERSON>(days=1)  # Move to the previous day\n", "\n", "print(date_set)\n", "\n", "all_brand_df=[]\n", "for date in date_set:\n", "    file = f'./data/{brand_name}/{brand_name}-商品列表-清洗后数据-{date}.csv'\n", "    if os.path.isfile(file):\n", "        all_brand_df.append(pd.read_csv(file))\n", "if len(all_brand_df) > 0:\n", "    all_brand_df=pd.concat(all_brand_df)\n", "\n", "aliases_with_multiple_prices = pandasql.sqldf(\"select alias,count(distinct price) as cnt from all_brand_df group by alias having cnt>1\")\n", "\n", "has_price_changed_df=all_brand_df[all_brand_df['alias'].isin(aliases_with_multiple_prices['alias'].unique())][['alias','title','二级类目名','price','img','url','数据获取时间']]\n", "has_price_changed_df.sort_values(['alias','数据获取时间'], inplace=True)\n", "deduplicated_df = has_price_changed_df.drop_duplicates(subset=['alias','数据获取时间'])\n", "deduplicated_df.to_csv(f'./data/{brand_name}/{brand_name}-价格有变动的商品列表-{date_of_now}.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
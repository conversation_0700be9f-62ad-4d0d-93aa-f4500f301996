{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "*************, headers:{'STOREID': '3821371'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "THREAD_CNT = int(os.environ.get('THREAD_CNT', 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={'STOREID':'3821371',}\n", "brand_name='张业食品'\n", "competitor_name_en='zhang<PERSON>hipin'\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1597040233672712523 797673 面粉类\n", "\n", "1597041880147263124 797679 金味、金鹂油脂\n", "1597041942282843894 797680 糖类\n", "1597041990841463811 797681 辅料系列\n", "1604207038567427787 838368 奶油/淡奶油类\n", "1604207454171135505 838372 维朗系列\n", "1604207514595667727 838376 馅料类\n", "1604208717640366036 838385 冷冻半成品\n", "1604208789603633192 838388 美果树系列\n", "1604209026609355487 838393 沙拉酱系列\n", "1604219358455943195 838415 银豹\n", "1604299355358859039 838620 肉松系列\n", "1634613349159167160 1043625 棒师傅系列\n", "1634613447157966345 1043626 冷冻肉制品\n", "1634613462563426391 1043627 干果类\n", "1634613493453502407 1043631 巧克力类\n", "1634613831047109621 1043633 宝笙系列\n", "1634613840578752857 1043634 妙利系列\n", "1634613851391933491 1043635 新意系列\n", "1634613865177149007 1043636 焙乐道系列\n", "1634613873141305185 1043637 焙之玺系列\n", "1634613948750308849 1043651 科麦系列\n", "1634621993328472055 1043744 早苗系列\n", "\n", "1635227090813828399 1046734 芝士系列\n", "1637714672985136291 1066108 布丁、饼干类\n", "1646467050298565248 1087172 蓝彪系列\n", "1646474539896039488 1087214 玉米粒\n", "1646731836307181340 1090801 奥昆系列\n", "1676685392846137065 1223710 伯乐滋系列\n", "1676692014595241769 1223738 马卡龙系列\n", "1676968276471810557 1224342 贝一系列\n", "1677056727127797968 1224689 安佳系列\n", "1677056977080531577 1224690 妙可蓝多系列\n", "1677057124314651608 1224691 丘比系列\n", "1677056586674609237 1224692 南桥油脂\n", "1677057800439617199 1224693 阿黛尔系列\n", "1677057893392838202 1224694 炼奶系列\n", "1677057987611807503 1224696 味斯美系列\n", "1677058497080127615 1224699 速冻系列\n", "1677062860783749092 1224703 奶粉/牛奶类\n", "1677063065924916098 1224704 其他油脂类\n", "1677063810799306786 1224706 京日系列\n", "1677063959503150147 1224707 立高系列\n", "1677807176220646223 1226501 包装类\n", "1679050406205929192 1231321 雀巢系列\n", "1679110666883419256 1231420 雪媚娘皮类\n", "1679310691910146636 1231638 仟菓匠\n", "1679387389889307205 1231808 双其乐\n", "1679459355929892386 1231875 体验店商品\n", "1680836896910435498 1233994 和福大福系列\n", "1683101112093309047 1241386 荷美尔\n", "1690592098252681756 1257856 大成系列\n", "1690592215548298556 1257857 南顺面粉\n", "1690592276595681689 1257858 海融系列\n", "1690592297426616558 1257859 雷之音系列\n", "1690592327788472027 1257860 麦维客系列\n", "1690592352490781708 1257861 势道系列\n"]}], "source": ["body={\n", "    \"storeId\": 3821371,\n", "    \"includeAttributes\": <PERSON><PERSON><PERSON>,\n", "    \"includeAllProducts\": False,\n", "    \"includeHotSale\": True,\n", "    \"includeCombo\": True,\n", "    \"isMultiCategory\": True,\n", "    \"multiLevel\": \"\",\n", "    \"includePresale\": True,\n", "    \"includeGroupSale\": True,\n", "    \"includeBargainSale\": True,\n", "    \"includeSeckillSale\": True,\n", "    \"includePeriod\": True,\n", "    \"includeNew\": True,\n", "    \"isRefresh\": True,\n", "    \"v\": 2,\n", "    \"hideProductByMode\": True\n", "}\n", "\n", "url='https://wxservice-stg.pospal.cn/wxapi/product/categories'\n", "categories=requests.post(url,headers=headers, json=body).json()['categories']\n", "for category in categories:\n", "    print(category['CategoryUid'], category['CategoryId'],category['DisplayName'])\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1597040233672712523 797673 面粉类\n", "\n", "面粉类\n", " 商品个数:18\n", "1597041880147263124 797679 金味、金鹂油脂\n", "金味、金鹂油脂 商品个数:11\n", "1597041942282843894 797680 糖类\n", "糖类 商品个数:3\n", "1597041990841463811 797681 辅料系列\n", "辅料系列 商品个数:26\n", "1604207038567427787 838368 奶油/淡奶油类\n", "奶油/淡奶油类 商品个数:10\n", "1604207454171135505 838372 维朗系列\n", "维朗系列 商品个数:48\n", "1604207514595667727 838376 馅料类\n", "馅料类 商品个数:7\n", "1604208717640366036 838385 冷冻半成品\n", "冷冻半成品 商品个数:2\n", "1604208789603633192 838388 美果树系列\n", "美果树系列 商品个数:27\n", "1604209026609355487 838393 沙拉酱系列\n", "沙拉酱系列 商品个数:7\n", "1604219358455943195 838415 银豹\n", "银豹 商品个数:1\n", "1604299355358859039 838620 肉松系列\n", "肉松系列 商品个数:3\n", "1634613349159167160 1043625 棒师傅系列\n", "棒师傅系列 商品个数:5\n", "1634613447157966345 1043626 冷冻肉制品\n", "冷冻肉制品 商品个数:1\n", "1634613462563426391 1043627 干果类\n", "干果类 商品个数:6\n", "1634613493453502407 1043631 巧克力类\n", "巧克力类 商品个数:9\n", "1634613831047109621 1043633 宝笙系列\n", "宝笙系列 商品个数:1\n", "1634613840578752857 1043634 妙利系列\n", "妙利系列 商品个数:1\n", "1634613851391933491 1043635 新意系列\n", "新意系列 商品个数:3\n", "1634613865177149007 1043636 焙乐道系列\n", "焙乐道系列 商品个数:6\n", "1634613873141305185 1043637 焙之玺系列\n", "焙之玺系列 商品个数:3\n", "1634613948750308849 1043651 科麦系列\n", "科麦系列 商品个数:3\n", "1634621993328472055 1043744 早苗系列\n", "\n", "早苗系列\n", " 商品个数:4\n", "1635227090813828399 1046734 芝士系列\n", "芝士系列 商品个数:2\n", "1637714672985136291 1066108 布丁、饼干类\n", "布丁、饼干类 商品个数:0\n", "1646467050298565248 1087172 蓝彪系列\n", "蓝彪系列 商品个数:12\n", "1646474539896039488 1087214 玉米粒\n", "玉米粒 商品个数:1\n", "1646731836307181340 1090801 奥昆系列\n", "奥昆系列 商品个数:10\n", "1676685392846137065 1223710 伯乐滋系列\n", "伯乐滋系列 商品个数:9\n", "1676692014595241769 1223738 马卡龙系列\n", "马卡龙系列 商品个数:3\n", "1676968276471810557 1224342 贝一系列\n", "贝一系列 商品个数:14\n", "1677056727127797968 1224689 安佳系列\n", "安佳系列 商品个数:4\n", "1677056977080531577 1224690 妙可蓝多系列\n", "妙可蓝多系列 商品个数:6\n", "1677057124314651608 1224691 丘比系列\n", "丘比系列 商品个数:3\n", "1677056586674609237 1224692 南桥油脂\n", "南桥油脂 商品个数:9\n", "1677057800439617199 1224693 阿黛尔系列\n", "阿黛尔系列 商品个数:8\n", "1677057893392838202 1224694 炼奶系列\n", "炼奶系列 商品个数:4\n", "1677057987611807503 1224696 味斯美系列\n", "味斯美系列 商品个数:16\n", "1677058497080127615 1224699 速冻系列\n", "速冻系列 商品个数:2\n", "1677062860783749092 1224703 奶粉/牛奶类\n", "奶粉/牛奶类 商品个数:2\n", "1677063065924916098 1224704 其他油脂类\n", "其他油脂类 商品个数:6\n", "1677063810799306786 1224706 京日系列\n", "京日系列 商品个数:3\n", "1677063959503150147 1224707 立高系列\n", "立高系列 商品个数:10\n", "1677807176220646223 1226501 包装类\n", "包装类 商品个数:2\n", "1679050406205929192 1231321 雀巢系列\n", "雀巢系列 商品个数:5\n", "1679110666883419256 1231420 雪媚娘皮类\n", "雪媚娘皮类 商品个数:4\n", "1679310691910146636 1231638 仟菓匠\n", "仟菓匠 商品个数:6\n", "1679387389889307205 1231808 双其乐\n", "双其乐 商品个数:60\n", "1679459355929892386 1231875 体验店商品\n", "体验店商品 商品个数:0\n", "1680836896910435498 1233994 和福大福系列\n", "和福大福系列 商品个数:0\n", "1683101112093309047 1241386 荷美尔\n", "荷美尔 商品个数:10\n", "1690592098252681756 1257856 大成系列\n", "大成系列 商品个数:34\n", "1690592215548298556 1257857 南顺面粉\n", "南顺面粉 商品个数:4\n", "1690592276595681689 1257858 海融系列\n", "海融系列 商品个数:12\n", "1690592297426616558 1257859 雷之音系列\n", "雷之音系列 商品个数:7\n", "1690592327788472027 1257860 麦维客系列\n", "麦维客系列 商品个数:11\n", "1690592352490781708 1257861 势道系列\n", "势道系列 商品个数:3\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>isPromotionProduct</th>\n", "      <th>name</th>\n", "      <th>defaultproductimage</th>\n", "      <th>category</th>\n", "      <th>sellPrice</th>\n", "      <th>sellPrice2</th>\n", "      <th>buyPrice</th>\n", "      <th>stock</th>\n", "      <th>id</th>\n", "      <th>uid</th>\n", "      <th>...</th>\n", "      <th>weightUnit</th>\n", "      <th>virtualStock</th>\n", "      <th>crossBorderProduct</th>\n", "      <th>allowExpress</th>\n", "      <th>attribute5</th>\n", "      <th>attribute7</th>\n", "      <th>brandName</th>\n", "      <th>serviceTime</th>\n", "      <th>productSeries</th>\n", "      <th>specProductOrder</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>编映山红高筋粉25kg/包</td>\n", "      <td>{'id': '11054149', 'imagepath': 'https://img.p...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>107.00</td>\n", "      <td>107.00</td>\n", "      <td>0.0</td>\n", "      <td>27.0</td>\n", "      <td>34607883</td>\n", "      <td>514008276044876898</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>False</td>\n", "      <td>炜源糕粉30kg/包</td>\n", "      <td>{'id': '6642639', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>355.00</td>\n", "      <td>355.00</td>\n", "      <td>0.0</td>\n", "      <td>300.0</td>\n", "      <td>34608061</td>\n", "      <td>497181791760173834</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>False</td>\n", "      <td>三象糯粉500g*20包/箱</td>\n", "      <td>{'id': '8249818', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>140.00</td>\n", "      <td>140.00</td>\n", "      <td>0.0</td>\n", "      <td>300.0</td>\n", "      <td>34608202</td>\n", "      <td>342260936090990116</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>False</td>\n", "      <td>纸袋蓝金山面包粉25kg/包</td>\n", "      <td>{'id': '6685977', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>140.00</td>\n", "      <td>140.00</td>\n", "      <td>0.0</td>\n", "      <td>276.0</td>\n", "      <td>34943228</td>\n", "      <td>694771248476873828</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>益海嘉里</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>False</td>\n", "      <td>纸袋花鼓蛋糕专用粉25kg/包</td>\n", "      <td>{'id': '8223564', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>120.00</td>\n", "      <td>120.00</td>\n", "      <td>0.0</td>\n", "      <td>283.0</td>\n", "      <td>34943242</td>\n", "      <td>1131338457855239687</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>益海嘉里</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>False</td>\n", "      <td>王后低筋粉25kg/包</td>\n", "      <td>{'id': '8914979', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>155.00</td>\n", "      <td>155.00</td>\n", "      <td>0.0</td>\n", "      <td>40.0</td>\n", "      <td>43627180</td>\n", "      <td>1124106227545490496</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>False</td>\n", "      <td>王后高筋面粉25kg/包</td>\n", "      <td>{'id': '8915017', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>164.00</td>\n", "      <td>164.00</td>\n", "      <td>0.0</td>\n", "      <td>88.0</td>\n", "      <td>43628028</td>\n", "      <td>422911358720106330</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>False</td>\n", "      <td>编织袋中粮玉米淀粉25kg/包</td>\n", "      <td>{'id': '8940032', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>105.00</td>\n", "      <td>105.00</td>\n", "      <td>0.0</td>\n", "      <td>20.0</td>\n", "      <td>43632655</td>\n", "      <td>830855309250408659</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>False</td>\n", "      <td>柳叶梅全麦粉5kg/包</td>\n", "      <td>{'id': '8962686', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>60.00</td>\n", "      <td>60.00</td>\n", "      <td>0.0</td>\n", "      <td>20.0</td>\n", "      <td>43632669</td>\n", "      <td>978476689082620055</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>False</td>\n", "      <td>测试</td>\n", "      <td>{'id': '0', 'imagepath': 'https://img.pospal.c...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>0.01</td>\n", "      <td>0.01</td>\n", "      <td>0.0</td>\n", "      <td>-1.0</td>\n", "      <td>45824354</td>\n", "      <td>1089227030782993184</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 51 columns</p>\n", "</div>"], "text/plain": ["   isPromotionProduct             name  \\\n", "0               False    编映山红高筋粉25kg/包   \n", "1               False       炜源糕粉30kg/包   \n", "2               False   三象糯粉500g*20包/箱   \n", "3               False   纸袋蓝金山面包粉25kg/包   \n", "4               False  纸袋花鼓蛋糕专用粉25kg/包   \n", "5               False      王后低筋粉25kg/包   \n", "6               False     王后高筋面粉25kg/包   \n", "7               False  编织袋中粮玉米淀粉25kg/包   \n", "8               False      柳叶梅全麦粉5kg/包   \n", "9               False               测试   \n", "\n", "                                 defaultproductimage  \\\n", "0  {'id': '11054149', 'imagepath': 'https://img.p...   \n", "1  {'id': '6642639', 'imagepath': 'https://img.po...   \n", "2  {'id': '8249818', 'imagepath': 'https://img.po...   \n", "3  {'id': '6685977', 'imagepath': 'https://img.po...   \n", "4  {'id': '8223564', 'imagepath': 'https://img.po...   \n", "5  {'id': '8914979', 'imagepath': 'https://img.po...   \n", "6  {'id': '8915017', 'imagepath': 'https://img.po...   \n", "7  {'id': '8940032', 'imagepath': 'https://img.po...   \n", "8  {'id': '8962686', 'imagepath': 'https://img.po...   \n", "9  {'id': '0', 'imagepath': 'https://img.pospal.c...   \n", "\n", "                                            category  sellPrice  sellPrice2  \\\n", "0  {'id': '797673', 'uid': '1597040233672712523',...     107.00      107.00   \n", "1  {'id': '797673', 'uid': '1597040233672712523',...     355.00      355.00   \n", "2  {'id': '797673', 'uid': '1597040233672712523',...     140.00      140.00   \n", "3  {'id': '797673', 'uid': '1597040233672712523',...     140.00      140.00   \n", "4  {'id': '797673', 'uid': '1597040233672712523',...     120.00      120.00   \n", "5  {'id': '797673', 'uid': '1597040233672712523',...     155.00      155.00   \n", "6  {'id': '797673', 'uid': '1597040233672712523',...     164.00      164.00   \n", "7  {'id': '797673', 'uid': '1597040233672712523',...     105.00      105.00   \n", "8  {'id': '797673', 'uid': '1597040233672712523',...      60.00       60.00   \n", "9  {'id': '797673', 'uid': '1597040233672712523',...       0.01        0.01   \n", "\n", "   buyPrice  stock        id                  uid  ...  weightUnit  \\\n", "0       0.0   27.0  34607883   514008276044876898  ...         1.0   \n", "1       0.0  300.0  34608061   497181791760173834  ...         NaN   \n", "2       0.0  300.0  34608202   342260936090990116  ...         NaN   \n", "3       0.0  276.0  34943228   694771248476873828  ...         1.0   \n", "4       0.0  283.0  34943242  1131338457855239687  ...         NaN   \n", "5       0.0   40.0  43627180  1124106227545490496  ...         1.0   \n", "6       0.0   88.0  43628028   422911358720106330  ...         1.0   \n", "7       0.0   20.0  43632655   830855309250408659  ...         1.0   \n", "8       0.0   20.0  43632669   978476689082620055  ...         1.0   \n", "9       0.0   -1.0  45824354  1089227030782993184  ...         1.0   \n", "\n", "  virtualStock crossBorderProduct  allowExpress attribute5  attribute7  \\\n", "0          NaN                NaN           NaN        NaN         NaN   \n", "1          NaN                NaN           NaN        NaN         NaN   \n", "2          0.0                0.0           1.0        NaN         NaN   \n", "3          NaN                NaN           NaN                          \n", "4          NaN                NaN           NaN        NaN         NaN   \n", "5          NaN                NaN           NaN                          \n", "6          0.0                0.0           1.0        NaN         NaN   \n", "7          NaN                NaN           NaN        NaN         NaN   \n", "8          NaN                NaN           NaN                          \n", "9          NaN                NaN           NaN        NaN         NaN   \n", "\n", "  brandName serviceTime productSeries specProductOrder  \n", "0       NaN         NaN           NaN              NaN  \n", "1       NaN         NaN           NaN              NaN  \n", "2       NaN         NaN           NaN              NaN  \n", "3      益海嘉里         0.0           NaN              NaN  \n", "4      益海嘉里         NaN           NaN              NaN  \n", "5       NaN         0.0           NaN              NaN  \n", "6       NaN         NaN           NaN              NaN  \n", "7       NaN         NaN           NaN              NaN  \n", "8       NaN         0.0           NaN              NaN  \n", "9       NaN         NaN           NaN              NaN  \n", "\n", "[10 rows x 51 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["p_body={\n", "    \"storeId\": 3821371,\n", "    \"tags\": \"\",\n", "    \"brandUid\": \"\",\n", "    \"key\": \"\",\n", "    \"cUids\": \"1597041880147263124\",\n", "    \"pageIdx\": 0,\n", "    \"size\": 60,\n", "    \"includeTag\": True,\n", "     \"includeFuncTag\": True,\n", "    \"ignoreCart\": True,\n", "    \"isSeries\": True,\n", "    \"pUids\": \"\",\n", "    \"orderType\": 0,\n", "    \"minprice\": \"\",\n", "    \"maxprice\": \"\",\n", "    \"times\": <PERSON><PERSON><PERSON>,\n", "    \"includeSale\": 0,\n", "    \"checkCatSaleTime\": True,\n", "    \"hideProductByMode\": True,\n", "    \"nextCustomerCategoryUid\": \"10000\"\n", "}\n", "\n", "\n", "product_list_all=[]\n", "for category in categories:\n", "    CategoryUid=category['CategoryUid']\n", "    print(category['CategoryUid'], category['CategoryId'],category['DisplayName'])\n", "    p_body['cUids']=CategoryUid\n", "    url='https://wxservice-stg.pospal.cn/wxapi/product/listmulti'\n", "    products=requests.post(url, headers=headers, json=p_body).json()['data']\n", "    product_list_all.extend(products)\n", "    print(f\"{category['DisplayName']} 商品个数:{len(products)}\")\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "***************:34323\n", "11***********:30299\n", "**************:41156\n", "************:36523\n", "**************:49551\n", "*************:48925\n", "***************:35457\n", "*************:36621\n", "************:33452\n", "*************:38487\n", "['***************:34323', '11***********:30299', '**************:41156', '************:36523', '**************:49551', '*************:48925', '***************:35457', '*************:36621', '************:33452', '*************:38487']\n", "成功写入odps:summerfarm_ds.spider_zhangyeshipin_product_result_df, partition_spec:ds=20240222,competitor_name=zhangyeshipin, attemp:0\n", "sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_zhangyeshipin_product_result_df\n", "                             where ds>='20240123' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240222</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>487</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ds competitor_name  recods\n", "0  20240222   <PERSON>hang<PERSON><PERSON>in     487"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "product_list_all_df['competitor']=brand_name\n", "all_products_df=product_list_all_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 写入odps\n", "import requests\n", "import json\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import os\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json,logging\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"content-type\": \"application/x-www-form-urlencoded\",\n", "    \"Connection\": \"keep-alive\",\n", "    \"Referer\": \"https://servicewechat.com/wx467c5c95f5a7d792/1/page-frame.html\",\n", "    \"X-Token\": \"aHR0cHM6Ly93d3cuempoZWppYW5nLmNvbRNWGREfVnBGE1VNH09SWVFGSwIcAk8XCgk3FQBSSQsYHEVGQB1CUl5JSBwSCxVxCgtPUkxGR0oDFxYK\",\n", "    \"X-App-Version\": \"5.11.9\",\n", "    \"Accept-Encoding\": \"gzip,compress,br,deflate\",\n", "    \"Mch-Access-Token\": \"[object Undefined]\",\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 18_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.53(0x18003531) NetType/4G Language/zh_CN\",\n", "    \"X-Access-Token\": \"zYvLz6QYbtLqrW23BFgVA5bRqSlKBX_H\",\n", "    \"X-Form-Id-List\": '[{\"value\":\"requestFormId:fail deprecated\",\"type\":0,\"remains\":1,\"expires_at\":\"2024-11-28 15:23:47\"}]',\n", "    \"X-App-Platform\": \"wxapp\",\n", "    \"Host\": \"www.mosyy.com\",\n", "    \"X-Requested-With\": \"XMLHttpRequest\",\n", "}\n", "brand_name = \"焙小菓\"\n", "competitor_name_en = \"beixia<PERSON><PERSON>\"\n", "\n", "logging.info(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["category_list_url = \"https://www.mosyy.com/web/index.php?_mall_id=15814&r=api/cat/list&cat_id=&select_cat_id=\"\n", "category_list = (\n", "    get_remote_data_with_proxy_json(url=category_list_url, headers=headers)\n", "    .get(\"data\", {})\n", "    .get(\"list\", [])\n", ")\n", "logging.info(f\"category_list:{category_list}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_products_of_category(id=362258, currentPage=1):\n", "    url = f\"https://www.mosyy.com/web/index.php?_mall_id=15814&r=api/default/goods-list&page={currentPage}&cat_id={id}\"\n", "    products = (\n", "        get_remote_data_with_proxy_json(url=url, headers=headers)\n", "        .get(\"data\", {})\n", "        .get(\"list\", [])\n", "    )\n", "    if products is None or len(products) <= 0:\n", "        return []\n", "    elif len(products) < 10:\n", "        return products\n", "    # 递归查询\n", "    products.extend(get_products_of_category(id, currentPage + 1))\n", "    return products\n", "\n", "\n", "print(get_products_of_category())\n", "\n", "all_products = []\n", "for cate in category_list:\n", "    currentPage = 1\n", "    sub_list = get_products_of_category(cate[\"id\"], currentPage=currentPage)\n", "    if sub_list is None:\n", "        continue\n", "    if len(sub_list) > 0:\n", "        for product in sub_list:\n", "            product[\"category_name\"] = cate[\"name\"]\n", "        all_products.extend(sub_list)\n", "\n", "\n", "all_products_df = pd.DataFrame(all_products)\n", "all_products_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logging.info(f\"爬取了：{len(all_products_df)}个商品。\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "all_products_df['competitor']=brand_name\n", "all_products_df=all_products_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name  order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}
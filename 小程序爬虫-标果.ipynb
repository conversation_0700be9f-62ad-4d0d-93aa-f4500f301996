{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import os\n", "from scripts.proxy_setup import (\n", "    get_remote_data_with_proxy_json,\n", "    write_pandas_df_into_odps,\n", "    get_odps_sql_result_as_df,\n", "    THREAD_CNT,\n", ")\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID = os.environ[\"ALIBABA_CLOUD_ACCESS_KEY_ID\"]\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET = os.environ[\"ALIBABA_CLOUD_ACCESS_KEY_SECRET\"]\n", "THREAD_CNT = int(os.environ.get(\"THREAD_CNT\", 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project=\"summerfarm_ds_dev\",\n", "    endpoint=\"http://service.cn-hangzhou.maxcompute.aliyun.com/api\",\n", ")\n", "\n", "hints = {\"odps.sql.hive.compatible\": True, \"odps.sql.type.system.odps2\": True}\n", "\n", "\n", "def get_odps_sql_result_as_df(sql):\n", "    instance = odps.execute_sql(sql, hints=hints)\n", "    instance.wait_for_success()\n", "    pd_df = None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "# 线上：\n", "# 'token':'d962d0ba06514ffa8b80a335d851563f',\n", "# 'sid':'7731297',\n", "\n", "headers = {\n", "    \"token\": \"5b0c07e2078e46d4b92423e9d0eca1b2\",\n", "    \"sid\": \"8200918\",\n", "    \"time\": \"1702521175012\",\n", "}\n", "brand_name = \"标果-长沙\"\n", "competitor_name_en = \"biaoguo\"\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取门店信息：\n", "user_url='https://api.guohe.biaoguoworks.com/users/api/h5/user/info'\n", "user_url='https://demeter-api.biaoguoworks.com/users/api/h5/user/info'\n", "user=get_remote_data_with_proxy_json(url=user_url, headers={\n", "            \"token\": \"d962d0ba06514ffa8b80a335d851563f\",\n", "            \"sid\": \"7731297\",\n", "            \"referer\": \"https://servicewechat.com/wx6fffdc1a67a2acb6/156/page-frame.html\",\n", "            \"time\": \"1702521175012\",\n", "        }, json={})\n", "user\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url=\"https://demeter-api.biaoguoworks.com/demeter/api/h5/store/goods/goods-prop?goodsId=546750&goodsPropBaseId=4\"\n", "\n", "goods_prop=requests.post(url=url, verify=False).json()\n", "goods_prop"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["list = [\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果耗材_保鲜膜\",\n", "        \"id\": 7566,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果耗材_托盒\",\n", "        \"id\": 7565,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果耗材_更多耗材.\",\n", "        \"id\": 4651,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果耗材_刀具工具.\",\n", "        \"id\": 4650,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果耗材_水果盒\",\n", "        \"id\": 4649,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果耗材_水果袋.\",\n", "        \"id\": 4646,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果耗材_陈列道具.\",\n", "        \"id\": 4645,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果宣传_设计定制\",\n", "        \"id\": 11107,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果宣传_标签贴纸.\",\n", "        \"id\": 4664,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果宣传_氛围牌.\",\n", "        \"id\": 4663,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_草莓_巧克力草莓\",\n", "        \"id\": 1448,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_草莓_其他草莓\",\n", "        \"id\": 2832,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蓝莓_树莓/黑莓\",\n", "        \"id\": 1415,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蓝莓_进口蓝莓\",\n", "        \"id\": 1414,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蓝莓_国产蓝莓\",\n", "        \"id\": 14635,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_团餐业务_水果\",\n", "        \"id\": 4862,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_团餐业务_冻品\",\n", "        \"id\": 4860,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_团餐业务_蔬菜\",\n", "        \"id\": 8711,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_团餐业务_干调\",\n", "        \"id\": 18869,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_小番茄_圣女果\",\n", "        \"id\": 1454,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_小番茄_千禧番茄\",\n", "        \"id\": 1453,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_黄心猕猴桃\",\n", "        \"id\": 1423,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_绿心猕猴桃\",\n", "        \"id\": 1422,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_红心猕猴桃\",\n", "        \"id\": 1421,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_礼盒\",\n", "        \"id\": 9375,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_进口猕猴桃\",\n", "        \"id\": 3087,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柠檬/百香果_青柠檬\",\n", "        \"id\": 1452,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柠檬/百香果_黄柠檬\",\n", "        \"id\": 1451,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柠檬/百香果_百香果\",\n", "        \"id\": 2644,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_夏黑葡萄\",\n", "        \"id\": 6111,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_阳光玫瑰\",\n", "        \"id\": 3311,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_水果黄瓜_水果黄瓜\",\n", "        \"id\": 5879,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_更多提\",\n", "        \"id\": 1408,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_普通红提\",\n", "        \"id\": 1407,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_无籽红提\",\n", "        \"id\": 2623,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_凤梨/菠萝_进口凤梨\",\n", "        \"id\": 3086,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_凤梨/菠萝_普通凤梨\",\n", "        \"id\": 2638,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_凤梨/菠萝_金钻凤梨\",\n", "        \"id\": 2134,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_凤梨/菠萝_大菠萝\",\n", "        \"id\": 2133,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_椰子_青椰\",\n", "        \"id\": 1457,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_椰子_椰皇\",\n", "        \"id\": 2648,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_椰子_椰青\",\n", "        \"id\": 2646,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_牛油果_进口牛油果\",\n", "        \"id\": 1455,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝蜜_红肉菠萝蜜\",\n", "        \"id\": 1432,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_龙眼_泰国龙眼\",\n", "        \"id\": 1409,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_更多芒果\",\n", "        \"id\": 1460,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_水仙芒\",\n", "        \"id\": 1428,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_青芒\",\n", "        \"id\": 1427,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_台芒\",\n", "        \"id\": 1426,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_火龙果_进口白心果\",\n", "        \"id\": 1401,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_火龙果_进口红心果\",\n", "        \"id\": 1400,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_火龙果_国产红心果\",\n", "        \"id\": 2611,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柚子_红心蜜柚\",\n", "        \"id\": 1374,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柚子_更多柚子\",\n", "        \"id\": 1373,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柚子_白心蜜柚\",\n", "        \"id\": 1372,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柚子_三红蜜柚\",\n", "        \"id\": 2643,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柚子_葡萄柚\",\n", "        \"id\": 14618,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橘/桔_更多桔\",\n", "        \"id\": 2574,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橘/桔_金桔\",\n", "        \"id\": 2479,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橘/桔_礼盒\",\n", "        \"id\": 14666,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橘/桔_砂糖桔\",\n", "        \"id\": 16584,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_礼盒\",\n", "        \"id\": 5877,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_沃柑\",\n", "        \"id\": 1435,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_椪柑/晚芦\",\n", "        \"id\": 1434,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_不知火柑\",\n", "        \"id\": 2610,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_皇帝柑\",\n", "        \"id\": 2480,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_春见/耙耙柑\",\n", "        \"id\": 2478,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐今日必买_礼盒类\",\n", "        \"id\": 13826,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐今日必买_今日大放价\",\n", "        \"id\": 4829,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柿子_火晶/龙眼柿\",\n", "        \"id\": 2658,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柿子_柿饼\",\n", "        \"id\": 2068,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_玉菇甜瓜\",\n", "        \"id\": 6044,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_更多蜜瓜\",\n", "        \"id\": 1245,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_网纹蜜瓜\",\n", "        \"id\": 1244,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_25号蜜瓜\",\n", "        \"id\": 1242,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_黄金蜜瓜\",\n", "        \"id\": 2612,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_农夫山泉橙\",\n", "        \"id\": 16342,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_品牌蓝莓\",\n", "        \"id\": 16339,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_官方精选\",\n", "        \"id\": 16338,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_佳沃车厘子\",\n", "        \"id\": 20156,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_脆蜜金桔\",\n", "        \"id\": 19280,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_黄胖子维纳斯\",\n", "        \"id\": 19072,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_吹海风苹果\",\n", "        \"id\": 16661,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜_甜王西瓜\",\n", "        \"id\": 1240,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜_特小凤西瓜\",\n", "        \"id\": 1236,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜_黑美人西瓜\",\n", "        \"id\": 1235,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜_麒麟西瓜\",\n", "        \"id\": 1232,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜_P2西瓜\",\n", "        \"id\": 2945,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蕉_苹果蕉\",\n", "        \"id\": 1229,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蕉_皇帝蕉\",\n", "        \"id\": 1228,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蕉_香蕉\",\n", "        \"id\": 1227,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_荔枝_妃子笑\",\n", "        \"id\": 2176,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_核桃/花生/板栗_板栗\",\n", "        \"id\": 7437,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_榴莲_金枕榴莲\",\n", "        \"id\": 6050,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_榴莲_干尧榴莲\",\n", "        \"id\": 8398,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_榴莲_其他榴莲\",\n", "        \"id\": 6313,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_进口脐橙\",\n", "        \"id\": 1442,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_冰糖橙~\",\n", "        \"id\": 1441,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_更多橙\",\n", "        \"id\": 1440,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_礼盒\",\n", "        \"id\": 19618,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_血橙\",\n", "        \"id\": 9374,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_长红橙\",\n", "        \"id\": 4856,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_圆红橙\",\n", "        \"id\": 4855,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_脐橙\",\n", "        \"id\": 4854,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_爱媛/果冻橙\",\n", "        \"id\": 2632,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_赣南脐橙\",\n", "        \"id\": 2455,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_山竹_山竹\",\n", "        \"id\": 4767,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_雪花梨\",\n", "        \"id\": 1946,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_皇冠梨\",\n", "        \"id\": 1450,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_丰水梨\",\n", "        \"id\": 1447,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_更多梨\",\n", "        \"id\": 1438,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_香梨\",\n", "        \"id\": 1437,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_礼盒\",\n", "        \"id\": 9598,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_红香酥梨\",\n", "        \"id\": 4878,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_贡梨\",\n", "        \"id\": 4877,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_秋月梨\",\n", "        \"id\": 2434,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_冰糖心苹果\",\n", "        \"id\": 1945,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_秦冠苹果\",\n", "        \"id\": 1940,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_更多苹果\",\n", "        \"id\": 1420,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_红富士\",\n", "        \"id\": 1419,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_苹果礼盒\",\n", "        \"id\": 9372,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_进口苹果\",\n", "        \"id\": 2614,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_寒富/蜜脆\",\n", "        \"id\": 2475,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐热卖进口品_进口油桃\",\n", "        \"id\": 4974,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐热卖进口品_进口柑橘橙\",\n", "        \"id\": 10245,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_无花果_无花果\",\n", "        \"id\": 6239,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_甘蔗_工具类\",\n", "        \"id\": 14564,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_甘蔗_甘蔗\",\n", "        \"id\": 2192,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐小众水果合集_桑葚\",\n", "        \"id\": 5994,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐小众水果合集_雪莲果\",\n", "        \"id\": 2670,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐小众水果合集_红薯\",\n", "        \"id\": 4195,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐小众水果合集_山楂\",\n", "        \"id\": 4193,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐小众水果合集_姑娘果\",\n", "        \"id\": 4191,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐小众水果合集_芭乐\",\n", "        \"id\": 4187,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐小众水果合集_马蹄果\",\n", "        \"id\": 4186,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐小众水果合集_人参果\",\n", "        \"id\": 4185,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐小众水果合集_甜角\",\n", "        \"id\": 2072,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐小众水果合集_释迦/莲雾\",\n", "        \"id\": 2059,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_车厘子_进口车厘子\",\n", "        \"id\": 2505,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_木瓜/杨桃_杨桃\",\n", "        \"id\": 2669,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_木瓜/杨桃_红心木瓜\",\n", "        \"id\": 2649,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_枣_牛奶枣\",\n", "        \"id\": 2495,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_枣_更多枣\",\n", "        \"id\": 2053,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_枣_青枣\",\n", "        \"id\": 2052,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝蜜_菠萝蜜肉\",\n", "        \"id\": 19283,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝蜜_红肉蜜\",\n", "        \"id\": 1671,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝蜜_黄肉蜜\",\n", "        \"id\": 1670,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_山楂_鲜山楂\",\n", "        \"id\": 19070,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_水果黄瓜_南瓜\",\n", "        \"id\": 1663,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_水果黄瓜_水果黄瓜\",\n", "        \"id\": 1662,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十三楼精选_维纳斯苹果\",\n", "        \"id\": 19284,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十三楼精选_官方精选\",\n", "        \"id\": 8829,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十三楼精选_脆蜜金桔\",\n", "        \"id\": 18953,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十三楼精选_佳沛金果\",\n", "        \"id\": 16622,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十三楼精选_蓝莓\",\n", "        \"id\": 16620,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_牛油果_进口牛油果\",\n", "        \"id\": 1660,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_降价热榜_今日特惠\",\n", "        \"id\": 13308,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_木瓜_红心木瓜\",\n", "        \"id\": 2764,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_木瓜_牛奶木瓜\",\n", "        \"id\": 1669,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_高乐蜜芒\",\n", "        \"id\": 2979,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_更多芒果\",\n", "        \"id\": 2750,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_青芒\",\n", "        \"id\": 2749,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_水仙芒\",\n", "        \"id\": 1622,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_台芒\",\n", "        \"id\": 1620,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_象牙芒\",\n", "        \"id\": 5237,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_金煌芒\",\n", "        \"id\": 5236,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_红心猕猴桃\",\n", "        \"id\": 1600,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_黄心猕猴桃\",\n", "        \"id\": 1599,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_绿心猕猴桃\",\n", "        \"id\": 1598,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_进口猕猴桃/奇异果\",\n", "        \"id\": 3197,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蓝莓_桑葚\",\n", "        \"id\": 2986,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蓝莓_蓝莓·\",\n", "        \"id\": 1664,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_草莓_其他草莓\",\n", "        \"id\": 3895,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_草莓_更多草莓\",\n", "        \"id\": 1656,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_草莓_奶油草莓\",\n", "        \"id\": 1654,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_草莓_红颜草莓\",\n", "        \"id\": 1653,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_枣_青枣\",\n", "        \"id\": 2731,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_枣_牛奶枣\",\n", "        \"id\": 1580,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_樱桃/车厘子_进口车厘子\",\n", "        \"id\": 2549,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝/凤梨_大菠萝\",\n", "        \"id\": 2747,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝/凤梨_金钻凤梨\",\n", "        \"id\": 1683,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝/凤梨_进口凤梨\",\n", "        \"id\": 3094,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_甘蔗_黑皮甘蔗\",\n", "        \"id\": 2766,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_甘蔗_青皮甘蔗\",\n", "        \"id\": 2411,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柠_黄柠檬\",\n", "        \"id\": 2753,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柠_青柠檬\",\n", "        \"id\": 1608,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柠_香水柠檬\",\n", "        \"id\": 1607,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_百香果_黄金百香果\",\n", "        \"id\": 3346,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_百香果_红皮百香果\",\n", "        \"id\": 3345,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橘/桔_丑桔\",\n", "        \"id\": 8056,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橘/桔_金桔\",\n", "        \"id\": 2733,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橘/桔_青金桔\",\n", "        \"id\": 1637,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橘/桔_砂糖桔\",\n", "        \"id\": 1635,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_香瓜/甜瓜_玉菇甜瓜\",\n", "        \"id\": 2758,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_爱媛橙\",\n", "        \"id\": 7984,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_血橙\",\n", "        \"id\": 4823,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_冰糖/伦晚橙\",\n", "        \"id\": 2730,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_脐橙\",\n", "        \"id\": 2729,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_进口橙\",\n", "        \"id\": 1593,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_赣南脐橙\",\n", "        \"id\": 16635,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_纽荷尔脐橙\",\n", "        \"id\": 16634,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜集结号_无籽西瓜\",\n", "        \"id\": 2984,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜集结号_小凤西瓜\",\n", "        \"id\": 2773,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜集结号_麒麟西瓜\",\n", "        \"id\": 1555,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜集结号_黑美人\",\n", "        \"id\": 3295,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜集结号_甘美4K\",\n", "        \"id\": 3085,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柿_脆柿\",\n", "        \"id\": 2742,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_雪花梨\",\n", "        \"id\": 4068,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_贡梨\",\n", "        \"id\": 2728,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_红香酥梨\",\n", "        \"id\": 2727,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_水晶梨\",\n", "        \"id\": 2726,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_啤梨\",\n", "        \"id\": 19067,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_更多梨\",\n", "        \"id\": 1614,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_香梨\",\n", "        \"id\": 1613,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_皇冠梨\",\n", "        \"id\": 1611,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_早酥梨\",\n", "        \"id\": 3567,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_阳光玫瑰葡萄\",\n", "        \"id\": 4007,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_巨峰葡萄\",\n", "        \"id\": 2740,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_夏黑葡萄\",\n", "        \"id\": 2739,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_更多葡萄\",\n", "        \"id\": 1569,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_茉莉香葡萄\",\n", "        \"id\": 1568,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柚子_葡萄柚\",\n", "        \"id\": 8034,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柚子_更多柚子\",\n", "        \"id\": 1648,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柚子_沙田柚/西柚\",\n", "        \"id\": 1647,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柚子_三红蜜柚\",\n", "        \"id\": 1646,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柚子_红心蜜柚\",\n", "        \"id\": 1644,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蕉_粉蕉\",\n", "        \"id\": 4732,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蕉_▲必囤~香蕉\",\n", "        \"id\": 1316,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桃_进口油桃\",\n", "        \"id\": 10246,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_其他柑\",\n", "        \"id\": 2720,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_玫瑰柑\",\n", "        \"id\": 1634,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_沃柑\",\n", "        \"id\": 1631,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_耙耙柑\",\n", "        \"id\": 1630,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_椰子_椰青\",\n", "        \"id\": 1605,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_加力果\",\n", "        \"id\": 6904,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_更多苹果\",\n", "        \"id\": 2718,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_冰糖心苹果\",\n", "        \"id\": 2717,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_蛇果\",\n", "        \"id\": 19068,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_华硕红玫瑰\",\n", "        \"id\": 1597,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_红富士苹果\",\n", "        \"id\": 1595,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_秦冠苹果\",\n", "        \"id\": 5234,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_黄元帅\",\n", "        \"id\": 4199,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_青苹果\",\n", "        \"id\": 4198,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_榴莲_更多榴莲\",\n", "        \"id\": 2735,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_八六王\",\n", "        \"id\": 4011,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_黄皮哈密瓜\",\n", "        \"id\": 2981,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_网纹蜜\",\n", "        \"id\": 1325,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_25号蜜瓜\",\n", "        \"id\": 1323,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_火龙果_进口白心果\",\n", "        \"id\": 1583,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_火龙果_国产红心果\",\n", "        \"id\": 1582,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_小番茄_千禧樱桃番茄\",\n", "        \"id\": 1590,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_小番茄_圣女果\",\n", "        \"id\": 1589,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_山竹_2A\",\n", "        \"id\": 3289,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_山竹_5A\",\n", "        \"id\": 3279,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_山竹_4A\",\n", "        \"id\": 3278,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_山竹_3A\",\n", "        \"id\": 3277,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_年货礼盒_零食礼盒\",\n", "        \"id\": 20386,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_年货礼盒_礼盒耗材\",\n", "        \"id\": 13901,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_年货礼盒_水果礼盒\",\n", "        \"id\": 13900,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_普通黑提\",\n", "        \"id\": 4009,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_进口红提\",\n", "        \"id\": 5876,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_普通红提\",\n", "        \"id\": 2722,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_无籽红提\",\n", "        \"id\": 2467,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_进口黑提\",\n", "        \"id\": 5199,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_拼团秒杀_小件乳饮\",\n", "        \"id\": 5984,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_拼团秒杀_小件零食\",\n", "        \"id\": 4787,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_拼团秒杀_小件水果\",\n", "        \"id\": 4785,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_拼团秒杀_常备耗材\",\n", "        \"id\": 8521,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_石榴_软籽石榴\",\n", "        \"id\": 1682,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_水煮花生_水煮花生\",\n", "        \"id\": 4824,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_莲雾_大叶红莲雾\",\n", "        \"id\": 6308,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_莲雾_黑金刚莲雾\",\n", "        \"id\": 6307,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_龙眼_蓝标/绿标\",\n", "        \"id\": 2751,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_龙眼_金标\",\n", "        \"id\": 1676,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_龙眼_红标\",\n", "        \"id\": 1674,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_稀奇水果_更多稀奇\",\n", "        \"id\": 2774,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_稀奇水果_释迦果\",\n", "        \"id\": 2757,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_稀奇水果_雪莲果\",\n", "        \"id\": 4481,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_稀奇水果_火参果/人参果\",\n", "        \"id\": 2080,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐江南市场_金桔\",\n", "        \"id\": 5766,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐江南市场_白心火龙果\",\n", "        \"id\": 19064,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐江南市场_进口蓝莓\",\n", "        \"id\": 19063,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐江南市场_耙耙柑\",\n", "        \"id\": 4723,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐江南市场_车厘子\",\n", "        \"id\": 4722,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐江南市场_苹果\",\n", "        \"id\": 4719,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐江南市场_凤梨\",\n", "        \"id\": 13797,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐江南市场_其他单品\",\n", "        \"id\": 12525,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐江南市场_牛油果\",\n", "        \"id\": 12507,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐江南市场_水果黄瓜\",\n", "        \"id\": 3093,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_▼捡漏专区_好货捡漏\",\n", "        \"id\": 8883,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装耗材_水果耗材_保鲜膜.\",\n", "        \"id\": 8048,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装耗材_水果耗材_托盒.\",\n", "        \"id\": 8041,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装耗材_水果耗材_更多耗材.\",\n", "        \"id\": 4662,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装耗材_水果耗材_刀具工具.\",\n", "        \"id\": 4661,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装耗材_水果耗材_水果盒.\",\n", "        \"id\": 4660,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装耗材_水果耗材_水果袋.\",\n", "        \"id\": 4659,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装耗材_水果耗材_陈列道具.\",\n", "        \"id\": 3560,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装耗材_水果宣传_标签贴纸.\",\n", "        \"id\": 4272,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装耗材_水果宣传_氛围牌.\",\n", "        \"id\": 4271,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装耗材_热卖推荐_应季水果耗材\",\n", "        \"id\": 11780,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_25号晓蜜瓜\",\n", "        \"id\": 1366,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_榴莲_金枕榴莲\",\n", "        \"id\": 1213,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_火龙果_进口红心火龙果\",\n", "        \"id\": 1833,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桃_水蜜桃\",\n", "        \"id\": 2969,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桃_油桃\",\n", "        \"id\": 2968,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桃_更多桃\",\n", "        \"id\": 1860,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桃_国产黄桃\",\n", "        \"id\": 3380,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_青提\",\n", "        \"id\": 1720,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_茉莉香/玫瑰香\",\n", "        \"id\": 2959,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_巨峰葡萄\",\n", "        \"id\": 1694,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_红富士苹果\",\n", "        \"id\": 1737,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_荔枝_其他荔枝\",\n", "        \"id\": 3320,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_椰子_椰青\",\n", "        \"id\": 2031,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑/沃柑_不知火柑\",\n", "        \"id\": 3378,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蕉_苹果蕉\",\n", "        \"id\": 1354,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蕉_皇帝蕉\",\n", "        \"id\": 1353,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桔/橘_更多桔\",\n", "        \"id\": 1888,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_冬枣_A级\",\n", "        \"id\": 2368,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_冬枣_陕西冬枣\",\n", "        \"id\": 4750,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜_麒麟西瓜\",\n", "        \"id\": 1361,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜_口口脆\",\n", "        \"id\": 3256,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐上新·鸡蛋_鸡蛋\",\n", "        \"id\": 4691,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_李子_蜂糖李\",\n", "        \"id\": 2956,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_李子_更多李\",\n", "        \"id\": 1895,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_李子_青红脆李\",\n", "        \"id\": 3299,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_千禧/水果黄瓜_水果黄瓜\",\n", "        \"id\": 3054,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_千禧/水果黄瓜_千禧小番茄\",\n", "        \"id\": 1839,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃_进口猕猴桃\",\n", "        \"id\": 3275,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柠檬/百香果_百香果\",\n", "        \"id\": 1843,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柠檬/百香果_柠檬\",\n", "        \"id\": 2854,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_普通梨_更多梨\",\n", "        \"id\": 1510,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_库尔勒香梨_库尔勒香梨\",\n", "        \"id\": 4804,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐防疫三件套_梨·清甜润肺\",\n", "        \"id\": 4868,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐防疫三件套_柠檬·提高免疫\",\n", "        \"id\": 4867,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_香瓜/甜瓜_羊角蜜\",\n", "        \"id\": 1524,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果盒_带盖盒\",\n", "        \"id\": 6082,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果盒_托盒\",\n", "        \"id\": 6081,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"包装/耗材_水果袋_水果袋\",\n", "        \"id\": 6078,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_更多葡萄\",\n", "        \"id\": 5379,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_葡萄_阳光玫瑰\",\n", "        \"id\": 5377,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_普通红提\",\n", "        \"id\": 5372,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_提子_无籽红提\",\n", "        \"id\": 5371,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝蜜_菠萝蜜工具\",\n", "        \"id\": 16248,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝蜜_盒装菠萝蜜\",\n", "        \"id\": 16247,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝蜜_红肉菠萝蜜\",\n", "        \"id\": 5365,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐⭐⭐今日推荐_今日推荐\",\n", "        \"id\": 5745,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_草莓_更多草莓\",\n", "        \"id\": 5354,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_草莓_红颜草莓\",\n", "        \"id\": 5353,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_草莓_奶油草莓\",\n", "        \"id\": 5352,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_草莓_草莓礼盒\",\n", "        \"id\": 9383,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐水果礼盒_水果礼盒\",\n", "        \"id\": 6403,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐水果礼盒_水果礼品\",\n", "        \"id\": 6244,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桔/橘_其他桔\",\n", "        \"id\": 5346,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桔/橘_金桔\",\n", "        \"id\": 5345,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桔/橘_砂糖桔\",\n", "        \"id\": 5343,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桔/橘_桔/橘礼盒\",\n", "        \"id\": 9378,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_黄胖子维纳斯\",\n", "        \"id\": 16359,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_佳沛金果\",\n", "        \"id\": 16351,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_品牌蓝莓\",\n", "        \"id\": 16350,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_官方精选\",\n", "        \"id\": 16349,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_脆蜜金柑\",\n", "        \"id\": 18954,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_吹海风野苹果\",\n", "        \"id\": 16618,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐十二楼精选_17.5°橙\",\n", "        \"id\": 16393,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_香蕉_进口香蕉\",\n", "        \"id\": 8397,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_更多蜜瓜\",\n", "        \"id\": 5335,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_网纹蜜瓜\",\n", "        \"id\": 5334,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜瓜_25号小蜜\",\n", "        \"id\": 5332,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜柚_葡萄柚\",\n", "        \"id\": 14743,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜柚_更多柚子\",\n", "        \"id\": 5369,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蜜柚_红心蜜柚\",\n", "        \"id\": 5323,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_椰子_椰青\",\n", "        \"id\": 5321,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_稀奇水果_牛油果\",\n", "        \"id\": 5438,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_稀奇水果_水果黄瓜\",\n", "        \"id\": 5437,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_稀奇水果_人参果/释迦果\",\n", "        \"id\": 5436,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_稀奇水果_木瓜/杨桃\",\n", "        \"id\": 5435,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_稀奇水果_莲雾/芭乐\",\n", "        \"id\": 5434,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_稀奇水果_雪莲果/马蹄果\",\n", "        \"id\": 5319,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_小番茄_小众小番茄\",\n", "        \"id\": 10537,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_小番茄_千禧樱桃小番茄\",\n", "        \"id\": 5317,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_小众水果_小众水果\",\n", "        \"id\": 14530,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_香瓜/甜瓜_羊角蜜\",\n", "        \"id\": 5415,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_香瓜/甜瓜_玉菇甜瓜\",\n", "        \"id\": 5315,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_香瓜/甜瓜_更多瓜\",\n", "        \"id\": 5314,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柠檬/百香果_百香果\",\n", "        \"id\": 5442,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柠檬/百香果_柠檬\",\n", "        \"id\": 5441,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜_更多西瓜\",\n", "        \"id\": 5382,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜_无籽西瓜\",\n", "        \"id\": 5310,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_西瓜_麒麟西瓜\",\n", "        \"id\": 5309,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_山竹_山竹\",\n", "        \"id\": 5307,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐新品专区_本周新品\",\n", "        \"id\": 6842,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_山楂_鲜山楂\",\n", "        \"id\": 5305,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_进口苹果\",\n", "        \"id\": 13274,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_更多苹果\",\n", "        \"id\": 5361,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_冰糖心苹果\",\n", "        \"id\": 5302,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_蛇果\",\n", "        \"id\": 5301,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_红富士苹果\",\n", "        \"id\": 5300,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_奶油富士苹果\",\n", "        \"id\": 5299,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_黄元帅苹果\",\n", "        \"id\": 5298,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_苹果_苹果礼盒\",\n", "        \"id\": 9379,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_枣_更多枣\",\n", "        \"id\": 5428,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_枣_牛奶枣\",\n", "        \"id\": 5427,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_枣_青枣\",\n", "        \"id\": 5426,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_樱桃/车厘子_宾车厘子\",\n", "        \"id\": 21316,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_樱桃/车厘子_更多进口车厘子\",\n", "        \"id\": 19053,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_樱桃/车厘子_科迪亚车厘子\",\n", "        \"id\": 19043,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_樱桃/车厘子_拉宾斯车厘子\",\n", "        \"id\": 9376,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_进口黄心猕猴桃\",\n", "        \"id\": 11120,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_红心猕猴桃\",\n", "        \"id\": 5503,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_绿心猕猴桃\",\n", "        \"id\": 5423,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_礼盒\",\n", "        \"id\": 9414,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_猕猴桃/奇异果_黄心猕猴桃\",\n", "        \"id\": 5289,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_更多芒果\",\n", "        \"id\": 5394,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_台芒\",\n", "        \"id\": 5392,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_青芒\",\n", "        \"id\": 7440,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_芒果_水仙芒\",\n", "        \"id\": 5287,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐团购专区_团购水果\",\n", "        \"id\": 6055,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_⭐团购专区_团购零食\",\n", "        \"id\": 7888,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_龙眼_更多龙眼\",\n", "        \"id\": 5946,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_龙眼_绿标龙眼\",\n", "        \"id\": 5357,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_龙眼_蓝标龙眼\",\n", "        \"id\": 5356,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_龙眼_金标龙眼\",\n", "        \"id\": 5355,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_龙眼_红标龙眼\",\n", "        \"id\": 5285,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_龙眼_龙眼礼盒\",\n", "        \"id\": 9380,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_榴莲_干尧榴莲\",\n", "        \"id\": 14737,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_榴莲_更多榴莲\",\n", "        \"id\": 5282,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_桃_进口油桃\",\n", "        \"id\": 5405,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_酥梨\",\n", "        \"id\": 5360,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_秋月梨\",\n", "        \"id\": 5359,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_贡梨\",\n", "        \"id\": 5358,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_皇冠梨\",\n", "        \"id\": 5277,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_水晶梨\",\n", "        \"id\": 5276,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_更多梨\",\n", "        \"id\": 5275,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_玉露香梨\",\n", "        \"id\": 14386,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_梨_香梨\",\n", "        \"id\": 14384,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_火龙果_白心火龙果\",\n", "        \"id\": 5362,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_火龙果_国产红心火龙果\",\n", "        \"id\": 5272,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_火龙果_进口红心火龙果\",\n", "        \"id\": 5271,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_耙耙柑\",\n", "        \"id\": 5341,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_沃柑\",\n", "        \"id\": 5340,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_柑礼盒\",\n", "        \"id\": 9377,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_柑_更多柑\",\n", "        \"id\": 5269,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_甘蔗_青皮甘蔗\",\n", "        \"id\": 5397,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_甘蔗_黑皮甘蔗\",\n", "        \"id\": 5396,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_小颗粒浆果_小颗粒浆果\",\n", "        \"id\": 5267,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_进口橙\",\n", "        \"id\": 13275,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_爱媛橙\",\n", "        \"id\": 5348,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_赣南脐橙\",\n", "        \"id\": 5347,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_橙子礼盒\",\n", "        \"id\": 9413,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_圆红/血橙\",\n", "        \"id\": 5264,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_橙_更多橙\",\n", "        \"id\": 5263,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝/凤梨_金钻凤梨\",\n", "        \"id\": 5400,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝/凤梨_进口凤梨\",\n", "        \"id\": 14582,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_菠萝/凤梨_大菠萝\",\n", "        \"id\": 5261,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_石榴_石榴礼盒\",\n", "        \"id\": 13917,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_石榴_软籽石榴\",\n", "        \"id\": 5384,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蓝莓_蓝莓礼盒\",\n", "        \"id\": 21339,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蓝莓_国产蓝莓\",\n", "        \"id\": 16392,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "    {\n", "        \"categoryLevel\": 3,\n", "        \"categoryName\": \"水果_蓝莓_进口蓝莓\",\n", "        \"id\": 16391,\n", "        \"ruleType\": \"BACK_CATEGORY\",\n", "        \"goodsCount\": 100,\n", "    },\n", "]\n", "\n", "old_category_df=pd.DataFrame(list)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-02-19 18:37:05][INFO][proxy_setup.py] - 随机的使用一个代理IP:**************:38779\n", "[2025-02-19 18:37:05][INFO][proxy_setup.py] - url:https://demeter-api.biaoguoworks.com/leechee/api/h5/store/front-categorys?size=10000, using proxy: http://***********:8gTcEKLs@**************:38779, retry_cnt:0, headers:{'sid': '12394075', 'token': ''}\n", "[2025-02-19 18:37:05][INFO][proxy_setup.py] - response status:200, proxy used:http://***********:8gTcEKLs@**************:38779\n"]}, {"name": "stdout", "output_type": "stream", "text": ["199\n", "{'id': 8575, 'parentId': 7960, 'categoryName': '菌菇类', 'enabled': 1, 'remark': None, 'categoryCode': '/1/7960/8575/', 'categoryLevel': 2, 'sortNo': 1642, 'leaf': 0, 'hideUnitPriceCatty': None, 'buyInstructionConf': None, 'businessConfigId': 0, 'ruleType': 'MANUAL_ADD', 'categoryImg': None, 'goodsCount': 0}\n"]}], "source": ["# data = {\"size\": 200, \"current\": 0, \"categoryLevel\": 3, \"parentCategoryId\": 1}\n", "\n", "# data = {}\n", "\n", "# url = \"https://demeter-api.biaoguoworks.com/leechee/api/h5/store/front-categorys\"\n", "\n", "# response = requests.post(url, data=data).json()[\"content\"]\n", "\n", "# print(len(response), response)\n", "\n", "headers={\"sid\": \"12394076\", \"token\": \"e1d1568dbe824d849f7c695e414d0c97\"},\n", "\n", "category_list_result = get_remote_data_with_proxy_json(\n", "    \"https://demeter-api.biaoguoworks.com/leechee/api/h5/store/front-categorys?size=10000\",\n", "    headers={\"sid\": \"12394075\", \"token\": \"\"},\n", "    data={\"size\": 200, \"current\": 0, \"categoryLevel\": 3, \"parentCategoryId\": 1},\n", ")\n", "if isinstance(category_list_result, dict) and \"content\" in category_list_result:\n", "    category_list = category_list_result[\"content\"]\n", "    print(len(category_list))\n", "    if category_list:  # Check if category_list is not empty\n", "        print(category_list[-1])\n", "    else:\n", "        print(\"category_list is empty\")\n", "else:\n", "    print(\"Error fetching category list:\")\n", "    print(category_list_result)"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>categoryLevel</th>\n", "      <th>categoryName</th>\n", "      <th>id</th>\n", "      <th>ruleType</th>\n", "      <th>goodsCount</th>\n", "      <th>parentId</th>\n", "      <th>enabled</th>\n", "      <th>remark</th>\n", "      <th>categoryCode</th>\n", "      <th>sortNo</th>\n", "      <th>leaf</th>\n", "      <th>hideUnitPriceCatty</th>\n", "      <th>buyInstructionConf</th>\n", "      <th>businessConfigId</th>\n", "      <th>categoryImg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>包装/耗材_水果耗材_保鲜膜</td>\n", "      <td>7566</td>\n", "      <td>BACK_CATEGORY</td>\n", "      <td>100</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>包装/耗材_水果耗材_托盒</td>\n", "      <td>7565</td>\n", "      <td>BACK_CATEGORY</td>\n", "      <td>100</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>包装/耗材_水果耗材_更多耗材.</td>\n", "      <td>4651</td>\n", "      <td>BACK_CATEGORY</td>\n", "      <td>100</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>包装/耗材_水果耗材_刀具工具.</td>\n", "      <td>4650</td>\n", "      <td>BACK_CATEGORY</td>\n", "      <td>100</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3</td>\n", "      <td>包装/耗材_水果耗材_水果盒</td>\n", "      <td>4649</td>\n", "      <td>BACK_CATEGORY</td>\n", "      <td>100</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>652</th>\n", "      <td>2</td>\n", "      <td>水发产品</td>\n", "      <td>13175</td>\n", "      <td>MANUAL_ADD</td>\n", "      <td>0</td>\n", "      <td>9534.0</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>/1/9534/13175/</td>\n", "      <td>1463.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>653</th>\n", "      <td>2</td>\n", "      <td>小番茄</td>\n", "      <td>763</td>\n", "      <td>MANUAL_ADD</td>\n", "      <td>0</td>\n", "      <td>627.0</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>/1/627/763/</td>\n", "      <td>2100.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>654</th>\n", "      <td>2</td>\n", "      <td>团餐业务</td>\n", "      <td>4859</td>\n", "      <td>MANUAL_ADD</td>\n", "      <td>0</td>\n", "      <td>627.0</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>/1/627/4859/</td>\n", "      <td>1761.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>655</th>\n", "      <td>2</td>\n", "      <td>草莓</td>\n", "      <td>766</td>\n", "      <td>MANUAL_ADD</td>\n", "      <td>0</td>\n", "      <td>627.0</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>/1/627/766/</td>\n", "      <td>2097.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>656</th>\n", "      <td>2</td>\n", "      <td>菌菇类</td>\n", "      <td>8575</td>\n", "      <td>MANUAL_ADD</td>\n", "      <td>0</td>\n", "      <td>7960.0</td>\n", "      <td>1.0</td>\n", "      <td>None</td>\n", "      <td>/1/7960/8575/</td>\n", "      <td>1642.0</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>634 rows × 15 columns</p>\n", "</div>"], "text/plain": ["     categoryLevel      categoryName     id       ruleType  goodsCount  \\\n", "0                3    包装/耗材_水果耗材_保鲜膜   7566  BACK_CATEGORY         100   \n", "1                3     包装/耗材_水果耗材_托盒   7565  BACK_CATEGORY         100   \n", "2                3  包装/耗材_水果耗材_更多耗材.   4651  BACK_CATEGORY         100   \n", "3                3  包装/耗材_水果耗材_刀具工具.   4650  BACK_CATEGORY         100   \n", "4                3    包装/耗材_水果耗材_水果盒   4649  BACK_CATEGORY         100   \n", "..             ...               ...    ...            ...         ...   \n", "652              2              水发产品  13175     MANUAL_ADD           0   \n", "653              2               小番茄    763     MANUAL_ADD           0   \n", "654              2              团餐业务   4859     MANUAL_ADD           0   \n", "655              2                草莓    766     MANUAL_ADD           0   \n", "656              2               菌菇类   8575     MANUAL_ADD           0   \n", "\n", "     parentId  enabled remark    categoryCode  sortNo  leaf  \\\n", "0         NaN      NaN    NaN             NaN     NaN   NaN   \n", "1         NaN      NaN    NaN             NaN     NaN   NaN   \n", "2         NaN      NaN    NaN             NaN     NaN   NaN   \n", "3         NaN      NaN    NaN             NaN     NaN   NaN   \n", "4         NaN      NaN    NaN             NaN     NaN   NaN   \n", "..        ...      ...    ...             ...     ...   ...   \n", "652    9534.0      1.0   None  /1/9534/13175/  1463.0   0.0   \n", "653     627.0      1.0   None     /1/627/763/  2100.0   0.0   \n", "654     627.0      1.0   None    /1/627/4859/  1761.0   0.0   \n", "655     627.0      1.0   None     /1/627/766/  2097.0   0.0   \n", "656    7960.0      1.0   None   /1/7960/8575/  1642.0   0.0   \n", "\n", "    hideUnitPriceCatty buyInstructionConf  businessConfigId categoryImg  \n", "0                  NaN                NaN               NaN         NaN  \n", "1                  NaN                NaN               NaN         NaN  \n", "2                  NaN                NaN               NaN         NaN  \n", "3                  NaN                NaN               NaN         NaN  \n", "4                  NaN                NaN               NaN         NaN  \n", "..                 ...                ...               ...         ...  \n", "652               None               None               0.0        None  \n", "653               None               None               0.0        None  \n", "654               None               None               0.0        None  \n", "655               None               None               0.0        None  \n", "656               None               None               0.0        None  \n", "\n", "[634 rows x 15 columns]"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["catergory_list_df = pd.DataFrame(category_list)\n", "\n", "merged_category_df = pd.concat(\n", "    [old_category_df, catergory_list_df], ignore_index=True\n", ").drop_duplicates(subset=[\"id\"], keep=\"first\")\n", "merged_category_df"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-02-19 17:50:12][INFO][proxy_setup.py] - 随机的使用一个代理IP:**************:45297\n", "[2025-02-19 17:50:12][INFO][proxy_setup.py] - url:https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods, using proxy: http://***********:8gTcEKLs@**************:45297, retry_cnt:0, headers:{'token': '5b0c07e2078e46d4b92423e9d0eca1b2', 'sid': '8200918', 'time': '1702521175012'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[2025-02-19 17:50:13][INFO][proxy_setup.py] - response status:200, proxy used:http://***********:8gTcEKLs@**************:45297\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[{'id': 730664, 'goodsName': '黄胖子山东黄金维纳斯苹果70#3.3斤B级【水果 礼盒】', 'babyName': '黄胖子山东黄金维纳斯苹果70#3.3斤B级【水果 礼盒】', 'skuCode': '6031533865396', 'specification': '约3.3斤', 'unit': 'PIECE', 'volume': None, 'netWeight': 3.0, 'grossWeight': 3.3, 'status': 'ALREADY_UP', 'upTime': '2025-02-18 19:40:11', 'sortNo': 55555, 'standardPrice': -1, 'activityPrice': None, 'singleLimited': 50, 'totalLimited': None, 'attaListIcon': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1737688411407/70.jpeg', 'attachUrl': '/web/goodsImage/1737688411407/70.jpeg', 'snapshotVersion': '57d70699600e4940819a633201e2854b', 'initSaleNum': 0, 'aviableSaleNum': 1975, 'lockSaleNum': 0, 'minSellingNum': None, 'multipleSale': None, 'alreadyBought': 0, 'endCountdown': None, 'planUpTime': None, 'planDownTime': None, 'planType': None, 'inquiryPriceTime': '2025-02-17 11:48:54', 'lastTimeSalePrice': -1, 'lastTimeUnitPriceCatty': -1, 'lastTimeInquiryPriceTime': '2025-02-14 08:41:22', 'lastTimeStandardPrice': -1, 'lastTimeActivityPrice': None, 'currentDate': '2025-02-19 17:50:13', 'displaySaleNum': 1, 'saleTagId': 4661, 'saleTagName': '流量位机会爆品主图标签', 'sellingPoint': None, 'companyId': 2, 'killed': 0, 'sellerId': 20743, 'sellerName': '十二楼果业', 'sellerType': 'BUSINESS', 'sellerNameExhibition': False, 'settlementType': 'STANDER', 'length': None, 'width': None, 'height': None, 'unitPriceCatty': -1, 'backCategoryId': 7864, 'attachUrlR': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1737688411407/70.jpeg', 'timeLimit': 'ONE_DAY', 'purchaseUserName': None, 'purchaseUserId': None, 'enablePlan': None, 'goodsType': 'ORDINARY', 'afterSaleDesc': '', 'crossedPrice': None, 'basketTypeId': None, 'backCategoryName': '官方精选', 'backCategoryCode': '/1/500/785/809/7864/', 'firstBackCategoryId': 500, 'intervalStatus': 'DISABLED', 'listingPeriod': '', 'materialLastTimeUpdateTime': '2025-02-07 13:41:54', 'downTime': None, 'sellingGoodsPoint': None, 'praiseNum': 0, 'stepNum': 0, 'monthSale': 107, 'sevenDayAfterSale': None, 'detailTagList': [{'id': 4870, 'tagName': '机会爆品', 'enabled': 1, 'tagDisplayType': 'TEXT', 'tagIconAttachUrl': None, 'tagIconAttachUrlH': None, 'countGoods': None, 'beginTime': None, 'endTime': None, 'goodsTagTypeEnum': 'SALE_DETAIL_TAG', 'companyId': None, 'hasTagRule': True}], 'decisionTag': None, 'behaviorTagDTO': None, 'mainTag': {'id': 4661, 'tagName': None, 'enabled': 1, 'tagDisplayType': 'ICON', 'tagIconAttachUrl': '/web/goodsImage/1723465699089/%E6%9C%BA%E4%BC%9A%E7%88%86%E5%93%81.png', 'tagIconAttachUrlH': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1723465699089/%E6%9C%BA%E4%BC%9A%E7%88%86%E5%93%81.png', 'countGoods': None, 'beginTime': None, 'endTime': None, 'goodsTagTypeEnum': None, 'companyId': 2, 'hasTagRule': False}, 'activityGoodsDto': {'activityName': '流量位爆品', 'skuCode': '6031533865396', 'goodsId': 730664, 'activityId': 59310, 'settlementType': 'STANDER', 'unitPriceCatty': -1, 'standardPrice': -1, 'crossedPrice': 12.9, 'originalUnitPriceCatty': None, 'originalStandardPrice': 12.9, 'singleLimited': 50, 'snapshotVersion': '034bb16d09634dea919c5e560994a2a9', 'activityType': 'SECKILL', 'orderMethod': 'COMPLEX', 'startTime': '2025-02-19 10:00:56', 'endTime': '2025-02-20 00:10:00', 'status': 'PROCESS', 'grossWeight': 3.3, 'initSaleNum': 211, 'inventoryId': 4986018, 'totalLimited': 300, 'alreadySaleNum': 5, 'aviableSaleNum': 295, 'lockSaleNum': 0, 'platformShare': 0.0, 'activityTagName': '爆品', 'activityTag': None, 'deliveryDate': None, 'activityDesc': '流量位爆品', 'activityDisplayName': '流量位爆品', 'id': 4200124, 'commissionSuperposition': 'SUPERPOSITION_UP_DOWN', 'orderStartTime': None, 'orderEndTime': None, 'groupPurchaseItemDTO': None, 'reduceAmount': 13.9, 'reduceAmountRate': -0.07751937}, 'rank': None, 'rankInterval': None, 'firstUpTime': '2025-01-24 17:12:03', 'dcdpResultDTO': None, 'basketType': None, 'sellerSiphonCommissionRate': 5.5, 'goodsSiphonCommissionRate': 11.0, 'goodsIntervalList': None, 'allowDeliver': True, 'ifAfterSalesBanned': False, 'sellerBanned': False, 'hideTagDTO': {'categoryId': 7864, 'categoryName': '小众苹果', 'hideUnitPriceCatty': 'DISPLAY'}, 'afterSaleStatisticalCycle': None, 'saleArea': None, 'firstDiscountDTO': None, 'finalStandardPrice': 11.6, 'finalUnitPriceCatty': None, 'finalCrossedPrice': None, 'priceRegionGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceResult': {'goodsId': 730664, 'unitPriceCatty': 0.0, 'standardPrice': 11.6, 'crossedPrice': 12.9, 'commissionRatio': 11.0, 'goodsInterval': None, 'priceCalcReason': 'ACTIVITY', 'priceRegionGoodsSnapshotVersion': None, 'priceGroupGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceRegionCategoryCommissionSnapshotVersion': None, 'priceGroupGoodsCommissionSnapshotVersion': None, 'originCommissionRatio': 11.0, 'originUnitPriceCatty': None, 'originStandardPrice': 12.9, 'originCrossedPrice': None, 'activityStandardPrice': 12.9, 'activityUnitPriceCatty': None, 'commissionSuperposition': 'SUPERPOSITION_UP_DOWN', 'firstDiscountCrossedStandardPrice': None, 'firstDiscountCrossedUnitPriceCatty': None, 'freight': None, 'stageStoreFlag': 0}, 'goodsScoreTags': [{'goodsId': 730664, 'backCategoryId': 7864, 'companyId': 2, 'dimension': 'RE_BUY', 'rank': 1}, {'goodsId': 730664, 'backCategoryId': 7864, 'companyId': 2, 'dimension': 'TOTAL', 'rank': 3}, {'goodsId': 730664, 'backCategoryId': 7864, 'companyId': 2, 'dimension': 'REAL_COUNT', 'rank': 3}, {'goodsId': 730664, 'backCategoryId': 7864, 'companyId': 2, 'dimension': 'PRAISE_STEP', 'rank': 3}], 'createTime': '2025-01-24 11:14:40', 'afterSalesBannedInfo': None, 'sellerLocalPsDto': {'sellerId': 20743, 'schemaId': None, 'startTime': None, 'endTime': None, 'open': True}, 'canGetCoupon': False, 'isolationPrice': -1, 'couponGoodsDTO': None, 'strengthStore': None, 'goodsLabel': None, 'goodsFeatureList': None, 'dispatchTimeAndBatchDTO': None, 'trafficPositionType': None, 'calcSortScore': None, 'goodsStage': None, 'opSortScore': 11026, 'goodsUnpackDetailDTO': None, 'unpackInfoDTO': None, 'planUpTimeHour': None, 'upDayNum': 26, 'increaseScale': -1}, {'id': 728387, 'goodsName': '山东黄金维纳斯苹果75#10斤B级【水果礼盒】', 'babyName': '山东黄金维纳斯苹果75#10斤B级【水果礼盒】', 'skuCode': '6800069040226', 'specification': '约10斤', 'unit': 'PIECE', 'volume': None, 'netWeight': 9.6, 'grossWeight': 10.0, 'status': 'ALREADY_UP', 'upTime': '2025-02-18 19:40:33', 'sortNo': 666, 'standardPrice': -1, 'activityPrice': None, 'singleLimited': 50, 'totalLimited': None, 'attaListIcon': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1737687615528/75.jpeg', 'attachUrl': '/web/goodsImage/1737687615528/75.jpeg', 'snapshotVersion': 'c86b0b4e6a9c46849ce92d4027f6ec4a', 'initSaleNum': 0, 'aviableSaleNum': 322, 'lockSaleNum': 0, 'minSellingNum': None, 'multipleSale': None, 'alreadyBought': 0, 'endCountdown': None, 'planUpTime': None, 'planDownTime': None, 'planType': None, 'inquiryPriceTime': '2025-02-17 11:02:06', 'lastTimeSalePrice': -1, 'lastTimeUnitPriceCatty': -1, 'lastTimeInquiryPriceTime': '2025-02-17 10:58:57', 'lastTimeStandardPrice': -1, 'lastTimeActivityPrice': None, 'currentDate': '2025-02-19 17:50:13', 'displaySaleNum': 1, 'saleTagId': 4661, 'saleTagName': '流量位机会爆品主图标签', 'sellingPoint': '', 'companyId': 2, 'killed': 0, 'sellerId': 20743, 'sellerName': '十二楼果业', 'sellerType': 'BUSINESS', 'sellerNameExhibition': False, 'settlementType': 'STANDER', 'length': None, 'width': None, 'height': None, 'unitPriceCatty': -1, 'backCategoryId': 7864, 'attachUrlR': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1737687615528/75.jpeg', 'timeLimit': 'ONE_DAY', 'purchaseUserName': None, 'purchaseUserId': None, 'enablePlan': None, 'goodsType': 'ORDINARY', 'afterSaleDesc': '', 'crossedPrice': None, 'basketTypeId': None, 'backCategoryName': '官方精选', 'backCategoryCode': '/1/500/785/809/7864/', 'firstBackCategoryId': 500, 'intervalStatus': 'DISABLED', 'listingPeriod': '', 'materialLastTimeUpdateTime': '2025-01-24 11:00:37', 'downTime': None, 'sellingGoodsPoint': None, 'praiseNum': 0, 'stepNum': 0, 'monthSale': 28, 'sevenDayAfterSale': None, 'detailTagList': [{'id': 4870, 'tagName': '机会爆品', 'enabled': 1, 'tagDisplayType': 'TEXT', 'tagIconAttachUrl': None, 'tagIconAttachUrlH': None, 'countGoods': None, 'beginTime': None, 'endTime': None, 'goodsTagTypeEnum': 'SALE_DETAIL_TAG', 'companyId': None, 'hasTagRule': True}], 'decisionTag': None, 'behaviorTagDTO': None, 'mainTag': {'id': 4661, 'tagName': None, 'enabled': 1, 'tagDisplayType': 'ICON', 'tagIconAttachUrl': '/web/goodsImage/1723465699089/%E6%9C%BA%E4%BC%9A%E7%88%86%E5%93%81.png', 'tagIconAttachUrlH': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1723465699089/%E6%9C%BA%E4%BC%9A%E7%88%86%E5%93%81.png', 'countGoods': None, 'beginTime': None, 'endTime': None, 'goodsTagTypeEnum': None, 'companyId': 2, 'hasTagRule': False}, 'activityGoodsDto': {'activityName': '流量位爆品', 'skuCode': '6800069040226', 'goodsId': 728387, 'activityId': 59310, 'settlementType': 'STANDER', 'unitPriceCatty': -1, 'standardPrice': -1, 'crossedPrice': 29.9, 'originalUnitPriceCatty': None, 'originalStandardPrice': 29.9, 'singleLimited': 50, 'snapshotVersion': 'd06c6d0015694390b3f6a8617e89f14c', 'activityType': 'SECKILL', 'orderMethod': 'COMPLEX', 'startTime': '2025-02-19 10:00:56', 'endTime': '2025-02-20 00:10:00', 'status': 'PROCESS', 'grossWeight': 10.0, 'initSaleNum': 211, 'inventoryId': 4986026, 'totalLimited': 300, 'alreadySaleNum': 4, 'aviableSaleNum': 296, 'lockSaleNum': 0, 'platformShare': 0.0, 'activityTagName': '爆品', 'activityTag': None, 'deliveryDate': None, 'activityDesc': '流量位爆品', 'activityDisplayName': '流量位爆品', 'id': 4200144, 'commissionSuperposition': 'SUPERPOSITION_UP_DOWN', 'orderStartTime': None, 'orderEndTime': None, 'groupPurchaseItemDTO': None, 'reduceAmount': 30.9, 'reduceAmountRate': -0.03344481}, 'rank': None, 'rankInterval': None, 'firstUpTime': '2025-01-19 19:26:25', 'dcdpResultDTO': None, 'basketType': None, 'sellerSiphonCommissionRate': 5.5, 'goodsSiphonCommissionRate': 11.0, 'goodsIntervalList': None, 'allowDeliver': True, 'ifAfterSalesBanned': False, 'sellerBanned': False, 'hideTagDTO': {'categoryId': 7864, 'categoryName': '小众苹果', 'hideUnitPriceCatty': 'DISPLAY'}, 'afterSaleStatisticalCycle': None, 'saleArea': None, 'firstDiscountDTO': None, 'finalStandardPrice': 26.9, 'finalUnitPriceCatty': None, 'finalCrossedPrice': None, 'priceRegionGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceResult': {'goodsId': 728387, 'unitPriceCatty': 0.0, 'standardPrice': 26.9, 'crossedPrice': 29.9, 'commissionRatio': 11.0, 'goodsInterval': None, 'priceCalcReason': 'ACTIVITY', 'priceRegionGoodsSnapshotVersion': None, 'priceGroupGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceRegionCategoryCommissionSnapshotVersion': None, 'priceGroupGoodsCommissionSnapshotVersion': None, 'originCommissionRatio': 11.0, 'originUnitPriceCatty': None, 'originStandardPrice': 29.9, 'originCrossedPrice': None, 'activityStandardPrice': 29.9, 'activityUnitPriceCatty': None, 'commissionSuperposition': 'SUPERPOSITION_UP_DOWN', 'firstDiscountCrossedStandardPrice': None, 'firstDiscountCrossedUnitPriceCatty': None, 'freight': None, 'stageStoreFlag': 0}, 'goodsScoreTags': [{'goodsId': 728387, 'backCategoryId': 7864, 'companyId': 2, 'dimension': 'RE_BUY', 'rank': 1}, {'goodsId': 728387, 'backCategoryId': 7864, 'companyId': 2, 'dimension': 'PRAISE_STEP', 'rank': 3}], 'createTime': '2025-01-19 13:33:42', 'afterSalesBannedInfo': None, 'sellerLocalPsDto': {'sellerId': 20743, 'schemaId': None, 'startTime': None, 'endTime': None, 'open': True}, 'canGetCoupon': False, 'isolationPrice': -1, 'couponGoodsDTO': None, 'strengthStore': None, 'goodsLabel': None, 'goodsFeatureList': None, 'dispatchTimeAndBatchDTO': None, 'trafficPositionType': None, 'calcSortScore': None, 'goodsStage': None, 'opSortScore': 11026, 'goodsUnpackDetailDTO': None, 'unpackInfoDTO': None, 'planUpTimeHour': None, 'upDayNum': 31, 'increaseScale': -1}, {'id': 728648, 'goodsName': '桔乡里牌广西融安脆蜜金柑中果2.2斤A级【水果礼盒】', 'babyName': '桔乡里牌广西融安脆蜜金柑中果2.2斤A级【水果礼盒】', 'skuCode': '7938126264118', 'specification': '约2.2斤', 'unit': 'PIECE', 'volume': None, 'netWeight': 1.8, 'grossWeight': 2.2, 'status': 'ALREADY_UP', 'upTime': '2025-02-15 20:53:24', 'sortNo': 1, 'standardPrice': -1, 'activityPrice': None, 'singleLimited': 200, 'totalLimited': None, 'attaListIcon': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1735201737617/img_v3_02hu_7db24d00-43e2-4fe7-93a1-ff46d5109dag.jpg', 'attachUrl': '/web/goodsImage/1735201737617/img_v3_02hu_7db24d00-43e2-4fe7-93a1-ff46d5109dag.jpg', 'snapshotVersion': 'afc40a884e534f95acd7a1aea7d26027', 'initSaleNum': 0, 'aviableSaleNum': 51, 'lockSaleNum': 0, 'minSellingNum': None, 'multipleSale': None, 'alreadyBought': 0, 'endCountdown': None, 'planUpTime': None, 'planDownTime': None, 'planType': None, 'inquiryPriceTime': '2025-02-10 09:23:18', 'lastTimeSalePrice': -1, 'lastTimeUnitPriceCatty': -1, 'lastTimeInquiryPriceTime': '2025-02-04 11:19:01', 'lastTimeStandardPrice': -1, 'lastTimeActivityPrice': None, 'currentDate': '2025-02-19 17:50:13', 'displaySaleNum': 1, 'saleTagId': None, 'saleTagName': None, 'sellingPoint': '', 'companyId': 2, 'killed': 0, 'sellerId': 20743, 'sellerName': '十二楼果业', 'sellerType': 'BUSINESS', 'sellerNameExhibition': False, 'settlementType': 'STANDER', 'length': None, 'width': None, 'height': None, 'unitPriceCatty': -1, 'backCategoryId': 587, 'attachUrlR': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1735201737617/img_v3_02hu_7db24d00-43e2-4fe7-93a1-ff46d5109dag.jpg', 'timeLimit': 'ONE_DAY', 'purchaseUserName': None, 'purchaseUserId': None, 'enablePlan': None, 'goodsType': 'ORDINARY', 'afterSaleDesc': '', 'crossedPrice': None, 'basketTypeId': None, 'backCategoryName': '官方精选', 'backCategoryCode': '/1/500/513/516/587/', 'firstBackCategoryId': 500, 'intervalStatus': 'DISABLED', 'listingPeriod': '', 'materialLastTimeUpdateTime': '2025-01-27 19:08:12', 'downTime': None, 'sellingGoodsPoint': None, 'praiseNum': 3, 'stepNum': 0, 'monthSale': 112, 'sevenDayAfterSale': None, 'detailTagList': [], 'decisionTag': None, 'behaviorTagDTO': None, 'mainTag': None, 'activityGoodsDto': None, 'rank': None, 'rankInterval': None, 'firstUpTime': '2025-01-20 07:53:10', 'dcdpResultDTO': None, 'basketType': None, 'sellerSiphonCommissionRate': 5.5, 'goodsSiphonCommissionRate': 11.0, 'goodsIntervalList': None, 'allowDeliver': True, 'ifAfterSalesBanned': False, 'sellerBanned': False, 'hideTagDTO': {'categoryId': 587, 'categoryName': '金桔', 'hideUnitPriceCatty': 'DISPLAY'}, 'afterSaleStatisticalCycle': None, 'saleArea': None, 'firstDiscountDTO': None, 'finalStandardPrice': 26.28, 'finalUnitPriceCatty': None, 'finalCrossedPrice': None, 'priceRegionGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceResult': {'goodsId': 728648, 'unitPriceCatty': None, 'standardPrice': 26.28, 'crossedPrice': None, 'commissionRatio': 11.0, 'goodsInterval': None, 'priceCalcReason': 'ORIGIN', 'priceRegionGoodsSnapshotVersion': None, 'priceGroupGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceRegionCategoryCommissionSnapshotVersion': None, 'priceGroupGoodsCommissionSnapshotVersion': None, 'originCommissionRatio': 11.0, 'originUnitPriceCatty': None, 'originStandardPrice': 26.28, 'originCrossedPrice': None, 'activityStandardPrice': None, 'activityUnitPriceCatty': None, 'commissionSuperposition': 'SUPERPOSITION_UP_DOWN', 'firstDiscountCrossedStandardPrice': None, 'firstDiscountCrossedUnitPriceCatty': None, 'freight': None, 'stageStoreFlag': 0}, 'goodsScoreTags': [{'goodsId': 728648, 'backCategoryId': 587, 'companyId': 2, 'dimension': 'RE_BUY', 'rank': 1}], 'createTime': '2025-01-20 00:34:44', 'afterSalesBannedInfo': None, 'sellerLocalPsDto': {'sellerId': 20743, 'schemaId': None, 'startTime': None, 'endTime': None, 'open': True}, 'canGetCoupon': False, 'isolationPrice': -1, 'couponGoodsDTO': None, 'strengthStore': None, 'goodsLabel': None, 'goodsFeatureList': None, 'dispatchTimeAndBatchDTO': None, 'trafficPositionType': None, 'calcSortScore': None, 'goodsStage': None, 'opSortScore': 10002, 'goodsUnpackDetailDTO': None, 'unpackInfoDTO': None, 'planUpTimeHour': None, 'upDayNum': 30, 'increaseScale': -1}, {'id': 730666, 'goodsName': '黄胖子山东黄金维纳斯苹果75#3.5斤B级【水果礼盒】', 'babyName': '黄胖子山东黄金维纳斯苹果75#3.5斤B级【水果礼盒】', 'skuCode': '5078466169488', 'specification': '约3.5斤', 'unit': 'PIECE', 'volume': None, 'netWeight': 3.2, 'grossWeight': 3.5, 'status': 'ALREADY_UP', 'upTime': '2025-02-17 11:49:50', 'sortNo': 555, 'standardPrice': -1, 'activityPrice': None, 'singleLimited': 50, 'totalLimited': None, 'attaListIcon': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1737688670552/75.jpeg', 'attachUrl': '/web/goodsImage/1737688670552/75.jpeg', 'snapshotVersion': 'acbebbc1dcde43f6a29482f4a1f8ba4d', 'initSaleNum': 0, 'aviableSaleNum': 71, 'lockSaleNum': 0, 'minSellingNum': None, 'multipleSale': None, 'alreadyBought': 0, 'endCountdown': None, 'planUpTime': None, 'planDownTime': None, 'planType': None, 'inquiryPriceTime': '2025-02-17 11:49:50', 'lastTimeSalePrice': -1, 'lastTimeUnitPriceCatty': -1, 'lastTimeInquiryPriceTime': '2025-02-15 11:44:55', 'lastTimeStandardPrice': -1, 'lastTimeActivityPrice': None, 'currentDate': '2025-02-19 17:50:13', 'displaySaleNum': 1, 'saleTagId': None, 'saleTagName': None, 'sellingPoint': None, 'companyId': 2, 'killed': 0, 'sellerId': 20743, 'sellerName': '十二楼果业', 'sellerType': 'BUSINESS', 'sellerNameExhibition': False, 'settlementType': 'STANDER', 'length': None, 'width': None, 'height': None, 'unitPriceCatty': -1, 'backCategoryId': 7864, 'attachUrlR': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1737688670552/75.jpeg', 'timeLimit': 'ONE_DAY', 'purchaseUserName': None, 'purchaseUserId': None, 'enablePlan': None, 'goodsType': 'ORDINARY', 'afterSaleDesc': '', 'crossedPrice': None, 'basketTypeId': None, 'backCategoryName': '官方精选', 'backCategoryCode': '/1/500/785/809/7864/', 'firstBackCategoryId': 500, 'intervalStatus': 'DISABLED', 'listingPeriod': '', 'materialLastTimeUpdateTime': '2025-01-24 11:28:12', 'downTime': None, 'sellingGoodsPoint': None, 'praiseNum': 0, 'stepNum': 0, 'monthSale': 47, 'sevenDayAfterSale': None, 'detailTagList': [], 'decisionTag': None, 'behaviorTagDTO': None, 'mainTag': None, 'activityGoodsDto': None, 'rank': None, 'rankInterval': None, 'firstUpTime': '2025-01-24 17:12:28', 'dcdpResultDTO': None, 'basketType': None, 'sellerSiphonCommissionRate': 5.5, 'goodsSiphonCommissionRate': 11.0, 'goodsIntervalList': None, 'allowDeliver': True, 'ifAfterSalesBanned': False, 'sellerBanned': False, 'hideTagDTO': {'categoryId': 7864, 'categoryName': '小众苹果', 'hideUnitPriceCatty': 'DISPLAY'}, 'afterSaleStatisticalCycle': None, 'saleArea': None, 'firstDiscountDTO': None, 'finalStandardPrice': 22.9, 'finalUnitPriceCatty': None, 'finalCrossedPrice': None, 'priceRegionGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceResult': {'goodsId': 730666, 'unitPriceCatty': None, 'standardPrice': 22.9, 'crossedPrice': None, 'commissionRatio': 11.0, 'goodsInterval': None, 'priceCalcReason': 'ORIGIN', 'priceRegionGoodsSnapshotVersion': None, 'priceGroupGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceRegionCategoryCommissionSnapshotVersion': None, 'priceGroupGoodsCommissionSnapshotVersion': None, 'originCommissionRatio': 11.0, 'originUnitPriceCatty': None, 'originStandardPrice': 22.9, 'originCrossedPrice': None, 'activityStandardPrice': None, 'activityUnitPriceCatty': None, 'commissionSuperposition': 'SUPERPOSITION_UP_DOWN', 'firstDiscountCrossedStandardPrice': None, 'firstDiscountCrossedUnitPriceCatty': None, 'freight': None, 'stageStoreFlag': 0}, 'goodsScoreTags': [{'goodsId': 730666, 'backCategoryId': 7864, 'companyId': 2, 'dimension': 'RE_BUY', 'rank': 1}, {'goodsId': 730666, 'backCategoryId': 7864, 'companyId': 2, 'dimension': 'PRAISE_STEP', 'rank': 3}], 'createTime': '2025-01-24 11:18:27', 'afterSalesBannedInfo': None, 'sellerLocalPsDto': {'sellerId': 20743, 'schemaId': None, 'startTime': None, 'endTime': None, 'open': True}, 'canGetCoupon': False, 'isolationPrice': -1, 'couponGoodsDTO': None, 'strengthStore': None, 'goodsLabel': None, 'goodsFeatureList': None, 'dispatchTimeAndBatchDTO': None, 'trafficPositionType': None, 'calcSortScore': None, 'goodsStage': None, 'opSortScore': 10002, 'goodsUnpackDetailDTO': None, 'unpackInfoDTO': None, 'planUpTimeHour': None, 'upDayNum': 26, 'increaseScale': -1}, {'id': 677391, 'goodsName': '亿口乐蓝靛果蓝莓纯汁1箱20袋90ml', 'babyName': '亿口乐蓝靛果蓝莓纯汁1箱20袋90ml', 'skuCode': '7556711728723', 'specification': '20袋90ml', 'unit': 'PIECE', 'volume': None, 'netWeight': 4.0, 'grossWeight': 4.5, 'status': 'ALREADY_UP', 'upTime': '2025-01-23 16:51:23', 'sortNo': 100, 'standardPrice': -1, 'activityPrice': None, 'singleLimited': 10, 'totalLimited': None, 'attaListIcon': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1732184246463/微信图片_20241121175313.jpg', 'attachUrl': '/web/goodsImage/1732184246463/微信图片_20241121175313.jpg', 'snapshotVersion': 'd407049086fb4b04a78f6a67327bd522', 'initSaleNum': 0, 'aviableSaleNum': 24, 'lockSaleNum': 0, 'minSellingNum': None, 'multipleSale': None, 'alreadyBought': 0, 'endCountdown': None, 'planUpTime': None, 'planDownTime': None, 'planType': None, 'inquiryPriceTime': '2025-01-23 16:51:23', 'lastTimeSalePrice': -1, 'lastTimeUnitPriceCatty': -1, 'lastTimeInquiryPriceTime': '2024-11-26 14:40:33', 'lastTimeStandardPrice': -1, 'lastTimeActivityPrice': None, 'currentDate': '2025-02-19 17:50:13', 'displaySaleNum': 1, 'saleTagId': 4838, 'saleTagName': '奶新', 'sellingPoint': None, 'companyId': 2, 'killed': 0, 'sellerId': 20743, 'sellerName': '十二楼果业', 'sellerType': 'BUSINESS', 'sellerNameExhibition': False, 'settlementType': 'STANDER', 'length': None, 'width': None, 'height': None, 'unitPriceCatty': -1, 'backCategoryId': 4582, 'attachUrlR': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1732184246463/微信图片_20241121175313.jpg', 'timeLimit': 'ONE_DAY', 'purchaseUserName': None, 'purchaseUserId': None, 'enablePlan': None, 'goodsType': 'ORDINARY', 'afterSaleDesc': '', 'crossedPrice': None, 'basketTypeId': None, 'backCategoryName': '官方精选', 'backCategoryCode': '/1/549/600/760/4582/', 'firstBackCategoryId': 549, 'intervalStatus': 'DISABLED', 'listingPeriod': '', 'materialLastTimeUpdateTime': '2024-11-26 14:40:33', 'downTime': None, 'sellingGoodsPoint': None, 'praiseNum': 0, 'stepNum': 0, 'monthSale': 5, 'sevenDayAfterSale': None, 'detailTagList': [], 'decisionTag': None, 'behaviorTagDTO': None, 'mainTag': {'id': 4838, 'tagName': None, 'enabled': 1, 'tagDisplayType': 'ICON', 'tagIconAttachUrl': '/web/goodsImage/1721717517689/img_v2_4bb0dfd0-2b20-437a-ab73-76c3d7f16beg.png', 'tagIconAttachUrlH': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1721717517689/img_v2_4bb0dfd0-2b20-437a-ab73-76c3d7f16beg.png', 'countGoods': None, 'beginTime': None, 'endTime': None, 'goodsTagTypeEnum': None, 'companyId': 2004, 'hasTagRule': False}, 'activityGoodsDto': None, 'rank': None, 'rankInterval': None, 'firstUpTime': '2024-12-04 18:13:25', 'dcdpResultDTO': None, 'basketType': None, 'sellerSiphonCommissionRate': 5.5, 'goodsSiphonCommissionRate': 6.0, 'goodsIntervalList': None, 'allowDeliver': True, 'ifAfterSalesBanned': False, 'sellerBanned': False, 'hideTagDTO': {'categoryId': 4582, 'categoryName': '果蔬汁饮料', 'hideUnitPriceCatty': 'HIDE'}, 'afterSaleStatisticalCycle': None, 'saleArea': None, 'firstDiscountDTO': None, 'finalStandardPrice': 68.0, 'finalUnitPriceCatty': None, 'finalCrossedPrice': None, 'priceRegionGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceResult': {'goodsId': 677391, 'unitPriceCatty': None, 'standardPrice': 68.0, 'crossedPrice': None, 'commissionRatio': 6.0, 'goodsInterval': None, 'priceCalcReason': 'ORIGIN', 'priceRegionGoodsSnapshotVersion': None, 'priceGroupGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceRegionCategoryCommissionSnapshotVersion': None, 'priceGroupGoodsCommissionSnapshotVersion': None, 'originCommissionRatio': 6.0, 'originUnitPriceCatty': None, 'originStandardPrice': 68.0, 'originCrossedPrice': None, 'activityStandardPrice': None, 'activityUnitPriceCatty': None, 'commissionSuperposition': 'SUPERPOSITION_UP_DOWN', 'firstDiscountCrossedStandardPrice': None, 'firstDiscountCrossedUnitPriceCatty': None, 'freight': None, 'stageStoreFlag': 0}, 'goodsScoreTags': [{'goodsId': 677391, 'backCategoryId': 4582, 'companyId': 2, 'dimension': 'AFTER_SUBMIT_RATE', 'rank': 1}, {'goodsId': 677391, 'backCategoryId': 4582, 'companyId': 2, 'dimension': 'RE_BUY', 'rank': 1}, {'goodsId': 677391, 'backCategoryId': 4582, 'companyId': 2, 'dimension': 'PRAISE_STEP', 'rank': 1}, {'goodsId': 677391, 'backCategoryId': 4582, 'companyId': 2, 'dimension': 'TOTAL', 'rank': 2}, {'goodsId': 677391, 'backCategoryId': 4582, 'companyId': 2, 'dimension': 'REAL_COUNT', 'rank': 2}], 'createTime': '2024-11-21 18:18:36', 'afterSalesBannedInfo': None, 'sellerLocalPsDto': {'sellerId': 20743, 'schemaId': None, 'startTime': None, 'endTime': None, 'open': True}, 'canGetCoupon': False, 'isolationPrice': -1, 'couponGoodsDTO': None, 'strengthStore': None, 'goodsLabel': None, 'goodsFeatureList': None, 'dispatchTimeAndBatchDTO': None, 'trafficPositionType': None, 'calcSortScore': None, 'goodsStage': None, 'opSortScore': 10001, 'goodsUnpackDetailDTO': None, 'unpackInfoDTO': None, 'planUpTimeHour': None, 'upDayNum': 77, 'increaseScale': -1}, {'id': 708133, 'goodsName': '桔乡里牌广西融安滑皮金桔中果6.2斤A级（6合1）', 'babyName': '桔乡里牌广西融安滑皮金桔中果6.2斤A级（6合1）', 'skuCode': '3426534624603', 'specification': '约2.2斤', 'unit': 'PIECE', 'volume': None, 'netWeight': 6.0, 'grossWeight': 6.2, 'status': 'ALREADY_UP', 'upTime': '2025-02-10 09:18:40', 'sortNo': 1, 'standardPrice': -1, 'activityPrice': None, 'singleLimited': 50, 'totalLimited': None, 'attaListIcon': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1735202145929/img_v3_02hu_0a2229d4-2ab5-4e19-8171-1c8004459e7g.jpg', 'attachUrl': '/web/goodsImage/1735202145929/img_v3_02hu_0a2229d4-2ab5-4e19-8171-1c8004459e7g.jpg', 'snapshotVersion': '1ae71565b2be4864ac2b45e645e5cb89', 'initSaleNum': 0, 'aviableSaleNum': 0, 'lockSaleNum': 0, 'minSellingNum': None, 'multipleSale': None, 'alreadyBought': 0, 'endCountdown': None, 'planUpTime': None, 'planDownTime': None, 'planType': None, 'inquiryPriceTime': '2025-02-10 09:18:40', 'lastTimeSalePrice': -1, 'lastTimeUnitPriceCatty': -1, 'lastTimeInquiryPriceTime': '2025-02-10 09:18:23', 'lastTimeStandardPrice': -1, 'lastTimeActivityPrice': None, 'currentDate': '2025-02-19 17:50:13', 'displaySaleNum': 1, 'saleTagId': 5036, 'saleTagName': '融合4', 'sellingPoint': None, 'companyId': 2, 'killed': 0, 'sellerId': 20743, 'sellerName': '十二楼果业', 'sellerType': 'BUSINESS', 'sellerNameExhibition': False, 'settlementType': 'STANDER', 'length': None, 'width': None, 'height': None, 'unitPriceCatty': -1, 'backCategoryId': 587, 'attachUrlR': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1735202145929/img_v3_02hu_0a2229d4-2ab5-4e19-8171-1c8004459e7g.jpg', 'timeLimit': 'ONE_DAY', 'purchaseUserName': None, 'purchaseUserId': None, 'enablePlan': None, 'goodsType': 'ORDINARY', 'afterSaleDesc': '', 'crossedPrice': None, 'basketTypeId': None, 'backCategoryName': '官方精选', 'backCategoryCode': '/1/500/513/516/587/', 'firstBackCategoryId': 500, 'intervalStatus': 'DISABLED', 'listingPeriod': '', 'materialLastTimeUpdateTime': '2025-01-21 18:05:01', 'downTime': None, 'sellingGoodsPoint': None, 'praiseNum': 2, 'stepNum': 0, 'monthSale': 5, 'sevenDayAfterSale': None, 'detailTagList': [], 'decisionTag': None, 'behaviorTagDTO': None, 'mainTag': {'id': 5036, 'tagName': None, 'enabled': 1, 'tagDisplayType': 'ICON', 'tagIconAttachUrl': '/web/goodsImage/1734278587274/%E4%BB%8A%E6%97%A5%E7%89%B9%E4%BB%B7.png', 'tagIconAttachUrlH': 'https://ugc-pro.biaoguoworks.com/web/goodsImage/1734278587274/%E4%BB%8A%E6%97%A5%E7%89%B9%E4%BB%B7.png', 'countGoods': None, 'beginTime': None, 'endTime': None, 'goodsTagTypeEnum': None, 'companyId': 2, 'hasTagRule': False}, 'activityGoodsDto': None, 'rank': None, 'rankInterval': None, 'firstUpTime': '2024-12-26 19:57:56', 'dcdpResultDTO': None, 'basketType': None, 'sellerSiphonCommissionRate': 5.5, 'goodsSiphonCommissionRate': 11.0, 'goodsIntervalList': None, 'allowDeliver': True, 'ifAfterSalesBanned': False, 'sellerBanned': False, 'hideTagDTO': {'categoryId': 587, 'categoryName': '金桔', 'hideUnitPriceCatty': 'DISPLAY'}, 'afterSaleStatisticalCycle': None, 'saleArea': None, 'firstDiscountDTO': None, 'finalStandardPrice': 63.0, 'finalUnitPriceCatty': None, 'finalCrossedPrice': None, 'priceRegionGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceResult': {'goodsId': 708133, 'unitPriceCatty': None, 'standardPrice': 63.0, 'crossedPrice': None, 'commissionRatio': 11.0, 'goodsInterval': None, 'priceCalcReason': 'ORIGIN', 'priceRegionGoodsSnapshotVersion': None, 'priceGroupGoodsSnapshotVersion': None, 'priceRegionGoodsCommissionSnapshotVersion': None, 'priceRegionCategoryCommissionSnapshotVersion': None, 'priceGroupGoodsCommissionSnapshotVersion': None, 'originCommissionRatio': 11.0, 'originUnitPriceCatty': None, 'originStandardPrice': 63.0, 'originCrossedPrice': None, 'activityStandardPrice': None, 'activityUnitPriceCatty': None, 'commissionSuperposition': 'SUPERPOSITION_UP_DOWN', 'firstDiscountCrossedStandardPrice': None, 'firstDiscountCrossedUnitPriceCatty': None, 'freight': None, 'stageStoreFlag': 0}, 'goodsScoreTags': [{'goodsId': 708133, 'backCategoryId': 587, 'companyId': 2, 'dimension': 'RE_BUY', 'rank': 1}], 'createTime': '2024-12-26 17:20:13', 'afterSalesBannedInfo': None, 'sellerLocalPsDto': {'sellerId': 20743, 'schemaId': None, 'startTime': None, 'endTime': None, 'open': True}, 'canGetCoupon': False, 'isolationPrice': -1, 'couponGoodsDTO': None, 'strengthStore': None, 'goodsLabel': None, 'goodsFeatureList': None, 'dispatchTimeAndBatchDTO': None, 'trafficPositionType': None, 'calcSortScore': None, 'goodsStage': None, 'opSortScore': 10000, 'goodsUnpackDetailDTO': None, 'unpackInfoDTO': None, 'planUpTimeHour': None, 'upDayNum': 55, 'increaseScale': -1}]\n"]}], "source": ["def get_products_for_category(categoryId=5745, current=1):\n", "    url='https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods'\n", "    data={\n", "        \"size\": 20,\n", "        \"current\": current,\n", "        \"categoryId\": categoryId,\n", "        \"goodsSourceType\": 0,\n", "        \"searchSortType\": \"COMPREHENSIVE\",\n", "        \"goodsSaleTagId\": \"\",\n", "        \"propertyValueIds\": []\n", "    }\n", "    return get_remote_data_with_proxy_json(url, headers=headers, json=data)['content']['records']\n", "print(get_products_for_category(16338))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_products_for_category(categoryId=5745, current=1):\n", "    url='https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods'\n", "    data={\n", "        \"size\": 20,\n", "        \"current\": current,\n", "        \"categoryId\": categoryId,\n", "        \"goodsSourceType\": 0,\n", "        \"searchSortType\": \"COMPREHENSIVE\",\n", "        \"goodsSaleTagId\": \"\",\n", "        \"propertyValueIds\": []\n", "    }\n", "    return get_remote_data_with_proxy_json(url, headers=headers, json=data)['content']['records']\n", "print(get_products_for_category())\n", "product_list_all=[]\n", "\n", "testing=1\n", "\n", "for category in category_list['content']:\n", "    testing=testing-1\n", "    categoryLevel=category['categoryLevel']\n", "    goodsCount=category[\"goodsCount\"]\n", "    categoryId=category['id']\n", "    categoryName=category[\"categoryName\"]\n", "    print(f'{categoryName}, categoryId:{categoryId}, goodsCount:{goodsCount}')\n", "    if categoryLevel != 3:\n", "        print(f\"非叶子类目:{categoryLevel}, categoryName:{categoryName}\")\n", "        continue\n", "    size=0\n", "    current=1\n", "    while size<goodsCount:\n", "        print(f\"current:{current}, size:{size}, category:{categoryName}-{categoryId}-level:{categoryLevel}\")\n", "        sub_list=get_products_for_category(categoryId=categoryId, current=current)\n", "        current=current+1\n", "        if sub_list is None or len(sub_list)<=0:\n", "            break\n", "        size=size+len(sub_list)\n", "        print(f\"{categoryName}:{sub_list[0]}\")\n", "        product_list_all.extend(sub_list)\n", "    if testing<=0:\n", "        break\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["product_list_all_df=pd.DataFrame(product_list_all)\n", "print(product_list_all[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["product_list_all_df[product_list_all_df['standardPrice']>0][['lastTimeSalePrice','babyName','standardPrice','activityPrice','skuCode']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "product_list_all_df['competitor']=brand_name\n", "all_products_df=product_list_all_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import pandas as pd\n", "\n", "# headers = {\n", "#     \"content-type\": \"application\\/json;charset=utf-8\",\n", "#     \"X-Select-Supply-Batch-Id\": \"\",\n", "#     \"Referer\": \"https:\\/\\/servicewechat.com\\/wxb5148e64929a9e26\\/576\\/page-frame.html\",\n", "#     \"token\": \"61a12d1623e141c9bae19b3d1dbff6f0\",\n", "#     \"Accept-Encoding\": \"gzip,compress,br,deflate\",\n", "#     \"Host\": \"demeter-api.biaoguoworks.com\",\n", "#     \"User-Agent\": \"Mozilla\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\/605.1.15 (KHTML, like Gecko) Mobile\\/15E148 MicroMessenger\\/8.0.50(0x18003237) NetType\\/4G Language\\/zh_CN\",\n", "#     \"sid\": \"9904903\",\n", "#     \"Connection\": \"keep-alive\",\n", "#     \"_wechatAppId\": \"wxb5148e64929a9e26\",\n", "#     \"Content-Length\": \"136\",\n", "#     \"Accept\": \"*\\/*\",\n", "# }\n", "\n", "url = \"https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods\"\n", "\n", "category_list = requests.post(\n", "    url=\"https://demeter-api.biaoguoworks.com/leechee/api/h5/store/front-categorys\",\n", "    timeout=15,\n", ").json().get('content',[])\n", "category_list_df=pd.DataFrame(category_list)\n", "category_list_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["category_list_df[['categoryName','leaf','categoryLevel','parentId','id']].head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["level3_category_list_df = category_list_df[category_list_df[\"categoryLevel\"] == 3]\n", "level3_category_list_df = pd.merge(\n", "    left=level3_category_list_df,\n", "    right=category_list_df[[\"categoryName\", \"id\", \"parentId\"]],\n", "    left_on=\"parentId\",\n", "    right_on=\"id\",\n", "    suffixes=[\"\", \"_lv2\"],\n", ")\n", "level3_category_list_df = pd.merge(\n", "    left=level3_category_list_df,\n", "    right=category_list_df[[\"categoryName\", \"id\", \"parentId\"]],\n", "    left_on=\"parentId_lv2\",\n", "    right_on=\"id\",\n", "    suffixes=[\"\", \"_lv1\"],\n", ")\n", "\n", "level3_category_list_df[\"categoryName\"] = level3_category_list_df.apply(\n", "    lambda row: f\"{row['categoryName_lv1']}_{row['categoryName_lv2']}_{row['categoryName']}\",\n", "    axis=1,\n", ")\n", "\n", "level3_category_list_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}
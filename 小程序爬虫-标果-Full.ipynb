{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/标果/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "\n", "headers={'token':'d962d0ba06514ffa8b80a335d851563f',\n", "'sid':'7731297',\n", "'time':'1702521175012'}\n", "brand_name='标果'\n", "competitor_name_en='biaoguo'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, headers:{headers}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def post_remote_data_with_proxy(url, json):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, json=json, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response.text\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["爬取分类信息"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["category_list=json.loads(post_remote_data_with_proxy(\"https://demeter-api.biaoguoworks.com/leechee/api/h5/store/front-categorys\", json={}))\n", "category_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["爬取商品信息"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_products_for_category(categoryId=5745, current=1):\n", "    url='https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods'\n", "    data={\n", "        \"size\": 20,\n", "        \"current\": current,\n", "        \"categoryId\": categoryId,\n", "        \"goodsSourceType\": 0,\n", "        \"searchSortType\": \"COMPREHENSIVE\",\n", "        \"goodsSaleTagId\": \"\",\n", "        \"propertyValueIds\": []\n", "    }\n", "    return json.loads(post_remote_data_with_proxy(url, json=data))['content']['records']\n", "print(get_products_for_category())\n", "product_list_all=[]\n", "for category in category_list['content']:\n", "    categoryLevel=category['categoryLevel']\n", "    goodsCount=category[\"goodsCount\"]\n", "    categoryId=category['id']\n", "    categoryName=category[\"categoryName\"]\n", "    print(f'{categoryName}, categoryId:{categoryId}, goodsCount:{goodsCount}')\n", "    if categoryLevel != 3:\n", "        print(f\"非叶子类目:{categoryLevel}, categoryName:{categoryName}\")\n", "        continue\n", "    size=0\n", "    current=1\n", "    while size<goodsCount:\n", "        print(f\"current:{current}, size:{size}, category:{categoryName}-{categoryId}-level:{categoryLevel}\")\n", "        sub_list=get_products_for_category(categoryId=categoryId, current=current)\n", "        current=current+1\n", "        if sub_list is None or len(sub_list)<=0:\n", "            break\n", "        size=size+len(sub_list)\n", "        print(f\"{categoryName}:{sub_list[0]}\")\n", "        product_list_all.extend(sub_list)\n", "\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(product_list_all_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_cate_list['title_embedding']=df_cate_list['goodsName'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}
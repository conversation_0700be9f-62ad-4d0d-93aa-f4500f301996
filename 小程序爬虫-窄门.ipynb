{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 写入odps\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"system\": \"ios\",\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x1800312b) NetType/4G Language/zh_CN\",\n", "    \"version\": \"1.4.08\",\n", "    \"identity\": \"0\",\n", "    \"Connection\": \"keep-alive\",\n", "    \"Host\": \"fa.kaoputou.com\",\n", "    \"sessionId\": \"wx_c2f5786e51ffff6495477c1b9d2b6880\",\n", "    # \"sessionId\": \"wx_c5c00169a6e4c3282eab63b649ababcd\",\n", "    \"Content-Type\": \"application/json\",\n", "    \"channel\": \"weapp\",\n", "    \"Accept-Encoding\": \"gzip,compress,br,deflate\",\n", "    \"Referer\": \"https://servicewechat.com/wx1f7b9a5ba245f136/389/page-frame.html\",\n", "}\n", "brand_name = \"窄门\"\n", "competitor_name_en = \"zhaimen\"\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_all_brands_of_zhaimen(page=1):\n", "    url = f\"https://fa.kaoputou.com/api/hot-brands?page={page}\"\n", "    result=get_remote_data_with_proxy_json(url=url, headers=headers)\n", "    records=[]\n", "    if result is not None:\n", "        print(f\"page:{page}, brands:{len(result['records'])}\")\n", "        records.extend(result['records'])\n", "        if result[\"hasNext\"]:\n", "            # has more data:\n", "            records.extend(get_all_brands_of_zhaimen(page=page+1))\n", "    return records\n", "\n", "all_brands=get_all_brands_of_zhaimen()\n", "all_brands_df=pd.DataFrame(all_brands)\n", "all_brands_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_brand_detail_by_id(id=88533840):\n", "    url=f\"https://fa.kaoputou.com/api/brand/{id}\"\n", "    product_detail=get_remote_data_with_proxy_json(url=url, headers=headers)\n", "    return product_detail\n", "\n", "all_brand_detail=[]\n", "for brand in all_brands:\n", "    detail=get_brand_detail_by_id(brand['slug'])\n", "    print(detail)\n", "    if detail is None or 'error' in detail:\n", "        print(f\"也许是被封了:{detail}\")\n", "        break\n", "    brand.update(detail)\n", "    all_brand_detail.append(brand)\n", "\n", "all_brand_detail_df=pd.DataFrame(all_brand_detail)\n", "all_brand_detail_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "all_brand_set = set()\n", "all_related_brand_set = set()\n", "all_related_brand_list=[]\n", "for brand in all_brand_detail:\n", "    all_brand_set.add(brand[\"slug\"])\n", "    # print(f\"{brand['slug']}, {brand['relatedBrands']}\")\n", "    # relatedBrands=json.loads(brand['relatedBrands'])\n", "    for related in brand[\"relatedBrands\"]:\n", "        new_slug = related[\"slug\"]\n", "        if new_slug in all_related_brand_set and new_slug in all_brand_set:\n", "            print(new_slug)\n", "            continue\n", "        else:\n", "            all_related_brand_list.append(related)\n", "            all_related_brand_set.add(new_slug)\n", "            all_brand_set.add(new_slug)\n", "\n", "print(f\"补充：{len(all_related_brand_set)}\")\n", "\n", "all_related_brand_detail = []\n", "for brand in all_related_brand_list:\n", "    detail = get_brand_detail_by_id(brand['slug'])\n", "    print(detail)\n", "    if detail is None or \"error\" in detail:\n", "        print(f\"也许是被封了:{detail}\")\n", "        break\n", "    brand.update(detail)\n", "    all_brand_detail.append(brand)\n", "\n", "all_related_brand_detail_df = pd.DataFrame(all_related_brand_detail)\n", "all_brand_detail_df.head(5)\n", "\n", "# Concatenate the dataframes\n", "merged_df = pd.concat([all_related_brand_detail_df, all_brand_detail_df])\n", "\n", "# Display the first few rows of the merged DataFrame\n", "merged_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "\n", "all_sku_list_df=merged_df\n", "# 写入odps\n", "all_sku_list_df['competitor']=brand_name\n", "all_products_df=all_sku_list_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"success\":false}\n", "\n"]}], "source": ["import requests\n", "\n", "h = {\n", "    \"Host\": \"fa.kaoputou.com\",\n", "    \"Accept\": \"application\\/json, text\\/plain, *\\/*\",\n", "    \"Connection\": \"keep-alive\",\n", "    \"sessionId\": \"mb_d93f284a67a153cc0fd9b00d14585dff\",\n", "    \"Accept-Encoding\": \"gzip, deflate, br\",\n", "    \"clientId\": \"fd52b5a81ccdb602d86c02f2eef1980c\",\n", "    \"responseType\": \"json\",\n", "    \"version\": \"1.8.3\",\n", "    \"channel\": \"app\",\n", "    \"system\": \"ios\",\n", "    \"Accept-Language\": \"en-US,en;q=0.9\",\n", "    \"identity\": \"0\",\n", "    \"Content-Type\": \"application\\/json\",\n", "    \"User-Agent\": \"Mozilla\\/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit\\/605.1.15 (KHTML, like Gecko) Mobile\\/15E148 Html5Plus\\/1.0 (Immersed\\/59) uni-app\",\n", "}\n", "\n", "r=requests.get(\"https://fa.kaoputou.com/api/brand/19366752\", headers=h).text\n", "\n", "print(r)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
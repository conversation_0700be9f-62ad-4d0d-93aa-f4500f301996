#!/bin/bash

# 手动检查爬虫进程状态的工具脚本

echo "🔍 检查当前爬虫进程状态..."

# 查找最新的临时目录
PID_DIR=$(ls -td ./temp_pids_* 2>/dev/null | head -1)
LOG_DIR=$(ls -td ./temp_logs_* 2>/dev/null | head -1)
START_TIME_DIR=$(ls -td ./temp_start_times_* 2>/dev/null | head -1)

if [ -z "$PID_DIR" ] || [ ! -d "$PID_DIR" ]; then
    echo "❌ 没有找到运行中的爬虫任务"
    exit 1
fi

echo "📂 使用目录: $PID_DIR"
echo ""

current_time=$(date +%s)
running_count=0
suspicious_count=0

# 检查进程是否存活
is_process_alive() {
    local pid="$1"
    if [ -z "$pid" ] || [ "$pid" = "0" ]; then
        return 1
    fi
    kill -0 "$pid" 2>/dev/null
}

echo "📋 当前运行的任务:"
echo "----------------------------------------"

for pid_file in "$PID_DIR"/*.pid; do
    if [ -f "$pid_file" ]; then
        pid=$(cat "$pid_file" 2>/dev/null || echo "")
        task_name=$(basename "$pid_file" .pid | sed 's/_spider\.py$//' | sed 's/\.py$//')
        
        if is_process_alive "$pid"; then
            running_count=$((running_count + 1))
            
            # 计算运行时间
            start_time_file="$START_TIME_DIR/${task_name}.py.start"
            if [ -f "$start_time_file" ]; then
                start_time=$(cat "$start_time_file" 2>/dev/null || echo "$current_time")
                elapsed=$((current_time - start_time))
                elapsed_min=$((elapsed / 60))
                elapsed_sec=$((elapsed % 60))
                
                # 检查是否可疑（运行超过5分钟）
                if [ $elapsed -gt 300 ]; then
                    suspicious_count=$((suspicious_count + 1))
                    status="⚠️  可疑"
                else
                    status="✅ 正常"
                fi
                
                echo "$status $task_name (PID: $pid, 运行: ${elapsed_min}分${elapsed_sec}秒)"
                
                # 显示进程详细信息
                if command -v ps >/dev/null 2>&1; then
                    ps_info=$(ps -p "$pid" -o state,pcpu,pmem,command 2>/dev/null | tail -1)
                    if [ -n "$ps_info" ]; then
                        echo "     进程信息: $ps_info"
                    fi
                fi
                
                # 检查日志文件
                log_file="$LOG_DIR/${task_name}.py.log"
                if [ -f "$log_file" ]; then
                    log_size=$(wc -l < "$log_file" 2>/dev/null || echo "0")
                    log_age=$((current_time - $(stat -f %m "$log_file" 2>/dev/null || echo "$current_time")))
                    echo "     日志: ${log_size}行, ${log_age}秒前更新"
                    
                    # 显示最后几行日志
                    echo "     最后输出:"
                    tail -2 "$log_file" 2>/dev/null | sed 's/^/       /' || echo "       无法读取日志"
                else
                    echo "     ❌ 日志文件不存在"
                fi
                echo ""
            else
                echo "⚠️  $task_name (PID: $pid, 无开始时间记录)"
                echo ""
            fi
        else
            echo "💀 $task_name (PID: $pid, 进程已死亡)"
            echo ""
        fi
    fi
done

echo "----------------------------------------"
echo "📊 统计信息:"
echo "   运行中任务: $running_count"
echo "   可疑任务: $suspicious_count"

if [ $suspicious_count -gt 0 ]; then
    echo ""
    echo "💡 建议:"
    echo "   - 检查可疑任务的日志文件"
    echo "   - 考虑手动终止长时间无响应的任务"
    echo "   - 使用 kill -9 <PID> 强制终止问题进程"
fi

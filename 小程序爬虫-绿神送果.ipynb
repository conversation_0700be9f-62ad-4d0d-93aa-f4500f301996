{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 写入odps\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json,logging\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN\",\n", "}\n", "brand_name = \"绿神送果-东莞\"\n", "competitor_name_en = \"lvshensongguo\"\n", "\n", "logging.info(\"即将爬取:%s, %s\", brand_name, competitor_name_en)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import pymysql\n", "# import os\n", "\n", "# mysql_host = os.getenv(\"COSFODB_HOST_NAME\", \"mysql-8-public.summerfarm.net\")\n", "# logging.info(f'using mysql host:{mysql_host}')\n", "\n", "# # Function to establish a database connection\n", "# def get_data_from_mysql(query: str = \"\"):\n", "#     conn = pymysql.connect(\n", "#         host=mysql_host,\n", "#         user=\"test\",\n", "#         password=\"xianmu619\",\n", "#         port=3307,\n", "#         db=\"front_db\",\n", "#         charset=\"utf8mb4\",\n", "#         cursorclass=pymysql.cursors.DictCursor,\n", "#     )\n", "#     try:\n", "#         with conn.cursor() as cursor:\n", "#             cursor.execute(query)\n", "#             rows = cursor.fetchall()\n", "#             return rows\n", "#     except Exception as e:\n", "#         logging.error(f\"从数据库获取登录token失败:{e}\")\n", "#         raise e\n", "#     finally:\n", "#         conn.close()\n", "\n", "\n", "# query = \"select * from app_req_record where app_name = 'baofeng' limit 50\"\n", "# req_info_list = get_data_from_mysql(query)\n", "# logging.info(f\"data from mysql:{req_info_list}\")\n", "# if len(req_info_list) <= 0:\n", "#     raise Exception(f\"未能从数据库获取到登录信息,SQL:{query}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-08-02 10:24:13 - INFO - 随机的使用一个代理IP:**************:45745\n", "2024-08-02 10:24:13 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategory, using proxy: http://***********:8gTcEKLs@**************:45745, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:24:13 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:45745\n"]}, {"data": {"text/plain": ["[{'param': '1,all', 'name': '全部', 'is_active': 1},\n", " {'param': '1,133', 'name': '乳制品', 'is_active': 0},\n", " {'param': '1,134', 'name': '糖', 'is_active': 0},\n", " {'param': '1,123', 'name': '冻品', 'is_active': 0},\n", " {'param': '1,108', 'name': '时令水果', 'is_active': 0},\n", " {'param': '1,107', 'name': '草莓|蓝莓', 'is_active': 0},\n", " {'param': '1,112', 'name': '桃|李|苹果|梨', 'is_active': 0},\n", " {'param': '1,106', 'name': '柠檬|金桔', 'is_active': 0},\n", " {'param': '1,105', 'name': '芒果', 'is_active': 0},\n", " {'param': '1,17', 'name': '瓜类', 'is_active': 0},\n", " {'param': '1,113', 'name': '提子|葡萄', 'is_active': 0},\n", " {'param': '1,120', 'name': '椰青', 'is_active': 0},\n", " {'param': '1,111', 'name': '橙|柑|橘', 'is_active': 0},\n", " {'param': '1,110', 'name': '火龙果', 'is_active': 0},\n", " {'param': '1,114', 'name': '菠萝|凤梨', 'is_active': 0},\n", " {'param': '1,115', 'name': '蔬菜', 'is_active': 0},\n", " {'param': '1,109', 'name': '奇异果|牛油果', 'is_active': 0},\n", " {'param': '1,116', 'name': '百香果|西柚', 'is_active': 0},\n", " {'param': '1,119', 'name': '辅料|小料', 'is_active': 0}]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["headers = {\n", "    \"Accept\": \"*\\/*\",\n", "    \"token\": \"6d3eeba5-81cc-48c1-be09-c3fa31eff89c\",\n", "    \"User-Agent\": \"Mozilla\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\/605.1.15 (KHTML, like Gecko) Mobile\\/15E148 Html5Plus\\/1.0 (Immersed\\/20) uni-app\",\n", "    \"Accept-Encoding\": \"gzip, deflate, br\",\n", "    \"Accept-Language\": \"en-US,en;q=0.9\",\n", "    \"Connection\": \"keep-alive\",\n", "    \"Host\": \"lvshen.wanxiangshengxian.com\",\n", "}\n", "cate_url = \"https://lvshen.wanxiangshengxian.com/api/Category/GetCategory\"\n", "\n", "category_list = get_remote_data_with_proxy_json(url=cate_url, headers=headers).get('data').get('level')\n", "category_list"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-08-02 10:25:47 - WARNING - 我们不爬全部类目，用二级类目来爬...:{'param': '1,all', 'name': '全部', 'is_active': 1}\n", "2024-08-02 10:25:47 - INFO - 随机的使用一个代理IP:**************:45745\n", "2024-08-02 10:25:47 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,133, using proxy: http://***********:8gTcEKLs@**************:45745, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:47 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:45745\n", "2024-08-02 10:25:47 - INFO - 类目有超过10个商品, 继续爬取...page:2\n", "2024-08-02 10:25:47 - INFO - 随机的使用一个代理IP:*************:41732\n", "2024-08-02 10:25:47 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=2&param=1,133, using proxy: http://***********:8gTcEKLs@*************:41732, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:48 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@*************:41732\n", "2024-08-02 10:25:48 - INFO - 类目有超过10个商品, 继续爬取...page:3\n", "2024-08-02 10:25:48 - INFO - 随机的使用一个代理IP:**************:45745\n", "2024-08-02 10:25:48 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=3&param=1,133, using proxy: http://***********:8gTcEKLs@**************:45745, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:48 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:45745\n", "2024-08-02 10:25:48 - INFO - 随机的使用一个代理IP:*************:40118\n", "2024-08-02 10:25:48 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,134, using proxy: http://***********:8gTcEKLs@*************:40118, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:49 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@*************:40118\n", "2024-08-02 10:25:49 - INFO - 随机的使用一个代理IP:**************:30215\n", "2024-08-02 10:25:49 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,123, using proxy: http://***********:8gTcEKLs@**************:30215, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:49 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:30215\n", "2024-08-02 10:25:49 - INFO - 类目有超过10个商品, 继续爬取...page:2\n", "2024-08-02 10:25:49 - INFO - 随机的使用一个代理IP:*************:40118\n", "2024-08-02 10:25:49 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=2&param=1,123, using proxy: http://***********:8gTcEKLs@*************:40118, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:50 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@*************:40118\n", "2024-08-02 10:25:50 - INFO - 随机的使用一个代理IP:*************:36274\n", "2024-08-02 10:25:50 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,108, using proxy: http://***********:8gTcEKLs@*************:36274, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:50 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@*************:36274\n", "2024-08-02 10:25:50 - INFO - 类目有超过10个商品, 继续爬取...page:2\n", "2024-08-02 10:25:50 - INFO - 随机的使用一个代理IP:***************:43522\n", "2024-08-02 10:25:50 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=2&param=1,108, using proxy: http://***********:8gTcEKLs@***************:43522, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:51 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@***************:43522\n", "2024-08-02 10:25:51 - INFO - 随机的使用一个代理IP:**************:30467\n", "2024-08-02 10:25:51 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,107, using proxy: http://***********:8gTcEKLs@**************:30467, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:52 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:30467\n", "2024-08-02 10:25:52 - INFO - 随机的使用一个代理IP:*************:40118\n", "2024-08-02 10:25:52 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,112, using proxy: http://***********:8gTcEKLs@*************:40118, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:52 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@*************:40118\n", "2024-08-02 10:25:52 - INFO - 随机的使用一个代理IP:*************:40118\n", "2024-08-02 10:25:52 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,106, using proxy: http://***********:8gTcEKLs@*************:40118, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:53 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@*************:40118\n", "2024-08-02 10:25:53 - INFO - 类目有超过10个商品, 继续爬取...page:2\n", "2024-08-02 10:25:53 - INFO - 随机的使用一个代理IP:**************:30215\n", "2024-08-02 10:25:53 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=2&param=1,106, using proxy: http://***********:8gTcEKLs@**************:30215, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:53 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:30215\n", "2024-08-02 10:25:53 - INFO - 类目有超过10个商品, 继续爬取...page:3\n", "2024-08-02 10:25:53 - INFO - 随机的使用一个代理IP:***************:31193\n", "2024-08-02 10:25:53 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=3&param=1,106, using proxy: http://***********:8gTcEKLs@***************:31193, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:54 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@***************:31193\n", "2024-08-02 10:25:54 - INFO - 随机的使用一个代理IP:***************:43522\n", "2024-08-02 10:25:54 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,105, using proxy: http://***********:8gTcEKLs@***************:43522, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:54 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@***************:43522\n", "2024-08-02 10:25:54 - INFO - 类目有超过10个商品, 继续爬取...page:2\n", "2024-08-02 10:25:54 - INFO - 随机的使用一个代理IP:**************:40394\n", "2024-08-02 10:25:54 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=2&param=1,105, using proxy: http://***********:8gTcEKLs@**************:40394, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:55 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:40394\n", "2024-08-02 10:25:55 - INFO - 随机的使用一个代理IP:**************:40394\n", "2024-08-02 10:25:55 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,17, using proxy: http://***********:8gTcEKLs@**************:40394, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:55 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:40394\n", "2024-08-02 10:25:55 - INFO - 类目有超过10个商品, 继续爬取...page:2\n", "2024-08-02 10:25:55 - INFO - 随机的使用一个代理IP:**************:30215\n", "2024-08-02 10:25:55 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=2&param=1,17, using proxy: http://***********:8gTcEKLs@**************:30215, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:56 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:30215\n", "2024-08-02 10:25:56 - INFO - 随机的使用一个代理IP:**************:47474\n", "2024-08-02 10:25:56 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,113, using proxy: http://***********:8gTcEKLs@**************:47474, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:56 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:47474\n", "2024-08-02 10:25:56 - INFO - 随机的使用一个代理IP:***********:42953\n", "2024-08-02 10:25:56 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,120, using proxy: http://***********:8gTcEKLs@***********:42953, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:57 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@***********:42953\n", "2024-08-02 10:25:57 - ERROR - 爬取失败:{'param': '1,120', 'name': '椰青', 'is_active': 0}\n", "2024-08-02 10:25:57 - INFO - 随机的使用一个代理IP:***************:31193\n", "2024-08-02 10:25:57 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,111, using proxy: http://***********:8gTcEKLs@***************:31193, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:57 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@***************:31193\n", "2024-08-02 10:25:57 - INFO - 随机的使用一个代理IP:**************:42029\n", "2024-08-02 10:25:57 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,110, using proxy: http://***********:8gTcEKLs@**************:42029, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:58 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:42029\n", "2024-08-02 10:25:58 - INFO - 随机的使用一个代理IP:*************:34649\n", "2024-08-02 10:25:58 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,114, using proxy: http://***********:8gTcEKLs@*************:34649, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:58 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@*************:34649\n", "2024-08-02 10:25:58 - INFO - 随机的使用一个代理IP:**************:30467\n", "2024-08-02 10:25:58 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,115, using proxy: http://***********:8gTcEKLs@**************:30467, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:59 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:30467\n", "2024-08-02 10:25:59 - INFO - 类目有超过10个商品, 继续爬取...page:2\n", "2024-08-02 10:25:59 - INFO - 随机的使用一个代理IP:***************:43522\n", "2024-08-02 10:25:59 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=2&param=1,115, using proxy: http://***********:8gTcEKLs@***************:43522, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:25:59 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@***************:43522\n", "2024-08-02 10:25:59 - INFO - 随机的使用一个代理IP:**************:45745\n", "2024-08-02 10:25:59 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,109, using proxy: http://***********:8gTcEKLs@**************:45745, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:26:00 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:45745\n", "2024-08-02 10:26:00 - INFO - 随机的使用一个代理IP:***********:42953\n", "2024-08-02 10:26:00 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,116, using proxy: http://***********:8gTcEKLs@***********:42953, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:26:01 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@***********:42953\n", "2024-08-02 10:26:01 - INFO - 随机的使用一个代理IP:**************:45745\n", "2024-08-02 10:26:01 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=1&param=1,119, using proxy: http://***********:8gTcEKLs@**************:45745, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:26:01 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:45745\n", "2024-08-02 10:26:01 - INFO - 类目有超过10个商品, 继续爬取...page:2\n", "2024-08-02 10:26:01 - INFO - 随机的使用一个代理IP:**************:47474\n", "2024-08-02 10:26:01 - INFO - url:https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page=2&param=1,119, using proxy: http://***********:8gTcEKLs@**************:47474, retry_cnt:0, headers:{'Accept': '*\\\\/*', 'token': '6d3eeba5-81cc-48c1-be09-c3fa31eff89c', 'User-Agent': 'Mozilla\\\\/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit\\\\/605.1.15 (KHTML, like Gecko) Mobile\\\\/15E148 Html5Plus\\\\/1.0 (Immersed\\\\/20) uni-app', 'Accept-Encoding': 'gzip, deflate, br', 'Accept-Language': 'en-US,en;q=0.9', 'Connection': 'keep-alive', 'Host': 'lvshen.wanxiangshengxian.com'}\n", "2024-08-02 10:26:02 - INFO - response status:200, proxy used:http://***********:8gTcEKLs@**************:47474\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>selectedNum</th>\n", "      <th>remarks</th>\n", "      <th>alway_buy_status</th>\n", "      <th>goods_id</th>\n", "      <th>spec_id</th>\n", "      <th>store_count_dot</th>\n", "      <th>produce_date</th>\n", "      <th>store_count</th>\n", "      <th>sort</th>\n", "      <th>goods_name</th>\n", "      <th>...</th>\n", "      <th>cost_price</th>\n", "      <th>is_guige</th>\n", "      <th>top_left_tag_img</th>\n", "      <th>supp_id</th>\n", "      <th>dp_name</th>\n", "      <th>set_goods_unit</th>\n", "      <th>set_goods_size</th>\n", "      <th>set_goods_color</th>\n", "      <th>set_goods_cart_keyboard</th>\n", "      <th>category_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>355</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>None</td>\n", "      <td>88</td>\n", "      <td>2500</td>\n", "      <td>悦鲜活鲜牛奶（冷藏） 950ml*12瓶</td>\n", "      <td>...</td>\n", "      <td>128.00</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>#dd524d</td>\n", "      <td>1</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>348</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>None</td>\n", "      <td>32</td>\n", "      <td>2006</td>\n", "      <td>椰树牌椰子汁 1L*12盒</td>\n", "      <td>...</td>\n", "      <td>122.00</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>#dd524d</td>\n", "      <td>1</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>280</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>None</td>\n", "      <td>15</td>\n", "      <td>2005</td>\n", "      <td>澳兰克纯牛奶 1L*12盒</td>\n", "      <td>...</td>\n", "      <td>65.00</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>#dd524d</td>\n", "      <td>1</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>295</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>2002</td>\n", "      <td>淳轩纯牛奶 1L*12瓶</td>\n", "      <td>...</td>\n", "      <td>73.00</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>#dd524d</td>\n", "      <td>1</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>272</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>None</td>\n", "      <td>26</td>\n", "      <td>2000</td>\n", "      <td>蒙牛纯牛奶 1L*6盒</td>\n", "      <td>...</td>\n", "      <td>44.00</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>#dd524d</td>\n", "      <td>1</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>341</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>None</td>\n", "      <td>22</td>\n", "      <td>1606</td>\n", "      <td>（1罐）黑白淡奶 400g*1罐</td>\n", "      <td>...</td>\n", "      <td>8.35</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>#dd524d</td>\n", "      <td>1</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>339</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>None</td>\n", "      <td>46</td>\n", "      <td>1604</td>\n", "      <td>（1罐）雀巢三花全脂淡奶 410g*1罐</td>\n", "      <td>...</td>\n", "      <td>8.10</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>#dd524d</td>\n", "      <td>1</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>336</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>None</td>\n", "      <td>100</td>\n", "      <td>1602</td>\n", "      <td>（1罐）雀巢三花植脂淡奶 410g*1罐</td>\n", "      <td>...</td>\n", "      <td>6.61</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>#dd524d</td>\n", "      <td>1</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>300</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "      <td>1601</td>\n", "      <td>雀巢鹰唛炼奶350g*48罐(整箱)</td>\n", "      <td>...</td>\n", "      <td>495.00</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>#dd524d</td>\n", "      <td>1</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>299</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>None</td>\n", "      <td>33</td>\n", "      <td>1600</td>\n", "      <td>雀巢鹰唛炼奶 350g*1罐(1罐)</td>\n", "      <td>...</td>\n", "      <td>10.32</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>#dd524d</td>\n", "      <td>1</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 32 columns</p>\n", "</div>"], "text/plain": ["  <PERSON><PERSON><PERSON> remarks alway_buy_status  goods_id spec_id store_count_dot  \\\n", "0           0                                355       0            0.00   \n", "1           0                                348       0            0.00   \n", "2           0                                280       0            0.00   \n", "3           0                                295       0            0.00   \n", "4           0                                272       0            0.00   \n", "5           0                                341       0            0.00   \n", "6           0                                339       0            0.00   \n", "7           0                                336       0            0.00   \n", "8           0                                300       0            0.00   \n", "9           0                                299       0            0.00   \n", "\n", "  produce_date store_count  sort            goods_name  ...  cost_price  \\\n", "0         None          88  2500  悦鲜活鲜牛奶（冷藏） 950ml*12瓶  ...      128.00   \n", "1         None          32  2006         椰树牌椰子汁 1L*12盒  ...      122.00   \n", "2         None          15  2005         澳兰克纯牛奶 1L*12盒  ...       65.00   \n", "3         None          17  2002          淳轩纯牛奶 1L*12瓶  ...       73.00   \n", "4         None          26  2000           蒙牛纯牛奶 1L*6盒  ...       44.00   \n", "5         None          22  1606      （1罐）黑白淡奶 400g*1罐  ...        8.35   \n", "6         None          46  1604  （1罐）雀巢三花全脂淡奶 410g*1罐  ...        8.10   \n", "7         None         100  1602  （1罐）雀巢三花植脂淡奶 410g*1罐  ...        6.61   \n", "8         None           1  1601    雀巢鹰唛炼奶350g*48罐(整箱)  ...      495.00   \n", "9         None          33  1600    雀巢鹰唛炼奶 350g*1罐(1罐)  ...       10.32   \n", "\n", "  is_guige top_left_tag_img  supp_id dp_name set_goods_unit  set_goods_size  \\\n", "0        0                         0    None              0               0   \n", "1        0                         0    None              0               0   \n", "2        0                         0    None              0               0   \n", "3        0                         0    None              0               0   \n", "4        0                         0    None              0               0   \n", "5        0                         0    None              0               0   \n", "6        0                         0    None              0               0   \n", "7        0                         0    None              0               0   \n", "8        0                         0    None              0               0   \n", "9        0                         0    None              0               0   \n", "\n", "   set_goods_color set_goods_cart_keyboard category_name  \n", "0          #dd524d                       1           乳制品  \n", "1          #dd524d                       1           乳制品  \n", "2          #dd524d                       1           乳制品  \n", "3          #dd524d                       1           乳制品  \n", "4          #dd524d                       1           乳制品  \n", "5          #dd524d                       1           乳制品  \n", "6          #dd524d                       1           乳制品  \n", "7          #dd524d                       1           乳制品  \n", "8          #dd524d                       1           乳制品  \n", "9          #dd524d                       1           乳制品  \n", "\n", "[10 rows x 32 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing import List, Dict, Any\n", "\n", "\n", "def fetch_category_product(category_id=\"1,all\", page=1) -> List[Dict[str, Any]]:\n", "    url = f\"https://lvshen.wanxiangshengxian.com/api/Category/GetCategoryGoods?page={page}&param={category_id}\"\n", "    products = (\n", "        get_remote_data_with_proxy_json(url=url, headers=headers)\n", "        .get(\"data\", {})\n", "        .get(\"goods\", [])\n", "    )\n", "    if len(products) >= 10:\n", "        logging.info(f\"类目有超过10个商品, 继续爬取...page:{page+1}\")\n", "        products_more = fetch_category_product(category_id=category_id, page=page + 1)\n", "        products.extend(products_more)\n", "    return products\n", "\n", "\n", "all_products = []\n", "for cate in category_list:\n", "    cate_name = cate[\"name\"]\n", "    if \"全部\" == cate_name:\n", "        logging.warning(f\"我们不爬全部类目，用二级类目来爬...:{cate}\")\n", "        continue\n", "    products = fetch_category_product(category_id=cate[\"param\"])\n", "    if len(products) <= 0:\n", "        logging.error(f\"爬取失败:{cate}\")\n", "        continue\n", "    for prod in products:\n", "        prod[\"category_name\"] = cate_name\n", "    all_products.extend(products)\n", "\n", "all_products_df = pd.DataFrame(all_products)\n", "\n", "all_products_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def fetch_product_detail(goods_id=45):\n", "#     url = f\"https://lvshen.wanxiangshengxian.com/api/Goods/goods_detail?goods_id={goods_id}&type=common\"\n", "#     good_detail = get_remote_data_with_proxy_json(url=url, headers=headers).get(\"data\")\n", "#     return good_detail\n", "\n", "\n", "# d = fetch_product_detail()\n", "# d"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-08-02 10:30:41 - INFO - 表不存在:summerfarm_ds.spider_lvshensongguo_product_result_df\n", "2024-08-02 10:30:47 - INFO - 成功写入odps:summerfarm_ds.spider_lvshensongguo_product_result_df, partition_spec:ds=20240802,competitor_name=lvshensongguo, attemp:0\n", "2024-08-02 10:30:52 - INFO - sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_lvshensongguo_product_result_df\n", "                             where ds>='20240703' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n", "2024-08-02 10:30:52 - INFO -          ds competitor_name  recods\n", "0  20240802   lvshensongguo     153\n", "2024-08-02 10:30:52 - INFO - ===new_record===绿神送果-东莞, 商品数:153\n"]}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "all_products_df.drop_duplicates(subset='goods_id', inplace=True)\n", "all_products_df=all_products_df.astype(str)\n", "all_products_df['competitor']=brand_name\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "logging.info(df.to_string())\n", "logging.info(f\"===new_record==={brand_name}, 商品数:{len(all_products_df)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}
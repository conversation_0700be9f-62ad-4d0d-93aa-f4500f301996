{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 写入odps\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json,logging\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN\",\n", "}\n", "brand_name = \"重庆宝丰\"\n", "competitor_name_en = \"bao<PERSON>\"\n", "\n", "logging.info(\"即将爬取:%s, %s\", brand_name, competitor_name_en)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'Format': 'Json', 'Content': {'PageSize': 20, 'PageIndex': 4, 'TotalCount': 701, 'Data': [{'ProductId': 6026795, 'ProductCode': '010425', 'BarCode': '', 'ProductName': '带骨带皮鸡腿肉（排腿）', 'DefaultPic': '//km5/1/9655/********/f98878cc343743c1ba4aab5a3a5f2e20.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com//km5/1/9655/********/f98878cc343743c1ba4aab5a3a5f2e20.jpg', 'Unit': 'kg', 'Shelved': 1, 'Status': 1, 'CategoryId': 421812, 'CategoryName': '鸡肉类', 'BrandId': 0, 'Stock': 31.1, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': 'kg', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 2.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 13.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 13, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '10kg*件', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421809', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503237, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': 'kg', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503237, 'Flag': 0, 'Qty': 0}, {'Id': 2466979, 'Name': '件', 'Translated': 10.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503237, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8175912, 'UnitId': 0, 'UnitName': 'kg', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 13.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '10': [{'Id': 8217337, 'UnitId': 2466979, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 132.25, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026813, 'ProductCode': '010447', 'BarCode': '', 'ProductName': '宝茸树莓果茸（覆盆子）', 'DefaultPic': '/km5/1/9655/20231010/bcb775ddba58437f97adcf9a87c82b4a.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/bcb775ddba58437f97adcf9a87c82b4a.jpg', 'Unit': '盒', 'Shelved': 1, 'Status': 1, 'CategoryId': 421798, 'CategoryName': '宝茸', 'BrandId': 77323, 'BrandName': '宝茸', 'Stock': 23, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '盒', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 5.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 126.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 126, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '1kg*6盒', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421795', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503255, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '盒', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503255, 'Flag': 0, 'Qty': 0}, {'Id': 2466989, 'Name': '件', 'Translated': 6.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503255, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8175930, 'UnitId': 0, 'UnitName': '盒', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 126.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '6': [{'Id': 8217351, 'UnitId': 2466989, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 756.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026822, 'ProductCode': '010456', 'BarCode': '', 'ProductName': '金枪鱼柳', 'DefaultPic': '//km5/1/9655/20240326/56022eaacca348389a3cbca68cffa6a0.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com//km5/1/9655/20240326/56022eaacca348389a3cbca68cffa6a0.jpg', 'Unit': 'kg', 'Shelved': 1, 'Status': 1, 'CategoryId': 421819, 'CategoryName': '鱼类制品', 'BrandId': 0, 'Stock': 0, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': 'kg', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 0.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 82.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 82, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '10kg*件', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421815', 'DeliveryCycle': '', 'StockText': '无货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503264, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': 'kg', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503264, 'Flag': 0, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8175939, 'UnitId': 0, 'UnitName': 'kg', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 82.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026823, 'ProductCode': '010457', 'BarCode': '', 'ProductName': '两点水芝士碎2kg', 'DefaultPic': '/km5/1/9655/20231005/a3a4437dc1b24e0aa5b8d6dd7d39d155.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231005/a3a4437dc1b24e0aa5b8d6dd7d39d155.jpg', 'Unit': '袋', 'Shelved': 1, 'Status': 1, 'CategoryId': 421762, 'CategoryName': '芝士碎/片', 'BrandId': 77362, 'BrandName': '两点水', 'Stock': 0, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '袋', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 0.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 88.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 88, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '2kg*6袋', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421755', 'DeliveryCycle': '', 'StockText': '无货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503265, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '袋', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503265, 'Flag': 0, 'Qty': 0}, {'Id': 2466991, 'Name': '件', 'Translated': 6.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503265, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8175940, 'UnitId': 0, 'UnitName': '袋', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 88.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '6': [{'Id': 0, 'UnitId': 2466991, 'UnitName': '件', 'MinQuantity': 1, 'MaxQuantity': 2147483647, 'Price': 528, 'Integral': 0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026830, 'ProductCode': '010464', 'BarCode': '', 'ProductName': '宝茸草莓果茸', 'DefaultPic': '/km5/1/9655/20231010/d249d81bc05d4198b7927cf9cf80909f.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/d249d81bc05d4198b7927cf9cf80909f.jpg', 'Unit': '盒', 'Shelved': 1, 'Status': 1, 'CategoryId': 421798, 'CategoryName': '宝茸', 'BrandId': 77323, 'BrandName': '宝茸', 'Stock': 34, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '盒', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 4.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 86.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 86, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '1kg*6', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421795', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503272, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '盒', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503272, 'Flag': 0, 'Qty': 0}, {'Id': 2466995, 'Name': '件', 'Translated': 6.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503272, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8175947, 'UnitId': 0, 'UnitName': '盒', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 86.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '6': [{'Id': 10895550, 'UnitId': 2466995, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 540.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026832, 'ProductCode': '010466', 'BarCode': '', 'ProductName': '宝茸蓝莓果茸', 'DefaultPic': '/km5/1/9655/20231010/edd52bf9a4cb42c0b2371eb58da27013.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/edd52bf9a4cb42c0b2371eb58da27013.jpg', 'Unit': '盒', 'Shelved': 1, 'Status': 1, 'CategoryId': 421798, 'CategoryName': '宝茸', 'BrandId': 77323, 'BrandName': '宝茸', 'Stock': 1, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '盒', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 0.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 130.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 130, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '1kg*6盒', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421795', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503274, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '盒', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503274, 'Flag': 0, 'Qty': 0}, {'Id': 2466997, 'Name': '件', 'Translated': 6.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503274, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8175949, 'UnitId': 0, 'UnitName': '盒', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 130.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '6': [{'Id': 13339575, 'UnitId': 2466997, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 780.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026841, 'ProductCode': '010475', 'BarCode': '', 'ProductName': '越南春卷皮', 'DefaultPic': '/km5/1/9655/20231010/6d169ef831844652af593436f8b215c7.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/6d169ef831844652af593436f8b215c7.jpg', 'Unit': '袋', 'Shelved': 1, 'Status': 1, 'CategoryId': 421786, 'CategoryName': '面|米', 'BrandId': 0, 'Stock': 28, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '袋', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 1.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 13.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 13, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '340g*40', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421780', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503283, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '袋', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503283, 'Flag': 0, 'Qty': 0}, {'Id': 2467003, 'Name': '件', 'Translated': 40.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503283, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8175958, 'UnitId': 0, 'UnitName': '袋', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 13.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '40': [{'Id': 8217373, 'UnitId': 2467003, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 517.5, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026856, 'ProductCode': '010486', 'BarCode': '', 'ProductName': '帕普拉朵抹茶冰淇淋', 'DefaultPic': '/km5/1/9655/20231010/97cd6ecbc8724c17b939491b266908ed.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/97cd6ecbc8724c17b939491b266908ed.jpg', 'Unit': '桶', 'Shelved': 1, 'Status': 1, 'CategoryId': 421820, 'CategoryName': '冰淇淋系列', 'BrandId': 77278, 'BrandName': '帕普拉朵', 'Stock': 0, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '桶', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 0.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 90.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 90, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '3kg*2桶/件', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '', 'DeliveryCycle': '', 'StockText': '无货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503298, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '桶', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503298, 'Flag': 0, 'Qty': 0}, {'Id': 2467007, 'Name': '件', 'Translated': 2.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503298, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8175973, 'UnitId': 0, 'UnitName': '桶', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 90.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '2': [{'Id': 0, 'UnitId': 2467007, 'UnitName': '件', 'MinQuantity': 1, 'MaxQuantity': 2147483647, 'Price': 180, 'Integral': 0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026857, 'ProductCode': '010487', 'BarCode': '', 'ProductName': '帕普拉朵草莓冰淇淋', 'DefaultPic': '/km5/1/9655/20231010/97cd6ecbc8724c17b939491b266908ed.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/97cd6ecbc8724c17b939491b266908ed.jpg', 'Unit': '桶', 'Shelved': 1, 'Status': 1, 'CategoryId': 421820, 'CategoryName': '冰淇淋系列', 'BrandId': 77278, 'BrandName': '帕普拉朵', 'Stock': 0, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '桶', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 0.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 90.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 90, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '3kg*2桶/件', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '', 'DeliveryCycle': '', 'StockText': '无货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503299, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '桶', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503299, 'Flag': 0, 'Qty': 0}, {'Id': 2467008, 'Name': '件', 'Translated': 2.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503299, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8175974, 'UnitId': 0, 'UnitName': '桶', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 90.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '2': [{'Id': 8217379, 'UnitId': 2467008, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 179.4, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026862, 'ProductCode': '010491', 'BarCode': '', 'ProductName': '安佳奶油芝士20kg', 'DefaultPic': '/km5/1/9655/20231010/3f668d7925c74996a258d1122450e42e.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/3f668d7925c74996a258d1122450e42e.jpg', 'Unit': '件', 'Shelved': 1, 'Status': 1, 'CategoryId': 594724, 'CategoryName': '奶酪|芝士', 'BrandId': 69454, 'BrandName': '安佳', 'Stock': 3, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '件', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 19.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 800.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 800, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '20KG', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421735', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503304, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '件', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503304, 'Flag': 0, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8175981, 'UnitId': 0, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 800.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026938, 'ProductCode': '010551', 'BarCode': '', 'ProductName': '大成德式香肠32cm', 'DefaultPic': '/km5/1/9655/20231005/e3bbc92d2277413984b4d7d2f33a092d.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231005/e3bbc92d2277413984b4d7d2f33a092d.jpg', 'Unit': '袋', 'Shelved': 1, 'Status': 1, 'CategoryId': 606104, 'CategoryName': '大成', 'BrandId': 69350, 'BrandName': '大成（食品）', 'Stock': 114, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '袋', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 128.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 37.5, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 37.5, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '12袋/件', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421809,421811', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503380, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '袋', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503380, 'Flag': 0, 'Qty': 0}, {'Id': 2467047, 'Name': '件', 'Translated': 12.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503380, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8176066, 'UnitId': 0, 'UnitName': '袋', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 37.5, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '12': [{'Id': 8217410, 'UnitId': 2467047, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 425.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026949, 'ProductCode': '010562', 'BarCode': '', 'ProductName': '安佳芝士黄片', 'DefaultPic': '/km5/1/9655/20231010/acb81ace6e7b43db81177efd9eec7e24.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/acb81ace6e7b43db81177efd9eec7e24.jpg', 'Unit': '包', 'Shelved': 1, 'Status': 1, 'CategoryId': 594724, 'CategoryName': '奶酪|芝士', 'BrandId': 69454, 'BrandName': '安佳', 'Stock': 165, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '包', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 20.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 60.5, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 60.5, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '1.04kg*10包/件', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421735', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503391, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '包', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503391, 'Flag': 0, 'Qty': 0}, {'Id': 2467052, 'Name': '件', 'Translated': 10.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503391, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8176077, 'UnitId': 0, 'UnitName': '包', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 60.5, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '10': [{'Id': 8217419, 'UnitId': 2467052, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 600.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026951, 'ProductCode': '010564', 'BarCode': '', 'ProductName': '草原宏宝法式羊排（十二肋法排）', 'DefaultPic': '/km5/1/9655/20231010/c3b16c59e4ee47ccaa803ca02df1d9c2.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/c3b16c59e4ee47ccaa803ca02df1d9c2.jpg', 'Unit': 'kg', 'Shelved': 1, 'Status': 1, 'CategoryId': 421814, 'CategoryName': '羊肉类', 'BrandId': 0, 'Stock': 192.73, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': 'kg', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 0.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 90.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 90, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '18kg/件', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421809', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503393, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': 'kg', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503393, 'Flag': 0, 'Qty': 0}, {'Id': 2467053, 'Name': '件', 'Translated': 17.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503393, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8176079, 'UnitId': 0, 'UnitName': 'kg', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 90.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '17': [{'Id': 0, 'UnitId': 2467053, 'UnitName': '件', 'MinQuantity': 1, 'MaxQuantity': 2147483647, 'Price': 1530, 'Integral': 0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026960, 'ProductCode': '010573', 'ProductName': '黑朗姆酒', 'DefaultPic': '/km5/1/9655/20240529/d04d279dcf1d42b892acd687e920c84e.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20240529/d04d279dcf1d42b892acd687e920c84e.jpg', 'Unit': '瓶', 'Shelved': 1, 'Status': 1, 'CategoryId': 421778, 'CategoryName': '酒', 'BrandId': 0, 'Stock': 2, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '瓶', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 0.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 68.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 68, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '750ml*12瓶/件', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421771', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503402, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '瓶', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'GoodsId': 7503402, 'Flag': 0, 'Qty': 0}, {'Id': 2467058, 'Name': '件', 'Translated': 12.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'GoodsId': 7503402, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8176088, 'UnitId': 0, 'UnitName': '瓶', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 68.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '12': [{'Id': 8217427, 'UnitId': 2467058, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 828.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6026998, 'ProductCode': '010609', 'BarCode': '', 'ProductName': '雀巢鹰唛炼乳350g', 'DefaultPic': '/km5/1/9655/20231010/48cede7a6ee1412693369870dae9c06c.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/48cede7a6ee1412693369870dae9c06c.jpg', 'Unit': '听', 'Shelved': 1, 'Status': 1, 'CategoryId': 594729, 'CategoryName': '炼乳', 'BrandId': 0, 'Stock': 243, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '听', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 15.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 13.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 13, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '350g*48听', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421735', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503440, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '听', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503440, 'Flag': 0, 'Qty': 0}, {'Id': 2467078, 'Name': '件', 'Translated': 48.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503440, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8176130, 'UnitId': 0, 'UnitName': '听', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 13.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '48': [{'Id': 8217445, 'UnitId': 2467078, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 544.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6027000, 'ProductCode': '010614', 'BarCode': '', 'ProductName': '安佳芝士块（马苏里拉）', 'DefaultPic': '/km5/1/9655/20231010/4481269d8708458fabc1179a9c6887a7.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/4481269d8708458fabc1179a9c6887a7.jpg', 'Unit': '条', 'Shelved': 1, 'Status': 1, 'CategoryId': 594724, 'CategoryName': '奶酪|芝士', 'BrandId': 69454, 'BrandName': '安佳', 'Stock': 26, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '条', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 0.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 435.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 435, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '2*10KG', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421735', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503442, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '条', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503442, 'Flag': 0, 'Qty': 0}, {'Id': 2467080, 'Name': '件', 'Translated': 2.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503442, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8176132, 'UnitId': 0, 'UnitName': '条', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 435.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '2': [{'Id': 8217485, 'UnitId': 2467080, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 866.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6027008, 'ProductCode': '010621', 'BarCode': '', 'ProductName': '帕普拉朵香草冰淇淋', 'DefaultPic': '/km5/1/9655/20231010/97cd6ecbc8724c17b939491b266908ed.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/97cd6ecbc8724c17b939491b266908ed.jpg', 'Unit': '桶', 'Shelved': 1, 'Status': 1, 'CategoryId': 421820, 'CategoryName': '冰淇淋系列', 'BrandId': 77278, 'BrandName': '帕普拉朵', 'Stock': 1, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '桶', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 0.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 100.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 100, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '3KG＊２', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503450, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '桶', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503450, 'Flag': 0, 'Qty': 0}, {'Id': 2467085, 'Name': '件', 'Translated': 2.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503450, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8176140, 'UnitId': 0, 'UnitName': '桶', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 100.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '2': [{'Id': 8217488, 'UnitId': 2467085, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 200.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6027019, 'ProductCode': '010633', 'BarCode': '', 'ProductName': '雀巢三花淡奶410g', 'DefaultPic': '/km5/1/9655/20230928/92e9485e1d214c878b3b335a8d5513ea.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20230928/92e9485e1d214c878b3b335a8d5513ea.jpg', 'Unit': '听', 'Shelved': 1, 'Status': 1, 'CategoryId': 421761, 'CategoryName': '牛奶/奶粉', 'BrandId': 0, 'Stock': 12, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '听', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 1.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 8.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 8, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '410g*48听', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421755', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503461, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '听', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503461, 'Flag': 0, 'Qty': 0}, {'Id': 2467093, 'Name': '件', 'Translated': 48.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503461, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8176151, 'UnitId': 0, 'UnitName': '听', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 8.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '48': [{'Id': 8217495, 'UnitId': 2467093, 'UnitName': '件', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 391.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6027036, 'ProductCode': '010649', 'BarCode': '', 'ProductName': '宝茸黑加仑/黑醋栗果茸', 'DefaultPic': '/km5/1/9655/20231010/74025c0055c54544905b7d0e68c35d93.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/74025c0055c54544905b7d0e68c35d93.jpg', 'Unit': '盒', 'Shelved': 1, 'Status': 1, 'CategoryId': 421798, 'CategoryName': '宝茸', 'BrandId': 77323, 'BrandName': '宝茸', 'Stock': 6, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '盒', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 0.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 130.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 130, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '1kg*6', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421795', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503478, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '盒', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503478, 'Flag': 0, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8176168, 'UnitId': 0, 'UnitName': '盒', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 130.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}, {'ProductId': 6027037, 'ProductCode': '010651', 'BarCode': '', 'ProductName': '铁塔奶油芝士2kg', 'DefaultPic': '/km5/1/9655/20231010/6fbf48c6325d41b69214d820be23fa68.jpg', 'DefaultPicUrl': 'https://img.km5.366kmpf.com/km5/1/9655/20231010/6fbf48c6325d41b69214d820be23fa68.jpg', 'Unit': '盒', 'Shelved': 1, 'Status': 1, 'CategoryId': 594724, 'CategoryName': '奶酪|芝士', 'BrandId': 77200, 'BrandName': '铁塔', 'Stock': 214, 'ZeroStockBuy': 0, 'Radix': 1, 'AuxUnit': '盒', 'BuyLowerLimit': 0.0, 'BuyUpperLimit': 0.0, 'Sells': 11.0, 'MultipleBuy': 0.0, 'FrontShowStockDetail': 0, 'Price': 0, 'SaleUnitPrice': 135.0, 'IsDeduction': 0, 'DeductRate': 0, 'SalesPrice': 135, 'SalesIntegral': 0, 'SaleMode': 0, 'PriceType': 0, 'SupplierId': 2938, 'SettleType': 2, 'SelfOperate': 0, 'SellerName': '宝丰食品', 'SupplierStatus': 1, 'GroupNumber': 0, 'MergeId': 0, 'PackageSpec': '2KG*6', 'EnableSKUTranslated': 0, 'DisableCoupon': 0, 'Edition': 0, 'ShowPrice': 1, 'ParentChain': '421735', 'DeliveryCycle': '', 'StockText': '有货', 'BuyCount': 0, 'IsSingle': 1, 'CartCount': 0.0, 'ProductGoodsId': 7503479, 'SpecValue': '无', 'AuxiliaryUnits': [{'Id': 0, 'Name': '盒', 'Translated': 1.0, 'IsMinSaleUnit': 1, 'IsDefaultUnit': 1, 'BarCode': '', 'GoodsId': 7503479, 'Flag': 0, 'Qty': 0}, {'Id': 2467101, 'Name': '箱', 'Translated': 6.0, 'IsMinSaleUnit': 0, 'IsDefaultUnit': 0, 'BarCode': '', 'GoodsId': 7503479, 'Flag': 1, 'Qty': 0}], 'UnitPrices': {'1': [{'Id': 8176169, 'UnitId': 0, 'UnitName': '盒', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 135.0, 'Integral': 0.0, 'MarketPrice': 0, 'Use': 0}], '6': [{'Id': 10970443, 'UnitId': 2467101, 'UnitName': '箱', 'MinQuantity': 1.0, 'MaxQuantity': 2147483647.0, 'Price': 810.0, 'Integral': 0.0, 'MarketPrice': 0}]}, 'ExtAttrs': [], 'DisplayStockValue': False}], 'TotalPage': 36}, 'Success': True, 'Code': 200, 'Message': '成功', 'RunId': '534'}\n"]}], "source": ["import requests\n", "from datetime import datetime\n", "\n", "h = {\n", "    \"content-length\": \"917\",\n", "    \"version\": \"3.3.510\",\n", "    \"terminalid\": \"5\",\n", "    \"deviceno\": \"oka-F5I3_2bpQZbFh8uHFGhA2d-M\",\n", "    \"xweb_xhr\": \"1\",\n", "    \"platform\": \"<PERSON>app\",\n", "    \"user-agent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF MacWechat/3.8.7(0x13080712) XWEB/1191\",\n", "    \"content-type\": \"application/x-www-form-urlencoded;charset=UTF-8\",\n", "    \"accept\": \"*/*\",\n", "    \"sec-fetch-site\": \"cross-site\",\n", "    \"sec-fetch-mode\": \"cors\",\n", "    \"sec-fetch-dest\": \"empty\",\n", "    \"referer\": \"https://servicewechat.com/wx77e2eb49f80331fc/2/page-frame.html\",\n", "    \"accept-encoding\": \"gzip, deflate, br\",\n", "    \"accept-language\": \"en-US,en;q=0.9\",\n", "}\n", "\n", "\n", "data = {\n", "    \"FKFlag\": \"2\",\n", "    \"FKId\": \"2938\",\n", "    \"ProprietorId\": \"2938\",\n", "    \"PageIndex\": \"4\",\n", "    \"PageSize\": \"20\",\n", "    \"ShowNoStock\": \"1\",\n", "    \"IsProduct\": \"1\",\n", "    \"storeisopenb2c\": \"1\",\n", "    \"sortField\": \"sortorder\",\n", "    \"sortDirect\": \"desc\",\n", "    \"Orderby\": \"<sortorder>desc</sortorder>\",\n", "    \"UserId\": \"4141086\",\n", "    \"Token\": \"4B38F1DF6FF1A96C2B30C18D4EB4B340B9D2DE9D34AB8BA3E865AE95DC282C8623750C45A180DF1AD57181D0567677C71CAA3DB7EA5DB803A1BE4A05B1E3D1544FE7A2D6EDD087C74C5E34D6822E682FF10A859183F2EE333D27B5C36B310035E381B063934B2FB255D38892A36C717AEC632FA6BE364DE3F2C88914F8C9BD9312A81F80997A2672594705068F14BA7782782A93BCAB7344A9A0A196324D868F5DF5985FFD2FFBD9707FD216A42FB2EAB3B24F571E0166DB545BA68D10639DA7CB597FD6E8BACB5DCAD2F8EC2F75483F43B56769C73A3CC44A7840B0099E5B2FEA6099B22DEA303BFC4DCE95F0ADA1D6F5D68201F2D77EF08B8EDB86CF99B9A4D003AC4FB184E3EF55FCE8CECEBF87BD48A452780FD8294FA75F68DBA0E975C73CB2EA434E473A72\",\n", "    \"v\": \"3.0\",\n", "    \"method\": \"vast.mall.product.page\",\n", "    \"appid\": \"4a364f2d1f1fb842\",\n", "    \"timestamp\": \"1723606178\",\n", "    # \"timestamp\": f\"{int(datetime.now().timestamp())}\",\n", "    \"sign\": \"57d801e984919bcb905924fe17bf4a86\",\n", "}\n", "\n", "r = requests.post(\n", "    \"https://km5.366kmpf.com/kmd/m7ck/Route.axd\", data=data, headers=h\n", ").json()\n", "\n", "print(r)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FKId=2938&FKFlag=2&Token=4B38F1DF6FF1A96C2B30C18D4EB4B340B9D2DE9D34AB8BA3E865AE95DC282C8623750C45A180DF1AD57181D0567677C71CAA3DB7EA5DB80322639C61168D60C388ED85F9B6820431DCCD2A81D3E00A984F46FC6D3712B7F8E97CB9D447C6EC6165AF6E64078376F6DA7C69AA3118BCBEBBF4776291C355235EF109D6A10B188A41F1643D9FD4A3CC59A54AE5B94D75EFC283C7962747250DAE7ED743017786C79B89D37D7781B313B064619164489737B16BBC0D4D9793AC64B595AD4A949914F76E6B2CB4F59AA0F8FD967E982DADD16852E9F406DD277956CE19315C4AF369EDC055416E0E80A73CF68C063D1190B7EE3D2CE564E3C8F183BC909BB70A4063EA36AE41CBC574DF140F583AB90B3A5199D8EB7B5A7A8B7646440F124A9E1ADB0CA440651C62DF5D&UserId=5372745&proprietor=2&proprietorId=2938&v=3.0&method=vast.order.cart.count&appid=4a364f2d1f1fb842&timestamp=1723601638&sign=3064cc3515492249f7affa1275a76e24\n"]}], "source": ["import base64\n", "\n", "def decode_base64(encoded_str):\n", "    \"\"\"\n", "    Decodes a Base64 encoded string.\n", "\n", "    Args:\n", "        encoded_str (str): The Base64 encoded string.\n", "\n", "    Returns:\n", "        str: The decoded string.\n", "    \"\"\"\n", "    # Decode the Base64 encoded string\n", "    decoded_bytes = base64.b64decode(encoded_str)\n", "    \n", "    # Convert the bytes back to a string\n", "    decoded_str = decoded_bytes.decode('utf-8')\n", "    \n", "    return decoded_str\n", "\n", "# Example usage:\n", "encoded_str = \"RktJZD0yOTM4JkZLRmxhZz0yJlRva2VuPTRCMzhGMURGNkZGMUE5NkMyQjMwQzE4RDRFQjRCMzQwQjlEMkRFOUQzNEFCOEJBM0U4NjVBRTk1REMyODJDODYyMzc1MEM0NUExODBERjFBRDU3MTgxRDA1Njc2NzdDNzFDQUEzREI3RUE1REI4MDMyMjYzOUM2MTE2OEQ2MEMzODhFRDg1RjlCNjgyMDQzMURDQ0QyQTgxRDNFMDBBOTg0RjQ2RkM2RDM3MTJCN0Y4RTk3Q0I5RDQ0N0M2RUM2MTY1QUY2RTY0MDc4Mzc2RjZEQTdDNjlBQTMxMThCQ0JFQkJGNDc3NjI5MUMzNTUyMzVFRjEwOUQ2QTEwQjE4OEE0MUYxNjQzRDlGRDRBM0NDNTlBNTRBRTVCOTRENzVFRkMyODNDNzk2Mjc0NzI1MERBRTdFRDc0MzAxNzc4NkM3OUI4OUQzN0Q3NzgxQjMxM0IwNjQ2MTkxNjQ0ODk3MzdCMTZCQkMwRDREOTc5M0FDNjRCNTk1QUQ0QTk0OTkxNEY3NkU2QjJDQjRGNTlBQTBGOEZEOTY3RTk4MkRBREQxNjg1MkU5RjQwNkREMjc3OTU2Q0UxOTMxNUM0QUYzNjlFREMwNTU0MTZFMEU4MEE3M0NGNjhDMDYzRDExOTBCN0VFM0QyQ0U1NjRFM0M4RjE4M0JDOTA5QkI3MEE0MDYzRUEzNkFFNDFDQkM1NzRERjE0MEY1ODNBQjkwQjNBNTE5OUQ4RUI3QjVBN0E4Qjc2NDY0NDBGMTI0QTlFMUFEQjBDQTQ0MDY1MUM2MkRGNUQmVXNlcklkPTUzNzI3NDUmcHJvcHJpZXRvcj0yJnByb3ByaWV0b3JJZD0yOTM4JnY9My4wJm1ldGhvZD12YXN0Lm9yZGVyLmNhcnQuY291bnQmYXBwaWQ9NGEzNjRmMmQxZjFmYjg0MiZ0aW1lc3RhbXA9MTcyMzYwMTYzOCZzaWduPTMwNjRjYzM1MTU0OTIyNDlmN2FmZmExMjc1YTc2ZTI0\"\n", "decoded_str = decode_base64(encoded_str)\n", "print(decoded_str)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2024-08-14 10:44:43][INFO][2097479055.py] - using mysql host:mysql-8-public.summerfarm.net\n", "[2024-08-14 10:44:44][INFO][2097479055.py] - data from mysql:[{'id': 3, 'created_at': datetime.datetime(2024, 6, 6, 14, 41, 16), 'app_name': 'b<PERSON><PERSON>', 'req_info': '{\"ip\": \"127.0.0.1\", \"url\": \"https://km5.366kmpf.com/kmd/m7ck/Route.axd\", \"body\": \"FKId=2938&FKFlag=2&Token=4B38F1DF6FF1A96C2B30C18D4EB4B340B9D2DE9D34AB8BA3E865AE95DC282C8623750C45A180DF1AD57181D0567677C71CAA3DB7EA5DB80322639C61168D60C388ED85F9B6820431DCCD2A81D3E00A984F46FC6D3712B7F8E97CB9D447C6EC6165AF6E64078376F6DA7C69AA3118BCBEBBF4776291C355235EF109D6A10B188A41F1643D9FD4A3CC59A54AE5B94D75EFC283C7962747250DAE7ED743017786C79B89D37D7781B313B064619164489737B16BBC0D4D9793AC64B595AD4A949914F76E6B2CB4F59AA0F8FD967E982DADD16852E9F406DD277956CE19315C4AF369EDC055416E0E80A73CF68C063D1190B7EE3D2CE564E3C8F183BC909BB70A4063EA36AE41CBC574DF140F583AB90B3A5199D8EB7B5A7A8B7646440F124A9E1ADB0CA440651C62DF5D&UserId=5372745&proprietor=2&proprietorId=2938&v=3.0&method=vast.order.cart.count&appid=4a364f2d1f1fb842&timestamp=1723601638&sign=3064cc3515492249f7affa1275a76e24\", \"port\": \"54260\", \"size\": 780, \"base64\": \"RktJZD0yOTM4JkZLRmxhZz0yJlRva2VuPTRCMzhGMURGNkZGMUE5NkMyQjMwQzE4RDRFQjRCMzQwQjlEMkRFOUQzNEFCOEJBM0U4NjVBRTk1REMyODJDODYyMzc1MEM0NUExODBERjFBRDU3MTgxRDA1Njc2NzdDNzFDQUEzREI3RUE1REI4MDMyMjYzOUM2MTE2OEQ2MEMzODhFRDg1RjlCNjgyMDQzMURDQ0QyQTgxRDNFMDBBOTg0RjQ2RkM2RDM3MTJCN0Y4RTk3Q0I5RDQ0N0M2RUM2MTY1QUY2RTY0MDc4Mzc2RjZEQTdDNjlBQTMxMThCQ0JFQkJGNDc3NjI5MUMzNTUyMzVFRjEwOUQ2QTEwQjE4OEE0MUYxNjQzRDlGRDRBM0NDNTlBNTRBRTVCOTRENzVFRkMyODNDNzk2Mjc0NzI1MERBRTdFRDc0MzAxNzc4NkM3OUI4OUQzN0Q3NzgxQjMxM0IwNjQ2MTkxNjQ0ODk3MzdCMTZCQkMwRDREOTc5M0FDNjRCNTk1QUQ0QTk0OTkxNEY3NkU2QjJDQjRGNTlBQTBGOEZEOTY3RTk4MkRBREQxNjg1MkU5RjQwNkREMjc3OTU2Q0UxOTMxNUM0QUYzNjlFREMwNTU0MTZFMEU4MEE3M0NGNjhDMDYzRDExOTBCN0VFM0QyQ0U1NjRFM0M4RjE4M0JDOTA5QkI3MEE0MDYzRUEzNkFFNDFDQkM1NzRERjE0MEY1ODNBQjkwQjNBNTE5OUQ4RUI3QjVBN0E4Qjc2NDY0NDBGMTI0QTlFMUFEQjBDQTQ0MDY1MUM2MkRGNUQmVXNlcklkPTUzNzI3NDUmcHJvcHJpZXRvcj0yJnByb3ByaWV0b3JJZD0yOTM4JnY9My4wJm1ldGhvZD12YXN0Lm9yZGVyLmNhcnQuY291bnQmYXBwaWQ9NGEzNjRmMmQxZjFmYjg0MiZ0aW1lc3RhbXA9MTcyMzYwMTYzOCZzaWduPTMwNjRjYzM1MTU0OTIyNDlmN2FmZmExMjc1YTc2ZTI0\", \"method\": \"POST\", \"headers\": {\"host\": \"km5.366kmpf.com\", \"accept\": \"*/*\", \"referer\": \"https://servicewechat.com/wx77e2eb49f80331fc/2/page-frame.html\", \"version\": \"3.3.510\", \"deviceno\": \"oka-F5K1YTGdupuhweNNzpzAdD38\", \"platform\": \"Weapp\", \"xweb_xhr\": \"1\", \"terminalid\": \"5\", \"user-agent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF MacWechat/3.8.7(0x13080710) XWEB/1191\", \"content-type\": \"application/x-www-form-urlencoded;charset=UTF-8\", \"content-length\": \"780\", \"sec-fetch-dest\": \"empty\", \"sec-fetch-mode\": \"cors\", \"sec-fetch-site\": \"cross-site\", \"accept-encoding\": \"gzip, br\", \"accept-language\": \"zh-CN,zh;q=0.9\"}, \"httpVersion\": \"2.0\", \"rawHeaderNames\": {\"connection\": \"Connection\", \"proxy-authorization\": \"Proxy-Authorization\"}}', 'update_at': datetime.datetime(2024, 8, 14, 10, 13, 58)}]\n"]}], "source": ["import pymysql\n", "import os\n", "\n", "mysql_host = os.getenv(\"COSFODB_HOST_NAME\", \"mysql-8-public.summerfarm.net\")\n", "logging.info(f'using mysql host:{mysql_host}')\n", "\n", "# Function to establish a database connection\n", "def get_data_from_mysql(query: str = \"\"):\n", "    conn = pymysql.connect(\n", "        host=mysql_host,\n", "        user=\"test\",\n", "        password=\"xianmu619\",\n", "        port=3307,\n", "        db=\"front_db\",\n", "        charset=\"utf8mb4\",\n", "        cursorclass=pymysql.cursors.DictCursor,\n", "    )\n", "    try:\n", "        with conn.cursor() as cursor:\n", "            cursor.execute(query)\n", "            rows = cursor.fetchall()\n", "            return rows\n", "    except Exception as e:\n", "        logging.error(f\"从数据库获取登录token失败:{e}\")\n", "        raise e\n", "    finally:\n", "        conn.close()\n", "\n", "\n", "query = \"select * from app_req_record where app_name = 'baofeng' limit 50\"\n", "req_info_list = get_data_from_mysql(query)\n", "logging.info(f\"data from mysql:{req_info_list}\")\n", "if len(req_info_list) <= 0:\n", "    raise Exception(f\"未能从数据库获取到登录信息,SQL:{query}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from urllib.parse import parse_qs\n", "\n", "param_string = json.loads(req_info_list[0][\"req_info\"]).get(\"body\")\n", "\n", "logging.info(f\"param_string:\\n{param_string}\")\n", "\n", "headers = json.loads(req_info_list[0][\"req_info\"]).get(\"headers\")\n", "\n", "\n", "def params_to_json(param_string=\"\"):\n", "    # Parse the parameter string into a dictionary\n", "    parsed_params = parse_qs(param_string)\n", "\n", "    # Convert single-element lists to values\n", "    params_dict = {k: v[0] if len(v) == 1 else v for k, v in parsed_params.items()}\n", "\n", "    return params_dict\n", "\n", "\n", "login_data = params_to_json(param_string)\n", "logging.info(\n", "    f\"pasred data:{json.dumps(login_data, indent=2, ensure_ascii=False)}\\n\\nheaders:\\n{json.dumps(headers, indent=2, ensure_ascii=False)}\"\n", ")\n", "\n", "data = {\n", "    \"FKFlag\": \"2\",\n", "    \"FKId\": \"2938\",\n", "    \"ProprietorId\": \"2938\",\n", "    \"PageIndex\": \"2\",\n", "    \"PageSize\": \"20\",\n", "    \"ShowNoStock\": \"1\",\n", "    \"IsProduct\": \"1\",\n", "    \"storeisopenb2c\": \"1\",\n", "    \"sortField\": \"sortorder\",\n", "    \"sortDirect\": \"desc\",\n", "    \"Orderby\": \"<sortorder>desc</sortorder>\",\n", "    \"UserId\": \"4141086\",\n", "    \"v\": \"3.0\",\n", "    \"method\": \"vast.mall.product.page\",\n", "    \"appid\": \"4a364f2d1f1fb842\",\n", "    \"timestamp\": \"1723604105\",\n", "    # \"sign\": \"c066e8f380fc95bbf2e0db5039db9fcb\",\n", "}\n", "\n", "data[\"sign\"] = login_data[\"sign\"]\n", "data[\"UserId\"] = login_data[\"UserId\"]\n", "data[\"appid\"] = login_data[\"appid\"]\n", "data[\"Token\"] = login_data[\"Token\"]\n", "data[\"timestamp\"] = login_data[\"timestamp\"]\n", "\n", "logging.info(f\"posting data:{json.dumps(data, indent=2, ensure_ascii=False)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import choice_proxy_randomly, proxy_list\n", "\n", "print(choice_proxy_randomly(proxy_list=proxy_list))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "from datetime import datetime\n", "import brotli\n", "import json\n", "from scripts.proxy_setup import choice_proxy_randomly, proxy_list\n", "\n", "\n", "def get_all_product(page_index: int = 1) -> str:\n", "    url = \"https://km5.366kmpf.com/m7ck/Route.axd\"\n", "    data[\"PageIndex\"] = f\"{page_index}\"\n", "    data[\"timestamp\"] = f\"{int(datetime.now().timestamp())}\"\n", "\n", "    proxies = choice_proxy_randomly(proxy_list)\n", "    logging.info(\"proxies:%s\", proxies)\n", "\n", "    response = requests.post(url, headers=headers, data=data, proxies=proxies)\n", "    logging.info(f\"response headers:{response.headers}\")\n", "\n", "    json_data = {}\n", "    try:\n", "        json_data = response.json()\n", "        if not json_data.get(\"Success\", False):\n", "            logging.error(f\"爬取失败，错误信息:{json_data}\")\n", "            raise Exception(f\"{brand_name}爬取失败:{json_data}\")\n", "\n", "        all_product = json_data.get(\"Content\", {}).get(\"Data\")\n", "        if len(all_product) >= 20:\n", "            logging.info(f\"获取到了20个商品, page_index:{page_index}, 本次调用返回的第一个商品:{all_product[0]}\")\n", "            all_product.extend(get_all_product(page_index=page_index + 1))\n", "            return all_product\n", "        else:\n", "            logging.info(f\"也许是最后一个批次咯:{page_index}, size:{len(all_product)}\")\n", "            return all_product\n", "    except Exception as e:\n", "        logging.error(\n", "            f\"解压失败:{e}, resposne:{response.text}, status:{response.status_code}, json_data:{json_data}\"\n", "        )\n", "        raise e\n", "\n", "\n", "# Usage\n", "all_products = []\n", "try:\n", "    all_products = get_all_product()\n", "except Exception as e:\n", "    logging.error(f\"{brand_name} 爬取失败了,{e}\")\n", "    raise e\n", "if len(all_products) <= 0:\n", "    raise Exception(f\"{brand_name} 爬取了0个商品！\")\n", "all_products_df = pd.DataFrame(all_products)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "all_products_df=all_products_df.astype(str)\n", "all_products_df['competitor']=brand_name\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "logging.info(df.to_string())\n", "logging.info(f\"===new_record==={brand_name}, 商品数:{len(all_products_df)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}
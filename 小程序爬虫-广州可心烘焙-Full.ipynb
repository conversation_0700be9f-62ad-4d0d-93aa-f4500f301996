{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 17:33:45, date_of_now:2024-03-06, brand_name:广州可心烘焙, headers:{'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJMb2dpblRpbWUiOjE3MDI1MjA4ODk0OTcsIlRZUEUiOiJYQ1hfQVBQIiwib3BlbmlkIjo3MTI0NTF9.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q', 'appcodenew': '7798c1f4306b4f89a9fc2a4c2cdc47ac', 'uid': '712451', 'time': '1702521175012'}\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/广州可心烘焙/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "\n", "headers={'token':'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJMb2dpblRpbWUiOjE3MDI1MjA4ODk0OTcsIlRZUEUiOiJYQ1hfQVBQIiwib3BlbmlkIjo3MTI0NTF9.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q',\n", "'appcodenew':'7798c1f4306b4f89a9fc2a4c2cdc47ac',\n", "'uid':'712451',\n", "'time':'1702521175012',}\n", "brand_name='广州可心烘焙'\n", "competitor_name_en='kexinhongbei'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, headers:{headers}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*************:46431\n", "*************:36597\n", "**************:31707\n", "*************:42200\n", "**************:44687\n", "***************:30433\n", "*************:36694\n", "**************:48058\n", "************:44740\n", "*************:40598\n", "['*************:46431', '*************:36597', '**************:31707', '*************:42200', '**************:44687', '***************:30433', '*************:36694', '**************:48058', '************:44740', '*************:40598']\n"]}], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url):\n", "    max_retries=3;\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, data=None, headers=headers, proxies=proxies, cookies=None, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None\n", "def post_remote_data_with_proxy(url):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, data=None, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["根据一级、二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ************************************************\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>mall_id</th>\n", "      <th>mch_id</th>\n", "      <th>parent_id</th>\n", "      <th>name</th>\n", "      <th>pic_url</th>\n", "      <th>sort</th>\n", "      <th>big_pic_url</th>\n", "      <th>advert_pic</th>\n", "      <th>advert_url</th>\n", "      <th>...</th>\n", "      <th>updated_at</th>\n", "      <th>deleted_at</th>\n", "      <th>is_delete</th>\n", "      <th>is_show</th>\n", "      <th>advert_open_type</th>\n", "      <th>advert_params</th>\n", "      <th>link</th>\n", "      <th>child</th>\n", "      <th>page_url</th>\n", "      <th>active</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>235243</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233367</td>\n", "      <td>奥昆塔壳系列</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:17:23</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=235243</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>235246</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233367</td>\n", "      <td>手工巧克力/马卡龙</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:17:57</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=235246</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>237109</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233365</td>\n", "      <td>果糖</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:33:01</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=237109</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>240447</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233365</td>\n", "      <td>其他</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:33:40</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=240447</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>255026</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233365</td>\n", "      <td>即食谷物</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-21 12:05:55</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=255026</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>233658</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233335</td>\n", "      <td>黄油</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:14:16</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233658</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>233659</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233335</td>\n", "      <td>奶油</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:14:35</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233659</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>233669</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233335</td>\n", "      <td>奶粉</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:14:51</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233669</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>233668</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233335</td>\n", "      <td>芝士片</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:15:15</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233668</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>233663</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233335</td>\n", "      <td>牛奶</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:15:31</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233663</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>233662</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233335</td>\n", "      <td>马苏里拉干酪</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:15:56</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233662</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>233661</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233335</td>\n", "      <td>奶油奶酪</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:16:14</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233661</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>235236</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233787</td>\n", "      <td>香肠</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:16:39</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=235236</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>235237</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233787</td>\n", "      <td>牛肉</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 13:32:48</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=235237</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>235241</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233370</td>\n", "      <td>调味品</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:18:54</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=235241</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>238312</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233370</td>\n", "      <td>意面</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:20:00</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=238312</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>235240</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233370</td>\n", "      <td>油</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:20:24</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=235240</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>237700</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233370</td>\n", "      <td>橄榄</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:20:47</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=237700</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>238957</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233370</td>\n", "      <td>其他</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:21:47</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=238957</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>235238</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>233370</td>\n", "      <td>薯条</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 13:33:25</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=235238</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>233670</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>231995</td>\n", "      <td>可可粉</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:22:15</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233670</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>233671</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>231995</td>\n", "      <td>面粉</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:22:45</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233671</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>233672</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>231995</td>\n", "      <td>巧克力</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:23:48</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[{'id': '235077', 'mall_id': '10297', 'mch_id'...</td>\n", "      <td>/pages/goods/list?cat_id=233672</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>234643</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>231995</td>\n", "      <td>肉松</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:27:33</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=234643</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>233673</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>231995</td>\n", "      <td>砂糖</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:29:44</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233673</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>233674</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>231995</td>\n", "      <td>果茸</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:30:04</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[{'id': '235081', 'mall_id': '10297', 'mch_id'...</td>\n", "      <td>/pages/goods/list?cat_id=233674</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>233677</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>231995</td>\n", "      <td>果酱</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:30:27</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233677</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>233676</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>231995</td>\n", "      <td>干果</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:30:51</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233676</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>233678</td>\n", "      <td>10297</td>\n", "      <td>0</td>\n", "      <td>231995</td>\n", "      <td>其他</td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>100</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>...</td>\n", "      <td>2021-04-20 12:31:13</td>\n", "      <td>0000-00-00 00:00:00</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>[]</td>\n", "      <td>/pages/goods/list?cat_id=233678</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>29 rows × 22 columns</p>\n", "</div>"], "text/plain": ["        id mall_id mch_id parent_id       name  \\\n", "0   235243   10297      0    233367     奥昆塔壳系列   \n", "1   235246   10297      0    233367  手工巧克力/马卡龙   \n", "2   237109   10297      0    233365         果糖   \n", "3   240447   10297      0    233365         其他   \n", "4   255026   10297      0    233365       即食谷物   \n", "5   233658   10297      0    233335         黄油   \n", "6   233659   10297      0    233335         奶油   \n", "7   233669   10297      0    233335         奶粉   \n", "8   233668   10297      0    233335        芝士片   \n", "9   233663   10297      0    233335         牛奶   \n", "10  233662   10297      0    233335     马苏里拉干酪   \n", "11  233661   10297      0    233335       奶油奶酪   \n", "12  235236   10297      0    233787         香肠   \n", "13  235237   10297      0    233787         牛肉   \n", "14  235241   10297      0    233370        调味品   \n", "15  238312   10297      0    233370         意面   \n", "16  235240   10297      0    233370          油   \n", "17  237700   10297      0    233370         橄榄   \n", "18  238957   10297      0    233370         其他   \n", "19  235238   10297      0    233370         薯条   \n", "20  233670   10297      0    231995        可可粉   \n", "21  233671   10297      0    231995         面粉   \n", "22  233672   10297      0    231995        巧克力   \n", "23  234643   10297      0    231995         肉松   \n", "24  233673   10297      0    231995         砂糖   \n", "25  233674   10297      0    231995         果茸   \n", "26  233677   10297      0    231995         果酱   \n", "27  233676   10297      0    231995         干果   \n", "28  233678   10297      0    231995         其他   \n", "\n", "                                              pic_url sort big_pic_url  \\\n", "0   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "1   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "2   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "3   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "4   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "5   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "6   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "7   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "8   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "9   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "10  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "11  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "12  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "13  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "14  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "15  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "16  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "17  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "18  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "19  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "20  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "21  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "22  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "23  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "24  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "25  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "26  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "27  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "28  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  100               \n", "\n", "   advert_pic advert_url  ...           updated_at           deleted_at  \\\n", "0                         ...  2021-04-20 12:17:23  0000-00-00 00:00:00   \n", "1                         ...  2021-04-20 12:17:57  0000-00-00 00:00:00   \n", "2                         ...  2021-04-20 12:33:01  0000-00-00 00:00:00   \n", "3                         ...  2021-04-20 12:33:40  0000-00-00 00:00:00   \n", "4                         ...  2021-04-21 12:05:55  0000-00-00 00:00:00   \n", "5                         ...  2021-04-20 12:14:16  0000-00-00 00:00:00   \n", "6                         ...  2021-04-20 12:14:35  0000-00-00 00:00:00   \n", "7                         ...  2021-04-20 12:14:51  0000-00-00 00:00:00   \n", "8                         ...  2021-04-20 12:15:15  0000-00-00 00:00:00   \n", "9                         ...  2021-04-20 12:15:31  0000-00-00 00:00:00   \n", "10                        ...  2021-04-20 12:15:56  0000-00-00 00:00:00   \n", "11                        ...  2021-04-20 12:16:14  0000-00-00 00:00:00   \n", "12                        ...  2021-04-20 12:16:39  0000-00-00 00:00:00   \n", "13                        ...  2021-04-20 13:32:48  0000-00-00 00:00:00   \n", "14                        ...  2021-04-20 12:18:54  0000-00-00 00:00:00   \n", "15                        ...  2021-04-20 12:20:00  0000-00-00 00:00:00   \n", "16                        ...  2021-04-20 12:20:24  0000-00-00 00:00:00   \n", "17                        ...  2021-04-20 12:20:47  0000-00-00 00:00:00   \n", "18                        ...  2021-04-20 12:21:47  0000-00-00 00:00:00   \n", "19                        ...  2021-04-20 13:33:25  0000-00-00 00:00:00   \n", "20                        ...  2021-04-20 12:22:15  0000-00-00 00:00:00   \n", "21                        ...  2021-04-20 12:22:45  0000-00-00 00:00:00   \n", "22                        ...  2021-04-20 12:23:48  0000-00-00 00:00:00   \n", "23                        ...  2021-04-20 12:27:33  0000-00-00 00:00:00   \n", "24                        ...  2021-04-20 12:29:44  0000-00-00 00:00:00   \n", "25                        ...  2021-04-20 12:30:04  0000-00-00 00:00:00   \n", "26                        ...  2021-04-20 12:30:27  0000-00-00 00:00:00   \n", "27                        ...  2021-04-20 12:30:51  0000-00-00 00:00:00   \n", "28                        ...  2021-04-20 12:31:13  0000-00-00 00:00:00   \n", "\n", "   is_delete is_show advert_open_type advert_params  link  \\\n", "0          0       1                                 None   \n", "1          0       1                                 None   \n", "2          0       1                                 None   \n", "3          0       1                                 None   \n", "4          0       1                                 None   \n", "5          0       1                                 None   \n", "6          0       1                                 None   \n", "7          0       1                                 None   \n", "8          0       1                                 None   \n", "9          0       1                                 None   \n", "10         0       1                                 None   \n", "11         0       1                                 None   \n", "12         0       1                                 None   \n", "13         0       1                                 None   \n", "14         0       1                                 None   \n", "15         0       1                                 None   \n", "16         0       1                                 None   \n", "17         0       1                                 None   \n", "18         0       1                                 None   \n", "19         0       1                                 None   \n", "20         0       1                                 None   \n", "21         0       1                                 None   \n", "22         0       1                                 None   \n", "23         0       1                                 None   \n", "24         0       1                                 None   \n", "25         0       1                                 None   \n", "26         0       1                                 None   \n", "27         0       1                                 None   \n", "28         0       1                                 None   \n", "\n", "                                                child  \\\n", "0                                                  []   \n", "1                                                  []   \n", "2                                                  []   \n", "3                                                  []   \n", "4                                                  []   \n", "5                                                  []   \n", "6                                                  []   \n", "7                                                  []   \n", "8                                                  []   \n", "9                                                  []   \n", "10                                                 []   \n", "11                                                 []   \n", "12                                                 []   \n", "13                                                 []   \n", "14                                                 []   \n", "15                                                 []   \n", "16                                                 []   \n", "17                                                 []   \n", "18                                                 []   \n", "19                                                 []   \n", "20                                                 []   \n", "21                                                 []   \n", "22  [{'id': '235077', 'mall_id': '10297', 'mch_id'...   \n", "23                                                 []   \n", "24                                                 []   \n", "25  [{'id': '235081', 'mall_id': '10297', 'mch_id'...   \n", "26                                                 []   \n", "27                                                 []   \n", "28                                                 []   \n", "\n", "                           page_url active  \n", "0   /pages/goods/list?cat_id=235243   True  \n", "1   /pages/goods/list?cat_id=235246  False  \n", "2   /pages/goods/list?cat_id=237109   True  \n", "3   /pages/goods/list?cat_id=240447  False  \n", "4   /pages/goods/list?cat_id=255026  False  \n", "5   /pages/goods/list?cat_id=233658   True  \n", "6   /pages/goods/list?cat_id=233659  False  \n", "7   /pages/goods/list?cat_id=233669  False  \n", "8   /pages/goods/list?cat_id=233668  False  \n", "9   /pages/goods/list?cat_id=233663  False  \n", "10  /pages/goods/list?cat_id=233662  False  \n", "11  /pages/goods/list?cat_id=233661  False  \n", "12  /pages/goods/list?cat_id=235236   True  \n", "13  /pages/goods/list?cat_id=235237  False  \n", "14  /pages/goods/list?cat_id=235241   True  \n", "15  /pages/goods/list?cat_id=238312  False  \n", "16  /pages/goods/list?cat_id=235240  False  \n", "17  /pages/goods/list?cat_id=237700  False  \n", "18  /pages/goods/list?cat_id=238957  False  \n", "19  /pages/goods/list?cat_id=235238  False  \n", "20  /pages/goods/list?cat_id=233670   True  \n", "21  /pages/goods/list?cat_id=233671  False  \n", "22  /pages/goods/list?cat_id=233672  False  \n", "23  /pages/goods/list?cat_id=234643  False  \n", "24  /pages/goods/list?cat_id=233673  False  \n", "25  /pages/goods/list?cat_id=233674  False  \n", "26  /pages/goods/list?cat_id=233677  False  \n", "27  /pages/goods/list?cat_id=233676  False  \n", "28  /pages/goods/list?cat_id=233678  False  \n", "\n", "[29 rows x 22 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["cate_list=json.loads(get_remote_data_with_proxy(\"https://chengxu.97jindianzi.com/addons/zjhj_bd/web/index.php?_mall_id=10297&r=api/cat/list&cat_id=&select_cat_id=\").text)['data']['list']\n", "cate_list_all=[]\n", "for first_cate in cate_list:\n", "    cate_list_all.extend(first_cate['child'])\n", "cate_list_df=pd.DataFrame(cate_list_all)\n", "cate_list_df"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': '235243', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233367', 'name': '奥昆塔壳系列', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210327/a7c6563c11d2052fbc050ab9401957a2.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:17:23', 'updated_at': '2021-04-20 12:17:23', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=235243', 'active': True}\n", "Using proxy: ************************************************\n", "{'id': '235246', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233367', 'name': '手工巧克力/马卡龙', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210327/733e6b35dbe06cc713fc0faaefe90ee1.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:17:57', 'updated_at': '2021-04-20 12:17:57', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=235246', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '237109', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233365', 'name': '果糖', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210328/a7746bc96ec79b591b50b6a22f155116.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:33:01', 'updated_at': '2021-04-20 12:33:01', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=237109', 'active': True}\n", "Using proxy: ***********************************************\n", "{'id': '240447', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233365', 'name': '其他', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210330/d6722c1bd2591b84becc436a4a68aaf0.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:33:40', 'updated_at': '2021-04-20 12:33:40', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=240447', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '255026', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233365', 'name': '即食谷物', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210421/8ab28ff0f0f48686ca2c655e84303f81.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-21 12:05:55', 'updated_at': '2021-04-21 12:05:55', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=255026', 'active': False}\n", "Using proxy: ************************************************\n", "{'id': '233658', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233335', 'name': '黄油', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210324/2a9f7a424590e0a899126a543af19097.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:14:16', 'updated_at': '2021-04-20 12:14:16', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233658', 'active': True}\n", "Using proxy: ***********************************************\n", "{'id': '233659', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233335', 'name': '奶油', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/2a941449f30f268d6e889c7d213d39df.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:14:35', 'updated_at': '2021-04-20 12:14:35', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233659', 'active': False}\n", "Using proxy: ************************************************\n", "{'id': '233669', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233335', 'name': '奶粉', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/e249bfd70362876c109bb8a99b62b511.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:14:51', 'updated_at': '2021-04-20 12:14:51', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233669', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '233668', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233335', 'name': '芝士片', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210324/520a7ff8dce7e4694c6e8deb9131ad92.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:15:15', 'updated_at': '2021-04-20 12:15:15', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233668', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '233663', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233335', 'name': '牛奶', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/e1998a34db34b9b1facb2e71d9c46b73.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:15:31', 'updated_at': '2021-04-20 12:15:31', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233663', 'active': False}\n", "Using proxy: *************************************************\n", "{'id': '233662', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233335', 'name': '马苏里拉干酪', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210327/1220dddbb48b55e2eff4159bd2077702.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:15:56', 'updated_at': '2021-04-20 12:15:56', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233662', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '233661', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233335', 'name': '奶油奶酪', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/9d1c3d00ebf9433b313e6344670ff9b4.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:16:14', 'updated_at': '2021-04-20 12:16:14', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233661', 'active': False}\n", "Using proxy: ************************************************\n", "{'id': '235236', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233787', 'name': '香肠', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210326/8394b729ac13a132a7317125540fc1fc.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:16:39', 'updated_at': '2021-04-20 12:16:39', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=235236', 'active': True}\n", "Using proxy: ************************************************\n", "{'id': '235237', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233787', 'name': '牛肉', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210420/b52256091200b88d6e7a4b16b9213d8d.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 13:32:48', 'updated_at': '2021-04-20 13:32:48', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=235237', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '235241', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233370', 'name': '调味品', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210406/cc2f7a33083a6e82ba5347a1e29a7bb8.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:18:54', 'updated_at': '2021-04-20 12:18:54', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=235241', 'active': True}\n", "Using proxy: ************************************************\n", "{'id': '238312', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233370', 'name': '意面', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210329/a8fd42415f3dd59b5309592426ac76bf.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:20:00', 'updated_at': '2021-04-20 12:20:00', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=238312', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '235240', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233370', 'name': '油', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210328/8372bbea9b4c3a0769aa0ca29db02813.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:20:24', 'updated_at': '2021-04-20 12:20:24', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=235240', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '237700', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233370', 'name': '橄榄', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210328/c61b6a795ddc34e45932d1c0bf877272.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:20:47', 'updated_at': '2021-04-20 12:20:47', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=237700', 'active': False}\n", "Using proxy: ************************************************\n", "{'id': '238957', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233370', 'name': '其他', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210406/0f300ab91f549b96d2e9568ed5f903e4.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:21:47', 'updated_at': '2021-04-20 12:21:47', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=238957', 'active': False}\n", "Using proxy: ************************************************\n", "{'id': '235238', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233370', 'name': '薯条', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210420/d3f431e2f275ec65e02fa682b62e37b8.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 13:33:25', 'updated_at': '2021-04-20 13:33:25', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=235238', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '233670', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '231995', 'name': '可可粉', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/b745dfda5db83b0f3fc4075fe14a38b8.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:22:15', 'updated_at': '2021-04-20 12:22:15', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233670', 'active': True}\n", "Using proxy: *************************************************\n", "{'id': '233671', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '231995', 'name': '面粉', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/821d615d6e78c785274adb5f7bb5030b.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:22:45', 'updated_at': '2021-04-20 12:22:45', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233671', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '233672', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '231995', 'name': '巧克力', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/005cf472ad2eb97dc1bed12fc59fc08e.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:23:48', 'updated_at': '2021-04-20 12:23:48', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [{'id': '235077', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233672', 'name': '嘉利宝巧克力系列', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/a2ca3fc1fc5f39ce3853e83beebb3555.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:24:56', 'updated_at': '2021-04-20 12:24:56', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'page_url': '/pages/goods/list?cat_id=235077', 'active': True}, {'id': '240592', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233672', 'name': '法芙娜巧克力', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210331/8d4900ab13474e7bbaec06a781459e38.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:27:02', 'updated_at': '2021-04-20 12:27:02', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'page_url': '/pages/goods/list?cat_id=240592', 'active': False}, {'id': '240591', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233672', 'name': '梵豪登巧克力', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210330/29f6e4b7824921d3412b244cba1e11d3.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:27:58', 'updated_at': '2021-04-20 12:27:58', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'page_url': '/pages/goods/list?cat_id=240591', 'active': False}, {'id': '237084', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233672', 'name': '其他', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210328/b0c700f52e35b06bbde091444a1b6b22.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:28:21', 'updated_at': '2021-04-20 12:28:21', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'page_url': '/pages/goods/list?cat_id=237084', 'active': False}, {'id': '235079', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233672', 'name': '赛梦巧克力系列', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210326/058eaddc67d1988254d6422328d5b67f.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:28:56', 'updated_at': '2021-04-20 12:28:56', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'page_url': '/pages/goods/list?cat_id=235079', 'active': False}, {'id': '235078', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233672', 'name': '可可百利巧克力系列', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/c468c7b798f1891ddee2bfcade4218cd.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:29:20', 'updated_at': '2021-04-20 12:29:20', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'page_url': '/pages/goods/list?cat_id=235078', 'active': False}], 'page_url': '/pages/goods/list?cat_id=233672', 'active': False}\n", "Using proxy: ************************************************\n", "{'id': '234643', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '231995', 'name': '肉松', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/f90a3c2e13c844e095a41ed204634228.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:27:33', 'updated_at': '2021-04-20 12:27:33', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=234643', 'active': False}\n", "Using proxy: ************************************************\n", "{'id': '233673', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '231995', 'name': '砂糖', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/ef61312fa78d1673a9c82a2741c38379.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:29:44', 'updated_at': '2021-04-20 12:29:44', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233673', 'active': False}\n", "Using proxy: **********************************************\n", "{'id': '233674', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '231995', 'name': '果茸', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210326/421957201cbc1f1d23ddffc7f3d1652f.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:30:04', 'updated_at': '2021-04-20 12:30:04', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [{'id': '235081', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233674', 'name': '法国宝茸系列', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/d8147f0f9e6be1f10cc2764039f6aa21.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:31:44', 'updated_at': '2021-04-20 12:31:44', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'page_url': '/pages/goods/list?cat_id=235081', 'active': True}, {'id': '235083', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233674', 'name': '法国乐果芬系列', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210326/383c20031767283ed3cd551cc6984599.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:32:08', 'updated_at': '2021-04-20 12:32:08', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'page_url': '/pages/goods/list?cat_id=235083', 'active': False}, {'id': '235082', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '233674', 'name': '法国自由国园系列', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210326/421957201cbc1f1d23ddffc7f3d1652f.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:32:26', 'updated_at': '2021-04-20 12:32:26', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'page_url': '/pages/goods/list?cat_id=235082', 'active': False}], 'page_url': '/pages/goods/list?cat_id=233674', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '233677', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '231995', 'name': '果酱', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210325/801f8a6cef9cdae9a7697da4e93795ec.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:30:27', 'updated_at': '2021-04-20 12:30:27', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233677', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '233676', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '231995', 'name': '干果', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210327/31531044454af88eed9d613b6af8d3d1.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:30:51', 'updated_at': '2021-04-20 12:30:51', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233676', 'active': False}\n", "Using proxy: ***********************************************\n", "{'id': '233678', 'mall_id': '10297', 'mch_id': '0', 'parent_id': '231995', 'name': '其他', 'pic_url': 'https://jindianzihj.oss-cn-hangzhou.aliyuncs.com/uploads/mall10297/20210409/7131df6d62221edb0239de8e15df1fae.jpg', 'sort': '100', 'big_pic_url': '', 'advert_pic': '', 'advert_url': '', 'status': '1', 'created_at': '2021-04-20 12:31:13', 'updated_at': '2021-04-20 12:31:13', 'deleted_at': '0000-00-00 00:00:00', 'is_delete': '0', 'is_show': '1', 'advert_open_type': '', 'advert_params': '', 'link': None, 'child': [], 'page_url': '/pages/goods/list?cat_id=233678', 'active': False}\n", "Using proxy: ***********************************************\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>min_number</th>\n", "      <th>goods_warehouse_id</th>\n", "      <th>mch_id</th>\n", "      <th>status</th>\n", "      <th>sign</th>\n", "      <th>name</th>\n", "      <th>subtitle</th>\n", "      <th>keyword</th>\n", "      <th>cover_pic</th>\n", "      <th>...</th>\n", "      <th>sales</th>\n", "      <th>goods_stock</th>\n", "      <th>goods_num</th>\n", "      <th>type</th>\n", "      <th>buy_goods_auth</th>\n", "      <th>sell_time</th>\n", "      <th>is_finish_sell</th>\n", "      <th>limit_buy</th>\n", "      <th>level_show</th>\n", "      <th>vip_card_appoint</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3573750</td>\n", "      <td>1</td>\n", "      <td>1058433</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>奥昆葡式蛋挞液907g*12 耐焙烤调理淡奶油蛋塔 蛋挞水/蛋挞汁</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售346件</td>\n", "      <td>118</td>\n", "      <td>118</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3562253</td>\n", "      <td>1</td>\n", "      <td>1052307</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>新西兰原装进口安佳黄油227g有盐黄油</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售489个</td>\n", "      <td>279</td>\n", "      <td>279</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3560907</td>\n", "      <td>1</td>\n", "      <td>1051778</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>法国总统黄油卷黄油棒250g*24卷整箱动物</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售642件</td>\n", "      <td>450</td>\n", "      <td>450</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3561107</td>\n", "      <td>1</td>\n", "      <td>1051797</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>安佳片状黄油1kg整箱20起酥牛角可颂烘焙原料</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售246件</td>\n", "      <td>163</td>\n", "      <td>163</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3560761</td>\n", "      <td>1</td>\n", "      <td>1051776</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>总统淡味黄油块动物性发酵进口牛油500g</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售271件</td>\n", "      <td>9945</td>\n", "      <td>9945</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>3648691</td>\n", "      <td>1</td>\n", "      <td>1090144</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>椰夫黄金烤椰片2.5kg 海南香脆椰子碎片即食 蛋糕装饰烘焙原料</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售245袋</td>\n", "      <td>189</td>\n", "      <td>189</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>3561168</td>\n", "      <td>1</td>\n", "      <td>1051858</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>百事达栗子蓉栗子泥1kg</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售432袋</td>\n", "      <td>123</td>\n", "      <td>123</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>3561173</td>\n", "      <td>1</td>\n", "      <td>1051861</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>日本丸久五十铃抹茶粉100g</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售350袋</td>\n", "      <td>9121</td>\n", "      <td>9121</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>3654807</td>\n", "      <td>1</td>\n", "      <td>1093478</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>椰夫免洗提子干 红提子葡萄干 蛋糕面包西饼干糖果烘焙原料9kg/箱</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售63件</td>\n", "      <td>1567</td>\n", "      <td>1567</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>3561181</td>\n", "      <td>1</td>\n", "      <td>1051869</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>马达加斯加香草荚 香子兰豆  烘焙精棒条18-22CM100g</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售197包</td>\n", "      <td>245</td>\n", "      <td>245</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>99 rows × 30 columns</p>\n", "</div>"], "text/plain": ["         id  min_number  goods_warehouse_id  mch_id  status sign  \\\n", "0   3573750           1             1058433       0       1        \n", "1   3562253           1             1052307       0       1        \n", "2   3560907           1             1051778       0       1        \n", "3   3561107           1             1051797       0       1        \n", "4   3560761           1             1051776       0       1        \n", "..      ...         ...                 ...     ...     ...  ...   \n", "94  3648691           1             1090144       0       1        \n", "95  3561168           1             1051858       0       1        \n", "96  3561173           1             1051861       0       1        \n", "97  3654807           1             1093478       0       1        \n", "98  3561181           1             1051869       0       1        \n", "\n", "                                 name subtitle keyword  \\\n", "0   奥昆葡式蛋挞液907g*12 耐焙烤调理淡奶油蛋塔 蛋挞水/蛋挞汁                    \n", "1                 新西兰原装进口安佳黄油227g有盐黄油                    \n", "2              法国总统黄油卷黄油棒250g*24卷整箱动物                    \n", "3             安佳片状黄油1kg整箱20起酥牛角可颂烘焙原料                    \n", "4                总统淡味黄油块动物性发酵进口牛油500g                    \n", "..                                ...      ...     ...   \n", "94   椰夫黄金烤椰片2.5kg 海南香脆椰子碎片即食 蛋糕装饰烘焙原料                    \n", "95                       百事达栗子蓉栗子泥1kg                    \n", "96                     日本丸久五十铃抹茶粉100g                    \n", "97  椰夫免洗提子干 红提子葡萄干 蛋糕面包西饼干糖果烘焙原料9kg/箱                    \n", "98    马达加斯加香草荚 香子兰豆  烘焙精棒条18-22CM100g                    \n", "\n", "                                            cover_pic  ...   sales  \\\n", "0   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售346件   \n", "1   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售489个   \n", "2   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售642件   \n", "3   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售246件   \n", "4   https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售271件   \n", "..                                                ...  ...     ...   \n", "94  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售245袋   \n", "95  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售432袋   \n", "96  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售350袋   \n", "97  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...   已售63件   \n", "98  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售197包   \n", "\n", "   goods_stock goods_num   type  buy_goods_auth  sell_time  is_finish_sell  \\\n", "0          118       118  goods            True          0           False   \n", "1          279       279  goods            True          0           False   \n", "2          450       450  goods            True          0           False   \n", "3          163       163  goods            True          0           False   \n", "4         9945      9945  goods            True          0           False   \n", "..         ...       ...    ...             ...        ...             ...   \n", "94         189       189  goods            True          0           False   \n", "95         123       123  goods            True          0           False   \n", "96        9121      9121  goods            True          0           False   \n", "97        1567      1567  goods            True          0           False   \n", "98         245       245  goods            True          0           False   \n", "\n", "                                            limit_buy level_show  \\\n", "0   {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "1   {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "2   {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "3   {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "4   {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "..                                                ...        ...   \n", "94  {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "95  {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "96  {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "97  {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "98  {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "\n", "                                     vip_card_appoint  \n", "0   {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "1   {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "2   {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "3   {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "4   {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "..                                                ...  \n", "94  {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "95  {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "96  {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "97  {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "98  {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "\n", "[99 rows x 30 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["product_list_all=[]\n", "for cate in cate_list_all:\n", "    cat_id=cate['id']\n", "    print(cate)\n", "    url=f'https://chengxu.97jindianzi.com/addons/zjhj_bd/web/index.php?_mall_id=10297&r=api/default/goods-list&mch_id=&page=1&cat_id={cat_id}&sort=1&sort_type=1&keyword=&coupon_id=0&sign='\n", "    product_list=json.loads(get_remote_data_with_proxy(url).text)['data'][\"list\"]\n", "    product_list_all.extend(product_list)\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df\n", "    "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>min_number</th>\n", "      <th>goods_warehouse_id</th>\n", "      <th>mch_id</th>\n", "      <th>status</th>\n", "      <th>sign</th>\n", "      <th>name</th>\n", "      <th>subtitle</th>\n", "      <th>keyword</th>\n", "      <th>cover_pic</th>\n", "      <th>...</th>\n", "      <th>sales</th>\n", "      <th>goods_stock</th>\n", "      <th>goods_num</th>\n", "      <th>type</th>\n", "      <th>buy_goods_auth</th>\n", "      <th>sell_time</th>\n", "      <th>is_finish_sell</th>\n", "      <th>limit_buy</th>\n", "      <th>level_show</th>\n", "      <th>vip_card_appoint</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3573750</td>\n", "      <td>1</td>\n", "      <td>1058433</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>奥昆葡式蛋挞液907g*12 耐焙烤调理淡奶油蛋塔 蛋挞水/蛋挞汁</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售346件</td>\n", "      <td>118</td>\n", "      <td>118</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3562253</td>\n", "      <td>1</td>\n", "      <td>1052307</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>新西兰原装进口安佳黄油227g有盐黄油</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售489个</td>\n", "      <td>279</td>\n", "      <td>279</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3560907</td>\n", "      <td>1</td>\n", "      <td>1051778</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>法国总统黄油卷黄油棒250g*24卷整箱动物</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...</td>\n", "      <td>...</td>\n", "      <td>已售642件</td>\n", "      <td>450</td>\n", "      <td>450</td>\n", "      <td>goods</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>{'status': 0, 'rest_number': 0, 'type': 'day',...</td>\n", "      <td>2</td>\n", "      <td>{'discount': None, 'is_my_vip_card_goods': 0, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 30 columns</p>\n", "</div>"], "text/plain": ["        id  min_number  goods_warehouse_id  mch_id  status sign  \\\n", "0  3573750           1             1058433       0       1        \n", "1  3562253           1             1052307       0       1        \n", "2  3560907           1             1051778       0       1        \n", "\n", "                                name subtitle keyword  \\\n", "0  奥昆葡式蛋挞液907g*12 耐焙烤调理淡奶油蛋塔 蛋挞水/蛋挞汁                    \n", "1                新西兰原装进口安佳黄油227g有盐黄油                    \n", "2             法国总统黄油卷黄油棒250g*24卷整箱动物                    \n", "\n", "                                           cover_pic  ...   sales goods_stock  \\\n", "0  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售346件         118   \n", "1  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售489个         279   \n", "2  https://jindianzihj.oss-cn-hangzhou.aliyuncs.c...  ...  已售642件         450   \n", "\n", "  goods_num   type  buy_goods_auth  sell_time  is_finish_sell  \\\n", "0       118  goods            True          0           False   \n", "1       279  goods            True          0           False   \n", "2       450  goods            True          0           False   \n", "\n", "                                           limit_buy level_show  \\\n", "0  {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "1  {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "2  {'status': 0, 'rest_number': 0, 'type': 'day',...          2   \n", "\n", "                                    vip_card_appoint  \n", "0  {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "1  {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "2  {'discount': None, 'is_my_vip_card_goods': 0, ...  \n", "\n", "[3 rows x 30 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(product_list_all_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:奥昆葡式蛋挞液907g*12 耐焙烤调理淡奶油蛋塔 蛋挞水/蛋挞汁, usage:{\"prompt_tokens\": 54, \"total_tokens\": 54}, time cost:628.5810470581055ms\n", "inputText:新西兰原装进口安佳黄油227g有盐黄油, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:538.3431911468506ms\n", "inputText:法国总统黄油卷黄油棒250g*24卷整箱动物, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:1215.1424884796143ms\n", "inputText:安佳片状黄油1kg整箱20起酥牛角可颂烘焙原料, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:502.0124912261963ms\n", "inputText:总统淡味黄油块动物性发酵进口牛油500g, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:564.0354156494141ms\n", "inputText:总统黄油10kg法国原装进口动物性淡味发酵大黄油, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:532.461404800415ms\n", "inputText:立牧牌黄油 立牧牌牛油砖 立牧人造奶油 有盐黄油 185g/块*40, usage:{\"prompt_tokens\": 50, \"total_tokens\": 50}, time cost:538.203239440918ms\n", "inputText:安佳黄油5kg 原味烘焙动物原装进口大淡味牛油, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:664.7412776947021ms\n", "inputText:新西兰黄油25kg 安佳淡味大黄油新西兰25黄油公斤 NZMP两点水黄油, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:638.6449337005615ms\n", "inputText:安佳大黄油25kg进口原味动物性黄油牛油面包饼干蛋糕烘焙原料, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:557.2371482849121ms\n", "inputText:安佳黄油454g家用新西兰动物性淡味黄油面包饼干牛轧糖, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:547.2872257232666ms\n", "inputText:纽麦福淡奶油 动物性奶油易打发蛋糕裱花鲜奶油 , usage:{\"prompt_tokens\": 42, \"total_tokens\": 42}, time cost:637.1626853942871ms\n", "inputText:安佳淡奶油动物性原装冰淇淋蛋糕烘焙原材料奶油整箱, usage:{\"prompt_tokens\": 41, \"total_tokens\": 41}, time cost:657.5295925140381ms\n", "inputText:铁塔淡奶油 法国原装鲜奶油 爱乐薇稀奶油忌廉1L*12, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:654.4766426086426ms\n", "inputText:法国伊斯尼淡奶油1L*6瓶 稀奶油动物鲜奶油蛋糕裱花, usage:{\"prompt_tokens\": 47, \"total_tokens\": 47}, time cost:565.7219886779785ms\n", "inputText:肯迪亚淡奶油1l*12盒 肯迪雅淡奶油稀奶油动物性裱花奶油, usage:{\"prompt_tokens\": 47, \"total_tokens\": 47}, time cost:568.8824653625488ms\n", "inputText:英国进口蓝风车淡奶油整箱包邮动物性蓝米吉稀奶油蛋糕裱花, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:509.92512702941895ms\n", "inputText:总统淡奶油1L *6整箱法国进口动物性淡忌廉蛋糕裱花装饰烘焙, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:616.0883903503418ms\n", "inputText:雀巢淡奶油1L动物性.稀奶油打发.淡奶油.蛋糕裱花.冰淇淋烘焙原, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:533.6565971374512ms\n", "inputText:新西兰牧恩淡奶油1L*12 稀奶油动物性奶油蛋糕裱花奶盖冰淇淋奶油, usage:{\"prompt_tokens\": 56, \"total_tokens\": 56}, time cost:603.7466526031494ms\n", "inputText:琪雷萨淡奶油1l*12盒动物性淡奶油蛋糕裱花鲜奶油咖啡稀奶油整箱, usage:{\"prompt_tokens\": 56, \"total_tokens\": 56}, time cost:613.1339073181152ms\n", "inputText:新西兰原装进口安佳全脂烘焙奶粉袋装400g, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:599.8332500457764ms\n", "inputText:妙可蓝多芝士片 黄片橙片三明治汉堡即食奶酪片80片984g, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:566.9100284576416ms\n", "inputText:安佳芝士片84片 橙色片切达奶酪黄色干酪片 1.04kg, usage:{\"prompt_tokens\": 37, \"total_tokens\": 37}, time cost:573.2274055480957ms\n", "inputText:安佳芝士片84片 白色片切达奶酪黄色干酪片 1.04kg, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:593.0273532867432ms\n", "inputText:安佳芝士片250g 独立包装12片儿童辅食三明治汉堡奶酪, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:597.9154109954834ms\n", "inputText:马苏里拉芝士碎奶酪丝 爱氏晨曦2kg*6 披萨原料, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:566.6797161102295ms\n", "inputText:安佳马苏里拉芝士碎2kg/包 安佳碎 披萨焗饭起司拉丝, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:569.6640014648438ms\n", "inputText:安佳马苏里拉芝士新西兰原装进口10kg/条奶油奶酪披萨专用, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:594.9087142944336ms\n", "inputText:安佳芝士碎12kg 新西兰安佳马苏碎奶酪丁 快餐店披萨专用拉丝, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:552.6578426361084ms\n", "inputText:法国进口kiri凯瑞奶油芝士1kg奶油奶酪乳酪蛋糕烘焙原料, usage:{\"prompt_tokens\": 48, \"total_tokens\": 48}, time cost:542.4911975860596ms\n", "inputText:琪雷萨马斯卡普芝士500G*6盒提拉米苏原料马斯卡布尼奶酪, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:565.9701824188232ms\n", "inputText:整块20kg 安佳奶油奶酪芝士 鲜奶油干酪忌廉芝士乳酪蛋糕原料, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:588.2048606872559ms\n", "inputText:格尔巴尼马斯卡布尼奶酪芝士提拉米苏原料 500g, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:625.3299713134766ms\n", "inputText:安佳高熔点芝士块2kg奶酪丁车打切达干酪耐高温烤不化欧包, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:667.2604084014893ms\n", "inputText:澳大利亚百嘉奶油奶酪 必佳芝士2kg CreamCheese 奶油芝士, usage:{\"prompt_tokens\": 43, \"total_tokens\": 43}, time cost:577.1336555480957ms\n", "inputText:伊斯尼白乳酪 微酸乳酪诺曼底风味奶酪500g, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:559.6697330474854ms\n", "inputText:蓝多奶油芝士240g/盒 轻奶酪芝士蛋糕面包家庭烘焙原料, usage:{\"prompt_tokens\": 47, \"total_tokens\": 47}, time cost:582.4484825134277ms\n", "inputText:澳洲进口mg新泰尔奶油芝士 mg奶油奶酪奶酪起司 10kg/箱, usage:{\"prompt_tokens\": 42, \"total_tokens\": 42}, time cost:687.0431900024414ms\n", "inputText:铁塔奶油芝士2kg 奶酪奶油 起司奶盖 乳酪干酪2KG, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:576.9448280334473ms\n", "inputText:俏果椰浆泰国进口CHAOKOH椰浆400ml*24瓶整箱 冬阴功咖喱西米露料, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:577.1481990814209ms\n", "inputText:赛梦可可粉1kg 烘焙用可可粉, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:632.418155670166ms\n", "inputText:法芙娜可可粉纯巧克力粉250g, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:672.5583076477051ms\n", "inputText:法芙娜可可粉1kg, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:650.3121852874756ms\n", "inputText:法国进口可可百利防潮可可粉, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:649.2419242858887ms\n", "inputText:日清百合花中筋粉 2.5kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:784.3804359436035ms\n", "inputText:日本进口 日清山茶花 高筋面粉, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:549.3834018707275ms\n", "inputText:赛梦踪迹系列象牙海岸黑巧克力粒68%2.5kg 烘焙用纯脂调温巧克力, usage:{\"prompt_tokens\": 51, \"total_tokens\": 51}, time cost:547.8692054748535ms\n", "inputText:法国进口赛梦踪迹系列黑巧克力粒75%2.5kg cemoi烘焙调温巧克力, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:720.0913429260254ms\n", "inputText:赛梦粒状黑巧克力50% 烘焙蛋糕面包用巧克力材料 可调温巧克力, usage:{\"prompt_tokens\": 49, \"total_tokens\": 49}, time cost:1179.8591613769531ms\n", "inputText:赛梦粒状牛奶巧克力35%1kg 烘焙蛋糕面包用巧克力材料 可调温巧克, usage:{\"prompt_tokens\": 53, \"total_tokens\": 53}, time cost:554.6152591705322ms\n", "inputText:法国赛梦粒状黑巧克64% 1kg 进口烘焙用蛋糕面包巧克力, usage:{\"prompt_tokens\": 43, \"total_tokens\": 43}, time cost:645.2479362487793ms\n", "inputText:赛梦踪迹系列象牙海岸可可液块2.5kg 烘焙用巧克力, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:646.9757556915283ms\n", "inputText:法国赛梦cemoi传承系列粒状牛奶巧克力38% 烘焙用可调温巧克力粒, usage:{\"prompt_tokens\": 47, \"total_tokens\": 47}, time cost:569.3097114562988ms\n", "inputText:法国赛梦传承系列粒状白巧克力31% 进口烘焙用蛋糕面包巧克力, usage:{\"prompt_tokens\": 47, \"total_tokens\": 47}, time cost:533.5180759429932ms\n", "inputText:法国进口赛梦粒状黑巧克力72% 烘焙用调温巧克力粒巧克力原材料, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:519.1924571990967ms\n", "inputText:法国进口赛梦粒状黑巧克力58.5% 烘焙用调温巧克力粒巧克力原材料, usage:{\"prompt_tokens\": 48, \"total_tokens\": 48}, time cost:509.2313289642334ms\n", "inputText:闽众禧原味肉松H12.5kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:754.3668746948242ms\n", "inputText:闽源丰海苔肉松 海苔酥脆松肉粉松蛋糕寿司肉松小贝用1kg/袋, usage:{\"prompt_tokens\": 52, \"total_tokens\": 52}, time cost:565.079927444458ms\n", "inputText:味斯美肉松海苔酥脆松海苔A级2.5kg肉松, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:535.5432033538818ms\n", "inputText:味斯美海苔肉松碎儿童肉松寿司饭团专用烘焙脆肉松小贝, usage:{\"prompt_tokens\": 48, \"total_tokens\": 48}, time cost:542.1490669250488ms\n", "inputText:味斯美原味酥脆松2kg 饭团寿司手抓饼面包小贝肉松3A香酥脆肉松, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:539.2012596130371ms\n", "inputText:味斯美装饰松松短纤金丝肉松馅料1kg, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:560.8866214752197ms\n", "inputText:味斯美肉松蟹黄味酥脆松2kg蟹小方肉松蛋糕原料, usage:{\"prompt_tokens\": 43, \"total_tokens\": 43}, time cost:548.2258796691895ms\n", "inputText:黑旗心语原味辣味肉松2号肉粉松零食寿司烘焙原料1.5kg, usage:{\"prompt_tokens\": 42, \"total_tokens\": 42}, time cost:515.4294967651367ms\n", "inputText:黑旗肉松 海苔酥松烘焙肉粉松海苔脆松, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:665.4856204986572ms\n", "inputText:黑旗心语原味辣味肉松2号猪肉粉松零食寿司烘焙原料1.5kg, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:654.4201374053955ms\n", "inputText:太古优级白砂糖太古白糖包424包×5g, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:671.8325614929199ms\n", "inputText:太古黄糖包 纯正优质金黄赤砂糖 咖啡调糖伴侣 5gX454, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:488.7855052947998ms\n", "inputText:太古糖霜糖粉一级糖霜蛋糕面包饼干装饰原料原装1kg, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:537.9502773284912ms\n", "inputText:太古金黄糖浆 广式月饼华夫饼铜锣烧 黄金糖浆 6.81L烘焙原料, usage:{\"prompt_tokens\": 52, \"total_tokens\": 52}, time cost:533.6771011352539ms\n", "inputText:烘焙原料 太古糖浆 太古金黄糖浆 甜点 烘焙 咖啡材料 400g*12/箱, usage:{\"prompt_tokens\": 52, \"total_tokens\": 52}, time cost:520.3392505645752ms\n", "inputText:太古红标A级糖霜烘焙商用糖粉13.62kg, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:632.1377754211426ms\n", "inputText:太古糖霜13.62kg铁桶蓝标糖粉饼干蛋糕幼滑糖霜烘焙原料, usage:{\"prompt_tokens\": 53, \"total_tokens\": 53}, time cost:563.7385845184326ms\n", "inputText:太古赤砂糖 太古红糖甘蔗原汁榨取红糖5000克 赤砂糖, usage:{\"prompt_tokens\": 51, \"total_tokens\": 51}, time cost:513.4825706481934ms\n", "inputText:太古赤砂糖1kg 红糖甜品饮料咖啡调味烘焙原料, usage:{\"prompt_tokens\": 42, \"total_tokens\": 42}, time cost:606.1146259307861ms\n", "inputText:太古黄金幼砂糖金黄黄糖赤砂糖细颗粒1KG, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:494.2173957824707ms\n", "inputText: 安德鲁速冻草莓果茸1kg果溶泥, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:599.233865737915ms\n", "inputText: 安德鲁速冻树莓果茸1kg果溶泥, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:532.4571132659912ms\n", "inputText:法国原装进口Boiron宝茸速冻蓝莓果茸, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:522.0105648040771ms\n", "inputText:法国乐果纷速冻芒果果茸10kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:611.6459369659424ms\n", "inputText: 安德鲁速冻阿方索芒果茸1kg果溶泥, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:556.647539138794ms\n", "inputText:法国原装进口Boiron宝茸速冻芒果茸, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:566.3349628448486ms\n", "inputText:法国原装进口Boiron宝茸速冻草莓果茸, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:607.1944236755371ms\n", "inputText:法国原装进口Boiron宝茸速冻覆盆子/红梅果茸, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:923.2356548309326ms\n", "inputText:安德鲁速冻果溶青柠檬果泥果茸, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:556.3657283782959ms\n", "inputText:法国乐果纷速冻荔枝果茸1kg, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:548.964262008667ms\n", "inputText:日本进口正荣绿开心果酱绿开心果泥, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:534.1196060180664ms\n", "inputText:土耳其进口榛子粉 烘焙原料熟榛子仁粉1kg, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:600.4385948181152ms\n", "inputText:瑞士Hero英雄牌原味栗子泥900g, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:516.7157649993896ms\n", "inputText:碧琪瑞士进口栗子茸900g, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:529.7317504882812ms\n", "inputText:六合雪媚娘皮600g*15包/箱 日式雪梅娘皮, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:569.1359043121338ms\n", "inputText:大卫吉利丁片 明胶片 吉利丁片约2.5g/片 鱼胶片凝胶片烘焙原料, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:532.4485301971436ms\n", "inputText:椰夫牌黄金烤椰粒黄金椰蓉麻糬雪媚娘椰丝原料, usage:{\"prompt_tokens\": 48, \"total_tokens\": 48}, time cost:664.0312671661377ms\n", "inputText:椰夫黄金烤椰片2.5kg 海南香脆椰子碎片即食 蛋糕装饰烘焙原料, usage:{\"prompt_tokens\": 54, \"total_tokens\": 54}, time cost:551.0141849517822ms\n", "inputText:百事达栗子蓉栗子泥1kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:497.85685539245605ms\n", "inputText:日本丸久五十铃抹茶粉100g, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:670.3736782073975ms\n", "inputText:椰夫免洗提子干 红提子葡萄干 蛋糕面包西饼干糖果烘焙原料9kg/箱, usage:{\"prompt_tokens\": 54, \"total_tokens\": 54}, time cost:647.8080749511719ms\n", "inputText:马达加斯加香草荚 香子兰豆  烘焙精棒条18-22CM100g, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:561.9533061981201ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['name'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
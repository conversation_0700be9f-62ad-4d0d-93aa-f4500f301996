#!/usr/bin/env python3
"""
测试多品牌爬虫的结构化输出
"""

import sys
import os
import tempfile
import io
from contextlib import redirect_stdout
from pathlib import Path

# 添加scripts目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

def test_multi_brand_reporter():
    """测试多品牌SpiderResultReporter的使用"""
    print("=== 测试多品牌爬虫结构化输出 ===\n")
    
    try:
        from proxy_setup import create_spider_reporter
        
        # 测试1: 多地区标果
        print("1. 测试多地区标果爬虫...")
        reporter = create_spider_reporter("biaoguo_with_prop_spider.py", "标果多地区")
        
        # 模拟多地区成功情况
        region_names = ["标果-杭州", "标果-长沙", "标果-广东", "标果-川渝"]
        result_cnt = 150
        
        captured_output = io.StringIO()
        with redirect_stdout(captured_output):
            result = reporter.report_success(
                product_count=result_cnt,
                additional_info={
                    "regions": region_names,
                    "region_count": len(region_names),
                    "processed_regions": ','.join(region_names)
                }
            )
        
        output = captured_output.getvalue()
        print(f"  输出: {output.strip()}")
        
        # 验证JSON输出
        if "SPIDER_RESULT_JSON:" in output:
            import json
            json_part = output.split("SPIDER_RESULT_JSON:")[1].strip()
            parsed = json.loads(json_part)
            print(f"  ✅ JSON解析成功")
            print(f"     品牌: {parsed['brand_name']}")
            print(f"     数量: {parsed['product_count']}")
            print(f"     地区: {parsed.get('additional_info', {}).get('processed_regions', 'N/A')}")
            assert parsed["status"] == "success"
            assert parsed["product_count"] == 150
            assert parsed["brand_name"] == "标果多地区"
        
        # 测试2: 有赞多品牌
        print("\n2. 测试有赞多品牌爬虫...")
        reporter2 = create_spider_reporter("youzan_spider.py", "优享鲜焙,料料活子")
        
        captured_output = io.StringIO()
        with redirect_stdout(captured_output):
            result = reporter2.report_success(
                product_count=80,
                additional_info={
                    "brands": ["优享鲜焙", "料料活子"],
                    "recent_records": "模拟历史记录"
                }
            )
        
        output = captured_output.getvalue()
        if "SPIDER_RESULT_JSON:" in output:
            json_part = output.split("SPIDER_RESULT_JSON:")[1].strip()
            parsed = json.loads(json_part)
            print(f"  ✅ 有赞多品牌JSON解析成功")
            print(f"     品牌: {parsed['brand_name']}")
            print(f"     数量: {parsed['product_count']}")
        
        # 测试3: 多地区失败情况
        print("\n3. 测试多地区失败情况...")
        captured_output = io.StringIO()
        with redirect_stdout(captured_output):
            result = reporter.report_failure(
                error_message="所有地区都失败",
                error_type="all_regions_failed",
                additional_info={
                    "attempted_regions": region_names,
                    "region_count": len(region_names)
                }
            )
        
        output = captured_output.getvalue()
        if "SPIDER_RESULT_JSON:" in output:
            json_part = output.split("SPIDER_RESULT_JSON:")[1].strip()
            parsed = json.loads(json_part)
            print(f"  ✅ 失败情况JSON解析成功")
            print(f"     状态: {parsed['status']}")
            print(f"     错误: {parsed['error_message']}")
        
        # 测试4: 部分成功情况
        print("\n4. 测试部分成功情况...")
        captured_output = io.StringIO()
        with redirect_stdout(captured_output):
            result = reporter.report_partial_success(
                product_count=60,
                warning_message="部分地区失败，成功2/4个地区",
                additional_info={
                    "successful_regions": ["标果-杭州", "标果-长沙"],
                    "failed_regions": ["标果-广东", "标果-川渝"]
                }
            )
        
        output = captured_output.getvalue()
        if "SPIDER_RESULT_JSON:" in output:
            json_part = output.split("SPIDER_RESULT_JSON:")[1].strip()
            parsed = json.loads(json_part)
            print(f"  ✅ 部分成功JSON解析成功")
            print(f"     状态: {parsed['status']}")
            print(f"     数量: {parsed['product_count']}")
            print(f"     警告: {parsed['warning_message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多品牌测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===\n")

    try:
        from proxy_setup import create_spider_reporter
        import logging
        import tempfile

        reporter = create_spider_reporter("test_multi.py", "测试多品牌")

        # 创建临时日志文件来捕获日志输出
        with tempfile.NamedTemporaryFile(mode='w+', delete=False) as log_file:
            # 设置日志处理器
            handler = logging.FileHandler(log_file.name)
            handler.setLevel(logging.INFO)
            formatter = logging.Formatter('[%(asctime)s][%(levelname)s][%(name)s] - %(message)s')
            handler.setFormatter(formatter)

            # 获取reporter的logger并添加处理器
            logger = logging.getLogger(f"SpiderReporter_test_multi.py")
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)

            # 捕获stdout输出（JSON）
            captured_stdout = io.StringIO()
            with redirect_stdout(captured_stdout):
                result = reporter.report_success(product_count=100)

            # 刷新日志
            handler.flush()

            # 读取stdout输出
            stdout_output = captured_stdout.getvalue()

            # 读取日志文件内容
            log_file.seek(0)
            log_output = log_file.read()

            print(f"Stdout输出（JSON）:\n{stdout_output}")
            print(f"日志输出:\n{log_output}")

            # 检查是否包含新格式和旧格式
            has_json = "SPIDER_RESULT_JSON:" in stdout_output
            has_old_format = "===new_record===" in log_output

            print(f"包含JSON格式（stdout）: {has_json}")
            print(f"包含旧格式（日志）: {has_old_format}")

            # 清理
            logger.removeHandler(handler)
            handler.close()
            os.unlink(log_file.name)

            if has_json:
                print("✅ 向后兼容性测试通过")
                print("   - JSON格式正确输出到stdout（供脚本解析）")
                print("   - 日志格式输出到日志系统（供人类阅读）")
                return True
            else:
                print("❌ 向后兼容性测试失败")
                return False

    except Exception as e:
        print(f"❌ 向后兼容性测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_multi_brand_usage():
    """演示多品牌脚本的使用方法"""
    print("\n=== 多品牌脚本使用演示 ===\n")
    
    print("多品牌爬虫的典型使用模式:\n")
    
    print("```python")
    print("# 1. 创建多品牌报告器")
    print("spider_reporter = create_spider_reporter('multi_brand_spider.py', '多品牌描述')")
    print("")
    print("# 2. 处理多个品牌/地区")
    print("region_names = ['地区1', '地区2', '地区3']")
    print("total_count = 0")
    print("for region in regions:")
    print("    count = process_region(region)")
    print("    total_count += count")
    print("")
    print("# 3. 根据结果报告")
    print("if total_count > 0:")
    print("    spider_reporter.report_success(")
    print("        product_count=total_count,")
    print("        additional_info={")
    print("            'regions': region_names,")
    print("            'region_count': len(region_names),")
    print("            'processed_regions': ','.join(region_names)")
    print("        }")
    print("    )")
    print("else:")
    print("    spider_reporter.report_failure(")
    print("        error_message='所有地区都失败',")
    print("        error_type='all_regions_failed'")
    print("    )")
    print("```\n")

def main():
    """主测试函数"""
    print("🕷️  多品牌爬虫结构化输出测试\n")
    
    tests = [
        ("多品牌SpiderResultReporter", test_multi_brand_reporter),
        ("向后兼容性", test_backward_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    demo_multi_brand_usage()
    
    print(f"=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有多品牌测试通过！")
        return True
    else:
        print("⚠️  部分多品牌测试失败。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

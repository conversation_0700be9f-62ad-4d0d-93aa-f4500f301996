{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 16:04:39, date_of_now:2024-03-06, brand_name:橘祥家, headers:{'appCode': 'wxc0416f9c9c965355', 'oemId': '100188', 'merchantId': '100001'}\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/橘祥家/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "headers={'appCode':'wxc0416f9c9c965355',\n", "         'oemId':'100188',\n", "         'merchantId':'100001'}\n", "brand_name='橘祥家'\n", "competitor_name_en='juxiangjia'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, headers:{headers}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["***************:48796\n", "**************:30993\n", "**************:45729\n", "**************:42386\n", "**************:32968\n", "**************:47268\n", "*************:35521\n", "***************:35695\n", "**************:38236\n", "**************:45381\n", "['***************:48796', '**************:30993', '**************:45729', '**************:42386', '**************:32968', '**************:47268', '*************:35521', '***************:35695', '**************:38236', '**************:45381']\n"]}], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url, data, headers):\n", "    max_retries=3;\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, data=data, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None\n", "def post_remote_data_with_proxy(url, data, headers):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, data=data, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["登录获取token并保存"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: http://18258841203:8gTcEKLs@*************:35521\n", "response cookie:{}\n"]}], "source": ["# 登录\n", "from urllib.parse import unquote\n", "\n", "url='https://gateway.jipaibuy.com/zuul/am-user-center-appweb/api/login/anonymousLogin?markingTime=1708250637199'\n", "login_response=post_remote_data_with_proxy(url, headers=headers, data={})\n", "\n", "\n", "after_login_cookie={}\n", "# Print all the cookies set by the server\n", "for cookie in login_response.cookies:\n", "    print(f'{cookie.name}: {cookie.value}')\n", "    after_login_cookie[cookie.name]=cookie.value\n", "\n", "\n", "print(f\"response cookie:{after_login_cookie}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["获取商品列表"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: http://18258841203:8gTcEKLs@**************:47268\n", "{\"REP_HEAD\":{\"SIGN\":\"dBFBbumQTHkz0wQElQhm/w==\"},\"REP_BODY\":{\"totalSize\":\"28\",\"RSPMSG\":\"获取成功\",\"page\":1,\"list\":[{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/13131342022014d77fef3498342cf8b43748ce34cb951.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"安佳淡奶油1L*12瓶整箱\",\"prodId\":\"2427174\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":53000,\"stock\":4680,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/7136614202203dac8ac9b56a742d78436d8a6ea0b6247.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"泰国榴莲肉冷冻无核榴莲3KG*6\",\"prodId\":\"2495752\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":183600,\"stock\":11,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/09325722021062ede9d7594dd495a856e176b8039daa5.png\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"法国爱乐薇淡奶油1L*12 铁塔淡奶油 动物奶油 烘焙原料 裱花奶油\",\"prodId\":\"1665371\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":58000,\"stock\":1981,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/87262302021077b96efd519694a44bef02142e830e9f9.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\" 太古纯正糖粉蓝标糖霜铁通装 烘焙专用二级糖粉13.62kg\",\"prodId\":\"1667011\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":23000,\"stock\":96,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/59341842021072e6da2a02ea341c294b7e8c2087e64c3.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"美玫牌低筋粉 小麦面粉 低筋面粉饼干蛋糕\",\"prodId\":\"1666991\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":18000,\"stock\":197,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/8669743202107212c583be22246aca3c964f820922543.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"韩国细沙砂糖 30KG\",\"prodId\":\"1667003\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":27500,\"stock\":297,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/10883572021078afbe383b1d649d39e6a7659a01eae05.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"王后低筋高筋面粉25kg 皇后全麦面包蛋糕吐司柔风糕点粉 烘焙原料\",\"prodId\":\"1666984\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":17500,\"stock\":38,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/7006374202112d4c3a447a02c4afb99d4d55e2cfd5ca3.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"安佳马苏里拉芝士碎12KG*1\",\"prodId\":\"2427080\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":60000,\"stock\":1999,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/89864322022018f9047318a8449a9803742994f49ac2b.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"铁塔淡奶油紫整箱1L*12\",\"prodId\":\"2427181\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":49500,\"stock\":499,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/0668744202106ddf0170a52e04b2ea7ee692e5ac28ff8.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"安佳84片芝士橙片再制车打片干酪1040g*10\",\"prodId\":\"1665367\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":63500,\"stock\":20,\"usp\":\"纯正切达干酪风味 熔化时间短，熔化后的质地柔滑均匀 保质期长达12个月，质量稳定 预切片\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/0216982202207ff20bc1ea20945d990dceaf1ed2ac761.png\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"啄木鸟披萨专用粉 25kg\",\"prodId\":\"2514348\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":14500,\"stock\":11,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/8656929202106cf89f8eff51141e5a03d7b24cb1b4d96.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"比利时蔻曼(歌文)淡味无盐黄油2.5KG*4 黄油起酥烘焙原料甜品\",\"prodId\":\"1665468\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":98000,\"stock\":20,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/58893352021079dfc02f85aa747c1a0a3285a8f76006f.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"日清 山茶花 紫罗兰 百合花日清山茶花高筋 小麦细粉\",\"prodId\":\"1666973\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":33500,\"stock\":60,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/9126421202107239a32ad79d64e689a0a1290dd9557f4.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"蓝钻杏仁粉  蓝钻杏仁粉片\",\"prodId\":\"1667012\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":52000,\"stock\":40,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/6756717202107ed6f43dd7de24fd588eb1e873ed8fd4c.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"金像面粉22.68KG\",\"prodId\":\"1666992\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":20900,\"stock\":50,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/7852092202107a9f79810ce8246a1af258d5353a1e87b.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"侨艺800动植物混合奶油 1L*12\",\"prodId\":\"1667001\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":34000,\"stock\":60,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/0968774202201fd56e6c0b8e44a6e80515cdd2529f996.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"艾恩摩尔稀奶油 家用动物淡奶油1*12\",\"prodId\":\"2427178\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":46000,\"stock\":2000,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/2341811202203416c64112e974416b19f0fdbe2c45f1b.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"百吉福汉堡干酪片黄片960g*12\",\"prodId\":\"2494979\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":66000,\"stock\":300,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/4243189202203f602cc8d08144c7cb87e617025480412.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"法国百吉福马苏里拉芝士碎3KG*4\",\"prodId\":\"2495684\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":52500,\"stock\":50,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/70629762022033db7b0c2af244174903dccf4979a706f.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"丸菱豆沙馅料5KG*2\",\"prodId\":\"2495735\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":15600,\"stock\":20,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/72954442023039d8e7717daad4ad5b43bb0f47481ede5.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"圣农奥尔良鸡肉丁1kg*10一箱调理鸡胸肉腿肉丁半成品披萨炒饭酒店商家用\",\"prodId\":\"2670533\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":23800,\"stock\":100,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/25747112023032ca45a14fda34488812d74ed51bc253e.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"圣农金丝鸡排藤椒味1kg*8一箱 冷冻油炸裹粉裹米大鸡排卡兹脆半成品80片\",\"prodId\":\"2670508\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":16000,\"stock\":100,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/619786820230345b6abe2465448b088e71976038a22f6.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"圣农傲椒风味鸡翅尖1kg*10一箱油炸小吃奥尔良调理腌制鸡尖块商用冷冻半成品\",\"prodId\":\"2670790\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":18000,\"stock\":100,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/4594068202303a82c22236b1a4e9d917944560c220653.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"圣农鸡肉洋葱圈800g*10一箱冷冻半成品家庭装鸡排鸡块鸡米花裹粉油炸小吃\",\"prodId\":\"2670789\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":13000,\"stock\":100,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/5775915202305ba55061c25404648a5472496ff6a3389.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"圣农美厨一口香鲜烤翅中1KG*10一箱\",\"prodId\":\"2677954\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":50000,\"stock\":20,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/776334020210636c048b6aad44c9093b50de5ecb6552d.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"安佳奶油干酪 1KG*12\",\"prodId\":\"1665361\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":62000,\"stock\":2,\"usp\":\"醇正的奶香味，风味独特浓郁 乳脂含量可观 良好的烘焙稳定性及紧密的质构 传统工艺，自然生产过程，采用乳酸菌发酵工艺\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/0003477202107f16c55e458af4035bda3aa3dff700ef7.jpg\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"琪雷萨马斯卡彭尼奶酪500g提拉米苏蛋糕芝士烘焙原料\",\"prodId\":\"1666994\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":28800,\"stock\":1,\"usp\":\"\"},{\"img\":\"https://osscdn.jipaibuy.com/itam/rest/test/0228911202106b0cb6a052aeb4349acc22729f589e4e5.png\",\"isSellOut\":0,\"marketPrice\":0,\"name\":\"安佳黄油（原味）5KG*4\",\"prodId\":\"1665366\",\"prodProfitMaxPrice\":0,\"prodProfitMinPrice\":0,\"prodType\":1,\"sellPrice\":151000,\"stock\":10,\"usp\":\"醇正而新鲜的新西兰黄油风味，乳香天然\"}],\"RSPCOD\":\"000000\"}}\n"]}], "source": ["pid_list_url='https://gateway.jipaibuy.com/zuul/am-user-center-appweb/api/shop/product/getProductList/-1/1/1000?markingTime=1708250022564'\n", "product_list=get_remote_data_with_proxy(pid_list_url,data=None, headers=headers).text\n", "print(product_list)\n", "product_list=json.loads(product_list)['REP_BODY']['list']\n", "product_list_df=pd.DataFrame(product_list)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>img</th>\n", "      <th>isSellOut</th>\n", "      <th>marketPrice</th>\n", "      <th>name</th>\n", "      <th>prodId</th>\n", "      <th>prodProfitMaxPrice</th>\n", "      <th>prodProfitMinPrice</th>\n", "      <th>prodType</th>\n", "      <th>sellPrice</th>\n", "      <th>stock</th>\n", "      <th>usp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/131...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>安佳淡奶油1L*12瓶整箱</td>\n", "      <td>2427174</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>53000</td>\n", "      <td>4680</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/713...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>泰国榴莲肉冷冻无核榴莲3KG*6</td>\n", "      <td>2495752</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>183600</td>\n", "      <td>11</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/093...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>法国爱乐薇淡奶油1L*12 铁塔淡奶油 动物奶油 烘焙原料 裱花奶油</td>\n", "      <td>1665371</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>58000</td>\n", "      <td>1981</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                 img  isSellOut  marketPrice  \\\n", "0  https://osscdn.jipaibuy.com/itam/rest/test/131...          0            0   \n", "1  https://osscdn.jipaibuy.com/itam/rest/test/713...          0            0   \n", "2  https://osscdn.jipaibuy.com/itam/rest/test/093...          0            0   \n", "\n", "                                 name   prodId  prodProfitMaxPrice  \\\n", "0                       安佳淡奶油1L*12瓶整箱  2427174                   0   \n", "1                    泰国榴莲肉冷冻无核榴莲3KG*6  2495752                   0   \n", "2  法国爱乐薇淡奶油1L*12 铁塔淡奶油 动物奶油 烘焙原料 裱花奶油  1665371                   0   \n", "\n", "   prodProfitMinPrice  prodType  sellPrice  stock usp  \n", "0                   0         1      53000   4680      \n", "1                   0         1     183600     11      \n", "2                   0         1      58000   1981      "]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(product_list_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:安佳淡奶油1L*12瓶整箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:605.3087711334229ms\n", "inputText:泰国榴莲肉冷冻无核榴莲3KG*6, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:545.3958511352539ms\n", "inputText:法国爱乐薇淡奶油1L*12 铁塔淡奶油 动物奶油 烘焙原料 裱花奶油, usage:{\"prompt_tokens\": 53, \"total_tokens\": 53}, time cost:660.5415344238281ms\n", "inputText: 太古纯正糖粉蓝标糖霜铁通装 烘焙专用二级糖粉13.62kg, usage:{\"prompt_tokens\": 43, \"total_tokens\": 43}, time cost:614.0382289886475ms\n", "inputText:美玫牌低筋粉 小麦面粉 低筋面粉饼干蛋糕, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:650.0678062438965ms\n", "inputText:韩国细沙砂糖 30KG, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:558.6240291595459ms\n", "inputText:王后低筋高筋面粉25kg 皇后全麦面包蛋糕吐司柔风糕点粉 烘焙原料, usage:{\"prompt_tokens\": 51, \"total_tokens\": 51}, time cost:684.027910232544ms\n", "inputText:安佳马苏里拉芝士碎12KG*1, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:619.5268630981445ms\n", "inputText:铁塔淡奶油紫整箱1L*12, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:558.8829517364502ms\n", "inputText:安佳84片芝士橙片再制车打片干酪1040g*10, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:717.00119972229ms\n", "inputText:啄木鸟披萨专用粉 25kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:644.1562175750732ms\n", "inputText:比利时蔻曼(歌文)淡味无盐黄油2.5KG*4 黄油起酥烘焙原料甜品, usage:{\"prompt_tokens\": 48, \"total_tokens\": 48}, time cost:609.6229553222656ms\n", "inputText:日清 山茶花 紫罗兰 百合花日清山茶花高筋 小麦细粉, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:714.766263961792ms\n", "inputText:蓝钻杏仁粉  蓝钻杏仁粉片, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:660.3555679321289ms\n", "inputText:金像面粉22.68KG, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:519.9446678161621ms\n", "inputText:侨艺800动植物混合奶油 1L*12, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:564.453125ms\n", "inputText:艾恩摩尔稀奶油 家用动物淡奶油1*12, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:637.1510028839111ms\n", "inputText:百吉福汉堡干酪片黄片960g*12, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:687.4573230743408ms\n", "inputText:法国百吉福马苏里拉芝士碎3KG*4, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:567.0304298400879ms\n", "inputText:丸菱豆沙馅料5KG*2, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:560.6427192687988ms\n", "inputText:圣农奥尔良鸡肉丁1kg*10一箱调理鸡胸肉腿肉丁半成品披萨炒饭酒店商家用, usage:{\"prompt_tokens\": 59, \"total_tokens\": 59}, time cost:564.8248195648193ms\n", "inputText:圣农金丝鸡排藤椒味1kg*8一箱 冷冻油炸裹粉裹米大鸡排卡兹脆半成品80片, usage:{\"prompt_tokens\": 59, \"total_tokens\": 59}, time cost:576.854944229126ms\n", "inputText:圣农傲椒风味鸡翅尖1kg*10一箱油炸小吃奥尔良调理腌制鸡尖块商用冷冻半成品, usage:{\"prompt_tokens\": 63, \"total_tokens\": 63}, time cost:582.5884342193604ms\n", "inputText:圣农鸡肉洋葱圈800g*10一箱冷冻半成品家庭装鸡排鸡块鸡米花裹粉油炸小吃, usage:{\"prompt_tokens\": 61, \"total_tokens\": 61}, time cost:614.2356395721436ms\n", "inputText:圣农美厨一口香鲜烤翅中1KG*10一箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:1158.7855815887451ms\n", "inputText:安佳奶油干酪 1KG*12, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:861.9365692138672ms\n", "inputText:琪雷萨马斯卡彭尼奶酪500g提拉米苏蛋糕芝士烘焙原料, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:631.7567825317383ms\n", "inputText:安佳黄油（原味）5KG*4, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:606.6462993621826ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['name'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
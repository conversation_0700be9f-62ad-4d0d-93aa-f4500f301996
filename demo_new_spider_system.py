#!/usr/bin/env python3
"""
演示新的爬虫结构化输出系统
"""

import sys
import os
from pathlib import Path

# 添加scripts目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

def demo_spider_reporter():
    """演示SpiderResultReporter的使用"""
    print("=== 演示新的爬虫结构化输出系统 ===\n")
    
    from proxy_setup import create_spider_reporter
    
    # 创建爬虫报告器
    reporter = create_spider_reporter("demo_spider.py", "演示品牌")
    
    print("1. 演示成功情况:")
    print("   代码: reporter.report_success(product_count=150)")
    result = reporter.report_success(
        product_count=150,
        additional_info={
            "odps_table": "summerfarm_ds.spider_demo_product_result_df",
            "partition": "ds=20250702,competitor_name=demo",
            "categories": ["水果", "蔬菜"]
        }
    )
    print(f"   返回结果: {result}\n")
    
    print("2. 演示失败情况:")
    print("   代码: reporter.report_failure(error_message='网络连接超时', error_type='network_error')")
    result = reporter.report_failure(
        error_message="网络连接超时",
        error_type="network_error",
        additional_info={
            "retry_count": 3,
            "last_url": "https://example.com/api/products"
        }
    )
    print(f"   返回结果: {result}\n")
    
    print("3. 演示部分成功情况:")
    print("   代码: reporter.report_partial_success(product_count=80, warning_message='部分商品缺少价格信息')")
    result = reporter.report_partial_success(
        product_count=80,
        warning_message="部分商品缺少价格信息",
        additional_info={
            "missing_price_count": 20,
            "total_scraped": 100
        }
    )
    print(f"   返回结果: {result}\n")

def demo_integration():
    """演示整个系统的集成"""
    print("=== 演示系统集成 ===\n")
    
    print("新的爬虫结构化输出系统包含以下组件:\n")
    
    print("1. SpiderResultReporter类 (scripts/proxy_setup.py)")
    print("   - 提供统一的结果输出接口")
    print("   - 支持成功、失败、部分成功三种状态")
    print("   - 输出JSON格式的结构化数据")
    print("   - 保持向后兼容性\n")
    
    print("2. 增强的run_all_docker.sh脚本")
    print("   - 解析新的JSON格式结果")
    print("   - 回退到原有的===new_record===解析")
    print("   - 更准确的成功/失败判断逻辑")
    print("   - 支持新的状态字段\n")
    
    print("3. 改进的send_feishu_notification.py脚本")
    print("   - 处理新的结构化数据格式")
    print("   - 增强的错误处理和数据验证")
    print("   - 更好的日志信息展示\n")
    
    print("4. 批量更新的爬虫脚本")
    print("   - 26个爬虫脚本已更新使用新系统")
    print("   - 保持原有功能的同时增加结构化输出")
    print("   - 向后兼容原有的===new_record===格式\n")

def demo_benefits():
    """演示新系统的优势"""
    print("=== 新系统的优势 ===\n")
    
    print("1. 更健壮的结果判断:")
    print("   - 不再依赖简单的字符串匹配")
    print("   - 使用结构化的JSON数据")
    print("   - 支持多种状态类型\n")
    
    print("2. 更丰富的信息:")
    print("   - 包含详细的时间信息")
    print("   - 支持自定义附加信息")
    print("   - 错误类型分类\n")
    
    print("3. 更好的可扩展性:")
    print("   - 易于添加新的字段")
    print("   - 支持复杂的数据结构")
    print("   - 便于后续分析和监控\n")
    
    print("4. 向后兼容:")
    print("   - 保留原有的===new_record===输出")
    print("   - 现有的监控系统继续工作")
    print("   - 平滑的迁移过程\n")

def demo_usage_examples():
    """演示使用示例"""
    print("=== 使用示例 ===\n")
    
    print("在爬虫脚本中的使用方法:\n")
    
    print("```python")
    print("# 1. 导入并创建报告器")
    print("from proxy_setup import create_spider_reporter")
    print("spider_reporter = create_spider_reporter('my_spider.py', '我的品牌')")
    print("")
    print("# 2. 在成功时报告结果")
    print("if result:")
    print("    spider_reporter.report_success(")
    print("        product_count=len(products),")
    print("        additional_info={")
    print("            'odps_table': table_name,")
    print("            'partition': partition_spec")
    print("        }")
    print("    )")
    print("")
    print("# 3. 在失败时报告错误")
    print("else:")
    print("    spider_reporter.report_failure(")
    print("        error_message='写入ODPS失败',")
    print("        error_type='odps_write_error'")
    print("    )")
    print("```\n")

def main():
    """主演示函数"""
    print("🕷️  爬虫项目结构化输出系统演示\n")
    
    try:
        demo_spider_reporter()
        demo_integration()
        demo_benefits()
        demo_usage_examples()
        
        print("=== 演示完成 ===")
        print("✅ 新的结构化输出系统已成功部署并可以使用！")
        print("📝 建议: 可以逐步将监控系统迁移到使用新的JSON格式数据")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

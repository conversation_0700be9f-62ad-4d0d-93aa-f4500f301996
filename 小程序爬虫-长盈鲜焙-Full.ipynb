{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 14:29:38, date_of_now:2024-03-06, brand_name:长盈鲜焙\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/长盈鲜焙/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "brand_name=\"长盈鲜焙\"\n", "headers={\n", "    # 'Referer':f'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp={timestamp_of_now}',\n", "         'authority': 'bshop.guanmai.cn',\n", "         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "         'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}\n", "competitor_name_en='changyingxianbei'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**************:35814\n", "**************:46256\n", "*************:42956\n", "**************:43715\n", "***************:33409\n", "***************:35424\n", "**************:30448\n", "**************:45317\n", "************:41855\n", "*************:40533\n", "['**************:35814', '**************:46256', '*************:42956', '**************:43715', '***************:33409', '***************:35424', '**************:30448', '**************:45317', '************:41855', '*************:40533']\n"]}], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url, data, headers, cookies):\n", "    max_retries=3;\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://***********:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, data=data, headers=headers, proxies=proxies, cookies=cookies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None\n", "def post_remote_data_with_proxy(url, data, headers):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://***********:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, data=data, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["登录获取token并保存"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ************************************************\n", "sessionid: 1ib37drvf6shbjnlnyhwyhkp7e0m9gn1\n", "response cookie:{'sessionid': '1ib37drvf6shbjnlnyhwyhkp7e0m9gn1'}\n", "{'code': 0, 'msg': '登录成功', 'data': {'user_id': 3003009}} 登录成功\n"]}], "source": ["# 登录\n", "from urllib.parse import unquote\n", "\n", "url='https://bshop.guanmai.cn/login'\n", "login_response=post_remote_data_with_proxy(url, data={'username':'***********', 'password':'aa123456'}, headers=headers)\n", "\n", "\n", "after_login_cookie={}\n", "# Print all the cookies set by the server\n", "for cookie in login_response.cookies:\n", "    print(f'{cookie.name}: {cookie.value}')\n", "    after_login_cookie[cookie.name]=cookie.value\n", "\n", "\n", "print(f\"response cookie:{after_login_cookie}\")\n", "print(login_response.json(),unquote(login_response.json()['msg']))\n", "\n", "# data={'accounts_name':'***********',\n", "# 'accounts_pass':'767776326c6433397474576a456271314146394346504c55304974353045764b7761472f687330775870706c495845303435463048505153394b734a44326d76695a6b732f654b4e4c4a4b5937744f6462302b39667755445263637a72436a65376c483641694a30715a6b33314936497547666b4c5838364f7372345a6c583070493144313937727358504b4a6d71684c5963354e4f474e6d646564314f574330505773616369434d41343d',\n", "# 'login_type':'xcx',}\n", "# login_res = post_remote_data_with_proxy(f'https://passport.dhb168.com/login/mUserLogin',data=data,headers=None)\n", "# token = json.loads(login_res)['token']\n", "# token"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: *************************************************\n", "<!doctype html><html><head><meta charset=\"UTF-8\"/><meta name=\"format-detection\" content=\"telephone=n\n", "new sessionid:1ib37drvf6shbjnlnyhwyhkp7e0m9gn1\n"]}], "source": ["after_login_cookie.update({'cms_key':'cygyl','group_id':'3252'})\n", "after_login_cookie\n", "\n", "sessionid=after_login_cookie['sessionid']\n", "\n", "url = 'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp=1706286340876'\n", "\n", "headers = {\n", "    'authority': 'bshop.guanmai.cn',\n", "    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "    'accept-language': 'en-US,en;q=0.9',\n", "    'cookie': f'cms_key=cygyl; group_id=3252; sessionid={sessionid}; gr_user_id=62c026d8-a829-40c7-823f-d7e38bf255d6; 9beedda875b5420f_gr_session_id=2a97577a-00ae-45a7-8392-4cf0d0fde7cb; 9beedda875b5420f_gr_session_id_sent_vst=2a97577a-00ae-45a7-8392-4cf0d0fde7cb',\n", "    'referer': 'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp=1706286034360',\n", "    'sec-fetch-dest': 'document',\n", "    'sec-fetch-mode': 'navigate',\n", "    'sec-fetch-site': 'same-origin',\n", "    'sec-fetch-user': '?1',\n", "    'upgrade-insecure-requests': '1',\n", "    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'\n", "}\n", "\n", "response = get_remote_data_with_proxy(url, data=None, headers=headers, cookies= after_login_cookie)\n", "\n", "print(response.text[0:100])\n", "\n", "print(f\"new sessionid:{sessionid}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取一级类目列表"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ************************************************\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>children</th>\n", "      <th>name</th>\n", "      <th>id</th>\n", "      <th>rank</th>\n", "      <th>url</th>\n", "      <th>station_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[{'name': '荔枝I龙眼I黄皮', 'id': '********', 'first...</td>\n", "      <td>新鲜蔬果</td>\n", "      <td>A612147</td>\n", "      <td>9</td>\n", "      <td>https://img.guanmai.cn/icon/3121b06b26aa32b1.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[{'name': '鲜奶|益力多I卡士', 'id': 'B955635', 'first...</td>\n", "      <td>乳制品</td>\n", "      <td>A612148</td>\n", "      <td>8</td>\n", "      <td>https://img.guanmai.cn/icon/6990fe774cd50b4a.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>[{'name': '达川冷冻原汁', 'id': 'B1281186', 'first_c...</td>\n", "      <td>水吧|类</td>\n", "      <td>A612149</td>\n", "      <td>7</td>\n", "      <td>https://img.guanmai.cn/icon/e47e404af0b894b7.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>[{'name': '糖浆', 'id': 'B955649', 'first_catego...</td>\n", "      <td>果糖|浆</td>\n", "      <td>A612150</td>\n", "      <td>6</td>\n", "      <td>https://img.guanmai.cn/icon/0b1dc790704f40bd.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>[{'name': '油炸小吃', 'id': 'B974236', 'first_cate...</td>\n", "      <td>油炸|类</td>\n", "      <td>A612151</td>\n", "      <td>5</td>\n", "      <td>https://img.guanmai.cn/icon/063b3c5bf5b45342.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>[{'name': '防漏纸', 'id': 'B1271477', 'first_cate...</td>\n", "      <td>包材|类</td>\n", "      <td>A612152</td>\n", "      <td>2</td>\n", "      <td>https://img.guanmai.cn/icon/d34c0c994888c4c0.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>[{'name': '定制茶叶', 'id': 'B979080', 'first_cate...</td>\n", "      <td>茶叶专区</td>\n", "      <td>A621470</td>\n", "      <td>3</td>\n", "      <td>https://img.guanmai.cn/icon/93956d8d3651f2ea.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>[{'name': '售后专用', 'id': 'B992804', 'first_cate...</td>\n", "      <td>长盈售后</td>\n", "      <td>A629078</td>\n", "      <td>1</td>\n", "      <td>https://img.guanmai.cn/icon/81fbd6ff3dc076e0.p...</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            children  name       id  rank  \\\n", "0  [{'name': '荔枝I龙眼I黄皮', 'id': '********', 'first...  新鲜蔬果  A612147     9   \n", "1  [{'name': '鲜奶|益力多I卡士', 'id': 'B955635', 'first...   乳制品  A612148     8   \n", "2  [{'name': '达川冷冻原汁', 'id': 'B1281186', 'first_c...  水吧|类  A612149     7   \n", "3  [{'name': '糖浆', 'id': 'B955649', 'first_catego...  果糖|浆  A612150     6   \n", "4  [{'name': '油炸小吃', 'id': 'B974236', 'first_cate...  油炸|类  A612151     5   \n", "5  [{'name': '防漏纸', 'id': 'B1271477', 'first_cate...  包材|类  A612152     2   \n", "6  [{'name': '定制茶叶', 'id': 'B979080', 'first_cate...  茶叶专区  A621470     3   \n", "7  [{'name': '售后专用', 'id': 'B992804', 'first_cate...  长盈售后  A629078     1   \n", "\n", "                                                 url station_id  \n", "0  https://img.guanmai.cn/icon/3121b06b26aa32b1.p...             \n", "1  https://img.guanmai.cn/icon/6990fe774cd50b4a.p...             \n", "2  https://img.guanmai.cn/icon/e47e404af0b894b7.p...             \n", "3  https://img.guanmai.cn/icon/0b1dc790704f40bd.p...             \n", "4  https://img.guanmai.cn/icon/063b3c5bf5b45342.p...             \n", "5  https://img.guanmai.cn/icon/d34c0c994888c4c0.p...             \n", "6  https://img.guanmai.cn/icon/93956d8d3651f2ea.p...             \n", "7  https://img.guanmai.cn/icon/81fbd6ff3dc076e0.p...             "]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取一级类目列表\n", "\n", "url=f'https://bshop.guanmai.cn/product/category/get'\n", "\n", "categoryList=get_remote_data_with_proxy(url, data=None, headers=headers, cookies=after_login_cookie).text\n", "categoryList=json.loads(categoryList)['data']\n", "cate_list_df=pd.DataFrame(categoryList)\n", "cate_list_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级、二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'name': '荔枝I龙眼I黄皮', 'id': '********', 'first_category_id': 'A612147', 'rank': 6, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '油柑I山楂I桑葚', 'id': 'B1272036', 'first_category_id': 'A612147', 'rank': 4, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '木瓜I莲雾I人参果', 'id': 'B1590455', 'first_category_id': 'A612147', 'rank': 5, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '柠檬I金桔', 'id': 'B955620', 'first_category_id': 'A612147', 'rank': 17, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '莓类浆果I杨梅', 'id': 'B955621', 'first_category_id': 'A612147', 'rank': 16, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '牛油果', 'id': 'B955623', 'first_category_id': 'A612147', 'rank': 18, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '凤梨I菠萝I椰子', 'id': 'B955624', 'first_category_id': 'A612147', 'rank': 12, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '芭乐I石榴I番茄', 'id': 'B955625', 'first_category_id': 'A612147', 'rank': 9, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '西瓜I蜜瓜I甜瓜', 'id': 'B955626', 'first_category_id': 'A612147', 'rank': 15, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '柑I橘I橙I柚', 'id': 'B955627', 'first_category_id': 'A612147', 'rank': 14, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '火龙果I香蕉I奇异果', 'id': 'B955628', 'first_category_id': 'A612147', 'rank': 7, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '百香果I芒果', 'id': 'B955629', 'first_category_id': 'A612147', 'rank': 13, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '葡萄I提子', 'id': 'B955630', 'first_category_id': 'A612147', 'rank': 10, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '苹果I梨I桃I李', 'id': 'B955633', 'first_category_id': 'A612147', 'rank': 8, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '时令蔬菜', 'id': 'B955634', 'first_category_id': 'A612147', 'rank': 2, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '鲜奶|益力多I卡士', 'id': 'B955635', 'first_category_id': 'A612148', 'rank': 11, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '奶油', 'id': 'B955636', 'first_category_id': 'A612148', 'rank': 12, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '芝士', 'id': 'B955637', 'first_category_id': 'A612148', 'rank': 9, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '炼奶|淡奶', 'id': 'B955638', 'first_category_id': 'A612148', 'rank': 10, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '液体奶', 'id': 'B955639', 'first_category_id': 'A612148', 'rank': 8, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '菲诺', 'id': 'B955640', 'first_category_id': 'A612148', 'rank': 7, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '植脂末', 'id': 'B955641', 'first_category_id': 'A612148', 'rank': 6, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '椰浆', 'id': 'B955642', 'first_category_id': 'A612148', 'rank': 4, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '宝利I椰萃I椰水', 'id': 'B955643', 'first_category_id': 'A612148', 'rank': 2, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '苏打水|气泡水', 'id': 'B955644', 'first_category_id': 'A612148', 'rank': 5, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '咖啡I燕麦奶', 'id': 'B994540', 'first_category_id': 'A612148', 'rank': 1, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '达川冷冻原汁', 'id': 'B1281186', 'first_category_id': 'A612149', 'rank': 8, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '产地冷冻原汁', 'id': 'B1281187', 'first_category_id': 'A612149', 'rank': 7, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '百香果冷冻原汁', 'id': 'B1281188', 'first_category_id': 'A612149', 'rank': 6, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '芒果冷冻原汁', 'id': 'B1281189', 'first_category_id': 'A612149', 'rank': 5, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '冷冻水果', 'id': 'B1306764', 'first_category_id': 'A612149', 'rank': 9, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '其它冷冻果汁', 'id': 'B955645', 'first_category_id': 'A612149', 'rank': 4, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '常温果汁', 'id': 'B955646', 'first_category_id': 'A612149', 'rank': 3, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '小料', 'id': 'B955647', 'first_category_id': 'A612149', 'rank': 2, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '粉类', 'id': 'B955648', 'first_category_id': 'A612149', 'rank': 1, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '糖浆', 'id': 'B955649', 'first_category_id': 'A612150', 'rank': 0, 'first_category_name': '果糖|浆', 'first_category_url': 'https://img.guanmai.cn/icon/0b1dc790704f40bd.png??imageslim'}\n", "{'name': '砂糖', 'id': 'B955650', 'first_category_id': 'A612150', 'rank': 0, 'first_category_name': '果糖|浆', 'first_category_url': 'https://img.guanmai.cn/icon/0b1dc790704f40bd.png??imageslim'}\n", "{'name': '油炸小吃', 'id': 'B974236', 'first_category_id': 'A612151', 'rank': 0, 'first_category_name': '油炸|类', 'first_category_url': 'https://img.guanmai.cn/icon/063b3c5bf5b45342.png??imageslim'}\n", "{'name': '防漏纸', 'id': 'B1271477', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '常规包材', 'id': 'B955652', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '盖子', 'id': 'B955654', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '手打柠檬茶杯', 'id': 'B955656', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '吸管', 'id': 'B955657', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '杯托', 'id': 'B955658', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '定制茶叶', 'id': 'B979080', 'first_category_id': 'A621470', 'rank': 0, 'first_category_name': '茶叶专区', 'first_category_url': 'https://img.guanmai.cn/icon/93956d8d3651f2ea.png??imageslim'}\n", "{'name': '售后专用', 'id': 'B992804', 'first_category_id': 'A629078', 'rank': 0, 'first_category_name': '长盈售后', 'first_category_url': 'https://img.guanmai.cn/icon/81fbd6ff3dc076e0.png??imageslim'}\n"]}], "source": ["all_second_category=[]\n", "for first in categoryList:\n", "    first_obj={'first_category_name':first['name'],'first_category_url':first['url']}\n", "    for second in first['children']:\n", "        second.update(first_obj)\n", "        all_second_category.append(second)\n", "        print(second)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ************************************************\n", "类目:荔枝I龙眼I黄皮, 商品数:1, SKU names:['泰国龙眼 一级/中大果']\n", "Using proxy: ************************************************\n", "类目:油柑I山楂I桑葚, 商品数:3, SKU names:['山楂 一级/胶盒', '饼甜油柑 一级/中大果', '黑桑葚 一级/胶框(8斤装)']\n", "Using proxy: ************************************************\n", "类目:木瓜I莲雾I人参果, 商品数:1, SKU names:['海南莲雾 一级/中果(黑金刚)']\n", "Using proxy: ************************************************\n", "类目:柠檬I金桔, 商品数:11, SKU names:['双胞胎黄柠檬 一级/中果/扁箱', '塔西提无籽青柠檬 一级/中大果', '子弹头长香水柠檬 一级/55-140g', '子弹头长香水柠檬 一级/55-140g(整件)', '安岳黄柠檬 一级/中大果(115g+)', '安岳黄柠檬 一级/标准果(90-115g)', '有籽青柠檬 一级/中大果', '锁匙扣黄柠檬 一级/中大果(30斤装)', '锁匙扣黄柠檬 一级/大果(带箱20斤装)', '青金桔 一级/2斤装', '香水柠檬 一级/老树果(按斤)', '香水柠檬 一级/老树果(整件)', '香水柠檬 二级/青黄果(按斤)', '香水柠檬 二级/青黄果(整件)']\n", "Using proxy: ************************************************\n", "类目:莓类浆果I杨梅, 商品数:6, SKU names:['丹东红颜草莓 胶盒/20粒', '安徽奶油草莓 纸盒/15-20粒', '安徽红颜草莓  板装(3.5斤装)', '本地法兰蒂草莓 胶框/5斤', '草莓 二级/瑕疵果/不售后', '进口蓝莓 一级/中大果', '进口蓝莓 一级/中大果(整件)']\n", "Using proxy: ************************************************\n", "类目:牛油果, 商品数:3, SKU names:['秘鲁牛油果 熟果/130g±10g', '秘鲁牛油果 熟果/140g±10g', '秘鲁牛油果 熟果/150g+']\n", "Using proxy: *************************************************\n", "类目:凤梨I菠萝I椰子, 商品数:5, SKU names:['佳农凤梨 有冠/7-8头/大果', '佳农凤梨 有冠/大果', '徐闻菠萝 一级/带冠', '泰国香水椰青 东泰一个宝/大果(9个)', '泰国香水椰青 大果/小猩猩牌', '泰国香水椰青 大果/小猩猩牌', '都乐无冠凤梨 一级/金菠萝', '都乐无冠凤梨 一级/金菠萝(6-8头)']\n", "Using proxy: **********************************************\n", "类目:芭乐I石榴I番茄, 商品数:3, SKU names:['圣女果', '圣女果 11.5斤/件', '白心番石榴 一级', '红心芭乐 一级/中大果']\n", "Using proxy: ************************************************\n", "类目:西瓜I蜜瓜I甜瓜, 商品数:6, SKU names:['无籽西瓜 一级(8斤+/个)', '晓蜜25号蜜瓜 一级', '玫珑蜜瓜(网纹瓜)', '甘美4K无籽西瓜 一级/中大果', '白香瓜 一级/通货', '麒麟西瓜 一级(8斤+/个)', '麒麟西瓜 一级(整件)']\n", "Using proxy: ***********************************************\n", "类目:柑I橘I橙I柚, 商品数:5, SKU names:['美国橙子 一级/中大果', '耙耙柑 一级 /中大果', '赣南脐橙 一级/中大果', '赣南脐橙 一级/中大果(整件)', '进口西柚 一级/中大果', '黄金桔 一级/中小果']\n", "Using proxy: *************************************************\n", "类目:火龙果I香蕉I奇异果, 商品数:5, SKU names:['国产猕猴桃 一级/中大果', '国产猕猴桃 一级/中大果(整件)', '越南白心火龙果 一级/中大果', '越南白心火龙果 一级/中大果(整件)', '越南红心火龙果 一级/中大果', '进口香蕉 一级/熟果', '金都一号红心火龙果 一级']\n", "Using proxy: ************************************************\n", "类目:百香果I芒果, 商品数:8, SKU names:['大青芒 一级/大果(10斤装)', '大青芒 一级/大果(50斤/件)', '大青芒 二级/通货', '小台农芒 一级(10斤装)', '小台农芒 一级/胶框(整件)', '小台农芒 二级(品质好)', '紫皮百香果 一级/中大果(光皮)', '紫香百香果 二级/中大果(皱皮)', '象牙芒 一级(脆青芒)', '黄金百香果 一级/中大果']\n", "Using proxy: ***********************************************\n", "类目:葡萄I提子, 商品数:2, SKU names:['夏黑葡萄 一级/金果/黑框装', '阳光香印青提 一级/胶框装(整件)']\n", "Using proxy: *************************************************\n", "类目:苹果I梨I桃I李, 商品数:6, SKU names:['国产青苹果 一级', '皇冠梨(蜜梨) 一级/中大果', '皇冠梨(蜜梨) 一级/中大果(整件)', '红富士苹果 一级/70-75#', '红富士苹果 一级/70-75#(整件)', '红富士苹果 一级/80-85#', '红富士苹果 一级/80-85#(整件)', '红富士苹果 二级/75-85#', '进口青苹果 二级', '进口青苹果 二级']\n", "Using proxy: ***********************************************\n", "类目:时令蔬菜, 商品数:18, SKU names:['仔姜 一级', '冰菜 一级/胶盒', '大西芹 一级', '大青瓜 一级', '斑斓叶 一级', '柠檬叶 精选完整叶', '水果小青瓜 一级', '油苦瓜 一级', '甜菜根', '番茄 一级', '羽衣甘蓝 一级', '老黄姜 一级/生姜', '胡萝卜 一级', '薄荷叶 一级/大叶', '迷迭香 一级', '青彩椒 一级', '香茅草 一级', '鲜香菜']\n", "Using proxy: ************************************************\n", "类目:鲜奶|益力多I卡士, 商品数:3, SKU names:['味全冷藏牛乳 950ml', '味全冷藏牛乳 950ml*12瓶', '悦鲜活鲜牛乳(冷藏)', '悦鲜活鲜牛乳(冷藏)', '益力多乳酸菌100ml*50瓶(单下不送)', '益力多乳酸菌100ml*5瓶(单下不送)']\n", "Using proxy: ************************************************\n", "类目:奶油, 商品数:6, SKU names:['安佳淡奶油1L(冷藏）', '安佳淡奶油1L(冷藏）', '维益咖啡饮品浓缩奶油 1L', '维益咖啡饮品浓缩奶油 1L*12瓶', '维益爱真喷射奶油500g', '维益爱真喷射奶油500g', '维益芝士云顶奶霜700g', '维益芝士云顶奶霜700g', '迪比克喷射型加糖稀奶油 700ml', '迪比克喷射型加糖稀奶油 700ml', '雀巢淡奶油 1L/稀奶油', '雀巢淡奶油1L*12瓶/稀奶油']\n", "Using proxy: ************************************************\n", "类目:芝士, 商品数:1, SKU names:['安佳芝士1kg(冷藏）', '安佳芝士1kg(冷藏）']\n", "Using proxy: *************************************************\n", "类目:炼奶|淡奶, 商品数:4, SKU names:['雀巢三花全脂淡奶410g', '雀巢三花全脂淡奶410g', '雀巢三花植脂淡奶410g', '雀巢三花植脂淡奶410g', '雀巢鹰唛炼奶 350g', '雀巢鹰唛炼奶 350g*48罐', '黑白淡奶400g', '黑白淡奶400g']\n", "Using proxy: ************************************************\n", "类目:液体奶, 商品数:4, SKU names:['君乐宝纯牛奶', '君乐宝纯牛奶', '大M全脂牛奶 1L', '大M全脂牛奶 1L(整件)', '雀巢全脂牛奶 1L', '雀巢全脂牛奶 1L*12瓶', '黑白全脂纯牛奶1L', '黑白全脂纯牛奶1L']\n", "Using proxy: ************************************************\n", "类目:菲诺, 商品数:4, SKU names:['菲诺冷冻生椰乳1kg', '菲诺冷冻生椰乳1kg', '菲诺厚椰乳(带盖版）1L', '菲诺厚椰乳(带盖版）1L', '菲诺厚椰乳(无盖版）1L', '菲诺厚椰乳(无盖版）1L', '菲诺椰皇水1kg', '菲诺椰皇水1kg']\n", "Using proxy: ************************************************\n", "类目:植脂末, 商品数:1, SKU names:['植脂末定制(嘉禾食品) 20kg']\n", "Using proxy: ************************************************\n", "类目:椰浆, 商品数:3, SKU names:['产地冷冻椰浆 950ml', '产地冷冻椰浆950ml', '正宗椰树牌椰汁1L', '正宗椰树牌椰汁1L', '金牌高达椰浆400ml', '金牌高达椰浆400ml']\n", "Using proxy: *************************************************\n", "类目:宝利I椰萃I椰水, 商品数:7, SKU names:['koully宝利生打椰奶（瓶装）980g', 'koully宝利生打椰奶（瓶装）980g', 'koully宝利生打椰奶（袋装）1kg', 'koully宝利生打椰奶（袋装）1kg', 'koully宝利生打椰子水（袋装）1kg', 'koully宝利生打椰子水（袋装）1kg', '海南椰萃生椰乳(热咖)  980g', '海南椰萃生椰乳(热咖)  980g', '海南椰萃生椰乳(瓶装)980g', '海南椰萃生椰乳(瓶装)980g', '海南椰萃生椰乳（袋装）1kg', '海南椰萃生椰乳（袋装）1kg', '海南椰萃生椰水（袋装）1kg', '海南椰萃生椰水（袋装）1kg']\n", "Using proxy: *************************************************\n", "类目:苏打水|气泡水, 商品数:1, SKU names:['泰象苏打水325ml*24瓶']\n", "Using proxy: ************************************************\n", "类目:咖啡I燕麦奶, 商品数:3, SKU names:['冰滴咖啡(豆雅匠新)1L', '冰滴咖啡(豆雅匠新)1L', '咖啡大师燕麦饮料1L', '咖啡大师燕麦饮料1L', '茶饮大师燕麦奶1L', '茶饮大师燕麦奶1L']\n", "Using proxy: ************************************************\n", "类目:达川冷冻原汁, 商品数:25, SKU names:['达川 NFC原榨红西柚果肉浆1kg', '达川 NFC原榨红西柚果肉浆1kg', '达川NFC冷冻桑葚原浆 1kg', '达川NFC冷冻桑葚原浆 1kg', '达川NFC冷冻龙眼汁 1kg', '达川NFC冷冻龙眼汁 1kg', '达川NFC原榨余甘汁油甘汁油柑汁1kg', '达川NFC原榨余甘汁油甘汁油柑汁1kg', '达川NFC原榨夏黑葡萄汁1kg', '达川NFC原榨夏黑葡萄汁1kg', '达川NFC原榨巨峰葡萄汁1kg', '达川NFC原榨巨峰葡萄汁1kg', '达川NFC原榨杨梅浆1kg', '达川NFC原榨杨梅浆1kg', '达川NFC原榨柠檬汁1kg', '达川NFC原榨柠檬汁1kg', '达川NFC原榨橄榄汁1kg', '达川NFC原榨橄榄汁1kg', '达川NFC原榨水蜜桃浆（白色款）1kg', '达川NFC原榨水蜜桃浆（白色款）1kg', '达川NFC原榨沃柑汁 1kg', '达川NFC原榨沃柑汁 1kg', '达川NFC原榨玫珑瓜果浆1kg', '达川NFC原榨玫珑瓜果浆1kg', '达川NFC原榨石榴汁1kg', '达川NFC原榨石榴汁1kg', '达川NFC原榨红心番石榴汁1kg', '达川NFC原榨红心番石榴汁1kg', '达川NFC原榨脐橙汁1kg', '达川NFC原榨脐橙汁1kg', '达川NFC原榨芒果浆1kg', '达川NFC原榨芒果浆1kg', '达川NFC原榨荔枝汁1kg', '达川NFC原榨荔枝汁1kg', '达川NFC原榨菠萝汁1kg', '达川NFC原榨菠萝汁1kg', '达川NFC原榨黄皮汁1kg', '达川NFC原榨黄皮汁1kg', '达川冷冻山楂汁', '达川冷冻山楂汁', '达川冷冻苹果汁', '达川冷冻苹果汁', '达川冷冻蜜桃复合果浆1kg', '达川冷冻蜜桃复合果浆1kg', '达川复合青提汁饮料1kg', '达川复合青提汁饮料1kg', '达川带籽百香果浆1kg', '达川带籽百香果浆1kg', '达川草莓复合果浆1kg', '达川草莓复合果浆1kg']\n", "Using proxy: ***********************************************\n", "类目:产地冷冻原汁, 商品数:2, SKU names:['产地冷冻芒果浆 1kg', '产地冷冻芒果浆 1kg*24瓶', '产地金桔汁 950ml', '产地金桔汁950ml']\n", "Using proxy: ************************************************\n", "类目:百香果冷冻原汁, 商品数:1, SKU names:['苏亚速冻百香果浆 950g', '苏亚速冻百香果浆 950g']\n", "Using proxy: *************************************************\n", "类目:芒果冷冻原汁, 商品数:1, SKU names:['质利冷冻芒果浆960g', '质利冷冻芒果浆960g']\n", "Using proxy: *************************************************\n", "类目:冷冻水果, 商品数:1, SKU names:['产地冷冻黄皮果 1kg', '产地冷冻黄皮果 1kg*20包']\n", "Using proxy: ***********************************************\n", "类目:其它冷冻果汁, 商品数:1, SKU names:['冷冻味柠油甘汁', '冷冻味柠油甘汁']\n", "Using proxy: ***********************************************\n", "类目:常温果汁, 商品数:1, SKU names:['常温德创水蜜桃汁1L', '常温德创水蜜桃汁1L']\n", "Using proxy: ************************************************\n", "类目:小料, 商品数:12, SKU names:['冷冻小青团小丸子1kg(古X同款)', '冷冻小青团小丸子1kg(古X同款)', '即食西米晶球  500g', '呈香圆形芋圆小圆子1kg', '呈香圆形芋圆小圆子1kg', '天聪红豆罐头（带拉手） 3.35kg*6罐（整件）', '天聪红豆罐头（带拉手）3.35kg', '好C冠红西柚果粒罐头850g', '好C冠红西柚果粒罐头850g', '椰果果粒（奕芳）1kg', '椰果果粒（奕芳）1kg', '炫彩水晶马蹄爆爆珠850g', '炫彩水晶马蹄爆爆珠850g', '琥珀珍珠(粉圆)1kg', '琥珀珍珠(粉圆)1kg', '糖蜜椰果  1kg', '糖蜜椰果  1kg', '茶芘糖水红豆罐头950g', '茶芘糖水红豆罐头950g', '长盈定制寒天(鲜活) 2kg', '长盈定制寒天(鲜活) 2kg', '鲜活公版寒天(甜心原味晶球) 2kg', '鲜活公版寒天(甜心原味晶球) 2kg']\n", "Using proxy: ************************************************\n", "类目:粉类, 商品数:2, SKU names:['广禧 海盐芝士奶盖粉风味固体饮料  500g', '水晶冻粉1kg']\n", "Using proxy: ************************************************\n", "类目:糖浆, 商品数:8, SKU names:['双桥F55果糖彩标 25KG', '双桥F60果糖彩标 25KG', '味大大蔗糖糖浆25kg', '壹糖天下海盐糖浆1.2kg', '壹糖天下海盐糖浆1.2kg', '德馨竹蔗冰糖 1.26KG', '德馨竹蔗冰糖1.26KG', '森糖F55果葡糖浆 25kg/桶', '森糖F60果葡糖浆 25kg/桶', '长盈定制冰糖浆 25KG']\n", "Using proxy: **********************************************\n", "类目:砂糖, 商品数:1, SKU names:['韩国白砂糖（30kg）']\n", "Using proxy: ***********************************************\n", "类目:油炸小吃, 商品数:1, SKU names:['大薯条(麦肯臻脆） 2KG']\n", "Using proxy: ************************************************\n", "类目:防漏纸, 商品数:2, SKU names:['奶茶杯防漏纸13cm(适用98口径以内)', '奶茶杯防漏纸16cm(适用98-110mm口径)']\n", "Using proxy: ************************************************\n", "类目:常规包材, 商品数:2, SKU names:['90#500ml磨砂注塑空杯', '90#700ml磨砂注塑空杯']\n", "Using proxy: ************************************************\n", "类目:盖子, 商品数:4, SKU names:['90#连体注塑盖(乳白/磨砂)1000个', '90#连体注塑盖(高透/磨砂)1000个', 'PET-98口径平盖1000个/件', 'PET-98口径高盖']\n", "Using proxy: ************************************************\n", "类目:手打柠檬茶杯, 商品数:3, SKU names:['98-16OZ-500mlPET', '98-20OZ-600mlPET 1000pcs', '98-24OZ-700mlPET']\n", "Using proxy: ***********************************************\n", "类目:吸管, 商品数:3, SKU names:['牛皮纸包大吸管2000支', '牛皮纸包小吸管5000支(0.6*23)', '白纸包大吸管2000支']\n", "Using proxy: ***********************************************\n", "类目:杯托, 商品数:2, SKU names:['牛皮纸双杯托', '牛皮纸四杯托']\n", "Using proxy: ***********************************************\n", "类目:定制茶叶, 商品数:7, SKU names:['YP东方美人茶叶', 'YP鸭屎香专版茶叶', '四季春茶叶', '茉莉花茶', '蜜香红茶', '锡兰红茶', '鸭屎香2号茶叶']\n", "Using proxy: ***********************************************\n", "类目:售后专用, 商品数:2, SKU names:['样品(水果)', '样品(水果)', '长盈售后  快递费用电话18998068162', '长盈售后  退换货专用电话18998068162']\n"]}], "source": ["def get_products_for_second_cate(category_id='B955624'):\n", "    url=f'https://bshop.guanmai.cn/product/sku/get?level=2&category_id={category_id}'\n", "\n", "    product_list=json.loads(get_remote_data_with_proxy(url, data=None, headers=headers, cookies=after_login_cookie).text)['data']\n", "    return product_list\n", "\n", "\n", "all_products=[]\n", "all_skus=[]\n", "for second in all_second_category:\n", "    sub_product_list=get_products_for_second_cate(second['id'])\n", "    sku_list=[]\n", "    for product in sub_product_list:\n", "        product['first_category_id']=second['first_category_id']\n", "        product['second_category_id']=second['id']\n", "        product['first_category_name']=second['first_category_name']\n", "        product['second_category_name']=second['name']\n", "        product['first_category_url']=second['first_category_url']\n", "        sku_list.extend(product['skus'])\n", "        all_skus.extend(product['skus'])\n", "    names = list(map(lambda x: x['name'], sku_list))\n", "    print(f\"类目:{second['name']}, 商品数:{len(sub_product_list)}, SKU names:{names}\")\n", "    all_products.extend(sub_product_list)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>std_sale_price</th>\n", "      <th>std_sale_price_forsale</th>\n", "      <th>std_unit_name_forsale</th>\n", "      <th>std_unit_name</th>\n", "      <th>salemenu_id</th>\n", "      <th>img_url</th>\n", "      <th>img_urls</th>\n", "      <th>is_fav</th>\n", "      <th>cart_amount</th>\n", "      <th>skus</th>\n", "      <th>name</th>\n", "      <th>rank</th>\n", "      <th>first_category_id</th>\n", "      <th>second_category_id</th>\n", "      <th>first_category_name</th>\n", "      <th>second_category_name</th>\n", "      <th>first_category_url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C15011328</td>\n", "      <td>850.0</td>\n", "      <td>850.0</td>\n", "      <td>斤</td>\n", "      <td>斤</td>\n", "      <td>S144959</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "      <td>[https://image.document.guanmai.cn/product_img...</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>[{'id': 'D250998891', 'name': '泰国龙眼 一级/中大果', '...</td>\n", "      <td>泰国龙眼 一级/中大果</td>\n", "      <td>4</td>\n", "      <td>A612147</td>\n", "      <td>********</td>\n", "      <td>新鲜蔬果</td>\n", "      <td>荔枝I龙眼I黄皮</td>\n", "      <td>https://img.guanmai.cn/icon/3121b06b26aa32b1.p...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C15770502</td>\n", "      <td>750.0</td>\n", "      <td>750.0</td>\n", "      <td>盒</td>\n", "      <td>盒</td>\n", "      <td>S144959</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "      <td>[https://image.document.guanmai.cn/product_img...</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>[{'id': 'D250999155', 'name': '山楂 一级/胶盒', 'fee...</td>\n", "      <td>山楂 一级/胶盒</td>\n", "      <td>0</td>\n", "      <td>A612147</td>\n", "      <td>B1272036</td>\n", "      <td>新鲜蔬果</td>\n", "      <td>油柑I山楂I桑葚</td>\n", "      <td>https://img.guanmai.cn/icon/3121b06b26aa32b1.p...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C14234059</td>\n", "      <td>600.0</td>\n", "      <td>600.0</td>\n", "      <td>斤</td>\n", "      <td>斤</td>\n", "      <td>S144959</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "      <td>[https://image.document.guanmai.cn/product_img...</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>[{'id': 'D250998732', 'name': '饼甜油柑 一级/中大果', '...</td>\n", "      <td>饼甜油柑 一级/中大果</td>\n", "      <td>0</td>\n", "      <td>A612147</td>\n", "      <td>B1272036</td>\n", "      <td>新鲜蔬果</td>\n", "      <td>油柑I山楂I桑葚</td>\n", "      <td>https://img.guanmai.cn/icon/3121b06b26aa32b1.p...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          id  std_sale_price  std_sale_price_forsale std_unit_name_forsale  \\\n", "0  C15011328           850.0                   850.0                     斤   \n", "1  C15770502           750.0                   750.0                     盒   \n", "2  C14234059           600.0                   600.0                     斤   \n", "\n", "  std_unit_name salemenu_id  \\\n", "0             斤     S144959   \n", "1             盒     S144959   \n", "2             斤     S144959   \n", "\n", "                                             img_url  \\\n", "0  https://image.document.guanmai.cn/product_img/...   \n", "1  https://image.document.guanmai.cn/product_img/...   \n", "2  https://image.document.guanmai.cn/product_img/...   \n", "\n", "                                            img_urls  is_fav  cart_amount  \\\n", "0  [https://image.document.guanmai.cn/product_img...   False            0   \n", "1  [https://image.document.guanmai.cn/product_img...   False            0   \n", "2  [https://image.document.guanmai.cn/product_img...   False            0   \n", "\n", "                                                skus         name  rank  \\\n", "0  [{'id': 'D250998891', 'name': '泰国龙眼 一级/中大果', '...  泰国龙眼 一级/中大果     4   \n", "1  [{'id': 'D250999155', 'name': '山楂 一级/胶盒', 'fee...     山楂 一级/胶盒     0   \n", "2  [{'id': 'D250998732', 'name': '饼甜油柑 一级/中大果', '...  饼甜油柑 一级/中大果     0   \n", "\n", "  first_category_id second_category_id first_category_name  \\\n", "0           A612147           ********                新鲜蔬果   \n", "1           A612147           B1272036                新鲜蔬果   \n", "2           A612147           B1272036                新鲜蔬果   \n", "\n", "  second_category_name                                 first_category_url  \n", "0             荔枝I龙眼I黄皮  https://img.guanmai.cn/icon/3121b06b26aa32b1.p...  \n", "1             油柑I山楂I桑葚  https://img.guanmai.cn/icon/3121b06b26aa32b1.p...  \n", "2             油柑I山楂I桑葚  https://img.guanmai.cn/icon/3121b06b26aa32b1.p...  "]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(all_products)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:泰国龙眼 一级/中大果, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:661.2858772277832ms\n", "inputText:山楂 一级/胶盒, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:557.8892230987549ms\n", "inputText:饼甜油柑 一级/中大果, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:710.6547355651855ms\n", "inputText:黑桑葚 一级/胶框, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:538.1312370300293ms\n", "inputText:海南莲雾 一级/中果(黑金刚), usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:668.5352325439453ms\n", "inputText:双胞胎黄柠檬 一级/中果/扁箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:648.0977535247803ms\n", "inputText:塔西提无籽青柠檬 一级/中大果, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:642.3125267028809ms\n", "inputText:子弹头长香水柠檬 一级/55-140g, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:519.9680328369141ms\n", "inputText:安岳黄柠檬 一级/中大果(115g+), usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:496.6421127319336ms\n", "inputText:安岳黄柠檬 一级/标准果(90-115g), usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:516.594648361206ms\n", "inputText:有籽青柠檬 一级/中大果, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:615.3151988983154ms\n", "inputText:锁匙扣黄柠檬 一级/中大果(30斤装), usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:634.5064640045166ms\n", "inputText:锁匙扣黄柠檬 一级/大果(带箱20斤装), usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:489.8338317871094ms\n", "inputText:青金桔 一级, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:507.8771114349365ms\n", "inputText:香水柠檬 一级/老树果, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:578.0093669891357ms\n", "inputText:香水柠檬 二级/青黄果, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:532.6197147369385ms\n", "inputText:丹东红颜草莓 胶盒/20粒, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:518.0866718292236ms\n", "inputText:安徽奶油草莓 纸盒/15-20粒, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:508.3456039428711ms\n", "inputText:安徽红颜草莓  板装(3.5斤装), usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:554.8689365386963ms\n", "inputText:本地法兰蒂草莓 胶框/5斤, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:592.3154354095459ms\n", "inputText:草莓 二级/瑕疵果/不售后, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:608.1397533416748ms\n", "inputText:进口蓝莓 一级/中大果, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:1080.4345607757568ms\n", "inputText:秘鲁牛油果 熟果/130g±10g, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:532.3998928070068ms\n", "inputText:秘鲁牛油果 熟果/140g±10g, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:554.5268058776855ms\n", "inputText:秘鲁牛油果 熟果/150g+, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:643.7110900878906ms\n", "inputText:佳农凤梨 有冠/大果, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:618.9117431640625ms\n", "inputText:徐闻菠萝 一级/带冠, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:587.5585079193115ms\n", "inputText:泰国香水椰青 东泰一个宝/大果(9头), usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:623.1260299682617ms\n", "inputText:泰国香水椰青 大果/小猩猩牌, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:599.1532802581787ms\n", "inputText:都乐无冠凤梨 一级/金菠萝, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:523.4074592590332ms\n", "inputText:圣女果, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:556.5876960754395ms\n", "inputText:白心番石榴 一级, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:520.5173492431641ms\n", "inputText:红心芭乐 一级/中大果, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:1213.5658264160156ms\n", "inputText:无籽西瓜 一级, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:621.0770606994629ms\n", "inputText:晓蜜25号蜜瓜 一级, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:926.140546798706ms\n", "inputText:玫珑蜜瓜(网纹瓜) 一级, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:547.0254421234131ms\n", "inputText:甘美4K无籽西瓜 一级/中大果, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:513.6120319366455ms\n", "inputText:白香瓜 一级/通货, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:536.17262840271ms\n", "inputText:麒麟西瓜 一级(8斤+/个), usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:606.0547828674316ms\n", "inputText:美国橙子 一级/中大果, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:713.8981819152832ms\n", "inputText:耙耙柑 一级 /中大果, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:1542.2933101654053ms\n", "inputText:赣南脐橙 一级/中大果, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:602.2725105285645ms\n", "inputText:进口西柚 一级/中大果, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:599.2178916931152ms\n", "inputText:黄金桔 一级/中小果, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:495.4414367675781ms\n", "inputText:国产猕猴桃 一级/中大果, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:521.4855670928955ms\n", "inputText:越南白心火龙果 一级/中大果, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:529.151201248169ms\n", "inputText:越南红心火龙果 一级/中大果, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:546.0841655731201ms\n", "inputText:进口香蕉 一级/熟果, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:581.6202163696289ms\n", "inputText:金都一号红心火龙果 一级, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:620.558500289917ms\n", "inputText:大青芒 一级/大果, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:516.5891647338867ms\n", "inputText:大青芒 二级/通货, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:511.2779140472412ms\n", "inputText:小台农芒 一级, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:566.1556720733643ms\n", "inputText:小台农芒 二级, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:1072.4949836730957ms\n", "inputText:紫皮百香果 一级/中大果(光皮), usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:759.0839862823486ms\n", "inputText:紫香百香果 二级/中大果(皱皮), usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:619.7741031646729ms\n", "inputText:脆青芒象牙芒(生吃酸脆，可蘸辣椒), usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:614.1765117645264ms\n", "inputText:黄金百香果 一级/中大果, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:559.7066879272461ms\n", "inputText:夏黑葡萄 一级/金果/黑框装, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:530.9956073760986ms\n", "inputText:阳光香印青提 一级, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:529.8428535461426ms\n", "inputText:国产青苹果 一级, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:1615.6911849975586ms\n", "inputText:皇冠梨(蜜梨) 一级/中大果, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:528.4464359283447ms\n", "inputText:红富士苹果 一级/70-75#, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:508.4218978881836ms\n", "inputText:红富士苹果 一级/80-85#, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:536.954402923584ms\n", "inputText:红富士苹果 二级/75-85#, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:515.4426097869873ms\n", "inputText:进口青苹果 二级, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:514.6446228027344ms\n", "inputText:仔姜 一级, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:552.1271228790283ms\n", "inputText:冰菜 一级/胶盒, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:535.0189208984375ms\n", "inputText:大西芹 一级, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:580.1188945770264ms\n", "inputText:大青瓜 一级, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:584.0885639190674ms\n", "inputText:斑斓叶 一级, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:594.2614078521729ms\n", "inputText:柠檬叶 精选完整叶, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:523.0464935302734ms\n", "inputText:水果小青瓜 一级, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:512.0902061462402ms\n", "inputText:油苦瓜 一级, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:522.1538543701172ms\n", "inputText:甜菜根, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:535.7348918914795ms\n", "inputText:番茄 一级, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:635.2965831756592ms\n", "inputText:羽衣甘蓝 一级, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:539.3381118774414ms\n", "inputText:老黄姜 一级/生姜, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:539.1485691070557ms\n", "inputText:胡萝卜 一级, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:494.7984218597412ms\n", "inputText:薄荷叶 一级/大叶, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:512.5901699066162ms\n", "inputText:迷迭香 一级, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:529.3216705322266ms\n", "inputText:青彩椒 一级, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:509.1269016265869ms\n", "inputText:香茅草 一级, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:504.81247901916504ms\n", "inputText:鲜香菜, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:627.488374710083ms\n", "inputText:味全冷藏牛乳 950ml, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:588.6561870574951ms\n", "inputText:悦鲜活鲜牛乳(冷藏), usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:614.1047477722168ms\n", "inputText:益力多乳酸菌100ml(单下不送), usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:522.7305889129639ms\n", "inputText:安佳淡奶油1L(冷藏）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:542.1535968780518ms\n", "inputText:维益咖啡饮品浓缩奶油 1L, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:670.4962253570557ms\n", "inputText:维益爱真喷射奶油500g, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:891.6051387786865ms\n", "inputText:维益芝士云顶奶霜700g, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:570.8718299865723ms\n", "inputText:迪比克喷射型加糖稀奶油 700ml, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:666.0654544830322ms\n", "inputText:雀巢淡奶油 1L/稀奶油, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:589.6856784820557ms\n", "inputText:安佳芝士1kg(冷藏）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:538.661003112793ms\n", "inputText:雀巢三花全脂淡奶410g, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:606.107234954834ms\n", "inputText:雀巢三花植脂淡奶410g, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:553.3273220062256ms\n", "inputText:雀巢鹰唛炼奶350g, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:513.0467414855957ms\n", "inputText:黑白淡奶400g, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:628.4511089324951ms\n", "inputText:君乐宝纯牛奶, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:544.3480014801025ms\n", "inputText:大M全脂牛奶 1L, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:564.2197132110596ms\n", "inputText:雀巢全脂牛奶 1L, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:511.4879608154297ms\n", "inputText:黑白全脂纯牛奶1L, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:523.7627029418945ms\n", "inputText:菲诺冷冻生椰乳1kg, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:566.2815570831299ms\n", "inputText:菲诺厚椰乳(带盖版）1L, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:538.0368232727051ms\n", "inputText:菲诺厚椰乳(无盖版）1L, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:588.3021354675293ms\n", "inputText:菲诺椰皇水1kg, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:523.7491130828857ms\n", "inputText:植脂末定制(嘉禾食品)20kg, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:500.7028579711914ms\n", "inputText:产地冷冻椰浆 950ml, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:573.5495090484619ms\n", "inputText:正宗椰树牌椰汁1L, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:519.8111534118652ms\n", "inputText:金牌高达椰浆400ml, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:1654.8688411712646ms\n", "inputText:koully宝利生打椰奶（瓶装）980g, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:500.78892707824707ms\n", "inputText:koully宝利生打椰奶（袋装）1kg, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:994.9493408203125ms\n", "inputText:koully宝利生打椰子水（袋装）1kg, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:623.6433982849121ms\n", "inputText:海南椰萃生椰乳(热咖)  980g, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:527.2016525268555ms\n", "inputText:海南椰萃生椰乳(瓶装)980g, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:654.8631191253662ms\n", "inputText:海南椰萃生椰乳（袋装）1kg, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:557.563304901123ms\n", "inputText:海南椰萃生椰水（袋装）1kg, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:596.5011119842529ms\n", "inputText:泰象苏打水325ml*24瓶, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:571.8882083892822ms\n", "inputText:冰滴咖啡(豆雅匠新)1L, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:567.2409534454346ms\n", "inputText:咖啡大师燕麦饮料1L, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:707.2958946228027ms\n", "inputText:茶饮大师燕麦奶1L, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:558.7539672851562ms\n", "inputText:达川 NFC原榨红西柚果肉浆1kg, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:515.899658203125ms\n", "inputText:达川NFC冷冻桑葚原浆 1kg, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:628.0627250671387ms\n", "inputText:达川NFC冷冻龙眼汁 1kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:567.6672458648682ms\n", "inputText:达川NFC原榨余甘汁油甘汁油柑汁1kg, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:583.2290649414062ms\n", "inputText:达川NFC原榨夏黑葡萄汁1kg, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:663.0897521972656ms\n", "inputText:达川NFC原榨巨峰葡萄汁1kg, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:566.3397312164307ms\n", "inputText:达川NFC原榨杨梅浆1kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:558.5041046142578ms\n", "inputText:达川NFC原榨柠檬汁1kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:573.1425285339355ms\n", "inputText:达川NFC原榨橄榄汁1kg, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:560.5056285858154ms\n", "inputText:达川NFC原榨水蜜桃浆（白色款）1kg, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:667.9797172546387ms\n", "inputText:达川NFC原榨沃柑汁 1kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:490.2069568634033ms\n", "inputText:达川NFC原榨玫珑瓜果浆1kg, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:574.2125511169434ms\n", "inputText:达川NFC原榨石榴汁1kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:512.6485824584961ms\n", "inputText:达川NFC原榨红心番石榴汁1kg, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:563.9564990997314ms\n", "inputText:达川NFC原榨脐橙汁1kg, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:635.8401775360107ms\n", "inputText:达川NFC原榨芒果浆1kg, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:519.7784900665283ms\n", "inputText:达川NFC原榨荔枝汁1kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:509.13119316101074ms\n", "inputText:达川NFC原榨菠萝汁1kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:534.3523025512695ms\n", "inputText:达川NFC原榨黄皮汁1kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:606.4901351928711ms\n", "inputText:达川冷冻山楂汁, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:512.5272274017334ms\n", "inputText:达川冷冻苹果汁, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:573.0164051055908ms\n", "inputText:达川冷冻蜜桃复合果浆1kg, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:569.4820880889893ms\n", "inputText:达川复合青提汁饮料1kg, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:520.5707550048828ms\n", "inputText:达川带籽百香果浆1kg, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:531.8317413330078ms\n", "inputText:达川草莓复合果浆1kg, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:528.5918712615967ms\n", "inputText:产地冷冻芒果浆 1kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:539.9770736694336ms\n", "inputText:产地金桔汁 950ml, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:531.3634872436523ms\n", "inputText:苏亚速冻百香果浆 950g, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:616.6157722473145ms\n", "inputText:质利冷冻芒果浆960g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:1205.2865028381348ms\n", "inputText:产地冷冻黄皮果 1kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:494.43578720092773ms\n", "inputText:冷冻味柠油甘汁, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:551.7680644989014ms\n", "inputText:常温德创水蜜桃汁 1L, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:612.743616104126ms\n", "inputText:冷冻小青团小丸子1kg(古X同款), usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:672.9192733764648ms\n", "inputText:即食西米晶球  500g, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:597.3546504974365ms\n", "inputText:呈香圆形芋圆小圆子1kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:537.9776954650879ms\n", "inputText:天聪红豆罐头（带拉手）  3.35kg, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:622.9124069213867ms\n", "inputText:好C冠红西柚果粒罐头850g, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:496.53005599975586ms\n", "inputText:椰果果粒（奕芳）1kg, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:505.98716735839844ms\n", "inputText:炫彩水晶马蹄爆爆珠850g, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:588.4010791778564ms\n", "inputText:琥珀珍珠(粉圆)1kg, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:540.4253005981445ms\n", "inputText:糖蜜椰果  1kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:496.5200424194336ms\n", "inputText:茶芘糖水红豆罐头950g, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:1391.5252685546875ms\n", "inputText:长盈定制寒天(鲜活) 2kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:509.22393798828125ms\n", "inputText:鲜活公版寒天(甜心原味晶球)  2kg, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:517.554521560669ms\n", "inputText:广禧   海盐芝士奶盖粉风味固体饮料  500g, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:568.6626434326172ms\n", "inputText:水晶冻粉1kg, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:531.6896438598633ms\n", "inputText:双桥F55果糖彩标, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:529.890775680542ms\n", "inputText:双桥F60果糖彩标, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:520.453691482544ms\n", "inputText:味大大蔗糖糖浆25kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:506.21509552001953ms\n", "inputText:壹糖天下海盐糖浆1.2kg, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:501.45649909973145ms\n", "inputText:德馨竹蔗冰糖1.26kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:1109.0946197509766ms\n", "inputText:森糖F55果葡糖浆, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:633.8376998901367ms\n", "inputText:森糖F60果葡糖浆, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:518.4760093688965ms\n", "inputText:长盈定制冰糖浆, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:532.5233936309814ms\n", "inputText:韩国白砂糖(30kg), usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:498.5067844390869ms\n", "inputText:大薯条(麦肯臻脆）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:542.5820350646973ms\n", "inputText:奶茶杯防漏纸13cm(适用98口径以内), usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:495.1972961425781ms\n", "inputText:奶茶杯防漏纸16cm(适用98-110mm口径), usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:545.2735424041748ms\n", "inputText:90#500ml磨砂注塑空杯, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:520.0033187866211ms\n", "inputText:90#700ml磨砂注塑空杯, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:530.3773880004883ms\n", "inputText:90#连体注塑盖(乳白色/磨砂), usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:575.8824348449707ms\n", "inputText:90#连体注塑盖(高透色/磨砂), usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:546.8161106109619ms\n", "inputText:PET-98口径平盖, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:549.1890907287598ms\n", "inputText:PET-98口径高盖, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:507.24148750305176ms\n", "inputText:98-16OZ-500mlPET, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:548.8665103912354ms\n", "inputText:98-20OZ-600mlPET, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:531.3882827758789ms\n", "inputText:98-24OZ-700mlPET, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:514.9588584899902ms\n", "inputText:牛皮纸包大吸管, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:498.14558029174805ms\n", "inputText:牛皮纸包小吸管(0.6*23), usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:537.9509925842285ms\n", "inputText:白皮纸大吸管, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:575.7505893707275ms\n", "inputText:牛皮纸双杯托, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:530.1003456115723ms\n", "inputText:牛皮纸四杯托, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:571.9716548919678ms\n", "inputText:YP东方美人茶叶, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:2028.0373096466064ms\n", "inputText:YP鸭屎香专版茶叶, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:527.6360511779785ms\n", "inputText:四季春茶叶, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:634.2422962188721ms\n", "inputText:茉莉花茶, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:598.320722579956ms\n", "inputText:蜜香红茶, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:523.7798690795898ms\n", "inputText:锡兰红茶, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:525.6669521331787ms\n", "inputText:鸭屎香2号茶叶, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:545.6111431121826ms\n", "inputText:样品(水果), usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:680.4590225219727ms\n", "inputText:长盈售后电话18998068162, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:547.5311279296875ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['name'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
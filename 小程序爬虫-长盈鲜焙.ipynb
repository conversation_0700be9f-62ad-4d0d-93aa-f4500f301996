{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "1709001478235, headers:{'authority': 'bshop.guanmai.cn', 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "THREAD_CNT = int(os.environ.get('THREAD_CNT', 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "hints={'odps.sql.hive.compatible':True,'odps.sql.type.system.odps2':True}\n", "def get_odps_sql_result_as_df(sql):\n", "    instance=odps.execute_sql(sql, hints=hints)\n", "    instance.wait_for_success()\n", "    pd_df=None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={\n", "    # 'Referer':f'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp={timestamp_of_now}',\n", "         'authority': 'bshop.guanmai.cn',\n", "         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "         'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}\n", "brand_name='长盈鲜焙'\n", "competitor_name_en='changyingxianbei'\n", "\n", "create_directory_if_not_exists(f\"./data/{brand_name}\")\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 先登录\n", "\n", "获取token并保存"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sessionid: lf9wv4f0wnw2an0uafiiwrmb6b8ju9fu\n", "response cookie:{'sessionid': 'lf9wv4f0wnw2an0uafiiwrmb6b8ju9fu'}\n", "{'code': 0, 'msg': '登录成功', 'data': {'user_id': 3003009}} 登录成功\n"]}], "source": ["# 登录\n", "from urllib.parse import unquote\n", "\n", "url='https://bshop.guanmai.cn/login'\n", "login_response=requests.post(url, headers=headers, data={'username':'17729941198', 'password':'aa123456'})\n", "\n", "\n", "after_login_cookie={}\n", "# Print all the cookies set by the server\n", "for cookie in login_response.cookies:\n", "    print(f'{cookie.name}: {cookie.value}')\n", "    after_login_cookie[cookie.name]=cookie.value\n", "\n", "\n", "print(f\"response cookie:{after_login_cookie}\")\n", "print(login_response.json(),unquote(login_response.json()['msg']))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<!doctype html><html><head><meta charset=\"UTF-8\"/><meta name=\"format-detection\" content=\"telephone=n\n", "new sessionid:lf9wv4f0wnw2an0uafiiwrmb6b8ju9fu\n"]}], "source": ["after_login_cookie.update({'cms_key':'cygyl','group_id':'3252'})\n", "after_login_cookie\n", "\n", "sessionid=after_login_cookie['sessionid']\n", "\n", "url = 'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp=1706286340876'\n", "\n", "headers = {\n", "    'authority': 'bshop.guanmai.cn',\n", "    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "    'accept-language': 'en-US,en;q=0.9',\n", "    'cookie': f'cms_key=cygyl; group_id=3252; sessionid={sessionid}; gr_user_id=62c026d8-a829-40c7-823f-d7e38bf255d6; 9beedda875b5420f_gr_session_id=2a97577a-00ae-45a7-8392-4cf0d0fde7cb; 9beedda875b5420f_gr_session_id_sent_vst=2a97577a-00ae-45a7-8392-4cf0d0fde7cb',\n", "    'referer': 'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp=1706286034360',\n", "    'sec-fetch-dest': 'document',\n", "    'sec-fetch-mode': 'navigate',\n", "    'sec-fetch-site': 'same-origin',\n", "    'sec-fetch-user': '?1',\n", "    'upgrade-insecure-requests': '1',\n", "    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'\n", "}\n", "\n", "response = requests.get(url, headers=headers)\n", "\n", "print(response.text[0:100])\n", "\n", "print(f\"new sessionid:{sessionid}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取一级类目列表"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>children</th>\n", "      <th>name</th>\n", "      <th>id</th>\n", "      <th>rank</th>\n", "      <th>url</th>\n", "      <th>station_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[{'name': '高端水果精品装', 'id': '********', 'first_...</td>\n", "      <td>新鲜蔬果</td>\n", "      <td>A612147</td>\n", "      <td>9</td>\n", "      <td>https://img.guanmai.cn/icon/3121b06b26aa32b1.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[{'name': '鲜奶|益力多I卡士', 'id': 'B955635', 'first...</td>\n", "      <td>乳制品</td>\n", "      <td>A612148</td>\n", "      <td>8</td>\n", "      <td>https://img.guanmai.cn/icon/6990fe774cd50b4a.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>[{'name': '达川冷冻原汁', 'id': 'B1281186', 'first_c...</td>\n", "      <td>水吧|类</td>\n", "      <td>A612149</td>\n", "      <td>7</td>\n", "      <td>https://img.guanmai.cn/icon/e47e404af0b894b7.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>[{'name': '糖浆', 'id': 'B955649', 'first_catego...</td>\n", "      <td>果糖|浆</td>\n", "      <td>A612150</td>\n", "      <td>6</td>\n", "      <td>https://img.guanmai.cn/icon/0b1dc790704f40bd.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>[{'name': '油炸小吃', 'id': 'B974236', 'first_cate...</td>\n", "      <td>油炸|类</td>\n", "      <td>A612151</td>\n", "      <td>5</td>\n", "      <td>https://img.guanmai.cn/icon/063b3c5bf5b45342.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>[{'name': '防漏纸', 'id': 'B1271477', 'first_cate...</td>\n", "      <td>包材|类</td>\n", "      <td>A612152</td>\n", "      <td>2</td>\n", "      <td>https://img.guanmai.cn/icon/d34c0c994888c4c0.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>[{'name': '定制茶叶', 'id': 'B979080', 'first_cate...</td>\n", "      <td>茶叶专区</td>\n", "      <td>A621470</td>\n", "      <td>3</td>\n", "      <td>https://img.guanmai.cn/icon/93956d8d3651f2ea.p...</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>[{'name': '售后专用', 'id': 'B992804', 'first_cate...</td>\n", "      <td>长盈售后</td>\n", "      <td>A629078</td>\n", "      <td>1</td>\n", "      <td>https://img.guanmai.cn/icon/81fbd6ff3dc076e0.p...</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            children  name       id  rank  \\\n", "0  [{'name': '高端水果精品装', 'id': '********', 'first_...  新鲜蔬果  A612147     9   \n", "1  [{'name': '鲜奶|益力多I卡士', 'id': 'B955635', 'first...   乳制品  A612148     8   \n", "2  [{'name': '达川冷冻原汁', 'id': 'B1281186', 'first_c...  水吧|类  A612149     7   \n", "3  [{'name': '糖浆', 'id': 'B955649', 'first_catego...  果糖|浆  A612150     6   \n", "4  [{'name': '油炸小吃', 'id': 'B974236', 'first_cate...  油炸|类  A612151     5   \n", "5  [{'name': '防漏纸', 'id': 'B1271477', 'first_cate...  包材|类  A612152     2   \n", "6  [{'name': '定制茶叶', 'id': 'B979080', 'first_cate...  茶叶专区  A621470     3   \n", "7  [{'name': '售后专用', 'id': 'B992804', 'first_cate...  长盈售后  A629078     1   \n", "\n", "                                                 url station_id  \n", "0  https://img.guanmai.cn/icon/3121b06b26aa32b1.p...             \n", "1  https://img.guanmai.cn/icon/6990fe774cd50b4a.p...             \n", "2  https://img.guanmai.cn/icon/e47e404af0b894b7.p...             \n", "3  https://img.guanmai.cn/icon/0b1dc790704f40bd.p...             \n", "4  https://img.guanmai.cn/icon/063b3c5bf5b45342.p...             \n", "5  https://img.guanmai.cn/icon/d34c0c994888c4c0.p...             \n", "6  https://img.guanmai.cn/icon/93956d8d3651f2ea.p...             \n", "7  https://img.guanmai.cn/icon/81fbd6ff3dc076e0.p...             "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取一级类目列表\n", "\n", "url=f'https://bshop.guanmai.cn/product/category/get'\n", "\n", "categoryList=requests.get(url, headers=headers, cookies=after_login_cookie).json()['data']\n", "\n", "cate_list_df=pd.DataFrame(categoryList)\n", "cate_list_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'name': '高端水果精品装', 'id': '********', 'first_category_id': 'A612147', 'rank': 19, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '荔枝I龙眼I黄皮', 'id': 'B1272035', 'first_category_id': 'A612147', 'rank': 6, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '油柑I山楂I桑葚', 'id': 'B1272036', 'first_category_id': 'A612147', 'rank': 4, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '木瓜I莲雾I人参果', 'id': 'B1590455', 'first_category_id': 'A612147', 'rank': 5, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '柠檬I金桔', 'id': 'B955620', 'first_category_id': 'A612147', 'rank': 17, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '莓类浆果I杨梅', 'id': 'B955621', 'first_category_id': 'A612147', 'rank': 16, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '牛油果', 'id': 'B955623', 'first_category_id': 'A612147', 'rank': 18, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '凤梨I菠萝I椰子', 'id': 'B955624', 'first_category_id': 'A612147', 'rank': 12, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '芭乐I石榴I番茄', 'id': 'B955625', 'first_category_id': 'A612147', 'rank': 9, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '西瓜I蜜瓜I甜瓜', 'id': 'B955626', 'first_category_id': 'A612147', 'rank': 15, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '柑I橘I橙I柚', 'id': 'B955627', 'first_category_id': 'A612147', 'rank': 14, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '火龙果I香蕉I奇异果', 'id': 'B955628', 'first_category_id': 'A612147', 'rank': 7, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '百香果I芒果', 'id': 'B955629', 'first_category_id': 'A612147', 'rank': 13, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '葡萄I提子', 'id': 'B955630', 'first_category_id': 'A612147', 'rank': 10, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '苹果I梨I桃I李', 'id': 'B955633', 'first_category_id': 'A612147', 'rank': 8, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '时令蔬菜', 'id': 'B955634', 'first_category_id': 'A612147', 'rank': 2, 'first_category_name': '新鲜蔬果', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n", "{'name': '鲜奶|益力多I卡士', 'id': 'B955635', 'first_category_id': 'A612148', 'rank': 11, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '奶油', 'id': 'B955636', 'first_category_id': 'A612148', 'rank': 12, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '芝士', 'id': 'B955637', 'first_category_id': 'A612148', 'rank': 9, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '炼奶|淡奶', 'id': 'B955638', 'first_category_id': 'A612148', 'rank': 10, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '液体奶', 'id': 'B955639', 'first_category_id': 'A612148', 'rank': 8, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '菲诺', 'id': 'B955640', 'first_category_id': 'A612148', 'rank': 7, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '植脂末', 'id': 'B955641', 'first_category_id': 'A612148', 'rank': 6, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '椰浆', 'id': 'B955642', 'first_category_id': 'A612148', 'rank': 4, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '宝利I椰萃I椰水', 'id': 'B955643', 'first_category_id': 'A612148', 'rank': 2, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '苏打水|气泡水', 'id': 'B955644', 'first_category_id': 'A612148', 'rank': 5, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '咖啡I燕麦奶', 'id': 'B994540', 'first_category_id': 'A612148', 'rank': 1, 'first_category_name': '乳制品', 'first_category_url': 'https://img.guanmai.cn/icon/6990fe774cd50b4a.png??imageslim'}\n", "{'name': '达川冷冻原汁', 'id': 'B1281186', 'first_category_id': 'A612149', 'rank': 8, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '产地冷冻原汁', 'id': 'B1281187', 'first_category_id': 'A612149', 'rank': 7, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '百香果冷冻原汁', 'id': 'B1281188', 'first_category_id': 'A612149', 'rank': 6, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '芒果冷冻原汁', 'id': 'B1281189', 'first_category_id': 'A612149', 'rank': 5, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '冷冻水果', 'id': 'B1306764', 'first_category_id': 'A612149', 'rank': 9, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '其它冷冻果汁', 'id': 'B955645', 'first_category_id': 'A612149', 'rank': 4, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '常温果汁', 'id': 'B955646', 'first_category_id': 'A612149', 'rank': 3, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '小料', 'id': 'B955647', 'first_category_id': 'A612149', 'rank': 2, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '粉类', 'id': 'B955648', 'first_category_id': 'A612149', 'rank': 1, 'first_category_name': '水吧|类', 'first_category_url': 'https://img.guanmai.cn/icon/e47e404af0b894b7.png??imageslim'}\n", "{'name': '糖浆', 'id': 'B955649', 'first_category_id': 'A612150', 'rank': 0, 'first_category_name': '果糖|浆', 'first_category_url': 'https://img.guanmai.cn/icon/0b1dc790704f40bd.png??imageslim'}\n", "{'name': '砂糖', 'id': 'B955650', 'first_category_id': 'A612150', 'rank': 0, 'first_category_name': '果糖|浆', 'first_category_url': 'https://img.guanmai.cn/icon/0b1dc790704f40bd.png??imageslim'}\n", "{'name': '油炸小吃', 'id': 'B974236', 'first_category_id': 'A612151', 'rank': 0, 'first_category_name': '油炸|类', 'first_category_url': 'https://img.guanmai.cn/icon/063b3c5bf5b45342.png??imageslim'}\n", "{'name': '防漏纸', 'id': 'B1271477', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '常规包材', 'id': 'B955652', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '盖子', 'id': 'B955654', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '手打柠檬茶杯', 'id': 'B955656', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '吸管', 'id': 'B955657', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '杯托', 'id': 'B955658', 'first_category_id': 'A612152', 'rank': 0, 'first_category_name': '包材|类', 'first_category_url': 'https://img.guanmai.cn/icon/d34c0c994888c4c0.png??imageslim'}\n", "{'name': '定制茶叶', 'id': 'B979080', 'first_category_id': 'A621470', 'rank': 0, 'first_category_name': '茶叶专区', 'first_category_url': 'https://img.guanmai.cn/icon/93956d8d3651f2ea.png??imageslim'}\n", "{'name': '售后专用', 'id': 'B992804', 'first_category_id': 'A629078', 'rank': 0, 'first_category_name': '长盈售后', 'first_category_url': 'https://img.guanmai.cn/icon/81fbd6ff3dc076e0.png??imageslim'}\n"]}], "source": ["all_second_category=[]\n", "for first in categoryList:\n", "    first_obj={'first_category_name':first['name'],'first_category_url':first['url']}\n", "    for second in first['children']:\n", "        second.update(first_obj)\n", "        all_second_category.append(second)\n", "        print(second)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["类目:高端水果精品装, 商品数:1, SKU names:['智利车厘子 J级/Lapins']\n", "类目:荔枝I龙眼I黄皮, 商品数:1, SKU names:['泰国龙眼 一级/中大果']\n", "类目:油柑I山楂I桑葚, 商品数:3, SKU names:['山楂 一级/胶盒', '饼甜油柑 一级/中大果', '黑桑葚 一级/胶框(8斤装)']\n", "类目:木瓜I莲雾I人参果, 商品数:1, SKU names:['海南莲雾 一级(黑金刚)']\n", "类目:柠檬I金桔, 商品数:11, SKU names:['双胞胎黄柠檬 一级/中果/扁箱', '塔西提无籽青柠檬 一级/中大果', '子弹头长香水柠檬 一级/55-140g', '子弹头长香水柠檬 一级/55-140g(整件)', '安岳黄柠檬 一级/中大果(115g+)', '安岳黄柠檬 一级/标准果(90-115g)', '有籽青柠檬 一级/中大果', '锁匙扣黄柠檬 一级/中大果(30斤装)', '锁匙扣黄柠檬 一级/大果(带箱20斤装)', '青金桔 一级/2斤装', '香水柠檬 一级/老树果(按斤)', '香水柠檬 一级/老树果(整件)', '香水柠檬 二级/青黄果(按斤)', '香水柠檬 二级/青黄果(整件)']\n", "类目:莓类浆果I杨梅, 商品数:6, SKU names:['丹东红颜草莓 胶盒/20粒', '安徽奶油草莓 纸盒/15-20粒', '安徽红颜草莓  板装(3.5斤装)', '本地法兰蒂草莓 胶框/5斤', '草莓 二级/瑕疵果/不售后', '进口蓝莓 一级/中大果', '进口蓝莓 一级/中大果(整件)']\n", "类目:牛油果, 商品数:3, SKU names:['秘鲁牛油果 熟果/130g±10g', '秘鲁牛油果 熟果/140g±10g', '秘鲁牛油果 熟果/150g+']\n", "类目:凤梨I菠萝I椰子, 商品数:5, SKU names:['佳农凤梨 有冠/7-8头/大果', '佳农凤梨 有冠/大果', '徐闻菠萝 一级/带冠', '泰国香水椰青 东泰一个宝/大果(9个)', '泰国香水椰青 大果/小猩猩牌', '泰国香水椰青 大果/小猩猩牌', '都乐无冠凤梨 一级/金菠萝', '都乐无冠凤梨 一级/金菠萝(6-8头)']\n", "类目:芭乐I石榴I番茄, 商品数:2, SKU names:['圣女果', '圣女果 11.5斤/件', '红心芭乐 一级/中大果']\n", "类目:西瓜I蜜瓜I甜瓜, 商品数:5, SKU names:['无籽西瓜 一级(8斤+/个)', '晓蜜25号蜜瓜 一级', '玫珑蜜瓜(网纹瓜)', '甘美4K无籽西瓜 一级/中大果', '麒麟西瓜 一级(8斤+/个)', '麒麟西瓜 一级(整件)']\n", "类目:柑I橘I橙I柚, 商品数:6, SKU names:['橙子 二级/通货/榨汁专用', '美国橙子 一级/中大果', '耙耙柑 一级 /中大果', '赣南脐橙 一级/中大果', '赣南脐橙 一级/中大果(整件)', '进口西柚 一级/中大果', '黄金桔 一级/中小果']\n", "类目:火龙果I香蕉I奇异果, 商品数:4, SKU names:['国产猕猴桃 一级/中大果', '国产猕猴桃 一级/中大果(整件)', '越南白心火龙果 一级/中大果', '越南白心火龙果 一级/中大果(整件)', '进口香蕉 一级/熟果', '金都一号红心火龙果 一级']\n", "类目:百香果I芒果, 商品数:8, SKU names:['大青芒 一级/大果(10斤装)', '大青芒 一级/大果(50斤/件)', '大青芒 二级/通货', '小台农芒 一级(10斤装)', '小台农芒 一级/胶框(整件)', '小台农芒 三级/轻微黑点', '小台农芒 二级(品质好)', '紫皮百香果 一级/中大果(光皮)', '紫香百香果 二级/中大果(皱皮)', '黄金百香果 一级/中大果']\n", "类目:葡萄I提子, 商品数:2, SKU names:['夏黑葡萄 一级/金果/黑框装', '阳光香印青提 一级/胶框装(整件)']\n", "类目:苹果I梨I桃I李, 商品数:5, SKU names:['皇冠梨(蜜梨) 一级/中大果', '皇冠梨(蜜梨) 一级/中大果(整件)', '红富士苹果 一级/70-75#', '红富士苹果 一级/70-75#(整件)', '红富士苹果 二级/75-85#', '进口青苹果 一级', '进口青苹果 二级', '进口青苹果 二级']\n", "类目:时令蔬菜, 商品数:18, SKU names:['仔姜 一级', '冰菜 一级/胶盒', '大西芹 一级', '大青瓜 一级', '斑斓叶 一级', '柠檬叶 精选完整叶', '水果小青瓜 一级', '油苦瓜 一级', '甜菜根', '番茄 一级', '羽衣甘蓝 一级', '老黄姜 一级/生姜', '胡萝卜 一级', '薄荷叶 一级/大叶', '迷迭香 一级', '青彩椒 一级', '香茅草 一级', '鲜香菜']\n", "类目:鲜奶|益力多I卡士, 商品数:4, SKU names:['卡士佐餐奶428ml', '卡士佐餐奶428ml', '味全冷藏牛乳 950ml', '味全冷藏牛乳 950ml*12瓶', '悦鲜活鲜牛乳(冷藏)', '悦鲜活鲜牛乳(冷藏)', '益力多乳酸菌100ml*50瓶(单下不送)', '益力多乳酸菌100ml*5瓶(单下不送)']\n", "类目:奶油, 商品数:6, SKU names:['安佳淡奶油1L(冷藏）', '安佳淡奶油1L(冷藏）', '维益咖啡饮品浓缩奶油 1L', '维益咖啡饮品浓缩奶油 1L*12瓶', '维益爱真喷射奶油500g', '维益爱真喷射奶油500g', '维益芝士云顶奶霜700g', '维益芝士云顶奶霜700g', '迪比克喷射型加糖稀奶油 700ml', '迪比克喷射型加糖稀奶油 700ml', '雀巢淡奶油 1L/稀奶油', '雀巢淡奶油1L*12瓶/稀奶油']\n", "类目:芝士, 商品数:1, SKU names:['安佳芝士1kg(冷藏）', '安佳芝士1kg(冷藏）']\n", "类目:炼奶|淡奶, 商品数:4, SKU names:['雀巢三花全脂淡奶410g', '雀巢三花全脂淡奶410g', '雀巢三花植脂淡奶410g', '雀巢三花植脂淡奶410g', '雀巢鹰唛炼奶 350g', '雀巢鹰唛炼奶 350g*48罐', '黑白淡奶400g', '黑白淡奶400g']\n", "类目:液体奶, 商品数:4, SKU names:['君乐宝纯牛奶', '君乐宝纯牛奶', '大M全脂牛奶 1L', '大M全脂牛奶 1L(整件)', '雀巢全脂牛奶 1L', '雀巢全脂牛奶 1L*12瓶', '黑白全脂纯牛奶1L', '黑白全脂纯牛奶1L']\n", "类目:菲诺, 商品数:4, SKU names:['菲诺冷冻生椰乳1kg', '菲诺冷冻生椰乳1kg', '菲诺厚椰乳(带盖版）1L', '菲诺厚椰乳(带盖版）1L', '菲诺厚椰乳(无盖版）1L', '菲诺厚椰乳(无盖版）1L', '菲诺椰皇水1kg', '菲诺椰皇水1kg']\n", "类目:植脂末, 商品数:1, SKU names:['植脂末定制(嘉禾食品) 20kg']\n", "类目:椰浆, 商品数:3, SKU names:['产地冷冻椰浆 950ml', '产地冷冻椰浆950ml', '正宗椰树牌椰汁1L', '正宗椰树牌椰汁1L', '金牌高达椰浆400ml', '金牌高达椰浆400ml']\n", "类目:宝利I椰萃I椰水, 商品数:7, SKU names:['koully宝利生打椰奶（瓶装）980g', 'koully宝利生打椰奶（瓶装）980g', 'koully宝利生打椰奶（袋装）1kg', 'koully宝利生打椰奶（袋装）1kg', 'koully宝利生打椰子水（袋装）1kg', 'koully宝利生打椰子水（袋装）1kg', '海南椰萃生椰乳(热咖)  980g', '海南椰萃生椰乳(热咖)  980g', '海南椰萃生椰乳(瓶装)980g', '海南椰萃生椰乳(瓶装)980g', '海南椰萃生椰乳（袋装）1kg', '海南椰萃生椰乳（袋装）1kg', '海南椰萃生椰水（袋装）1kg', '海南椰萃生椰水（袋装）1kg']\n", "类目:苏打水|气泡水, 商品数:1, SKU names:['泰象苏打水325ml*24瓶']\n", "类目:咖啡I燕麦奶, 商品数:3, SKU names:['冰滴咖啡(豆雅匠新)1L', '冰滴咖啡(豆雅匠新)1L', '咖啡大师燕麦饮料1L', '咖啡大师燕麦饮料1L', '茶饮大师燕麦奶1L', '茶饮大师燕麦奶1L']\n", "类目:达川冷冻原汁, 商品数:25, SKU names:['达川 NFC原榨红西柚果肉浆1kg', '达川 NFC原榨红西柚果肉浆1kg', '达川NFC冷冻桑葚原浆 1kg', '达川NFC冷冻桑葚原浆 1kg', '达川NFC冷冻龙眼汁 1kg', '达川NFC冷冻龙眼汁 1kg', '达川NFC原榨余甘汁油甘汁油柑汁1kg', '达川NFC原榨余甘汁油甘汁油柑汁1kg', '达川NFC原榨夏黑葡萄汁1kg', '达川NFC原榨夏黑葡萄汁1kg', '达川NFC原榨巨峰葡萄汁1kg', '达川NFC原榨巨峰葡萄汁1kg', '达川NFC原榨杨梅浆1kg', '达川NFC原榨杨梅浆1kg', '达川NFC原榨柠檬汁1kg', '达川NFC原榨柠檬汁1kg', '达川NFC原榨橄榄汁1kg', '达川NFC原榨橄榄汁1kg', '达川NFC原榨水蜜桃浆（白色款）1kg', '达川NFC原榨水蜜桃浆（白色款）1kg', '达川NFC原榨沃柑汁 1kg', '达川NFC原榨沃柑汁 1kg', '达川NFC原榨玫珑瓜果浆1kg', '达川NFC原榨玫珑瓜果浆1kg', '达川NFC原榨石榴汁1kg', '达川NFC原榨石榴汁1kg', '达川NFC原榨红心番石榴汁1kg', '达川NFC原榨红心番石榴汁1kg', '达川NFC原榨脐橙汁1kg', '达川NFC原榨脐橙汁1kg', '达川NFC原榨芒果浆1kg', '达川NFC原榨芒果浆1kg', '达川NFC原榨荔枝汁1kg', '达川NFC原榨荔枝汁1kg', '达川NFC原榨菠萝汁1kg', '达川NFC原榨菠萝汁1kg', '达川NFC原榨黄皮汁1kg', '达川NFC原榨黄皮汁1kg', '达川冷冻山楂汁', '达川冷冻山楂汁', '达川冷冻苹果汁', '达川冷冻苹果汁', '达川冷冻蜜桃复合果浆1kg', '达川冷冻蜜桃复合果浆1kg', '达川复合青提汁饮料1kg', '达川复合青提汁饮料1kg', '达川带籽百香果浆1kg', '达川带籽百香果浆1kg', '达川草莓复合果浆1kg', '达川草莓复合果浆1kg']\n", "类目:产地冷冻原汁, 商品数:2, SKU names:['产地冷冻芒果浆 1kg', '产地冷冻芒果浆 1kg*24瓶', '产地金桔汁 950ml', '产地金桔汁950ml']\n", "类目:百香果冷冻原汁, 商品数:1, SKU names:['苏亚速冻百香果浆 950g', '苏亚速冻百香果浆 950g']\n", "类目:芒果冷冻原汁, 商品数:1, SKU names:['质利冷冻芒果浆960g', '质利冷冻芒果浆960g']\n", "类目:冷冻水果, 商品数:1, SKU names:['产地冷冻黄皮果 1kg', '产地冷冻黄皮果 1kg*20包']\n", "类目:其它冷冻果汁, 商品数:1, SKU names:['冷冻味柠油甘汁', '冷冻味柠油甘汁']\n", "类目:常温果汁, 商品数:1, SKU names:['常温德创水蜜桃汁1L', '常温德创水蜜桃汁1L']\n", "类目:小料, 商品数:12, SKU names:['冷冻小青团小丸子1kg(古X同款)', '冷冻小青团小丸子1kg(古X同款)', '即食西米晶球  500g', '呈香圆形芋圆小圆子1kg', '呈香圆形芋圆小圆子1kg', '天聪红豆罐头（带拉手） 3.35kg*6罐（整件）', '天聪红豆罐头（带拉手）3.35kg', '好C冠红西柚果粒罐头850g', '好C冠红西柚果粒罐头850g', '椰果果粒（奕芳）1kg', '椰果果粒（奕芳）1kg', '炫彩水晶马蹄爆爆珠850g', '炫彩水晶马蹄爆爆珠850g', '琥珀珍珠(粉圆)1kg', '琥珀珍珠(粉圆)1kg', '糖蜜椰果  1kg', '糖蜜椰果  1kg', '茶芘糖水红豆罐头950g', '茶芘糖水红豆罐头950g', '长盈定制寒天(鲜活) 2kg', '长盈定制寒天(鲜活) 2kg', '鲜活公版寒天(甜心原味晶球) 2kg', '鲜活公版寒天(甜心原味晶球) 2kg']\n", "类目:粉类, 商品数:2, SKU names:['广禧 海盐芝士奶盖粉风味固体饮料  500g', '水晶冻粉1kg']\n", "类目:糖浆, 商品数:8, SKU names:['双桥F55果糖彩标 25KG', '双桥F60果糖彩标 25KG', '味大大蔗糖糖浆25kg', '壹糖天下海盐糖浆1.2kg', '壹糖天下海盐糖浆1.2kg', '德馨竹蔗冰糖 1.26KG', '德馨竹蔗冰糖1.26KG', '森糖F55果葡糖浆 25kg/桶', '森糖F60果葡糖浆 25kg/桶', '长盈定制冰糖浆 25KG']\n", "类目:砂糖, 商品数:1, SKU names:['韩国白砂糖（30kg）']\n", "类目:油炸小吃, 商品数:1, SKU names:['大薯条(麦肯臻脆） 2KG']\n", "类目:防漏纸, 商品数:2, SKU names:['奶茶杯防漏纸13cm(适用98口径以内)', '奶茶杯防漏纸16cm(适用98-110mm口径)']\n", "类目:常规包材, 商品数:2, SKU names:['90#500ml磨砂注塑空杯', '90#700ml磨砂注塑空杯']\n", "类目:盖子, 商品数:4, SKU names:['90#连体注塑盖(乳白/磨砂)1000个', '90#连体注塑盖(高透/磨砂)1000个', 'PET-98口径平盖1000个/件', 'PET-98口径高盖']\n", "类目:手打柠檬茶杯, 商品数:3, SKU names:['98-16OZ-500mlPET', '98-20OZ-600mlPET 1000pcs', '98-24OZ-700mlPET']\n", "类目:吸管, 商品数:3, SKU names:['牛皮纸包大吸管2000支', '牛皮纸包小吸管5000支(0.6*23)', '白纸包大吸管2000支']\n", "类目:杯托, 商品数:2, SKU names:['牛皮纸双杯托', '牛皮纸四杯托']\n", "类目:定制茶叶, 商品数:7, SKU names:['YP东方美人茶叶', 'YP鸭屎香专版茶叶', '四季春茶叶', '茉莉花茶', '蜜香红茶', '锡兰红茶', '鸭屎香2号茶叶']\n", "类目:售后专用, 商品数:2, SKU names:['样品(水果)', '样品(水果)', '长盈售后  快递费用电话18998068162', '长盈售后  退换货专用电话18998068162']\n"]}], "source": ["def get_products_for_second_cate(category_id='B955624'):\n", "    url=f'https://bshop.guanmai.cn/product/sku/get?level=2&category_id={category_id}'\n", "\n", "    product_list=requests.get(url, headers=headers, cookies=after_login_cookie).json()['data']\n", "    return product_list\n", "\n", "\n", "all_products=[]\n", "all_skus=[]\n", "for second in all_second_category:\n", "    sub_product_list=get_products_for_second_cate(second['id'])\n", "    sku_list=[]\n", "    for product in sub_product_list:\n", "        product['first_category_id']=second['first_category_id']\n", "        product['second_category_id']=second['id']\n", "        product['first_category_name']=second['first_category_name']\n", "        product['second_category_name']=second['name']\n", "        product['first_category_url']=second['first_category_url']\n", "        sku_list.extend(product['skus'])\n", "        all_skus.extend(product['skus'])\n", "    names = list(map(lambda x: x['name'], sku_list))\n", "    print(f\"类目:{second['name']}, 商品数:{len(sub_product_list)}, SKU names:{names}\")\n", "    all_products.extend(sub_product_list)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["200 {'id': '*********', 'std_sale_price': 850.0, 'std_sale_price_forsale': 850.0, 'std_unit_name_forsale': '斤', 'std_unit_name': '斤', 'salemenu_id': 'S144959', 'img_url': 'https://image.document.guanmai.cn/product_img/1665937879401-055094226843057426.jpeg??imageslim', 'img_urls': ['https://image.document.guanmai.cn/product_img/1665937879401-055094226843057426.jpeg??imageslim', 'https://image.document.guanmai.cn/product_img/1660376670293-9092891923980502.jpeg??imageslim', 'https://image.document.guanmai.cn/product_img/1661573944618-8889806629636121.jpeg??imageslim', 'https://image.document.guanmai.cn/product_img/1661573954330-7484253591652499.jpeg??imageslim'], 'is_fav': False, 'cart_amount': 0, 'skus': [{'id': 'D250998891', 'name': '泰国龙眼 一级/中大果', 'fee_type': 'CNY', 'desc': '按斤/净重', 'img_url': 'https://image.document.guanmai.cn/product_img/1665937879401-055094226843057426.jpeg??imageslim', 'img_urls': ['https://image.document.guanmai.cn/product_img/1665937879401-055094226843057426.jpeg??imageslim', 'https://image.document.guanmai.cn/product_img/1660376670293-9092891923980502.jpeg??imageslim', 'https://image.document.guanmai.cn/product_img/1661573944618-8889806629636121.jpeg??imageslim', 'https://image.document.guanmai.cn/product_img/1661573954330-7484253591652499.jpeg??imageslim'], 'category_id_1': 'A612147', 'category_id_2': 'B1272035', 'stocks': -99999, 'stock_type': 1, 'sale_num_least': 1.0, 'sale_price': 850.0, 'sale_unit_name': '斤', 'sale_ratio': 1.0, 'is_valid': True, 'state': 1, 'status': 2, 'std_sale_price': 850.0, 'std_unit_name': '斤', 'spu_id': '*********', 'salemenu_id': 'S144959', 'station_id': 'T220137', 'rule_price': None, 'is_price_timing': False, 'clean_food': False, 'promotion_price': None, 'limit_number': None, 'frequency': 0, 'std_sale_price_forsale': 850.0, 'std_unit_name_forsale': '斤', 'std_ratio': 1, 'rounding': 0, 'origin_area': None, 'origin_place': None, 'brand': None, 'specification_desc': None, 'feature_desc': None, 'after_sale_desc': None, 'is_step_price': 0, 'step_price_table': [], 'flash_price': None, 'flash_sale_data': {}, 'cart_amount': 0, 'search_key': 'B1272035'}], 'name': '泰国龙眼 一级/中大果', 'rank': 4, 'first_category_id': 'A612147', 'second_category_id': 'B1272035', 'first_category_name': '新鲜蔬果', 'second_category_name': '荔枝I龙眼I黄皮', 'first_category_url': 'https://img.guanmai.cn/icon/3121b06b26aa32b1.png??imageslim'}\n"]}], "source": ["print(len(all_products), all_products[1])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'spu_id': 'C15092196',\n", " 'name': '海南椰萃生椰乳（袋装）1kg',\n", " 'is_fav': <PERSON><PERSON><PERSON>,\n", " 'detail_images': [],\n", " 'img': ['https://image.document.guanmai.cn/product_img/1666071224036-8932348116388893.jpeg??imageslim'],\n", " 'cart_amount': 0,\n", " 'skus': [{'id': 'D250998861',\n", "   'name': '海南椰萃生椰乳（袋装）1kg',\n", "   'fee_type': 'CNY',\n", "   'desc': '保质期：2023.09.08-2024.09.08',\n", "   'img_url': 'https://image.document.guanmai.cn/product_img/1666071224036-8932348116388893.jpeg??imageslim',\n", "   'img_urls': ['https://image.document.guanmai.cn/product_img/1666071224036-8932348116388893.jpeg??imageslim'],\n", "   'category_id_1': 'A612148',\n", "   'category_id_2': 'B955643',\n", "   'stocks': 1035,\n", "   'stock_type': 2,\n", "   'sale_num_least': 1.0,\n", "   'sale_price': 1350.0,\n", "   'sale_unit_name': '包',\n", "   'sale_ratio': 1.0,\n", "   'is_valid': True,\n", "   'state': 1,\n", "   'status': 2,\n", "   'std_sale_price': 1350.0,\n", "   'std_unit_name': '包',\n", "   'spu_id': 'C15092196',\n", "   'salemenu_id': 'S144959',\n", "   'station_id': 'T220137',\n", "   'rule_price': None,\n", "   'is_price_timing': <PERSON>als<PERSON>,\n", "   'clean_food': <PERSON>alse,\n", "   'promotion_price': None,\n", "   'limit_number': None,\n", "   'frequency': 0,\n", "   'std_sale_price_forsale': 1350.0,\n", "   'std_unit_name_forsale': '包',\n", "   'std_ratio': 1,\n", "   'rounding': 0,\n", "   'origin_area': None,\n", "   'origin_place': None,\n", "   'brand': None,\n", "   'specification_desc': None,\n", "   'feature_desc': None,\n", "   'after_sale_desc': None,\n", "   'is_step_price': 0,\n", "   'step_price_table': [],\n", "   'flash_price': None,\n", "   'flash_sale_data': {},\n", "   'cart_amount': 0,\n", "   'order_begin_time': '2024-02-23 06:00:00',\n", "   'order_end_time': '2024-02-23 23:30:00'},\n", "  {'id': 'D250998862',\n", "   'name': '海南椰萃生椰乳（袋装）1kg',\n", "   'fee_type': 'CNY',\n", "   'desc': '保质期：2023.09.08-2024.09.08',\n", "   'img_url': 'https://image.document.guanmai.cn/product_img/1666071224036-8932348116388893.jpeg??imageslim',\n", "   'img_urls': ['https://image.document.guanmai.cn/product_img/1666071224036-8932348116388893.jpeg??imageslim'],\n", "   'category_id_1': 'A612148',\n", "   'category_id_2': 'B955643',\n", "   'stocks': 51,\n", "   'stock_type': 2,\n", "   'sale_num_least': 1.0,\n", "   'sale_price': 25000.0,\n", "   'sale_unit_name': '件',\n", "   'sale_ratio': 20.0,\n", "   'is_valid': True,\n", "   'state': 1,\n", "   'status': 2,\n", "   'std_sale_price': 1250.0,\n", "   'std_unit_name': '包',\n", "   'spu_id': 'C15092196',\n", "   'salemenu_id': 'S144959',\n", "   'station_id': 'T220137',\n", "   'rule_price': None,\n", "   'is_price_timing': <PERSON>als<PERSON>,\n", "   'clean_food': <PERSON>alse,\n", "   'promotion_price': None,\n", "   'limit_number': None,\n", "   'frequency': 0,\n", "   'std_sale_price_forsale': 1250.0,\n", "   'std_unit_name_forsale': '包',\n", "   'std_ratio': 1,\n", "   'rounding': 0,\n", "   'origin_area': None,\n", "   'origin_place': None,\n", "   'brand': None,\n", "   'specification_desc': None,\n", "   'feature_desc': None,\n", "   'after_sale_desc': None,\n", "   'is_step_price': 0,\n", "   'step_price_table': [],\n", "   'flash_price': None,\n", "   'flash_sale_data': {},\n", "   'cart_amount': 0,\n", "   'order_begin_time': '2024-02-23 06:00:00',\n", "   'order_end_time': '2024-02-23 23:30:00'}],\n", " 'combine_goods': [],\n", " 'is_open_nutrition': 0}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_product_detail(spu_id='C15092196'):\n", "    detail_url=f'https://bshop.guanmai.cn/product/sku/detail?spu_id={spu_id}'\n", "    product_detail=requests.get(detail_url, headers={}, cookies=after_login_cookie).json()['data']\n", "    return product_detail\n", "\n", "\n", "get_product_detail()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    [{'id': 'D264171923', 'name': '智利车厘子 J级/La<PERSON>s...\n", "Name: skus, dtype: object"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["all_products_df=pd.DataFrame(all_products)\n", "all_products_df.head(1)['skus']"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sku_id</th>\n", "      <th>sale_price</th>\n", "      <th>std_sale_price</th>\n", "      <th>spu_id</th>\n", "      <th>stocks</th>\n", "      <th>img_url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>D264171923</td>\n", "      <td>7200.0</td>\n", "      <td>3600.000000</td>\n", "      <td>*********</td>\n", "      <td>14.0</td>\n", "      <td>https://image.document.guanmai.cn/product_imag...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D250998891</td>\n", "      <td>850.0</td>\n", "      <td>850.000000</td>\n", "      <td>*********</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>D250999155</td>\n", "      <td>750.0</td>\n", "      <td>750.000000</td>\n", "      <td>C15770502</td>\n", "      <td>20.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>D250998732</td>\n", "      <td>900.0</td>\n", "      <td>900.000000</td>\n", "      <td>C14234059</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>D250998937</td>\n", "      <td>5440.0</td>\n", "      <td>680.000000</td>\n", "      <td>C16685444</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>D250999054</td>\n", "      <td>2000.0</td>\n", "      <td>2000.000000</td>\n", "      <td>C19606336</td>\n", "      <td>42.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>D250998702</td>\n", "      <td>4000.0</td>\n", "      <td>4000.000000</td>\n", "      <td>C14234022</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>D250998704</td>\n", "      <td>1000.0</td>\n", "      <td>1000.000000</td>\n", "      <td>C14234024</td>\n", "      <td>128.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>D250998701</td>\n", "      <td>350.0</td>\n", "      <td>320.000000</td>\n", "      <td>C14234018</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>D250998809</td>\n", "      <td>16000.0</td>\n", "      <td>320.000000</td>\n", "      <td>C14234018</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>D250998958</td>\n", "      <td>1600.0</td>\n", "      <td>320.000000</td>\n", "      <td>C17487018</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>D250998910</td>\n", "      <td>1200.0</td>\n", "      <td>240.000000</td>\n", "      <td>C15759045</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>D250998703</td>\n", "      <td>750.0</td>\n", "      <td>750.000000</td>\n", "      <td>C14234023</td>\n", "      <td>39.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>D250998810</td>\n", "      <td>8000.0</td>\n", "      <td>266.666667</td>\n", "      <td>C14234020</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>D250998801</td>\n", "      <td>7000.0</td>\n", "      <td>350.000000</td>\n", "      <td>C14234019</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>D250998705</td>\n", "      <td>950.0</td>\n", "      <td>950.000000</td>\n", "      <td>C14234025</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>D250998798</td>\n", "      <td>4500.0</td>\n", "      <td>850.000000</td>\n", "      <td>C14234014</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>D250998799</td>\n", "      <td>42500.0</td>\n", "      <td>850.000000</td>\n", "      <td>C14234014</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>D250998700</td>\n", "      <td>2750.0</td>\n", "      <td>520.000000</td>\n", "      <td>C14234017</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>D250998808</td>\n", "      <td>26000.0</td>\n", "      <td>520.000000</td>\n", "      <td>C14234017</td>\n", "      <td>-99999.0</td>\n", "      <td>https://image.document.guanmai.cn/product_img/...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        sku_id  sale_price  std_sale_price     spu_id   stocks  \\\n", "0   D264171923      7200.0     3600.000000  *********     14.0   \n", "1   D250998891       850.0      850.000000  ********* -99999.0   \n", "2   D250999155       750.0      750.000000  C15770502     20.0   \n", "3   D250998732       900.0      900.000000  C14234059 -99999.0   \n", "4   D250998937      5440.0      680.000000  C16685444 -99999.0   \n", "5   D250999054      2000.0     2000.000000  C19606336     42.0   \n", "6   D250998702      4000.0     4000.000000  C14234022 -99999.0   \n", "7   D250998704      1000.0     1000.000000  C14234024    128.0   \n", "8   D250998701       350.0      320.000000  C14234018 -99999.0   \n", "9   D250998809     16000.0      320.000000  C14234018 -99999.0   \n", "10  D250998958      1600.0      320.000000  C17487018 -99999.0   \n", "11  D250998910      1200.0      240.000000  C15759045 -99999.0   \n", "12  D250998703       750.0      750.000000  C14234023     39.0   \n", "13  D250998810      8000.0      266.666667  C14234020 -99999.0   \n", "14  D250998801      7000.0      350.000000  C14234019 -99999.0   \n", "15  D250998705       950.0      950.000000  C14234025 -99999.0   \n", "16  D250998798      4500.0      850.000000  C14234014 -99999.0   \n", "17  D250998799     42500.0      850.000000  C14234014 -99999.0   \n", "18  D250998700      2750.0      520.000000  C14234017 -99999.0   \n", "19  D250998808     26000.0      520.000000  C14234017 -99999.0   \n", "\n", "                                              img_url  \n", "0   https://image.document.guanmai.cn/product_imag...  \n", "1   https://image.document.guanmai.cn/product_img/...  \n", "2   https://image.document.guanmai.cn/product_img/...  \n", "3   https://image.document.guanmai.cn/product_img/...  \n", "4   https://image.document.guanmai.cn/product_img/...  \n", "5   https://image.document.guanmai.cn/product_img/...  \n", "6   https://image.document.guanmai.cn/product_img/...  \n", "7   https://image.document.guanmai.cn/product_img/...  \n", "8   https://image.document.guanmai.cn/product_img/...  \n", "9   https://image.document.guanmai.cn/product_img/...  \n", "10  https://image.document.guanmai.cn/product_img/...  \n", "11  https://image.document.guanmai.cn/product_img/...  \n", "12  https://image.document.guanmai.cn/product_img/...  \n", "13  https://image.document.guanmai.cn/product_img/...  \n", "14  https://image.document.guanmai.cn/product_img/...  \n", "15  https://image.document.guanmai.cn/product_img/...  \n", "16  https://image.document.guanmai.cn/product_img/...  \n", "17  https://image.document.guanmai.cn/product_img/...  \n", "18  https://image.document.guanmai.cn/product_img/...  \n", "19  https://image.document.guanmai.cn/product_img/...  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["all_sku_list=[]\n", "for spu in all_products:\n", "    spu['spu_id']=spu['id']\n", "    for sku in spu['skus']:\n", "        sku['sku_id']=sku['id']\n", "        sku.update(spu)\n", "        all_sku_list.append(sku)\n", "\n", "all_sku_list_df=pd.DataFrame(all_sku_list)\n", "all_sku_list_df.head(20)[['sku_id','sale_price','std_sale_price','spu_id','stocks','img_url']]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功写入odps:summerfarm_ds.spider_guanmai_product_result_df, partition_spec:ds=20240223,competitor_name=changyingxianbei, attemp:0\n", "成功写入odps:summerfarm_ds.spider_guanmai_product_raw_result_df, partition_spec:ds=20240223,competitor_name=changyingxianbei, attemp:0\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps \n", "# 写入odps\n", "all_sku_list_df['competitor']=brand_name\n", "all_products_df['competitor']=brand_name\n", "all_sku_list_df=all_sku_list_df.astype(str)\n", "all_products_df=all_products_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = 'summerfarm_ds.spider_guanmai_product_result_df'\n", "raw_table_name = 'summerfarm_ds.spider_guanmai_product_raw_result_df'\n", "\n", "write_pandas_df_into_odps(all_sku_list_df, table_name, partition_spec)\n", "write_pandas_df_into_odps(all_products_df, raw_table_name, partition_spec)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["299\n"]}], "source": ["print(len(all_sku_list))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
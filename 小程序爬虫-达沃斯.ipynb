{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "1708511689235, headers:{'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q', 'appcodenew': '7798c1f4306b4f89a9fc2a4c2cdc47ac', 'uid': '712451', 'time': '*************'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "THREAD_CNT = int(os.environ.get('THREAD_CNT', 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "hints={'odps.sql.hive.compatible':True,'odps.sql.type.system.odps2':True}\n", "def get_odps_sql_result_as_df(sql):\n", "    instance=odps.execute_sql(sql, hints=hints)\n", "    instance.wait_for_success()\n", "    pd_df=None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={'token':'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q',\n", "'appcodenew':'7798c1f4306b4f89a9fc2a4c2cdc47ac',\n", "'uid':'712451',\n", "'time':'*************',}\n", "brand_name='达沃斯'\n", "competitor_name_en='dawosi'\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 先登录\n", "\n", "获取token并保存"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 55, 'name': '水果'}\n", "{'id': 59, 'name': '半成品/肉类'}\n", "{'id': 62, 'name': '可可/巧克力'}\n", "{'id': 47, 'name': '油脂奶酪'}\n", "{'id': 106, 'name': '烘焙肉松'}\n", "{'id': 40, 'name': '奶油系列'}\n", "{'id': 52, 'name': '面粉/奶粉'}\n", "{'id': 80, 'name': '可可粉'}\n", "{'id': 53, 'name': '烘焙糖类'}\n", "{'id': 42, 'name': '水吧原料'}\n", "{'id': 68, 'name': '添加剂/酵母'}\n", "{'id': 57, 'name': '干货/罐头'}\n", "{'id': 64, 'name': '馅料果茸'}\n", "{'id': 61, 'name': '烘焙料酒'}\n", "{'id': 93, 'name': '月饼馅料'}\n", "{'id': 67, 'name': '其他产品'}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>goods_id</th>\n", "      <th>goods_name</th>\n", "      <th>image</th>\n", "      <th>line_price</th>\n", "      <th>marketing_goods_price</th>\n", "      <th>is_marketing</th>\n", "      <th>marketing_id</th>\n", "      <th>spec_type</th>\n", "      <th>vip_price</th>\n", "      <th>first_price</th>\n", "      <th>first_price_section</th>\n", "      <th>goods_price</th>\n", "      <th>upper_num</th>\n", "      <th>upper_price</th>\n", "      <th>textContent</th>\n", "      <th>a_type</th>\n", "      <th>goods_spec_id</th>\n", "      <th>isSq</th>\n", "      <th>shopping_cart_num</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>960</td>\n", "      <td>越南红心火龙果28斤左右</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/4d1...</td>\n", "      <td>0.00</td>\n", "      <td>159.90</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>越南红心火龙果</td>\n", "      <td>1</td>\n", "      <td>1559</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1000</td>\n", "      <td>秘鲁蓝莓 125g*2盒/一级果</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/e57...</td>\n", "      <td>0.00</td>\n", "      <td>25.50</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1599</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1001</td>\n", "      <td>千禧番茄  净重5斤左右 标准规格</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/9c9...</td>\n", "      <td>0.00</td>\n", "      <td>39.90</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1600</td>\n", "      <td>3</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1004</td>\n", "      <td>当季 新鲜印度凤梨 2个*1份/一级果</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/703...</td>\n", "      <td>0.00</td>\n", "      <td>55.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1603</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1006</td>\n", "      <td>秘鲁蓝莓 125g*12盒/一级果（整件）</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/e57...</td>\n", "      <td>0.00</td>\n", "      <td>149.90</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1605</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>887</td>\n", "      <td>碧琪镜面果膏（水晶啫喱）*5kg</td>\n", "      <td>https://mini.wssp0791.com/uploads/20230528/fb5...</td>\n", "      <td>0.00</td>\n", "      <td>180.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>1486</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>832</td>\n", "      <td>百利番茄沙司1kg*12包</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231216/5a5...</td>\n", "      <td>0.00</td>\n", "      <td>115.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1427</td>\n", "      <td>3</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>831</td>\n", "      <td>百利番茄沙司1kg</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231216/5a5...</td>\n", "      <td>0.00</td>\n", "      <td>15.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1426</td>\n", "      <td>3</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>830</td>\n", "      <td>百利烘焙沙拉酱900克*10包</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231216/e89...</td>\n", "      <td>0.00</td>\n", "      <td>140.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1425</td>\n", "      <td>3</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>829</td>\n", "      <td>百利烘焙沙拉酱900克</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231216/e89...</td>\n", "      <td>0.00</td>\n", "      <td>16.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1424</td>\n", "      <td>3</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>82 rows × 19 columns</p>\n", "</div>"], "text/plain": ["    goods_id             goods_name  \\\n", "0        960           越南红心火龙果28斤左右   \n", "1       1000       秘鲁蓝莓 125g*2盒/一级果   \n", "2       1001      千禧番茄  净重5斤左右 标准规格   \n", "3       1004    当季 新鲜印度凤梨 2个*1份/一级果   \n", "4       1006  秘鲁蓝莓 125g*12盒/一级果（整件）   \n", "..       ...                    ...   \n", "77       887       碧琪镜面果膏（水晶啫喱）*5kg   \n", "78       832          百利番茄沙司1kg*12包   \n", "79       831              百利番茄沙司1kg   \n", "80       830        百利烘焙沙拉酱900克*10包   \n", "81       829            百利烘焙沙拉酱900克   \n", "\n", "                                                image line_price  \\\n", "0   https://mini.wssp0791.com/uploads/20231020/4d1...       0.00   \n", "1   https://mini.wssp0791.com/uploads/20231020/e57...       0.00   \n", "2   https://mini.wssp0791.com/uploads/20231020/9c9...       0.00   \n", "3   https://mini.wssp0791.com/uploads/20231020/703...       0.00   \n", "4   https://mini.wssp0791.com/uploads/20231020/e57...       0.00   \n", "..                                                ...        ...   \n", "77  https://mini.wssp0791.com/uploads/20230528/fb5...       0.00   \n", "78  https://mini.wssp0791.com/uploads/20231216/5a5...       0.00   \n", "79  https://mini.wssp0791.com/uploads/20231216/5a5...       0.00   \n", "80  https://mini.wssp0791.com/uploads/20231216/e89...       0.00   \n", "81  https://mini.wssp0791.com/uploads/20231216/e89...       0.00   \n", "\n", "   marketing_goods_price  is_marketing  marketing_id spec_type  vip_price  \\\n", "0                 159.90             0             0        10          0   \n", "1                  25.50             0             0        10          0   \n", "2                  39.90             0             0        10          0   \n", "3                  55.00             0             0        10          0   \n", "4                 149.90             0             0        10          0   \n", "..                   ...           ...           ...       ...        ...   \n", "77                180.00             0             0        10          0   \n", "78                115.00             0             0        10          0   \n", "79                 15.00             0             0        10          0   \n", "80                140.00             0             0        10          0   \n", "81                 16.00             0             0        10          0   \n", "\n", "    first_price first_price_section  goods_price  upper_num  upper_price  \\\n", "0             0                0.00            0          2            0   \n", "1             0                0.00            0          2            0   \n", "2             0                0.00            0          2            0   \n", "3             0                0.00            0          2            0   \n", "4             0                0.00            0          2            0   \n", "..          ...                 ...          ...        ...          ...   \n", "77            0                0.00            0          2            0   \n", "78            0                0.00            0          2            0   \n", "79            0                0.00            0          2            0   \n", "80            0                0.00            0          2            0   \n", "81            0                0.00            0          2            0   \n", "\n", "   textContent  a_type  goods_spec_id  isSq    shopping_cart_num  \n", "0      越南红心火龙果       1           1559     2  {'num': 0, 'id': 0}  \n", "1                    1           1599     2  {'num': 0, 'id': 0}  \n", "2                    1           1600     3  {'num': 0, 'id': 0}  \n", "3                    1           1603     2  {'num': 0, 'id': 0}  \n", "4                    1           1605     2  {'num': 0, 'id': 0}  \n", "..         ...     ...            ...   ...                  ...  \n", "77                   2           1486     2  {'num': 0, 'id': 0}  \n", "78                   1           1427     3  {'num': 0, 'id': 0}  \n", "79                   1           1426     3  {'num': 0, 'id': 0}  \n", "80                   1           1425     3  {'num': 0, 'id': 0}  \n", "81                   1           1424     3  {'num': 0, 'id': 0}  \n", "\n", "[82 rows x 19 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["cate_list=requests.get(\"https://mini.wssp0791.com/api/item/getItemLists?token=f39f8c1a-7692-478e-9f9c-570e50f91fec&page=1&pagesize=6&category_id=47&second_cate_id=0&sales_order=&price_order=&comprehensive_order=&keyword=\").json()\n", "\n", "product_list_all=[]\n", "first_cate_list=cate_list['data']['cate_list']\n", "for cate in first_cate_list:\n", "    print(cate)\n", "    category_id=cate['id']\n", "    product_list=requests.get(f\"https://mini.wssp0791.com/api/item/getItemLists?token=f39f8c1a-7692-478e-9f9c-570e50f91fec&page=1&pagesize=6&category_id={category_id}&second_cate_id=0&sales_order=&price_order=&comprehensive_order=&keyword=\").json()['data']['list']\n", "    product_list_all.extend(product_list)\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "122.230.33.140:31696\n", "122.230.33.181:33666\n", "180.120.101.251:37394\n", "113.76.59.194:40026\n", "42.179.167.69:33663\n", "114.238.130.144:30786\n", "111.72.197.142:48422\n", "120.38.70.90:42846\n", "180.122.147.109:39268\n", "110.191.248.181:36969\n", "['122.230.33.140:31696', '122.230.33.181:33666', '180.120.101.251:37394', '113.76.59.194:40026', '42.179.167.69:33663', '114.238.130.144:30786', '111.72.197.142:48422', '120.38.70.90:42846', '180.122.147.109:39268', '110.191.248.181:36969']\n", "成功写入odps:summerfarm_ds.spider_dawosi_product_result_df, partition_spec:ds=20240221,competitor_name=dawosi, attemp:0\n", "sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_dawosi_product_result_df\n", "                             where ds>='20240122' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240221</td>\n", "      <td>da<PERSON><PERSON></td>\n", "      <td>82</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ds competitor_name  recods\n", "0  20240221          dawosi      82"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "product_list_all_df['competitor']=brand_name\n", "all_products_df=product_list_all_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 16:26:33, date_of_now:2024-03-06, brand_name:达沃斯, headers:{'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJMb2dpblRpbWUiOjE3MDI1MjA4ODk0OTcsIlRZUEUiOiJYQ1hfQVBQIiwib3BlbmlkIjo3MTI0NTF9.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q', 'appcodenew': '7798c1f4306b4f89a9fc2a4c2cdc47ac', 'uid': '712451', 'time': '1702521175012'}\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/达沃斯/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "\n", "headers={'token':'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJMb2dpblRpbWUiOjE3MDI1MjA4ODk0OTcsIlRZUEUiOiJYQ1hfQVBQIiwib3BlbmlkIjo3MTI0NTF9.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q',\n", "'appcodenew':'7798c1f4306b4f89a9fc2a4c2cdc47ac',\n", "'uid':'712451',\n", "'time':'1702521175012',}\n", "brand_name='达沃斯'\n", "competitor_name_en='dawosi'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, headers:{headers}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["************:44116\n", "**************:38256\n", "*************:31996\n", "**************:32433\n", "**************:44588\n", "**************:42632\n", "**************:39361\n", "**************:48202\n", "************:43888\n", "**************:38269\n", "['************:44116', '**************:38256', '*************:31996', '**************:32433', '**************:44588', '**************:42632', '**************:39361', '**************:48202', '************:43888', '**************:38269']\n"]}], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url):\n", "    max_retries=3;\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, data=None, headers=headers, proxies=proxies, cookies=None, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None\n", "def post_remote_data_with_proxy(url, data, headers):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, data=data, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["根据一级、二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ************************************************\n", "{'id': 55, 'name': '水果系列'}\n", "Using proxy: **********************************************\n", "{'id': 109, 'name': '新品|特价'}\n", "Using proxy: ************************************************\n", "{'id': 59, 'name': '半成品/肉类'}\n", "Using proxy: ***********************************************\n", "{'id': 62, 'name': '可可/巧克力'}\n", "Using proxy: ************************************************\n", "{'id': 47, 'name': '油脂奶酪'}\n", "Using proxy: ************************************************\n", "{'id': 42, 'name': '水吧原料'}\n", "Using proxy: ************************************************\n", "{'id': 40, 'name': '奶油系列'}\n", "Using proxy: ***********************************************\n", "{'id': 52, 'name': '面粉/奶粉'}\n", "Using proxy: ***********************************************\n", "{'id': 68, 'name': '添加剂/酵母'}\n", "Using proxy: **********************************************\n", "{'id': 57, 'name': '干货/罐头'}\n", "Using proxy: ************************************************\n", "{'id': 53, 'name': '烘焙糖类'}\n", "Using proxy: ************************************************\n", "{'id': 64, 'name': '馅料果茸'}\n", "Using proxy: ************************************************\n", "{'id': 67, 'name': '烘焙辅料'}\n", "Using proxy: **********************************************\n", "{'id': 61, 'name': '烘焙料酒'}\n", "Using proxy: ************************************************\n", "{'id': 93, 'name': '月饼馅料'}\n", "Using proxy: ***********************************************\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>goods_id</th>\n", "      <th>goods_name</th>\n", "      <th>image</th>\n", "      <th>line_price</th>\n", "      <th>marketing_goods_price</th>\n", "      <th>is_marketing</th>\n", "      <th>marketing_id</th>\n", "      <th>spec_type</th>\n", "      <th>vip_price</th>\n", "      <th>first_price</th>\n", "      <th>first_price_section</th>\n", "      <th>goods_price</th>\n", "      <th>upper_num</th>\n", "      <th>upper_price</th>\n", "      <th>textContent</th>\n", "      <th>a_type</th>\n", "      <th>goods_spec_id</th>\n", "      <th>isSq</th>\n", "      <th>shopping_cart_num</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>960</td>\n", "      <td>越南红心火龙果28斤左右</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/4d1...</td>\n", "      <td>0.00</td>\n", "      <td>139.90</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>越南红心火龙果</td>\n", "      <td>1</td>\n", "      <td>1559</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>724</td>\n", "      <td>双流鲜草莓（24粒/盒）*5盒</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231120/4cb...</td>\n", "      <td>0.00</td>\n", "      <td>52.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1319</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1000</td>\n", "      <td>秘鲁蓝莓 125g*2盒/一级果</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/e57...</td>\n", "      <td>0.00</td>\n", "      <td>22.90</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1599</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1001</td>\n", "      <td>千禧番茄  净重5斤左右 标准规格</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/9c9...</td>\n", "      <td>0.00</td>\n", "      <td>39.90</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1600</td>\n", "      <td>3</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1003</td>\n", "      <td>泰国龙眼5斤*1份/一级果</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/e79...</td>\n", "      <td>0.00</td>\n", "      <td>42.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1602</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>308</td>\n", "      <td>百加得白朗姆酒（750ML）</td>\n", "      <td>https://mini.wssp0791.com/uploads/20200608/9a5...</td>\n", "      <td>0.00</td>\n", "      <td>78.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>865</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>302</td>\n", "      <td>百利甜酒700ML</td>\n", "      <td>https://mini.wssp0791.com/uploads/20200608/271...</td>\n", "      <td>0.00</td>\n", "      <td>102.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>859</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>301</td>\n", "      <td>甘露咖啡力娇酒700ml</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231215/37e...</td>\n", "      <td>0.00</td>\n", "      <td>96.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>858</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>947</td>\n", "      <td>芝士流心馅5kg（美加福）</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231219/d9d...</td>\n", "      <td>0.00</td>\n", "      <td>180.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1546</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>946</td>\n", "      <td>奶黄流心馅5kg（美加福）</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231219/a0e...</td>\n", "      <td>0.00</td>\n", "      <td>220.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1545</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>84 rows × 19 columns</p>\n", "</div>"], "text/plain": ["    goods_id         goods_name  \\\n", "0        960       越南红心火龙果28斤左右   \n", "1        724    双流鲜草莓（24粒/盒）*5盒   \n", "2       1000   秘鲁蓝莓 125g*2盒/一级果   \n", "3       1001  千禧番茄  净重5斤左右 标准规格   \n", "4       1003      泰国龙眼5斤*1份/一级果   \n", "..       ...                ...   \n", "79       308     百加得白朗姆酒（750ML）   \n", "80       302          百利甜酒700ML   \n", "81       301       甘露咖啡力娇酒700ml   \n", "82       947      芝士流心馅5kg（美加福）   \n", "83       946      奶黄流心馅5kg（美加福）   \n", "\n", "                                                image line_price  \\\n", "0   https://mini.wssp0791.com/uploads/20231020/4d1...       0.00   \n", "1   https://mini.wssp0791.com/uploads/20231120/4cb...       0.00   \n", "2   https://mini.wssp0791.com/uploads/20231020/e57...       0.00   \n", "3   https://mini.wssp0791.com/uploads/20231020/9c9...       0.00   \n", "4   https://mini.wssp0791.com/uploads/20231020/e79...       0.00   \n", "..                                                ...        ...   \n", "79  https://mini.wssp0791.com/uploads/20200608/9a5...       0.00   \n", "80  https://mini.wssp0791.com/uploads/20200608/271...       0.00   \n", "81  https://mini.wssp0791.com/uploads/20231215/37e...       0.00   \n", "82  https://mini.wssp0791.com/uploads/20231219/d9d...       0.00   \n", "83  https://mini.wssp0791.com/uploads/20231219/a0e...       0.00   \n", "\n", "   marketing_goods_price  is_marketing  marketing_id spec_type  vip_price  \\\n", "0                 139.90             0             0        10          0   \n", "1                  52.00             0             0        10          0   \n", "2                  22.90             0             0        10          0   \n", "3                  39.90             0             0        10          0   \n", "4                  42.00             0             0        10          0   \n", "..                   ...           ...           ...       ...        ...   \n", "79                 78.00             0             0        10          0   \n", "80                102.00             0             0        10          0   \n", "81                 96.00             0             0        10          0   \n", "82                180.00             0             0        10          0   \n", "83                220.00             0             0        10          0   \n", "\n", "    first_price first_price_section  goods_price  upper_num  upper_price  \\\n", "0             0                0.00            0          2            0   \n", "1             0                0.00            0          2            0   \n", "2             0                0.00            0          2            0   \n", "3             0                0.00            0          2            0   \n", "4             0                0.00            0          2            0   \n", "..          ...                 ...          ...        ...          ...   \n", "79            0                0.00            0          2            0   \n", "80            0                0.00            0          2            0   \n", "81            0                0.00            0          2            0   \n", "82            0                0.00            0          2            0   \n", "83            0                0.00            0          2            0   \n", "\n", "   textContent  a_type  goods_spec_id  isSq    shopping_cart_num  \n", "0      越南红心火龙果       1           1559     2  {'num': 0, 'id': 0}  \n", "1                    1           1319     2  {'num': 0, 'id': 0}  \n", "2                    1           1599     2  {'num': 0, 'id': 0}  \n", "3                    1           1600     3  {'num': 0, 'id': 0}  \n", "4                    1           1602     2  {'num': 0, 'id': 0}  \n", "..         ...     ...            ...   ...                  ...  \n", "79                   1            865     2  {'num': 0, 'id': 0}  \n", "80                   2            859     2  {'num': 0, 'id': 0}  \n", "81                   1            858     2  {'num': 0, 'id': 0}  \n", "82                   1           1546     2  {'num': 0, 'id': 0}  \n", "83                   1           1545     2  {'num': 0, 'id': 0}  \n", "\n", "[84 rows x 19 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["cate_list=json.loads(get_remote_data_with_proxy(\"https://mini.wssp0791.com/api/item/getItemLists?token=f39f8c1a-7692-478e-9f9c-570e50f91fec&page=1&pagesize=6&category_id=47&second_cate_id=0&sales_order=&price_order=&comprehensive_order=&keyword=\").text)\n", "\n", "product_list_all=[]\n", "first_cate_list=cate_list['data']['cate_list']\n", "for cate in first_cate_list:\n", "    print(cate)\n", "    category_id=cate['id']\n", "    product_list=json.loads(get_remote_data_with_proxy(f\"https://mini.wssp0791.com/api/item/getItemLists?token=f39f8c1a-7692-478e-9f9c-570e50f91fec&page=1&pagesize=6&category_id={category_id}&second_cate_id=0&sales_order=&price_order=&comprehensive_order=&keyword=\").text)['data']['list']\n", "    product_list_all.extend(product_list)\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>goods_id</th>\n", "      <th>goods_name</th>\n", "      <th>image</th>\n", "      <th>line_price</th>\n", "      <th>marketing_goods_price</th>\n", "      <th>is_marketing</th>\n", "      <th>marketing_id</th>\n", "      <th>spec_type</th>\n", "      <th>vip_price</th>\n", "      <th>first_price</th>\n", "      <th>first_price_section</th>\n", "      <th>goods_price</th>\n", "      <th>upper_num</th>\n", "      <th>upper_price</th>\n", "      <th>textContent</th>\n", "      <th>a_type</th>\n", "      <th>goods_spec_id</th>\n", "      <th>isSq</th>\n", "      <th>shopping_cart_num</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>960</td>\n", "      <td>越南红心火龙果28斤左右</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/4d1...</td>\n", "      <td>0.00</td>\n", "      <td>139.90</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>越南红心火龙果</td>\n", "      <td>1</td>\n", "      <td>1559</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>724</td>\n", "      <td>双流鲜草莓（24粒/盒）*5盒</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231120/4cb...</td>\n", "      <td>0.00</td>\n", "      <td>52.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1319</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1000</td>\n", "      <td>秘鲁蓝莓 125g*2盒/一级果</td>\n", "      <td>https://mini.wssp0791.com/uploads/20231020/e57...</td>\n", "      <td>0.00</td>\n", "      <td>22.90</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1599</td>\n", "      <td>2</td>\n", "      <td>{'num': 0, 'id': 0}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   goods_id        goods_name  \\\n", "0       960      越南红心火龙果28斤左右   \n", "1       724   双流鲜草莓（24粒/盒）*5盒   \n", "2      1000  秘鲁蓝莓 125g*2盒/一级果   \n", "\n", "                                               image line_price  \\\n", "0  https://mini.wssp0791.com/uploads/20231020/4d1...       0.00   \n", "1  https://mini.wssp0791.com/uploads/20231120/4cb...       0.00   \n", "2  https://mini.wssp0791.com/uploads/20231020/e57...       0.00   \n", "\n", "  marketing_goods_price  is_marketing  marketing_id spec_type  vip_price  \\\n", "0                139.90             0             0        10          0   \n", "1                 52.00             0             0        10          0   \n", "2                 22.90             0             0        10          0   \n", "\n", "   first_price first_price_section  goods_price  upper_num  upper_price  \\\n", "0            0                0.00            0          2            0   \n", "1            0                0.00            0          2            0   \n", "2            0                0.00            0          2            0   \n", "\n", "  textContent  a_type  goods_spec_id  isSq    shopping_cart_num  \n", "0     越南红心火龙果       1           1559     2  {'num': 0, 'id': 0}  \n", "1                   1           1319     2  {'num': 0, 'id': 0}  \n", "2                   1           1599     2  {'num': 0, 'id': 0}  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(product_list_all_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:越南红心火龙果28斤左右, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:632.0958137512207ms\n", "inputText:双流鲜草莓（24粒/盒）*5盒, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:627.5200843811035ms\n", "inputText:秘鲁蓝莓 125g*2盒/一级果, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:618.5345649719238ms\n", "inputText:千禧番茄  净重5斤左右 标准规格, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:516.4995193481445ms\n", "inputText:泰国龙眼5斤*1份/一级果, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:1156.1508178710938ms\n", "inputText:当季 新鲜印度凤梨 2个*1份/一级果, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:526.7255306243896ms\n", "inputText:(幕美花田)国产纯动物淡奶油1L*12/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:508.8996887207031ms\n", "inputText:麦维高无盐黄油25kg, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:845.7181453704834ms\n", "inputText:麦维高大黄油  25kg, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:637.183666229248ms\n", "inputText:BS88面包乳化剂（英联马利）5kg*2桶（20240218到期）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:586.3049030303955ms\n", "inputText:M1000柔软面包改良剂（英联马利）500克*20包（20240311到期）, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:574.4795799255371ms\n", "inputText:梦巧卷慕斯*1板*8盒, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:621.6068267822266ms\n", "inputText:维益*约翰丹尼  4寸方形提拉米苏慕斯1*36盒*170克, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:1037.7542972564697ms\n", "inputText:维益* 约翰丹尼 4寸方形麦香牛油果慕斯1*36盒*170克, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:1192.594051361084ms\n", "inputText:维益*约翰丹尼 4寸方形红丝绒1*36盒*170克, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:620.7234859466553ms\n", "inputText:元宝-瑞士卷（黑森林）1*5条*1300克, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:658.7924957275391ms\n", "inputText:元宝-瑞士卷（抹茶）1*5条*1300克, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:646.9218730926514ms\n", "inputText:元宝-瑞士卷（酸奶）1*5条*1300克, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:686.0496997833252ms\n", "inputText:2.5kg嘉利宝黑巧克力粒70.5%, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:748.457670211792ms\n", "inputText:2.5kg嘉利宝白巧克力粒28%, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:635.453462600708ms\n", "inputText:西克莱特--咖啡巧克力酱-3kg, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:625.9751319885254ms\n", "inputText:西克莱特--树莓伯爵茶巧脆馅*3kg, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:678.5836219787598ms\n", "inputText:西克莱特--太妃榛巧流芯酱*3kg, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:641.456127166748ms\n", "inputText:西克莱特--黑糖布朗尼巧脆馅*3kg, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:614.476203918457ms\n", "inputText: 琪雷萨马斯卡彭500g/盒 , usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:621.6273307800293ms\n", "inputText:安佳黄油（咸味）（288*7g/件）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:587.5124931335449ms\n", "inputText:南侨铁桶酥油16kg , usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:614.2213344573975ms\n", "inputText:安佳碎条状马苏里拉干酪（蓝标）12kg, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:556.6000938415527ms\n", "inputText:海皇牌餐饮专用烹调油9%（棕榈油）2.2L, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:582.4315547943115ms\n", "inputText:安佳碎条状马苏里拉干酪（12kg/件）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:574.9053955078125ms\n", "inputText:莫奈花园纯牛奶1L*12/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:648.827314376831ms\n", "inputText:纯牛奶（慕美花田）1L*12瓶, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:545.7472801208496ms\n", "inputText:英龙植脂末1kg, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:582.1554660797119ms\n", "inputText:伊利全脂牛奶1L*12瓶, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:619.6365356445312ms\n", "inputText:雀巢纯牛奶（1L*12）/箱（经典餐饮装）, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:577.0297050476074ms\n", "inputText:雀巢炼乳350g, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:633.1343650817871ms\n", "inputText:安佳淡奶油1L*12/箱 , usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:635.8678340911865ms\n", "inputText:伊利环球甄选稀奶油1L*12/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:626.326322555542ms\n", "inputText:维益和牧牛奶奶油1L*12瓶/件, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:651.3481140136719ms\n", "inputText:维益爱真喷射稀奶油510克, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:630.3973197937012ms\n", "inputText:伊利东方灵感淡奶油1L*6/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:736.0732555389404ms\n", "inputText:铁塔淡奶油1L*12/箱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:722.8701114654541ms\n", "inputText:蓝钻杏仁粉  1kg, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:823.2345581054688ms\n", "inputText:科麦麻薯粉5kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:719.064474105835ms\n", "inputText:早苗塔塔粉1.35kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:788.2344722747803ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['goods_name'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
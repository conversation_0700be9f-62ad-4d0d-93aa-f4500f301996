{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 18:05:48, date_of_now:2024-03-06, brand_name:添品, headers:{'token': '', 'appcodenew': '7798c1f4306b4f89a9fc2a4c2cdc47ac', 'uid': '712451', 'time': '1702521175012'}\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/添品/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "\n", "headers={'token':'',\n", "'appcodenew':'7798c1f4306b4f89a9fc2a4c2cdc47ac',\n", "'uid':'712451',\n", "'time':'1702521175012',}\n", "brand_name='添品'\n", "competitor_name_en='tianpin'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, headers:{headers}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["***************:35662\n", "*************:41612\n", "*************:31188\n", "**************:48249\n", "**************:33266\n", "************:40606\n", "***************:40875\n", "*************:39208\n", "*************:37081\n", "***************:46146\n", "['***************:35662', '*************:41612', '*************:31188', '**************:48249', '**************:33266', '************:40606', '***************:40875', '*************:39208', '*************:37081', '***************:46146']\n"]}], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["爬取商品信息"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["http://m.txpchain.com/product?offset=-1\n", "{'id': '628', 'name': '明治牛乳', 'title': '明治牛乳', 'price_unit': '箱', 'product_price': '168.00', 'market_price': '174.00', 'spec': '950ml*12瓶', 'img_url': 'http://static.txpchain.com/file/2023/06/08/hvyx9qnh2xkznk55.png', 'bname': '明治', 'sort_numer': '5'}\n", "http://m.txpchain.com/product?offset=1\n", "{'id': '591', 'name': '味斯美海苔脆肉松', 'title': '味斯美海苔脆肉松', 'price_unit': '袋', 'product_price': '139.00', 'market_price': '0.00', 'spec': '2kg', 'img_url': 'http://static.txpchain.com/file/2018/06/28/nfc9oznq4qgp4vjy.jpg', 'bname': '味斯美', 'sort_numer': '428'}\n", "http://m.txpchain.com/product?offset=2\n", "{'id': '668', 'name': '丝诺含乳脂植脂奶油', 'title': '丝诺含乳脂植脂奶油980g*12', 'price_unit': '箱', 'product_price': '239.00', 'market_price': '0.00', 'spec': '980g*12瓶', 'img_url': 'http://static.txpchain.com/file/2019/03/15/t9awwk9s4nhnm9at.jpg', 'bname': '丝诺', 'sort_numer': '478'}\n", "http://m.txpchain.com/product?offset=3\n", "{'id': '548', 'name': '熊猫炼乳350g*6罐', 'title': '熊猫炼乳350g', 'price_unit': '组', 'product_price': '64.00', 'market_price': '0.00', 'spec': '350g*6罐', 'img_url': 'http://static.txpchain.com/file/2018/08/08/47m1g3kcfi9jd2uy.png', 'bname': '熊猫', 'sort_numer': '512'}\n", "http://m.txpchain.com/product?offset=4\n", "{'id': '525', 'name': '南侨液态酥油', 'title': '南侨液态酥油', 'price_unit': '桶', 'product_price': '379.00', 'market_price': '0.00', 'spec': '20kg', 'img_url': 'http://static.txpchain.com/file/2018/06/28/3422aluan45lp17d.jpg', 'bname': '南侨', 'sort_numer': '533'}\n", "http://m.txpchain.com/product?offset=5\n", "{'id': '712', 'name': '普利欧冷冻蛋糕－法式乳酪', 'title': '普利欧冷冻蛋糕－法式乳酪', 'price_unit': '盒', 'product_price': '75.00', 'market_price': '0.00', 'spec': '80g*10片/8英寸', 'img_url': 'http://static.txpchain.com/file/2019/05/22/ivfq4mx36jwfk27x.png', 'bname': '普利欧', 'sort_numer': '555'}\n", "http://m.txpchain.com/product?offset=6\n", "{'id': '714', 'name': '普利欧冷冻蛋糕－榴莲千层', 'title': '普利欧冷冻蛋糕－榴莲千层蛋糕', 'price_unit': '盒', 'product_price': '120.00', 'market_price': '0.00', 'spec': '120g*10片/8英寸', 'img_url': 'http://static.txpchain.com/file/2019/05/22/gj9dgnfjmym21i7b.png', 'bname': '普利欧', 'sort_numer': '579'}\n", "http://m.txpchain.com/product?offset=7\n", "{'id': '748', 'name': '明治meiji保加利亚式凝固型酸奶低脂清甜原味 150g*24盒', 'title': '明治meiji 保加利亚式凝固型酸奶 低脂清甜原味', 'price_unit': '箱', 'product_price': '204.00', 'market_price': '0.00', 'spec': '150g*24盒', 'img_url': 'http://static.txpchain.com/file/2019/06/17/gbtk2ucio1vl1tv8.png', 'bname': '明治', 'sort_numer': '601'}\n", "http://m.txpchain.com/product?offset=8\n", "{'id': '755', 'name': '普利欧冷冻蛋糕－抹茶芝士魔方', 'title': '普利欧冷冻蛋糕－抹茶芝士魔方60粒', 'price_unit': '盒', 'product_price': '78.00', 'market_price': '0.00', 'spec': '60粒', 'img_url': 'http://static.txpchain.com/file/2019/08/13/2zs3is1b8l7odjie.png', 'bname': '普利欧', 'sort_numer': '619'}\n", "http://m.txpchain.com/product?offset=9\n", "{'id': '642', 'name': '早苗泡打粉', 'title': '早苗泡打粉2.7kg', 'price_unit': '瓶', 'product_price': '77.00', 'market_price': '0.00', 'spec': '2.7kg', 'img_url': 'http://static.txpchain.com/file/2018/08/14/gbdt8mhmjlljs4y9.png', 'bname': '早苗', 'sort_numer': '644'}\n", "http://m.txpchain.com/product?offset=10\n", "{'id': '843', 'name': '奥昆绿豆饼', 'title': '奥昆绿豆饼35g*280个/箱', 'price_unit': '箱', 'product_price': '193.00', 'market_price': '0.00', 'spec': '35g*280个', 'img_url': 'http://static.txpchain.com/file/2020/05/29/xq4fb6sobdlgc5uo.jpg', 'bname': '奥昆', 'sort_numer': '679'}\n", "http://m.txpchain.com/product?offset=11\n", "{'id': '891', 'name': '多色马卡龙（清甜）5粒*4盒', 'title': '多色马卡龙5粒*4盒/组', 'price_unit': '组', 'product_price': '72.00', 'market_price': '0.00', 'spec': '5粒/盒*4盒', 'img_url': 'http://static.txpchain.com/file/2020/10/01/fdd7rog5m8qoda49.png', 'bname': None, 'sort_numer': '727'}\n", "http://m.txpchain.com/product?offset=12\n", "{'id': '924', 'name': '奥昆好禧坊巧克力慕斯蛋糕', 'title': '奥昆好禧坊巧克力慕斯蛋糕', 'price_unit': '盒', 'product_price': '57.00', 'market_price': '65.00', 'spec': '10片/8英寸', 'img_url': 'http://static.txpchain.com/file/2021/01/08/eng8u7x7yypwi1cb.png', 'bname': None, 'sort_numer': '760'}\n", "http://m.txpchain.com/product?offset=13\n", "{'id': '948', 'name': '一级水仙芒（ 12斤）', 'title': '水仙芒', 'price_unit': '箱', 'product_price': '89.00', 'market_price': '0.00', 'spec': '12斤±3两', 'img_url': 'http://static.txpchain.com/file/2021/03/30/lcu3k526rmxncnqy.jpg', 'bname': None, 'sort_numer': '784'}\n", "http://m.txpchain.com/product?offset=14\n", "{'id': '975', 'name': '奥昆甜甜圈（草莓果馅50g）', 'title': '奥昆甜甜圈', 'price_unit': '箱', 'product_price': '138.00', 'market_price': '0.00', 'spec': '50g*75个', 'img_url': 'http://static.txpchain.com/file/2021/05/25/5xfg4ckogx94xz5q.png', 'bname': None, 'sort_numer': '811'}\n", "http://m.txpchain.com/product?offset=15\n", "{'id': '1013', 'name': '普利欧（迷你马芬系列）-红丝绒枣泥蛋糕', 'title': '普利欧迷你马芬系列-红丝绒枣泥蛋糕', 'price_unit': '盒', 'product_price': '49.00', 'market_price': '0.00', 'spec': '500g，70粒/盒', 'img_url': 'http://static.txpchain.com/file/2021/09/27/612otq2d85dwstm5.png', 'bname': None, 'sort_numer': '849'}\n", "http://m.txpchain.com/product?offset=16\n", "{'id': '1039', 'name': '海南晓蜜瓜（4个）', 'title': '海南晓蜜瓜', 'price_unit': '箱', 'product_price': '89.00', 'market_price': '0.00', 'spec': '4个装，毛重14-16斤', 'img_url': 'http://static.txpchain.com/file/2021/12/26/3k7nw7qm9mrk952l.jpg', 'bname': None, 'sort_numer': '875'}\n", "http://m.txpchain.com/product?offset=17\n", "{'id': '1088', 'name': '三麟100%天然椰子水（330ml）', 'title': '三麟100%天然椰子水', 'price_unit': '箱', 'product_price': '59.00', 'market_price': '0.00', 'spec': '330ml*12瓶', 'img_url': 'http://static.txpchain.com/file/2022/07/21/c2xc186dj7e3f4vn.jpg', 'bname': None, 'sort_numer': '924'}\n", "http://m.txpchain.com/product?offset=18\n", "{'id': '1107', 'name': '莫林莫西多薄荷风味糖浆700Ml', 'title': '莫林莫西多薄荷风味糖浆700Ml', 'price_unit': '瓶', 'product_price': '72.00', 'market_price': '0.00', 'spec': '700Ml', 'img_url': 'http://static.txpchain.com/file/2022/08/27/b3570maxjbgv15kl.jpg', 'bname': None, 'sort_numer': '943'}\n", "http://m.txpchain.com/product?offset=19\n", "{'id': '1117', 'name': '莫林水蜜桃风味糖浆700ml', 'title': '莫林水蜜桃风味糖浆', 'price_unit': '瓶', 'product_price': '72.00', 'market_price': '0.00', 'spec': '700ml', 'img_url': 'http://static.txpchain.com/file/2023/04/03/jha46pq3vq1zamp7.jpg', 'bname': None, 'sort_numer': '953'}\n", "http://m.txpchain.com/product?offset=20\n", "{'id': '1137', 'name': '莫林班兰叶风味糖浆700ml', 'title': '莫林班兰叶糖浆', 'price_unit': '瓶', 'product_price': '72.00', 'market_price': '83.00', 'spec': '700ml', 'img_url': 'http://static.txpchain.com/file/2022/10/20/a8hfmd5qbpksx2ta.jpg', 'bname': None, 'sort_numer': '973'}\n", "http://m.txpchain.com/product?offset=21\n", "{'id': '1153', 'name': '莫林冰糖葫芦风味糖浆700ml', 'title': '莫林冰糖葫芦风味糖浆700ml', 'price_unit': '瓶', 'product_price': '72.00', 'market_price': '0.00', 'spec': '700ml', 'img_url': 'http://static.txpchain.com/file/2022/11/17/r0szpk9cbicjbuy2.jpg', 'bname': None, 'sort_numer': '989'}\n", "http://m.txpchain.com/product?offset=22\n", "{'id': '1179', 'name': '烘烤扁桃仁片（巴旦木片）500g', 'title': '烘烤扁桃仁片500g', 'price_unit': '袋', 'product_price': '34.00', 'market_price': '0.00', 'spec': '1kg', 'img_url': 'http://static.txpchain.com/file/2023/02/13/c9vopmy3ix8e9ex4.jpg', 'bname': None, 'sort_numer': '1015', 'discountType': '满减'}\n", "http://m.txpchain.com/product?offset=23\n", "{'id': '1208', 'name': '立高依乐斯乳脂奶油', 'title': '立高依乐斯乳脂奶油', 'price_unit': '箱', 'product_price': '230.00', 'market_price': '0.00', 'spec': '907g*12瓶', 'img_url': 'http://static.txpchain.com/file/2023/04/10/ic581sgbfvdkw24a.jpg', 'bname': None, 'sort_numer': '1044'}\n", "http://m.txpchain.com/product?offset=24\n", "{'id': '1247', 'name': '普利欧-萌兔抹茶慕斯蛋糕', 'title': '普利欧-萌兔抹茶慕斯蛋糕', 'price_unit': '盒', 'product_price': '85.00', 'market_price': '0.00', 'spec': '1000克/盒，10片', 'img_url': 'http://static.txpchain.com/file/2023/06/09/a6yqdgo8e3s85w78.png', 'bname': None, 'sort_numer': '1083'}\n", "http://m.txpchain.com/product?offset=25\n", "{'id': '1267', 'name': 'NFC100%葡萄汁', 'title': 'NFC100%葡萄汁', 'price_unit': '箱', 'product_price': '79.00', 'market_price': '0.00', 'spec': '325ml*15瓶', 'img_url': 'http://static.txpchain.com/file/2023/06/20/ndmqy8urz71qk10v.jpg', 'bname': None, 'sort_numer': '1103'}\n", "http://m.txpchain.com/product?offset=26\n", "{'id': '1281', 'name': '莫林巨峰葡萄香甜酒风味糖浆700ml ', 'title': '莫林巨峰葡萄香甜酒风味糖浆700ml ', 'price_unit': '瓶', 'product_price': '72.00', 'market_price': '0.00', 'spec': '700ml', 'img_url': 'http://static.txpchain.com/file/2023/07/02/0lmzz4etbeiw8hf8.jpg', 'bname': None, 'sort_numer': '1117'}\n", "http://m.txpchain.com/product?offset=27\n", "{'id': '1300', 'name': '德馨-大红袍茶汤1L', 'title': '德馨-大红袍茶汤1L', 'price_unit': '瓶', 'product_price': '14.00', 'market_price': '0.00', 'spec': '1L', 'img_url': 'http://static.txpchain.com/file/2023/07/06/p1lz0pduahm3vnyg.jpg', 'bname': None, 'sort_numer': '1136'}\n", "http://m.txpchain.com/product?offset=28\n", "{'id': '1316', 'name': '明治含乳饮料（香蕉味）', 'title': '明治含乳饮料（香蕉味）', 'price_unit': '箱', 'product_price': '122.00', 'market_price': '0.00', 'spec': '220ml*15瓶', 'img_url': 'http://static.txpchain.com/file/2023/08/10/pmbifr1b5hbahg1j.jpg', 'bname': None, 'sort_numer': '1152'}\n", "http://m.txpchain.com/product?offset=29\n", "{'id': '1332', 'name': '普利欧-紫藤香芋芝士蛋糕', 'title': '普利欧-紫藤香芋芝士蛋糕', 'price_unit': '盒', 'product_price': '38.00', 'market_price': '0.00', 'spec': '27*22*4.5/粒*10粒', 'img_url': 'http://static.txpchain.com/file/2023/09/25/e645xt2v9er349ge.png', 'bname': None, 'sort_numer': '1168'}\n", "http://m.txpchain.com/product?offset=30\n", "{'id': '1343', 'name': '丘比沙拉汁（日式口味）', 'title': '丘比沙拉汁（日式口味）', 'price_unit': '瓶', 'product_price': '38.00', 'market_price': '0.00', 'spec': '1.5L', 'img_url': 'http://static.txpchain.com/file/2023/10/13/rdx1li1ryd3c9dap.jpg', 'bname': None, 'sort_numer': '1179'}\n", "http://m.txpchain.com/product?offset=31\n", "{'id': '1353', 'name': '宇治抹茶粉50g*4包（白莲）', 'title': '宇治抹茶粉', 'price_unit': '组', 'product_price': '96.00', 'market_price': '0.00', 'spec': '50g*4包', 'img_url': 'http://static.txpchain.com/file/2023/10/26/9p0fswj427111f53.jpg', 'bname': None, 'sort_numer': '1189'}\n", "http://m.txpchain.com/product?offset=32\n", "{'id': '1364', 'name': '谷优玛丽亚饼干粉500g', 'title': '谷优玛丽亚饼干粉', 'price_unit': '包', 'product_price': '21.00', 'market_price': '0.00', 'spec': '500g', 'img_url': 'http://static.txpchain.com/file/2023/11/03/5zuw1n0g2t6j3xfp.png', 'bname': None, 'sort_numer': '1200'}\n", "http://m.txpchain.com/product?offset=33\n", "{'id': '1374', 'name': '南美虾堡排', 'title': '南美虾堡排', 'price_unit': '盒', 'product_price': '49.00', 'market_price': '0.00', 'spec': '70g/片*15片', 'img_url': 'http://static.txpchain.com/file/2023/11/21/5bbaqb84g8jl4nsw.png', 'bname': None, 'sort_numer': '1210'}\n", "http://m.txpchain.com/product?offset=34\n", "{'id': '1386', 'name': '普利欧冷冻蛋糕－小花抹茶芝士迷你挞', 'title': '普利欧冷冻蛋糕－小花抹茶芝士迷你挞', 'price_unit': '盒', 'product_price': '136.00', 'market_price': '0.00', 'spec': '48粒/670克', 'img_url': 'http://static.txpchain.com/file/2023/12/30/0o6qrpumxsxmbo4i.png', 'bname': None, 'sort_numer': '1222'}\n", "http://m.txpchain.com/product?offset=35\n", "{'id': '1396', 'name': '立高880淡奶油', 'title': '立高880淡奶油', 'price_unit': '箱', 'product_price': '280.00', 'market_price': '0.00', 'spec': '1L*12瓶', 'img_url': 'http://static.txpchain.com/file/2024/01/12/fm0909mk1b2bwa77.png', 'bname': None, 'sort_numer': '1232', 'discountType': '阶梯价'}\n", "http://m.txpchain.com/product?offset=36\n", "{'id': '1410', 'name': 'NFC100%桃汁', 'title': 'NFC100%桃汁', 'price_unit': '箱', 'product_price': '79.00', 'market_price': '0.00', 'spec': '325ml*15瓶', 'img_url': 'http://static.txpchain.com/file/2024/02/22/1eubd6nfd22q66sf.jpg', 'bname': None, 'sort_numer': '1246'}\n", "http://m.txpchain.com/product?offset=37\n", "{'id': '1420', 'name': '雅谷纯白棉花糖500g', 'title': '雅谷纯白棉花糖', 'price_unit': '袋', 'product_price': '9.50', 'market_price': '0.00', 'spec': '500g', 'img_url': 'http://static.txpchain.com/file/2024/02/28/j0lfn8785z1vq5ar.jpg', 'bname': None, 'sort_numer': '1256'}\n", "http://m.txpchain.com/product?offset=38\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>price_unit</th>\n", "      <th>product_price</th>\n", "      <th>market_price</th>\n", "      <th>spec</th>\n", "      <th>img_url</th>\n", "      <th>bname</th>\n", "      <th>sort_numer</th>\n", "      <th>discountType</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>628</td>\n", "      <td>明治牛乳</td>\n", "      <td>明治牛乳</td>\n", "      <td>箱</td>\n", "      <td>168.00</td>\n", "      <td>174.00</td>\n", "      <td>950ml*12瓶</td>\n", "      <td>http://static.txpchain.com/file/2023/06/08/hvy...</td>\n", "      <td>明治</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>688</td>\n", "      <td>明治咖啡专用牛乳</td>\n", "      <td>咖啡乳</td>\n", "      <td>箱</td>\n", "      <td>144.00</td>\n", "      <td>0.00</td>\n", "      <td>950ml*12瓶</td>\n", "      <td>http://static.txpchain.com/file/2022/01/08/vr9...</td>\n", "      <td>None</td>\n", "      <td>6</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>585</td>\n", "      <td>明治60%特纯黑巧克力</td>\n", "      <td>明治60%特纯黑巧克力</td>\n", "      <td>包</td>\n", "      <td>96.00</td>\n", "      <td>0.00</td>\n", "      <td>1kg</td>\n", "      <td>http://static.txpchain.com/file/2023/10/15/hoi...</td>\n", "      <td>None</td>\n", "      <td>9</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>392</td>\n", "      <td>安佳淡奶油</td>\n", "      <td>安佳淡奶油</td>\n", "      <td>箱</td>\n", "      <td>519.00</td>\n", "      <td>0.00</td>\n", "      <td>1L*12瓶</td>\n", "      <td>http://static.txpchain.com/file/2018/06/28/oq7...</td>\n", "      <td>安佳</td>\n", "      <td>19</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>437</td>\n", "      <td>铁塔动物淡奶油</td>\n", "      <td>铁塔淡奶油</td>\n", "      <td>箱</td>\n", "      <td>557.00</td>\n", "      <td>0.00</td>\n", "      <td>1L*12瓶</td>\n", "      <td>http://static.txpchain.com/file/2018/09/25/af2...</td>\n", "      <td>None</td>\n", "      <td>23</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>769</td>\n", "      <td>oatly（欧力）咖啡大师燕麦奶</td>\n", "      <td>oatly（欧力）咖啡大师燕麦奶1L*6</td>\n", "      <td>箱</td>\n", "      <td>108.00</td>\n", "      <td>120.00</td>\n", "      <td>1L*6瓶</td>\n", "      <td>http://static.txpchain.com/file/2019/09/19/6a3...</td>\n", "      <td>欧力</td>\n", "      <td>33</td>\n", "      <td>阶梯价</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>815</td>\n", "      <td>蓝风车淡奶油</td>\n", "      <td>蓝风车淡奶油1L*12/箱</td>\n", "      <td>箱</td>\n", "      <td>639.00</td>\n", "      <td>0.00</td>\n", "      <td>1L*12盒</td>\n", "      <td>http://static.txpchain.com/file/2021/09/26/4ja...</td>\n", "      <td>南侨</td>\n", "      <td>38</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>691</td>\n", "      <td>日清紫罗兰低筋小麦粉</td>\n", "      <td>日清紫罗兰低筋小麦粉25kg</td>\n", "      <td>袋</td>\n", "      <td>316.00</td>\n", "      <td>0.00</td>\n", "      <td>25kg</td>\n", "      <td>http://static.txpchain.com/file/2020/11/24/n0w...</td>\n", "      <td>日清</td>\n", "      <td>116</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>690</td>\n", "      <td>日清山茶花高筋小麦粉</td>\n", "      <td>日清山茶花高筋小麦粉25kg</td>\n", "      <td>袋</td>\n", "      <td>316.00</td>\n", "      <td>0.00</td>\n", "      <td>25kg</td>\n", "      <td>http://static.txpchain.com/file/2020/11/24/bf6...</td>\n", "      <td>日清</td>\n", "      <td>137</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>206</td>\n", "      <td>全脂奶粉</td>\n", "      <td>全脂奶粉</td>\n", "      <td>袋</td>\n", "      <td>750.00</td>\n", "      <td>0.00</td>\n", "      <td>25kg</td>\n", "      <td>http://static.txpchain.com/file/2020/06/01/7kt...</td>\n", "      <td>恒天然</td>\n", "      <td>357</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id              name                 title price_unit product_price  \\\n", "0  628              明治牛乳                  明治牛乳          箱        168.00   \n", "1  688          明治咖啡专用牛乳                   咖啡乳          箱        144.00   \n", "2  585       明治60%特纯黑巧克力           明治60%特纯黑巧克力          包         96.00   \n", "3  392             安佳淡奶油                 安佳淡奶油          箱        519.00   \n", "4  437           铁塔动物淡奶油                铁塔淡奶油           箱        557.00   \n", "5  769  oatly（欧力）咖啡大师燕麦奶  oatly（欧力）咖啡大师燕麦奶1L*6          箱        108.00   \n", "6  815            蓝风车淡奶油         蓝风车淡奶油1L*12/箱          箱        639.00   \n", "7  691        日清紫罗兰低筋小麦粉        日清紫罗兰低筋小麦粉25kg          袋        316.00   \n", "8  690        日清山茶花高筋小麦粉        日清山茶花高筋小麦粉25kg          袋        316.00   \n", "9  206              全脂奶粉                  全脂奶粉          袋        750.00   \n", "\n", "  market_price       spec                                            img_url  \\\n", "0       174.00  950ml*12瓶  http://static.txpchain.com/file/2023/06/08/hvy...   \n", "1         0.00  950ml*12瓶  http://static.txpchain.com/file/2022/01/08/vr9...   \n", "2         0.00        1kg  http://static.txpchain.com/file/2023/10/15/hoi...   \n", "3         0.00     1L*12瓶  http://static.txpchain.com/file/2018/06/28/oq7...   \n", "4         0.00     1L*12瓶  http://static.txpchain.com/file/2018/09/25/af2...   \n", "5       120.00      1L*6瓶  http://static.txpchain.com/file/2019/09/19/6a3...   \n", "6         0.00     1L*12盒  http://static.txpchain.com/file/2021/09/26/4ja...   \n", "7         0.00       25kg  http://static.txpchain.com/file/2020/11/24/n0w...   \n", "8         0.00       25kg  http://static.txpchain.com/file/2020/11/24/bf6...   \n", "9         0.00       25kg  http://static.txpchain.com/file/2020/06/01/7kt...   \n", "\n", "  bname sort_numer discountType  \n", "0    明治          5          NaN  \n", "1  None          6          NaN  \n", "2  None          9          NaN  \n", "3    安佳         19          NaN  \n", "4  None         23          NaN  \n", "5    欧力         33          阶梯价  \n", "6    南侨         38          NaN  \n", "7    日清        116          NaN  \n", "8    日清        137          NaN  \n", "9   恒天然        357          NaN  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["product_list_all=[]\n", "for offset in range(-1,50):\n", "    if offset==0:\n", "        continue\n", "    url=f'http://m.txpchain.com/product?offset={offset}'\n", "    print(url)\n", "    product_list=requests.get(url).json();\n", "    # TODO 通过proxy发送请求，连接被拒绝\n", "    # product_list=json.loads(get_remote_data_with_proxy(url).text);\n", "    if product_list is None or len(product_list)<=0:\n", "        break\n", "    else:\n", "        print(product_list[0])\n", "        product_list_all.extend(product_list)\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>price_unit</th>\n", "      <th>product_price</th>\n", "      <th>market_price</th>\n", "      <th>spec</th>\n", "      <th>img_url</th>\n", "      <th>bname</th>\n", "      <th>sort_numer</th>\n", "      <th>discountType</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>628</td>\n", "      <td>明治牛乳</td>\n", "      <td>明治牛乳</td>\n", "      <td>箱</td>\n", "      <td>168.00</td>\n", "      <td>174.00</td>\n", "      <td>950ml*12瓶</td>\n", "      <td>http://static.txpchain.com/file/2023/06/08/hvy...</td>\n", "      <td>明治</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>688</td>\n", "      <td>明治咖啡专用牛乳</td>\n", "      <td>咖啡乳</td>\n", "      <td>箱</td>\n", "      <td>144.00</td>\n", "      <td>0.00</td>\n", "      <td>950ml*12瓶</td>\n", "      <td>http://static.txpchain.com/file/2022/01/08/vr9...</td>\n", "      <td>None</td>\n", "      <td>6</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>585</td>\n", "      <td>明治60%特纯黑巧克力</td>\n", "      <td>明治60%特纯黑巧克力</td>\n", "      <td>包</td>\n", "      <td>96.00</td>\n", "      <td>0.00</td>\n", "      <td>1kg</td>\n", "      <td>http://static.txpchain.com/file/2023/10/15/hoi...</td>\n", "      <td>None</td>\n", "      <td>9</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id         name        title price_unit product_price market_price  \\\n", "0  628         明治牛乳         明治牛乳          箱        168.00       174.00   \n", "1  688     明治咖啡专用牛乳          咖啡乳          箱        144.00         0.00   \n", "2  585  明治60%特纯黑巧克力  明治60%特纯黑巧克力          包         96.00         0.00   \n", "\n", "        spec                                            img_url bname  \\\n", "0  950ml*12瓶  http://static.txpchain.com/file/2023/06/08/hvy...    明治   \n", "1  950ml*12瓶  http://static.txpchain.com/file/2022/01/08/vr9...  None   \n", "2        1kg  http://static.txpchain.com/file/2023/10/15/hoi...  None   \n", "\n", "  sort_numer discountType  \n", "0          5          NaN  \n", "1          6          NaN  \n", "2          9          NaN  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(product_list_all_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:明治牛乳, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:540.9018993377686ms\n", "inputText:明治咖啡专用牛乳, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:573.0516910552979ms\n", "inputText:明治60%特纯黑巧克力, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:690.9000873565674ms\n", "inputText:安佳淡奶油, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:551.210880279541ms\n", "inputText:铁塔动物淡奶油, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:544.9948310852051ms\n", "inputText:oatly（欧力）咖啡大师燕麦奶, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:580.0471305847168ms\n", "inputText:蓝风车淡奶油, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:598.1912612915039ms\n", "inputText:日清紫罗兰低筋小麦粉, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:601.2153625488281ms\n", "inputText:日清山茶花高筋小麦粉, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:555.8490753173828ms\n", "inputText:全脂奶粉, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:546.5302467346191ms\n", "inputText:味斯美海苔脆肉松, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:579.3592929840088ms\n", "inputText:韩国TS幼砂糖, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:716.0542011260986ms\n", "inputText:王后精制低筋粉, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:563.709020614624ms\n", "inputText:王后精制高筋小麦粉, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:683.6540699005127ms\n", "inputText:王后日式面包粉, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:586.3430500030518ms\n", "inputText:金像A面粉, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:762.3827457427979ms\n", "inputText:金像B高筋面粉（编织袋）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:775.8831977844238ms\n", "inputText:美玫300粉, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:677.1554946899414ms\n", "inputText:美玫低筋粉（纸袋）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:616.4686679840088ms\n", "inputText:玉米淀粉, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:575.5155086517334ms\n", "inputText:丝诺含乳脂植脂奶油, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:605.2646636962891ms\n", "inputText:金钻维益甜点植脂奶油, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:698.0612277984619ms\n", "inputText:金钻维益甜点植脂奶油（含乳脂）, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:611.7422580718994ms\n", "inputText:凯瑞芝士奶酪1kg, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:562.0875358581543ms\n", "inputText:不二欧喜可丝达（奶酪味）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:584.1188430786133ms\n", "inputText:爱护咖啡饮品浓缩奶油, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:849.5898246765137ms\n", "inputText:加利雪莓娘皮（9*9）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:600.3458499908447ms\n", "inputText:德馨-竹蔗冰糖浆1.26kg, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:616.4462566375732ms\n", "inputText:黑标烘炒咖啡豆1kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:539.9370193481445ms\n", "inputText:熊猫炼乳5kg, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:643.8877582550049ms\n", "inputText:熊猫炼乳350g*6罐, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:589.179277420044ms\n", "inputText:南侨维佳烤焙奶油, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:621.1330890655518ms\n", "inputText:京日红豆沙, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:651.3428688049316ms\n", "inputText:明治醇壹低脂牛奶 450ml（需预定）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:572.2393989562988ms\n", "inputText:奥利奥饼干碎400g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:667.6478385925293ms\n", "inputText:明治醇壹全脂牛奶450ml（需预定）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:593.8494205474854ms\n", "inputText:明治醇壹低脂牛奶200ml（需预定）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:561.2196922302246ms\n", "inputText:明治醇壹全脂牛奶200ml（需预定）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:717.4038887023926ms\n", "inputText:南侨无水酥油, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:547.6653575897217ms\n", "inputText:普利欧冷冻蛋糕－半熟芝士（抹茶味）, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:616.7027950286865ms\n", "inputText:南侨液态酥油, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:573.4641551971436ms\n", "inputText:普利欧冷冻蛋糕－半熟芝士（原味）, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:614.5110130310059ms\n", "inputText:明治保加利亚式轻酸奶 清甜原味（需预定）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:652.909517288208ms\n", "inputText:三叔公雪莓娘皮原味, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:576.836347579956ms\n", "inputText:奥昆速冻葡式Y1207蛋挞皮, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:627.7163028717041ms\n", "inputText:奥昆冷冻蛋黄酥胚（红豆味）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:545.5889701843262ms\n", "inputText:一级大青芒(毛重12斤), usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:649.4812965393066ms\n", "inputText:普利欧冷冻蛋糕－半熟芝士（榴莲味）, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:610.023021697998ms\n", "inputText:普利欧冷冻蛋糕－提拉米苏, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:600.4273891448975ms\n", "inputText:普利欧冷冻蛋糕－经典黑森林, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:618.3638572692871ms\n", "inputText:普利欧冷冻蛋糕－法式乳酪, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:596.3833332061768ms\n", "inputText:普利欧冷冻蛋糕－抹茶芝士, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:632.030725479126ms\n", "inputText:金枕头榴莲果泥1kg, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:535.4037284851074ms\n", "inputText:明治优漾杀菌型乳酸菌饮品 400ml（需预定）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:600.3234386444092ms\n", "inputText:明治保加利亚式轻酸奶 桃子味（需预定）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:646.1710929870605ms\n", "inputText:明治保加利亚式轻酸奶 青柠柠檬味（需预定）, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:509.4268321990967ms\n", "inputText:普利欧冷冻蛋糕－草莓芝士, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:522.7406024932861ms\n", "inputText:普利欧冷冻蛋糕－樱桃芝士, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:611.9873523712158ms\n", "inputText:普利欧冷冻蛋糕－卡布基诺, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:549.3485927581787ms\n", "inputText:奥昆速冻葡式207蛋挞皮, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:655.5807590484619ms\n", "inputText:普利欧冷冻蛋糕－榴莲千层, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:581.6047191619873ms\n", "inputText:普利欧冷冻蛋糕－抹茶千层, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:1248.5873699188232ms\n", "inputText:代可可脂黑巧克力砖1kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:623.0130195617676ms\n", "inputText:明治优漾杀菌型乳酸菌饮品 900ml（需预定）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:543.2088375091553ms\n", "inputText:明治meiji  保加利亚式凝固型酸奶 纯味不甜150g*24盒, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:798.9230155944824ms\n", "inputText:普利欧冷冻蛋糕－芒果千层, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:635.6122493743896ms\n", "inputText:普利欧冷冻蛋糕－巧克力千层, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:562.6544952392578ms\n", "inputText:明治meiji 保加利亚式凝固型酸奶 清甜原味150g*24盒, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:565.4058456420898ms\n", "inputText:南侨片状甜奶油（炼乳味）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:599.6928215026855ms\n", "inputText:明治保加利亚式凝固型酸奶 低脂清甜原味（需预定）, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:544.0726280212402ms\n", "inputText:明治meiji保加利亚式凝固型酸奶低脂清甜原味 150g*24盒, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:529.2654037475586ms\n", "inputText:普利欧冷冻蛋糕－草莓芝士魔方, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:598.7758636474609ms\n", "inputText:明治保加利亚式凝固型酸奶 纯味不甜（需预定）, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:574.4822025299072ms\n", "inputText:明治醇壹低脂牛乳950ml（需预定）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:792.2248840332031ms\n", "inputText:明治醇壹全脂牛乳950ml（需预定）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:662.9087924957275ms\n", "inputText:奥昆榴莲酥, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:617.051362991333ms\n", "inputText:奥昆甜甜圈（原味50g）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:783.0560207366943ms\n", "inputText:普利欧冷冻蛋糕－黑森林芝士魔方, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:695.8472728729248ms\n", "inputText:普利欧冷冻蛋糕－红丝绒芝士魔方, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:638.6065483093262ms\n", "inputText:普利欧冷冻蛋糕－芒果芝士魔方, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:605.7987213134766ms\n", "inputText:普利欧冷冻蛋糕－抹茶芝士魔方, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:619.2574501037598ms\n", "inputText:太古蓝标糖粉, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:614.2420768737793ms\n", "inputText:普利欧冷冻蛋糕－蓝莓芝士魔方, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:645.3909873962402ms\n", "inputText:普利欧冷冻蛋糕－迷你北海道双层－经典原味芝士, usage:{\"prompt_tokens\": 37, \"total_tokens\": 37}, time cost:573.3790397644043ms\n", "inputText:明治保加利亚式凝固型酸奶清甜原味（需预定）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:798.4433174133301ms\n", "inputText:普利欧冷冻蛋糕－迷你北海道双层－海盐芝士奶盖, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:712.0697498321533ms\n", "inputText:奥昆手撕包 （200g）, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:610.7282638549805ms\n", "inputText:代可可脂白巧克力砖1kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:673.1250286102295ms\n", "inputText:高脂可可粉1kg, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:615.4580116271973ms\n", "inputText:阳光大豆油（烘焙专用）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:604.1045188903809ms\n", "inputText:早苗泡打粉, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:637.1543407440186ms\n", "inputText:普利欧冷冻蛋糕－彩虹蛋糕, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:649.4669914245605ms\n", "inputText:奥昆迷你甜甜圈（草莓味）20g, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:610.1021766662598ms\n", "inputText:进口红心火龙果（11.5斤）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:589.5876884460449ms\n", "inputText:精品蓝莓2盒装, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:587.4710083007812ms\n", "inputText:红颜草莓2斤（大果）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:703.1240463256836ms\n", "inputText:早苗塔塔粉, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:703.5748958587646ms\n", "inputText:小高迪干红(红葡萄酒), usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:604.5358180999756ms\n", "inputText:好邦红豆沙5kg/包, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:595.6158638000488ms\n", "inputText:奥昆老婆饼, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:624.8650550842285ms\n", "inputText:奥昆绿豆饼, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:616.7547702789307ms\n", "inputText:奥昆红豆饼, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:572.1087455749512ms\n", "inputText:王后特制全麦粉（粗粒）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:771.9030380249023ms\n", "inputText:普利欧冷冻蛋糕－蒙布朗栗子芝士, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:537.2793674468994ms\n", "inputText:普利欧冷冻蛋糕－挚爱提拉米苏芝士, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:650.719404220581ms\n", "inputText:普利欧冷冻蛋糕－玫瑰红丝绒芝士, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:558.3431720733643ms\n", "inputText:普利欧冷冻蛋糕－蓝莓果果芝士, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:788.3875370025635ms\n", "inputText:奥昆206葡式迷你蛋挞皮, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:609.041690826416ms\n", "inputText:奥昆麻薯胚（紫薯味）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:609.7829341888428ms\n", "inputText:安佳再制切达干酪（84片）米色白片单包, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:629.5256614685059ms\n", "inputText:多色马卡龙（清甜）5粒*4盒, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:572.516679763794ms\n", "inputText:红颜草莓2斤（中果）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:571.7563629150391ms\n", "inputText:安佳再制切达干酪（84片）橙色黄片单包, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:580.1284313201904ms\n", "inputText:明治牛乳2箱+欧力咖啡大师燕麦奶1箱, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:619.9233531951904ms\n", "inputText:波兰全脂牛奶（大M）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:638.6473178863525ms\n", "inputText:维益爱真动物性淡奶油38%, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:634.6828937530518ms\n", "inputText:奥昆好禧坊芒果慕斯蛋糕, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:578.2849788665771ms\n", "inputText:奥昆好禧坊抹茶慕斯蛋糕, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:558.0666065216064ms\n", "inputText:奥昆好禧坊提拉米苏慕斯蛋糕, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:569.3521499633789ms\n", "inputText:奥昆好禧坊黑森林慕斯蛋糕, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:620.7807064056396ms\n", "inputText:奥昆好禧坊巧克力慕斯蛋糕, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:572.6959705352783ms\n", "inputText:奥昆好禧坊红丝绒慕斯蛋糕, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:586.0164165496826ms\n", "inputText:奥昆好禧坊咸味芝士慕斯蛋糕, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:647.2141742706299ms\n", "inputText:奥昆好禧坊双莓慕斯蛋糕, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:659.9011421203613ms\n", "inputText:四川尤力克黄柠檬（中果，10斤）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:610.7101440429688ms\n", "inputText:无籽西瓜(毛重26-29斤), usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:613.0824089050293ms\n", "inputText:奥昆好禧坊葡式蛋挞液, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:840.9860134124756ms\n", "inputText:海南青金桔3斤, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:697.1240043640137ms\n", "inputText:海南香水柠檬5斤, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:590.5306339263916ms\n", "inputText:海南有籽青柠檬3斤, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:587.5914096832275ms\n", "inputText:一级水仙芒（ 12斤）, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:828.5789489746094ms\n", "inputText:海南凤梨（6个）, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:628.3540725708008ms\n", "inputText:优惠或运费, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:547.2874641418457ms\n", "inputText:明治冰淇淋草莓味4.2kg, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:611.1483573913574ms\n", "inputText:明治冰淇淋红豆味4.2kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:751.8556118011475ms\n", "inputText:明治冰淇淋香草味4.2kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:569.9384212493896ms\n", "inputText:明治冰淇淋芒果味4.2kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:585.801362991333ms\n", "inputText:明治冰淇淋抹茶味4.2kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:628.6802291870117ms\n", "inputText:明治冰淇淋巧克力味4.2kg, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:540.1225090026855ms\n", "inputText:水桶乳茶, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:667.6023006439209ms\n", "inputText:奥昆甜甜圈（草莓果馅50g）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:610.1210117340088ms\n", "inputText:百加得白朗姆酒, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:553.5824298858643ms\n", "inputText:奥昆好禧坊牛角包（流沙蛋黄味）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:626.4510154724121ms\n", "inputText:明治保加利亚式酸奶（纯味不甜）400g（需预定）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:539.7553443908691ms\n", "inputText:海南越王头生椰乳, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:590.9802913665771ms\n", "inputText:新希望若雪酸奶, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:715.3980731964111ms\n", "inputText:牛油果22头一箱, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:604.5410633087158ms\n", "inputText:普利欧（芝士三角系列）-元气西柚慕斯蛋糕, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:658.9550971984863ms\n", "inputText:普利欧（芝士三角系列）-梦幻香芋慕斯蛋糕, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:544.377326965332ms\n", "inputText:普利欧（芝士三角系列）-浓情提拉米苏慕斯蛋糕, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:560.6913566589355ms\n", "inputText:普利欧（迷你马芬系列）-红丝绒枣泥蛋糕, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:540.4789447784424ms\n", "inputText:普利欧（迷你马芬系列）-暖心巧克力蛋糕, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:582.9868316650391ms\n", "inputText:普利欧（迷你马芬系列）-阳光香橙蛋糕, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:611.2689971923828ms\n", "inputText:普利欧（法式长条系列）-多C金桔慕斯蛋糕, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:517.9572105407715ms\n", "inputText:普利欧（法式长条系列）-啵啵黑糖慕斯蛋糕, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:591.8865203857422ms\n", "inputText:普利欧（法式长条系列）-芝芝抹茶慕斯蛋糕, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:564.2321109771729ms\n", "inputText:总统稀奶油, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:586.6775512695312ms\n", "inputText:蓝莓（整箱）, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:651.4644622802734ms\n", "inputText:奥昆小酥芙咸香芝士味, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:597.078800201416ms\n", "inputText:奥昆奶香片, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:612.5600337982178ms\n", "inputText:海南晓蜜瓜（4个）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:664.2014980316162ms\n", "inputText:耙耙柑（5斤）, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:611.8111610412598ms\n", "inputText:奥昆迷你甜甜圈（原味）20g, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:681.9174289703369ms\n", "inputText:普利欧-粉心提拉米苏慕斯蛋糕, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:616.640567779541ms\n", "inputText:普利欧-朵朵优格葡萄慕斯蛋糕, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:555.6302070617676ms\n", "inputText:普利欧-巧克力脆脆蛋糕, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:541.3942337036133ms\n", "inputText:普利欧-小熊草莓芝士蛋糕, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:544.2664623260498ms\n", "inputText:普利欧-小熊巧克力树莓芝士蛋糕, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:640.6219005584717ms\n", "inputText:泰国进口泰象原味苏打水, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:580.944299697876ms\n", "inputText:阳光玫瑰（净果1斤装）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:653.1729698181152ms\n", "inputText:三麟100%天然椰子水（330ml）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:607.4302196502686ms\n", "inputText:三麟100%天然椰子水（1L）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:644.524335861206ms\n", "inputText:泰国三象水磨糯米粉, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:531.1379432678223ms\n", "inputText:莫林纯蔗糖风味糖浆1L, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:537.4710559844971ms\n", "inputText:莫林榛果风味糖浆1L, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:526.5212059020996ms\n", "inputText:莫林焦糖风味糖浆1L, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:558.9637756347656ms\n", "inputText:莫林香草风味糖浆1L, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:1277.2676944732666ms\n", "inputText:莫林海盐焦糖风味糖浆1L, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:585.7186317443848ms\n", "inputText:莫林绿薄荷风味糖浆700ml, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:850.7542610168457ms\n", "inputText:莫林橘皮风味糖浆1L, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:605.5171489715576ms\n", "inputText:莫林莫西多薄荷风味糖浆700Ml, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:547.1079349517822ms\n", "inputText:莫林草莓风味糖浆700ml, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:574.2182731628418ms\n", "inputText:莫林百香果风味糖浆700ml, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:546.6058254241943ms\n", "inputText:莫林红石榴风味糖浆1L, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:590.1947021484375ms\n", "inputText:莫林桂花风味糖浆700ml, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:558.7255954742432ms\n", "inputText:莫林冰爽薄荷风味糖浆700ml, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:527.0388126373291ms\n", "inputText:莫林麦芽啤酒花风味糖浆700ml, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:577.8670310974121ms\n", "inputText:莫林接骨木花风味糖浆700ml, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:562.157154083252ms\n", "inputText:莫林玉桂风味糖浆700ml, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:750.2896785736084ms\n", "inputText:莫林红柚风味糖浆700Ml, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:608.8480949401855ms\n", "inputText:莫林水蜜桃风味糖浆700ml, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:640.8300399780273ms\n", "inputText:莫林玫瑰风味糖浆700ml, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:635.5783939361572ms\n", "inputText:莫林肉桂苹果风味糖浆700ml, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:542.2213077545166ms\n", "inputText:莫林栀子花风味糖浆700ml, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:529.7114849090576ms\n", "inputText:莫林青瓜风味糖浆700ml, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:552.1290302276611ms\n", "inputText:莫林山茶花风味糖浆700ml, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:682.2247505187988ms\n", "inputText:莫林荔枝风味糖浆700ml , usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:573.9789009094238ms\n", "inputText:普利欧-巴斯克芝士蛋糕, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:589.09010887146ms\n", "inputText:徐香猕猴桃（绿心，4粒装）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:558.8860511779785ms\n", "inputText:明治含乳饮料（草莓味）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:541.691780090332ms\n", "inputText:莫林班兰叶风味糖浆700ml, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:526.1020660400391ms\n", "inputText:莫林茉莉花风味糖浆700ml, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:531.3751697540283ms\n", "inputText:普麦尔奥尔良腿排100g（1kg）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:534.5187187194824ms\n", "inputText:普麦尔奥尔良鸡扒70g（980g）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:547.1291542053223ms\n", "inputText:普麦尔德式迷你肠15g（1kg）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:538.1786823272705ms\n", "inputText:普麦尔德式香肠30g（1kg）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:516.3395404815674ms\n", "inputText:普麦尔切片火腿10.5cm（500g）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:540.4231548309326ms\n", "inputText:普麦尔多汁猪扒（10片）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:700.6082534790039ms\n", "inputText:菲诺厚椰乳, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:524.4884490966797ms\n", "inputText:莫林蓝柑风味糖浆700ml, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:535.6481075286865ms\n", "inputText:莫林冰糖葫芦风味糖浆700ml, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:525.0828266143799ms\n", "inputText:莫林樱花风味糖浆700ml, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:556.0014247894287ms\n", "inputText:莫林烤板栗风味糖浆700ml, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:614.424467086792ms\n", "inputText:莫林白葡萄风味糖浆700ml, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:560.0180625915527ms\n", "inputText:莫林夏威夷果风味糖浆700ml, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:779.5712947845459ms\n", "inputText:莫林巧克力风味糖浆700ml, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:722.7334976196289ms\n", "inputText:奥昆彩虹蛋糕, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:513.0786895751953ms\n", "inputText:皇冠梨（5斤）, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:576.9391059875488ms\n", "inputText:红富士苹果（3斤）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:555.1650524139404ms\n", "inputText:普利欧兔兔奶糖芝士蛋糕, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:1612.1976375579834ms\n", "inputText:烘烤扁桃仁片（巴旦木片）500g, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:708.4898948669434ms\n", "inputText:韩小U  全脂纯牛奶, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:644.5465087890625ms\n", "inputText:切片蔓越莓果干（深红）500g, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:661.1042022705078ms\n", "inputText:火焰提子干（中）1kg, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:524.4989395141602ms\n", "inputText:明治优漾杀菌型乳酸菌饮品 桃子味 900ml （需预定）, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:521.146297454834ms\n", "inputText:明治优漾杀菌型乳酸菌饮品 桃子味 400ml（需预定）, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:549.4248867034912ms\n", "inputText:必如冰博客6瓶, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:534.2190265655518ms\n", "inputText:立高新仙尼-芒果果酱3kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:546.2288856506348ms\n", "inputText:茉莉花液, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:623.0206489562988ms\n", "inputText:100%NFC椰子水, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:570.1186656951904ms\n", "inputText:立高依乐斯乳脂奶油, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:523.8983631134033ms\n", "inputText:38%明治淡奶油（需预定）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:646.0962295532227ms\n", "inputText:冷冻杨梅汁150g *20包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:541.3515567779541ms\n", "inputText:明治白巧克力（草莓口味）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:664.586067199707ms\n", "inputText:明治50%特纯黑巧克力, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:562.5288486480713ms\n", "inputText:明治70%黑巧克力, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:530.3921699523926ms\n", "inputText:明治白巧克力（抹茶口味）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:544.7819232940674ms\n", "inputText:奥昆 开心果美莓蛋糕（生日蛋糕）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:537.1255874633789ms\n", "inputText:43%明治淡奶油（需预定）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:523.5378742218018ms\n", "inputText:立高稀奶油360pro, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:550.4159927368164ms\n", "inputText:普利欧-萌兔抹茶慕斯蛋糕, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:554.4295310974121ms\n", "inputText:普利欧-猫爪桃桃慕斯蛋糕, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:591.5753841400146ms\n", "inputText:普利欧-躺平鸭蛋黄慕斯蛋糕, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:1078.8955688476562ms\n", "inputText:普利欧-熊猫花生提拉米苏慕斯蛋糕（四寸）, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:543.1110858917236ms\n", "inputText:普利欧-发现生椰拿铁慕斯蛋糕（四寸）, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:626.9617080688477ms\n", "inputText:普利欧-向日葵芒芒慕斯蛋糕（四寸）, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:547.9626655578613ms\n", "inputText:NFC100%山楂混合汁, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:614.9029731750488ms\n", "inputText:NFC100%卡曼橘混合汁, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:644.7696685791016ms\n", "inputText:NFC100%橙混合汁, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:597.914457321167ms\n", "inputText:NFC100%芒果混合汁, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:563.4446144104004ms\n", "inputText:NFC100%葡萄汁, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:534.6686840057373ms\n", "inputText:德馨珍果鲜-葡萄果汁饮料浓浆1L, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:523.7722396850586ms\n", "inputText:德馨珍果鲜-小青柑果汁饮料浓浆1L, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:676.7141819000244ms\n", "inputText:德馨珍果鲜-青柚果汁饮料浓浆1L, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:527.2722244262695ms\n", "inputText:德馨珍果鲜-阳光青提果汁饮料浓浆1L, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:609.797477722168ms\n", "inputText:德馨珍果鲜-油柑果汁饮料浓浆1L, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:552.8817176818848ms\n", "inputText:德馨珍果鲜-菠萝果汁饮料浓浆1L, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:552.0427227020264ms\n", "inputText:德馨珍果鲜-荔枝果汁饮料浓浆1L, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:548.8545894622803ms\n", "inputText:德馨珍果鲜-红芭乐果汁饮料浓浆1L, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:561.8531703948975ms\n", "inputText:德馨-山茶花乌龙茶调味饮料1L, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:564.5639896392822ms\n", "inputText:莫林巨峰葡萄香甜酒风味糖浆700ml , usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:615.7374382019043ms\n", "inputText:阳光玫瑰（毛重6斤）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:526.4286994934082ms\n", "inputText:培根披萨6寸, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:537.8730297088623ms\n", "inputText:黑椒牛肉披萨6寸, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:709.3935012817383ms\n", "inputText:奥尔良鸡肉披萨6寸, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:661.1416339874268ms\n", "inputText:缤纷水果披萨6寸, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:586.36474609375ms\n", "inputText:夏威夷披萨6寸, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:600.6157398223877ms\n", "inputText:意式咖啡豆-宇宙黑洞, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:567.4729347229004ms\n", "inputText:意式咖啡豆-蜜糖谷, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:610.6030941009521ms\n", "inputText:德馨-港式红茶茶汤1L, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:495.2857494354248ms\n", "inputText:德馨-大红袍茶汤1L, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:601.5372276306152ms\n", "inputText:德馨-茉莉绿茶茶汤1L, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:528.7039279937744ms\n", "inputText:维益爱真300Pro稀奶油, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:553.6913871765137ms\n", "inputText:OATSIDE咖啡大师燕麦奶, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:576.1725902557373ms\n", "inputText:普利欧冷冻蛋糕－提拉米苏芝士魔方, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:534.2988967895508ms\n", "inputText:普利欧冷冻蛋糕－奥利奥芝士魔方, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:547.6114749908447ms\n", "inputText:赣南脐橙10斤, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:522.7041244506836ms\n", "inputText:明治鼎醇浓缩牛乳（餐饮专供）900ml, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:561.07497215271ms\n", "inputText:明治鼎醇浓缩牛乳400ml, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:542.3932075500488ms\n", "inputText:明治鼎醇浓缩牛乳200ml, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:506.528377532959ms\n", "inputText:明治含乳饮料（香蕉味）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:517.2784328460693ms\n", "inputText:明治含乳饮料（蜜瓜味）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:508.92019271850586ms\n", "inputText:明治含乳饮料（白桃乌龙味）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:614.2761707305908ms\n", "inputText:黄油羊角可颂, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:733.1111431121826ms\n", "inputText:野姜花液450g, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:565.2658939361572ms\n", "inputText:小粒马卡龙混装, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:603.9512157440186ms\n", "inputText:莫林法式布蕾风味糖浆700ml, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:586.6806507110596ms\n", "inputText:普利欧-琉璃蓝柑芝士蛋糕, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:615.2129173278809ms\n", "inputText:普利欧-落樱莓莓芝士蛋糕, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:548.2931137084961ms\n", "inputText:普利欧-抹茶松风芝士蛋糕, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:652.18186378479ms\n", "inputText:普利欧-紫藤香芋芝士蛋糕, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:592.2462940216064ms\n", "inputText:普利欧-桃夭菠萝芝士蛋糕, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:557.0142269134521ms\n", "inputText:普利欧-乳白芒芒芝士蛋糕, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:732.3791980743408ms\n", "inputText:妙可蓝多纯牛奶1L, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:560.8828067779541ms\n", "inputText:植物标签-醇香米乳, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:613.1882667541504ms\n", "inputText:德馨珍果鲜-香梨汁饮料浓浆1L, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:562.9093647003174ms\n", "inputText:德馨珍果鲜-红石榴果汁饮料浓浆1L, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:541.7764186859131ms\n", "inputText:雪花梨果酱, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:540.0161743164062ms\n", "inputText:百利凝胶片(明胶片), usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:526.0162353515625ms\n", "inputText:丘比沙拉汁（焙煎芝麻口味）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:652.0516872406006ms\n", "inputText:丘比沙拉汁（日式口味）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:601.7036437988281ms\n", "inputText:丘比沙拉酱（香甜口味无挤压瓶）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:640.4941082000732ms\n", "inputText:桂格即食燕麦片（经典原味）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:794.1837310791016ms\n", "inputText:普麦尔-德式香肠-50g（烟熏味）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:597.2857475280762ms\n", "inputText:荷兰进口黑白淡奶400g*6罐, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:575.5834579467773ms\n", "inputText:德宝无盐黄油, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:608.4268093109131ms\n", "inputText:莆田无核桂圆肉500g, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:676.6343116760254ms\n", "inputText:新疆无核免洗红枣500g, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:655.7188034057617ms\n", "inputText:浅二路核桃仁500g, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:654.5224189758301ms\n", "inputText:新疆绿色葡萄干500g, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:675.7142543792725ms\n", "inputText:宇治抹茶粉50g*4包（白莲）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:677.1559715270996ms\n", "inputText:新希望鲜选牛乳（隔日达）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:769.8101997375488ms\n", "inputText:太古锡兰红茶, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:632.0819854736328ms\n", "inputText:戴妃白牛奶巧克力, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:663.1476879119873ms\n", "inputText:戴妃苦甜巧克力, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:838.4075164794922ms\n", "inputText:戴妃牛奶巧克力, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:857.9127788543701ms\n", "inputText:水妈妈-木薯粉（淀粉）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:936.6753101348877ms\n", "inputText:安诺尼手指饼干10包, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:770.4870700836182ms\n", "inputText:法国燕子耐高糖即发酵母, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:851.1300086975098ms\n", "inputText:盐焗巴旦木（NP30-32）2400g, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:777.928352355957ms\n", "inputText:谷优玛丽亚饼干粉500g, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:865.2994632720947ms\n", "inputText:无花果干500g, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:828.0503749847412ms\n", "inputText:水蜜桃干片250g, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:749.2673397064209ms\n", "inputText:玫瑰花瓣干50g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:1280.0369262695312ms\n", "inputText:香橙干片100g, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:704.9434185028076ms\n", "inputText:草莓干片500g, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:837.3126983642578ms\n", "inputText:柠檬干片250g, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:1415.4677391052246ms\n", "inputText:普麦尔-烘焙肉扒（10片）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:692.6548480987549ms\n", "inputText:明治奶油芝士（奶酪）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:601.3000011444092ms\n", "inputText:奥昆多谷物小餐包, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:680.3574562072754ms\n", "inputText:南美虾堡排, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:652.2746086120605ms\n", "inputText:奥昆脆皮菠萝包, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:710.0658416748047ms\n", "inputText:荷美尔-精选后腿圆火腿片, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:755.4709911346436ms\n", "inputText:安佳无盐片状黄油, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:729.9714088439941ms\n", "inputText:明治草莓牛乳400ml（需预定）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:564.5544528961182ms\n", "inputText:明治草莓牛乳900ml（需预定）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:649.7213840484619ms\n", "inputText:小鹰咖黑巧风味浓缩咖啡液, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:573.2302665710449ms\n", "inputText:小鹰咖现磨风味咖啡原液(组合装), usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:534.653902053833ms\n", "inputText:普利欧冷冻蛋糕－一叶芒香芝士迷你挞, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:664.8356914520264ms\n", "inputText:普利欧冷冻蛋糕－粉墨蝴蝶结芝士迷你挞, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:591.6521549224854ms\n", "inputText:普利欧冷冻蛋糕－小花抹茶芝士迷你挞, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:679.0900230407715ms\n", "inputText:普利欧冷冻蛋糕－芋见四叶草乐乐杯蛋糕, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:576.8153667449951ms\n", "inputText:普利欧冷冻蛋糕－甜心草莓乐乐杯蛋糕, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:537.3139381408691ms\n", "inputText:普利欧冷冻蛋糕－仙人掌可可乐乐杯蛋糕, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:512.2678279876709ms\n", "inputText:普利欧-莓莓粉黑芝麻芝士蛋糕, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:530.6932926177979ms\n", "inputText:普利欧-德式兰姆黑糖芝士蛋糕, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:517.0457363128662ms\n", "inputText:普利欧-醉爱提拉米苏芝士蛋糕, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:545.1135635375977ms\n", "inputText:普利欧-抹茶优格芝士蛋糕, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:519.5803642272949ms\n", "inputText:普利欧冷冻蛋糕－飞龙在天熔岩慕斯蛋糕, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:502.02250480651855ms\n", "inputText:普利欧冷冻蛋糕－东海龙王榴芒慕斯蛋糕, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:599.8373031616211ms\n", "inputText:立高880淡奶油, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:530.5500030517578ms\n", "inputText:菠萝片干, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:718.3830738067627ms\n", "inputText:黄油小号迷你挞壳（4cm）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:600.6777286529541ms\n", "inputText:正荣开心果果酱（100g）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:493.239164352417ms\n", "inputText:柏札莱阿尔卑冷冻大水牛马苏里拉干酪, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:522.4905014038086ms\n", "inputText:进口红心火龙果（毛重29-30斤）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:545.7127094268799ms\n", "inputText:卡夫芝士粉（巴马干酪粉）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:613.4238243103027ms\n", "inputText:玫瑰金盏菊矢车菊混合干花（20g）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:546.9386577606201ms\n", "inputText:海南菠萝（12个）, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:513.7715339660645ms\n", "inputText:三麟苏打气泡水, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:570.9505081176758ms\n", "inputText:NFC100%桃汁, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:623.9397525787354ms\n", "inputText:NFC100%苹果汁, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:698.6839771270752ms\n", "inputText:乐菲利娜芝士粉(再制干酪), usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:733.8569164276123ms\n", "inputText:旺仔年轮蛋糕, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:662.895679473877ms\n", "inputText:屈臣氏汤力汽水, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:614.7937774658203ms\n", "inputText:巴黎水（原味）气泡水, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:505.7070255279541ms\n", "inputText:巴黎水（青柠味）气泡水, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:582.8568935394287ms\n", "inputText:味好美千岛酱1kg, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:564.490795135498ms\n", "inputText:科麦褐色栗子罐头1kg, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:609.058141708374ms\n", "inputText:焙乐道淑女牌镜面果胶500g, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:735.4145050048828ms\n", "inputText:雅谷纯白棉花糖500g, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:595.5188274383545ms\n", "inputText:三叔公麻薯心（椰奶味）500g, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:627.694845199585ms\n", "inputText:仙妮贝儿防潮干佩斯（通用型）454g, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:616.8732643127441ms\n", "inputText:韩味乐-韩式炸鸡蜂蜜芥末酱, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:670.3972816467285ms\n", "inputText:VEpiaopiao-蜂蜜芥末酱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:525.8219242095947ms\n", "inputText:南国椰子脆片（原味）250g, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:536.4899635314941ms\n", "inputText:展艺免煮即食黑珍珠粉圆, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:583.972692489624ms\n", "inputText:宝岛印象-防风林糯米船, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:543.4482097625732ms\n", "inputText:力创青稞米红豆馅5kg, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:574.8155117034912ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['name'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "123.189.131.248:40016\n", "12************:48716\n", "180.105.247.117:34032\n", "*************:45190\n", "*************:44070\n", "***************:31445\n", "***************:37108\n", "************:43777\n", "*************:33265\n", "***************:39296\n", "['123.189.131.248:40016', '12************:48716', '180.105.247.117:34032', '*************:45190', '*************:44070', '***************:31445', '***************:37108', '************:43777', '*************:33265', '***************:39296']\n", "Thread count: 20\n", "*************, headers:{'token': '', 'appcodenew': '7798c1f4306b4f89a9fc2a4c2cdc47ac', 'uid': '712451', 'time': '*************'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json,write_pandas_df_into_odps,get_odps_sql_result_as_df,THREAD_CNT\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "THREAD_CNT = int(os.environ.get('THREAD_CNT', 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "hints={'odps.sql.hive.compatible':True,'odps.sql.type.system.odps2':True}\n", "def get_odps_sql_result_as_df(sql):\n", "    instance=odps.execute_sql(sql, hints=hints)\n", "    instance.wait_for_success()\n", "    pd_df=None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={'token':'',\n", "'appcodenew':'7798c1f4306b4f89a9fc2a4c2cdc47ac',\n", "'uid':'712451',\n", "'time':'*************',}\n", "brand_name='添品'\n", "competitor_name_en='tianpin'\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["http://m.txpchain.com/product?offset=-1\n", "url:http://m.txpchain.com/product?offset=-1, using proxy: ************************************************, headers:{}\n", "response status:200, proxy used:{'http': '************************************************'}\n", "{'id': '628', 'name': '明治牛乳', 'title': '明治牛乳', 'price_unit': '箱', 'product_price': '168.00', 'market_price': '174.00', 'spec': '950ml*12瓶', 'img_url': 'http://static.txpchain.com/file/2023/06/08/hvyx9qnh2xkznk55.png', 'bname': '明治', 'sort_numer': '5'}\n", "http://m.txpchain.com/product?offset=1\n", "url:http://m.txpchain.com/product?offset=1, using proxy: ***********************************************, headers:{}\n", "Error getting data: HTTPConnectionPool(host='*************', port=44070): Max retries exceeded with url: http://m.txpchain.com/product?offset=1 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x104732020>, 'Connection to ************* timed out. (connect timeout=10)')), url:http://m.txpchain.com/product?offset=1, proxy used:{'http': '***********************************************'}\n", "url:http://m.txpchain.com/product?offset=1, using proxy: http://18258841203:8gTcEKLs@***************:37108, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:37108'}\n", "{'id': '582', 'name': '安佳大黄油5kg', 'title': '安佳大黄油5kg', 'price_unit': '块', 'product_price': '346.00', 'market_price': '0.00', 'spec': '5kg', 'img_url': 'http://static.txpchain.com/file/2018/06/28/bznaid5r3f8cb1lq.jpg', 'bname': '安佳', 'sort_numer': '421'}\n", "http://m.txpchain.com/product?offset=2\n", "url:http://m.txpchain.com/product?offset=2, using proxy: http://18258841203:8gTcEKLs@***************:37108, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:37108'}\n", "{'id': '626', 'name': '美玫低筋粉（纸袋）', 'title': '美玫低筋粉', 'price_unit': '袋', 'product_price': '163.00', 'market_price': '0.00', 'spec': '25kg', 'img_url': 'http://static.txpchain.com/file/2022/10/18/way0ajafek0fil3n.jpg', 'bname': '美玫', 'sort_numer': '471'}\n", "http://m.txpchain.com/product?offset=3\n", "url:http://m.txpchain.com/product?offset=3, using proxy: ***********************************************, headers:{}\n", "Error getting data: HTTPConnectionPool(host='*************', port=44070): Max retries exceeded with url: http://m.txpchain.com/product?offset=3 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x104732140>, 'Connection to ************* timed out. (connect timeout=10)')), url:http://m.txpchain.com/product?offset=3, proxy used:{'http': '***********************************************'}\n", "url:http://m.txpchain.com/product?offset=3, using proxy: http://18258841203:8gTcEKLs@*************:45190, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@*************:45190'}\n", "{'id': '818', 'name': '黑标烘炒咖啡豆1kg', 'title': '黑标烘炒咖啡豆1kg/袋', 'price_unit': '袋', 'product_price': '120.00', 'market_price': '0.00', 'spec': '', 'img_url': 'http://static.txpchain.com/file/2020/03/27/ylxe3c1j94b1t8fv.jpg', 'bname': None, 'sort_numer': '509'}\n", "http://m.txpchain.com/product?offset=4\n", "url:http://m.txpchain.com/product?offset=4, using proxy: *************************************************, headers:{}\n", "response status:200, proxy used:{'http': '*************************************************'}\n", "{'id': '390', 'name': '南侨无水酥油', 'title': '南侨无水酥油  ', 'price_unit': '桶', 'product_price': '319.00', 'market_price': '0.00', 'spec': '16kg/桶', 'img_url': 'http://static.txpchain.com/file/2018/11/06/ot5xw71o6imb6g5p.jpg', 'bname': '南侨', 'sort_numer': '526'}\n", "http://m.txpchain.com/product?offset=5\n", "url:http://m.txpchain.com/product?offset=5, using proxy: *************************************************, headers:{}\n", "response status:200, proxy used:{'http': '*************************************************'}\n", "{'id': '711', 'name': '普利欧冷冻蛋糕－经典黑森林', 'title': '普利欧经典黑森林', 'price_unit': '盒', 'product_price': '75.00', 'market_price': '0.00', 'spec': '72g*10片/8英寸', 'img_url': 'http://static.txpchain.com/file/2019/05/22/yuka96ghoz49x98f.png', 'bname': '普利欧', 'sort_numer': '553'}\n", "http://m.txpchain.com/product?offset=6\n", "url:http://m.txpchain.com/product?offset=6, using proxy: **********************************************, headers:{}\n", "response status:200, proxy used:{'http': '**********************************************'}\n", "{'id': '738', 'name': '奥昆速冻葡式207蛋挞皮', 'title': '奥昆速冻葡式207蛋挞皮', 'price_unit': '箱', 'product_price': '116.00', 'market_price': '0.00', 'spec': '30个/袋*10袋', 'img_url': 'http://static.txpchain.com/file/2022/09/28/x5p05ei1oppgau2r.jpg', 'bname': None, 'sort_numer': '574'}\n", "http://m.txpchain.com/product?offset=7\n", "url:http://m.txpchain.com/product?offset=7, using proxy: http://18258841203:8gTcEKLs@***************:39296, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:39296'}\n", "{'id': '750', 'name': '明治保加利亚式凝固型酸奶 低脂清甜原味（需预定）', 'title': '明治meiji 保加利亚式凝固型酸奶 低脂清甜原味100g*48瓶', 'price_unit': '箱', 'product_price': '212.00', 'market_price': '0.00', 'spec': '100g*48瓶', 'img_url': 'http://static.txpchain.com/file/2019/06/28/83ppagxccjq9b11w.png', 'bname': '明治', 'sort_numer': '598'}\n", "http://m.txpchain.com/product?offset=8\n", "url:http://m.txpchain.com/product?offset=8, using proxy: *************************************************, headers:{}\n", "response status:200, proxy used:{'http': '*************************************************'}\n", "{'id': '745', 'name': '普利欧冷冻蛋糕－芒果芝士魔方', 'title': '普利欧冷冻蛋糕－芒果芝士魔方60粒', 'price_unit': '盒', 'product_price': '78.00', 'market_price': '0.00', 'spec': '60粒', 'img_url': 'http://static.txpchain.com/file/2019/06/13/0b1w3eimc2y5h2o9.png', 'bname': '普利欧', 'sort_numer': '618'}\n", "http://m.txpchain.com/product?offset=9\n", "url:http://m.txpchain.com/product?offset=9, using proxy: **********************************************, headers:{}\n", "response status:200, proxy used:{'http': '**********************************************'}\n", "{'id': '807', 'name': '阳光大豆油（烘焙专用）', 'title': '阳光大豆油（烘焙专用）10L/桶', 'price_unit': '桶', 'product_price': '99.00', 'market_price': '0.00', 'spec': '10L', 'img_url': 'http://static.txpchain.com/file/2020/03/10/nl9pmn5obfk9r1xo.jpg', 'bname': '阳光', 'sort_numer': '643'}\n", "http://m.txpchain.com/product?offset=10\n", "url:http://m.txpchain.com/product?offset=10, using proxy: http://18258841203:8gTcEKLs@***************:37108, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:37108'}\n", "{'id': '836', 'name': '奥昆老婆饼', 'title': '奥昆老婆饼40g*260个/箱', 'price_unit': '箱', 'product_price': '165.00', 'market_price': '0.00', 'spec': '40g*260个', 'img_url': 'http://static.txpchain.com/file/2020/05/04/90cesm9i07tv3pc2.png', 'bname': '奥昆', 'sort_numer': '672'}\n", "http://m.txpchain.com/product?offset=11\n", "url:http://m.txpchain.com/product?offset=11, using proxy: *************************************************, headers:{}\n", "response status:200, proxy used:{'http': '*************************************************'}\n", "{'id': '905', 'name': '安佳再制切达干酪（84片）米色白片单包', 'title': '安佳再制切达干酪（84片）白片', 'price_unit': '包', 'product_price': '63.00', 'market_price': '0.00', 'spec': '1.04kg', 'img_url': 'http://static.txpchain.com/file/2020/12/08/idvbauf9qs09sb11.jpg', 'bname': '安佳', 'sort_numer': '722'}\n", "http://m.txpchain.com/product?offset=12\n", "url:http://m.txpchain.com/product?offset=12, using proxy: *************************************************, headers:{}\n", "response status:200, proxy used:{'http': '*************************************************'}\n", "{'id': '922', 'name': '奥昆好禧坊提拉米苏慕斯蛋糕', 'title': '奥昆好禧坊提拉米苏慕斯蛋糕', 'price_unit': '盒', 'product_price': '57.00', 'market_price': '65.00', 'spec': '10片/8英寸', 'img_url': 'http://static.txpchain.com/file/2021/01/08/rehbgmjhf55lrtc7.png', 'bname': None, 'sort_numer': '758'}\n", "http://m.txpchain.com/product?offset=13\n", "url:http://m.txpchain.com/product?offset=13, using proxy: http://18258841203:8gTcEKLs@***************:31445, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:31445'}\n", "{'id': '941', 'name': '海南香水柠檬5斤', 'title': '海南香水柠檬', 'price_unit': '组', 'product_price': '49.00', 'market_price': '0.00', 'spec': '5斤', 'img_url': 'http://static.txpchain.com/file/2021/03/12/t8w0r7atu0en9a0r.jpg', 'bname': None, 'sort_numer': '777'}\n", "http://m.txpchain.com/product?offset=14\n", "url:http://m.txpchain.com/product?offset=14, using proxy: *************************************************, headers:{}\n", "response status:200, proxy used:{'http': '*************************************************'}\n", "{'id': '965', 'name': '明治冰淇淋巧克力味4.2kg', 'title': '明治冰淇淋巧克力味4.2kg', 'price_unit': '箱', 'product_price': '166.00', 'market_price': '0.00', 'spec': '2.1kg*2盒', 'img_url': 'http://static.txpchain.com/file/2021/04/29/m0xguobq51ha4bqg.png', 'bname': None, 'sort_numer': '801'}\n", "http://m.txpchain.com/product?offset=15\n", "url:http://m.txpchain.com/product?offset=15, using proxy: http://18258841203:8gTcEKLs@*************:45190, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@*************:45190'}\n", "{'id': '1011', 'name': '普利欧（芝士三角系列）-梦幻香芋慕斯蛋糕', 'title': '普利欧芝士三角-梦幻香芋慕斯蛋糕', 'price_unit': '盒', 'product_price': '68.00', 'market_price': '0.00', 'spec': '1000g，66粒/盒', 'img_url': 'http://static.txpchain.com/file/2021/09/27/hgiclgzhf3ddtb0f.png', 'bname': None, 'sort_numer': '847'}\n", "http://m.txpchain.com/product?offset=16\n", "url:http://m.txpchain.com/product?offset=16, using proxy: http://18258841203:8gTcEKLs@***************:39296, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:39296'}\n", "{'id': '1035', 'name': '奥昆小酥芙咸香芝士味', 'title': '小酥芙咸香芝士味', 'price_unit': '箱', 'product_price': '218.00', 'market_price': '0.00', 'spec': '40个*8托', 'img_url': 'http://static.txpchain.com/file/2021/12/15/t2uqzo9ltj7ibs89.jpg', 'bname': None, 'sort_numer': '871'}\n", "http://m.txpchain.com/product?offset=17\n", "url:http://m.txpchain.com/product?offset=17, using proxy: http://18258841203:8gTcEKLs@***************:39296, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:39296'}\n", "{'id': '1085', 'name': '泰国进口泰象原味苏打水', 'title': '泰象苏打水', 'price_unit': '箱', 'product_price': '59.00', 'market_price': '66.00', 'spec': '325ml*24瓶', 'img_url': 'http://static.txpchain.com/file/2022/07/09/xazycg6sybq1ltx7.jpg', 'bname': None, 'sort_numer': '921', 'discountType': '阶梯价'}\n", "http://m.txpchain.com/product?offset=18\n", "url:http://m.txpchain.com/product?offset=18, using proxy: *************************************************, headers:{}\n", "response status:200, proxy used:{'http': '*************************************************'}\n", "{'id': '1105', 'name': '莫林绿薄荷风味糖浆700ml', 'title': '莫林绿薄荷风味糖浆700ml', 'price_unit': '瓶', 'product_price': '72.00', 'market_price': '0.00', 'spec': '700ml', 'img_url': 'http://static.txpchain.com/file/2022/08/27/f6xdb4dldv1n6g07.jpg', 'bname': None, 'sort_numer': '941'}\n", "http://m.txpchain.com/product?offset=19\n", "url:http://m.txpchain.com/product?offset=19, using proxy: http://18258841203:8gTcEKLs@***************:39296, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:39296'}\n", "{'id': '1115', 'name': '莫林玉桂风味糖浆700ml', 'title': '莫林玉桂风味糖浆700ml', 'price_unit': '瓶', 'product_price': '72.00', 'market_price': '0.00', 'spec': '700ml', 'img_url': 'http://static.txpchain.com/file/2022/08/27/5knsim2ac0q70a92.jpg', 'bname': None, 'sort_numer': '951'}\n", "http://m.txpchain.com/product?offset=20\n", "url:http://m.txpchain.com/product?offset=20, using proxy: http://18258841203:8gTcEKLs@*************:45190, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@*************:45190'}\n", "{'id': '1128', 'name': '徐香猕猴桃（绿心，4粒装）', 'title': '徐香猕猴桃', 'price_unit': '盒', 'product_price': '10.00', 'market_price': '0.00', 'spec': '4粒', 'img_url': 'http://static.txpchain.com/file/2022/09/17/ssywzgjokkp5ghsg.jpg', 'bname': None, 'sort_numer': '964'}\n", "http://m.txpchain.com/product?offset=21\n", "url:http://m.txpchain.com/product?offset=21, using proxy: http://18258841203:8gTcEKLs@***************:31445, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:31445'}\n", "{'id': '1151', 'name': '菲诺厚椰乳', 'title': '菲诺厚椰乳', 'price_unit': '箱', 'product_price': '136.00', 'market_price': '0.00', 'spec': '1L*12瓶', 'img_url': 'http://static.txpchain.com/file/2022/11/14/t3gmmaog4ge6aw2l.jpg', 'bname': None, 'sort_numer': '987', 'discountType': '阶梯价'}\n", "http://m.txpchain.com/product?offset=22\n", "url:http://m.txpchain.com/product?offset=22, using proxy: http://18258841203:8gTcEKLs@***************:31445, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:31445'}\n", "{'id': '1172', 'name': '红富士苹果（3斤）', 'title': '红富士苹果', 'price_unit': '袋', 'product_price': '25.00', 'market_price': '0.00', 'spec': '3斤', 'img_url': 'http://static.txpchain.com/file/2022/12/07/ipcw8fzh440bsx28.jpg', 'bname': None, 'sort_numer': '1008'}\n", "http://m.txpchain.com/product?offset=23\n", "url:http://m.txpchain.com/product?offset=23, using proxy: http://18258841203:8gTcEKLs@***************:37108, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:37108'}\n", "{'id': '1195', 'name': '茉莉花液', 'title': '茉莉花液', 'price_unit': '瓶', 'product_price': '60.00', 'market_price': '0.00', 'spec': '450ml', 'img_url': 'http://static.txpchain.com/file/2023/04/01/mrow6ayrpmw1zsgt.png', 'bname': None, 'sort_numer': '1031'}\n", "http://m.txpchain.com/product?offset=24\n", "url:http://m.txpchain.com/product?offset=24, using proxy: http://18258841203:8gTcEKLs@***************:39296, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:39296'}\n", "{'id': '1218', 'name': '明治白巧克力（抹茶口味）', 'title': '明治白巧克力（抹茶口味）', 'price_unit': '袋', 'product_price': '128.00', 'market_price': '0.00', 'spec': '1kg', 'img_url': 'http://static.txpchain.com/file/2023/04/14/wl5slymekmo5ia6l.png', 'bname': None, 'sort_numer': '1054'}\n", "http://m.txpchain.com/product?offset=25\n", "url:http://m.txpchain.com/product?offset=25, using proxy: http://18258841203:8gTcEKLs@***************:39296, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:39296'}\n", "{'id': '1263', 'name': 'NFC100%山楂混合汁', 'title': 'NFC100%山楂混合汁', 'price_unit': '箱', 'product_price': '79.00', 'market_price': '0.00', 'spec': '325ml*15瓶', 'img_url': 'http://static.txpchain.com/file/2023/06/20/jnsbd0ibowahetwi.jpg', 'bname': None, 'sort_numer': '1099'}\n", "http://m.txpchain.com/product?offset=26\n", "url:http://m.txpchain.com/product?offset=26, using proxy: ***********************************************, headers:{}\n", "response status:200, proxy used:{'http': '***********************************************'}\n", "{'id': '1276', 'name': '德馨珍果鲜-菠萝果汁饮料浓浆1L', 'title': '德馨珍果鲜-菠萝果汁饮料浓浆1L', 'price_unit': '瓶', 'product_price': '37.00', 'market_price': '0.00', 'spec': '1L', 'img_url': 'http://static.txpchain.com/file/2023/06/21/yk2ds766n3xd8z01.jpg', 'bname': None, 'sort_numer': '1112'}\n", "http://m.txpchain.com/product?offset=27\n", "url:http://m.txpchain.com/product?offset=27, using proxy: **********************************************, headers:{}\n", "response status:200, proxy used:{'http': '**********************************************'}\n", "{'id': '1290', 'name': '夏威夷披萨6寸', 'title': '夏威夷披萨6寸', 'price_unit': '盒', 'product_price': '8.50', 'market_price': '0.00', 'spec': '140g', 'img_url': 'http://static.txpchain.com/file/2023/07/02/olku4jqq6drb2lu7.jpg', 'bname': None, 'sort_numer': '1126', 'discountType': '阶梯价'}\n", "http://m.txpchain.com/product?offset=28\n", "url:http://m.txpchain.com/product?offset=28, using proxy: *************************************************, headers:{}\n", "response status:200, proxy used:{'http': '*************************************************'}\n", "{'id': '1312', 'name': '赣南脐橙10斤', 'title': '赣南脐橙', 'price_unit': '袋', 'product_price': '35.00', 'market_price': '0.00', 'spec': '10斤', 'img_url': 'http://static.txpchain.com/file/2023/08/03/g9wxlig7cclbm9dn.jpg', 'bname': None, 'sort_numer': '1148'}\n", "http://m.txpchain.com/product?offset=29\n", "url:http://m.txpchain.com/product?offset=29, using proxy: ************************************************, headers:{}\n", "response status:200, proxy used:{'http': '************************************************'}\n", "{'id': '1323', 'name': '小粒马卡龙混装', 'title': '小粒马卡龙混装', 'price_unit': '盒', 'product_price': '42.00', 'market_price': '0.00', 'spec': '单粒：2.6-2.8㎝，42粒，240g', 'img_url': 'http://static.txpchain.com/file/2023/08/19/260r7u7j2wqpc84s.jpg', 'bname': None, 'sort_numer': '1159'}\n", "http://m.txpchain.com/product?offset=30\n", "url:http://m.txpchain.com/product?offset=30, using proxy: ***********************************************, headers:{}\n", "response status:200, proxy used:{'http': '***********************************************'}\n", "{'id': '1336', 'name': '植物标签-醇香米乳', 'title': '植物标签-醇香米乳', 'price_unit': '箱', 'product_price': '138.00', 'market_price': '0.00', 'spec': '1kg*8瓶', 'img_url': 'http://static.txpchain.com/file/2023/10/08/ihm8gxr03t1hwdq8.png', 'bname': None, 'sort_numer': '1172', 'discountType': '阶梯价'}\n", "http://m.txpchain.com/product?offset=31\n", "url:http://m.txpchain.com/product?offset=31, using proxy: ***********************************************, headers:{}\n", "response status:200, proxy used:{'http': '***********************************************'}\n", "{'id': '1347', 'name': '荷兰进口黑白淡奶400g*6罐', 'title': '荷兰进口黑白淡奶400g*6罐', 'price_unit': '组', 'product_price': '56.00', 'market_price': '0.00', 'spec': '400g*6罐', 'img_url': 'http://static.txpchain.com/file/2023/10/17/mbw9gt8i3oskugk5.jpg', 'bname': None, 'sort_numer': '1183'}\n", "http://m.txpchain.com/product?offset=32\n", "url:http://m.txpchain.com/product?offset=32, using proxy: **********************************************, headers:{}\n", "response status:200, proxy used:{'http': '**********************************************'}\n", "{'id': '1358', 'name': '戴妃苦甜巧克力', 'title': '戴妃苦甜巧克力', 'price_unit': '块', 'product_price': '41.00', 'market_price': '0.00', 'spec': '1kg', 'img_url': 'http://static.txpchain.com/file/2023/11/03/s1wk95a9aub5lzdg.jpg', 'bname': None, 'sort_numer': '1194'}\n", "http://m.txpchain.com/product?offset=33\n", "url:http://m.txpchain.com/product?offset=33, using proxy: ***********************************************, headers:{}\n", "response status:200, proxy used:{'http': '***********************************************'}\n", "{'id': '1368', 'name': '香橙干片100g', 'title': '香橙干片', 'price_unit': '包', 'product_price': '8.80', 'market_price': '0.00', 'spec': '100g', 'img_url': 'http://static.txpchain.com/file/2023/11/03/k01p3zyl8cmumw17.jpg', 'bname': None, 'sort_numer': '1204'}\n", "http://m.txpchain.com/product?offset=34\n", "url:http://m.txpchain.com/product?offset=34, using proxy: http://18258841203:8gTcEKLs@***************:31445, headers:{}\n", "*************:34571\n", "**************:48381\n", "**************:32520\n", "**************:35118\n", "*************:44509\n", "*************:41713\n", "***************:43638\n", "**************:37258\n", "**************:43654\n", "**************:43608\n", "new proxy server:['*************:34571', '**************:48381', '**************:32520', '**************:35118', '*************:44509', '*************:41713', '***************:43638', '**************:37258', '**************:43654', '**************:43608']\n", "Error getting data: HTTPConnectionPool(host='***************', port=31445): Max retries exceeded with url: http://m.txpchain.com/product?offset=34 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x104730be0>, 'Connection to *************** timed out. (connect timeout=10)')), url:http://m.txpchain.com/product?offset=34, proxy used:{'http': 'http://18258841203:8gTcEKLs@***************:31445'}\n", "url:http://m.txpchain.com/product?offset=34, using proxy: http://18258841203:8gTcEKLs@*************:41713, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@*************:41713'}\n", "{'id': '1380', 'name': '明治草莓牛乳400ml（需预定）', 'title': '明治草莓牛乳', 'price_unit': '箱', 'product_price': '122.00', 'market_price': '0.00', 'spec': '400ml*12瓶', 'img_url': 'http://static.txpchain.com/file/2023/12/23/2aio29zgfru8kuzz.jpg', 'bname': None, 'sort_numer': '1216'}\n", "http://m.txpchain.com/product?offset=35\n", "url:http://m.txpchain.com/product?offset=35, using proxy: http://18258841203:8gTcEKLs@*************:41713, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@*************:41713'}\n", "{'id': '1390', 'name': '普利欧-莓莓粉黑芝麻芝士蛋糕', 'title': '普利欧-莓莓粉黑芝麻芝士蛋糕', 'price_unit': '盒', 'product_price': '132.00', 'market_price': '0.00', 'spec': '9英寸，12片/盒', 'img_url': 'http://static.txpchain.com/file/2024/01/03/w3owpqrs8bt8nj3k.png', 'bname': None, 'sort_numer': '1226'}\n", "http://m.txpchain.com/product?offset=36\n", "url:http://m.txpchain.com/product?offset=36, using proxy: http://18258841203:8gTcEKLs@**************:48381, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@**************:48381'}\n", "{'id': '1400', 'name': '柏札莱阿尔卑冷冻大水牛马苏里拉干酪', 'title': '柏札莱阿尔卑冷冻大水牛马苏里拉干酪', 'price_unit': '袋', 'product_price': '17.50', 'market_price': '0.00', 'spec': '100g', 'img_url': 'http://static.txpchain.com/file/2024/01/17/rm9b66ned4wfwc6s.jpg', 'bname': None, 'sort_numer': '1236'}\n", "http://m.txpchain.com/product?offset=37\n", "url:http://m.txpchain.com/product?offset=37, using proxy: http://18258841203:8gTcEKLs@*************:44509, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@*************:44509'}\n", "{'id': '1414', 'name': '屈臣氏汤力汽水', 'title': '屈臣氏汤力汽水', 'price_unit': '箱', 'product_price': '85.00', 'market_price': '0.00', 'spec': '330ml*24罐', 'img_url': 'http://static.txpchain.com/file/2024/02/28/biu5fgcgni32e2w4.jpg', 'bname': None, 'sort_numer': '1250'}\n", "http://m.txpchain.com/product?offset=38\n", "url:http://m.txpchain.com/product?offset=38, using proxy: http://18258841203:8gTcEKLs@**************:48381, headers:{}\n", "response status:200, proxy used:{'http': 'http://18258841203:8gTcEKLs@**************:48381'}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>title</th>\n", "      <th>price_unit</th>\n", "      <th>product_price</th>\n", "      <th>market_price</th>\n", "      <th>spec</th>\n", "      <th>img_url</th>\n", "      <th>bname</th>\n", "      <th>sort_numer</th>\n", "      <th>discountType</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>628</td>\n", "      <td>明治牛乳</td>\n", "      <td>明治牛乳</td>\n", "      <td>箱</td>\n", "      <td>168.00</td>\n", "      <td>174.00</td>\n", "      <td>950ml*12瓶</td>\n", "      <td>http://static.txpchain.com/file/2023/06/08/hvy...</td>\n", "      <td>明治</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>688</td>\n", "      <td>明治咖啡专用牛乳</td>\n", "      <td>咖啡乳</td>\n", "      <td>箱</td>\n", "      <td>144.00</td>\n", "      <td>0.00</td>\n", "      <td>950ml*12瓶</td>\n", "      <td>http://static.txpchain.com/file/2022/01/08/vr9...</td>\n", "      <td>None</td>\n", "      <td>6</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>585</td>\n", "      <td>明治60%特纯黑巧克力</td>\n", "      <td>明治60%特纯黑巧克力</td>\n", "      <td>包</td>\n", "      <td>96.00</td>\n", "      <td>0.00</td>\n", "      <td>1kg</td>\n", "      <td>http://static.txpchain.com/file/2023/10/15/hoi...</td>\n", "      <td>None</td>\n", "      <td>9</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>392</td>\n", "      <td>安佳淡奶油</td>\n", "      <td>安佳淡奶油</td>\n", "      <td>箱</td>\n", "      <td>536.00</td>\n", "      <td>0.00</td>\n", "      <td>1L*12瓶</td>\n", "      <td>http://static.txpchain.com/file/2018/06/28/oq7...</td>\n", "      <td>安佳</td>\n", "      <td>19</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>437</td>\n", "      <td>铁塔动物淡奶油</td>\n", "      <td>铁塔淡奶油</td>\n", "      <td>箱</td>\n", "      <td>567.00</td>\n", "      <td>0.00</td>\n", "      <td>1L*12瓶</td>\n", "      <td>http://static.txpchain.com/file/2018/09/25/af2...</td>\n", "      <td>None</td>\n", "      <td>23</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>769</td>\n", "      <td>oatly（欧力）咖啡大师燕麦奶</td>\n", "      <td>oatly（欧力）咖啡大师燕麦奶1L*6</td>\n", "      <td>箱</td>\n", "      <td>108.00</td>\n", "      <td>120.00</td>\n", "      <td>1L*6瓶</td>\n", "      <td>http://static.txpchain.com/file/2019/09/19/6a3...</td>\n", "      <td>欧力</td>\n", "      <td>33</td>\n", "      <td>阶梯价</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>815</td>\n", "      <td>蓝风车淡奶油</td>\n", "      <td>蓝风车淡奶油1L*12/箱</td>\n", "      <td>箱</td>\n", "      <td>672.00</td>\n", "      <td>0.00</td>\n", "      <td>1L*12盒</td>\n", "      <td>http://static.txpchain.com/file/2021/09/26/4ja...</td>\n", "      <td>南侨</td>\n", "      <td>38</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>691</td>\n", "      <td>日清紫罗兰低筋小麦粉</td>\n", "      <td>日清紫罗兰低筋小麦粉25kg</td>\n", "      <td>袋</td>\n", "      <td>316.00</td>\n", "      <td>0.00</td>\n", "      <td>25kg</td>\n", "      <td>http://static.txpchain.com/file/2020/11/24/n0w...</td>\n", "      <td>日清</td>\n", "      <td>116</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>690</td>\n", "      <td>日清山茶花高筋小麦粉</td>\n", "      <td>日清山茶花高筋小麦粉25kg</td>\n", "      <td>袋</td>\n", "      <td>316.00</td>\n", "      <td>0.00</td>\n", "      <td>25kg</td>\n", "      <td>http://static.txpchain.com/file/2020/11/24/bf6...</td>\n", "      <td>日清</td>\n", "      <td>137</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>206</td>\n", "      <td>全脂奶粉</td>\n", "      <td>全脂奶粉</td>\n", "      <td>袋</td>\n", "      <td>750.00</td>\n", "      <td>0.00</td>\n", "      <td>25kg</td>\n", "      <td>http://static.txpchain.com/file/2020/06/01/7kt...</td>\n", "      <td>恒天然</td>\n", "      <td>357</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id              name                 title price_unit product_price  \\\n", "0  628              明治牛乳                  明治牛乳          箱        168.00   \n", "1  688          明治咖啡专用牛乳                   咖啡乳          箱        144.00   \n", "2  585       明治60%特纯黑巧克力           明治60%特纯黑巧克力          包         96.00   \n", "3  392             安佳淡奶油                 安佳淡奶油          箱        536.00   \n", "4  437           铁塔动物淡奶油                铁塔淡奶油           箱        567.00   \n", "5  769  oatly（欧力）咖啡大师燕麦奶  oatly（欧力）咖啡大师燕麦奶1L*6          箱        108.00   \n", "6  815            蓝风车淡奶油         蓝风车淡奶油1L*12/箱          箱        672.00   \n", "7  691        日清紫罗兰低筋小麦粉        日清紫罗兰低筋小麦粉25kg          袋        316.00   \n", "8  690        日清山茶花高筋小麦粉        日清山茶花高筋小麦粉25kg          袋        316.00   \n", "9  206              全脂奶粉                  全脂奶粉          袋        750.00   \n", "\n", "  market_price       spec                                            img_url  \\\n", "0       174.00  950ml*12瓶  http://static.txpchain.com/file/2023/06/08/hvy...   \n", "1         0.00  950ml*12瓶  http://static.txpchain.com/file/2022/01/08/vr9...   \n", "2         0.00        1kg  http://static.txpchain.com/file/2023/10/15/hoi...   \n", "3         0.00     1L*12瓶  http://static.txpchain.com/file/2018/06/28/oq7...   \n", "4         0.00     1L*12瓶  http://static.txpchain.com/file/2018/09/25/af2...   \n", "5       120.00      1L*6瓶  http://static.txpchain.com/file/2019/09/19/6a3...   \n", "6         0.00     1L*12盒  http://static.txpchain.com/file/2021/09/26/4ja...   \n", "7         0.00       25kg  http://static.txpchain.com/file/2020/11/24/n0w...   \n", "8         0.00       25kg  http://static.txpchain.com/file/2020/11/24/bf6...   \n", "9         0.00       25kg  http://static.txpchain.com/file/2020/06/01/7kt...   \n", "\n", "  bname sort_numer discountType  \n", "0    明治          5          NaN  \n", "1  None          6          NaN  \n", "2  None          9          NaN  \n", "3    安佳         19          NaN  \n", "4  None         23          NaN  \n", "5    欧力         33          阶梯价  \n", "6    南侨         38          NaN  \n", "7    日清        116          NaN  \n", "8    日清        137          NaN  \n", "9   恒天然        357          NaN  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["product_list_all=[]\n", "for offset in range(-1,50):\n", "    if offset==0:\n", "        continue\n", "    url=f'http://m.txpchain.com/product?offset={offset}'\n", "    print(url)\n", "    product_list=get_remote_data_with_proxy_json(url)\n", "    if product_list is None or len(product_list)<=0:\n", "        break\n", "    else:\n", "        print(product_list[0])\n", "        product_list_all.extend(product_list)\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["表不存在:summerfarm_ds.spider_tianpin_product_result_df\n", "成功写入odps:summerfarm_ds.spider_tianpin_product_result_df, partition_spec:ds=20240229,competitor_name=tianpin, attemp:0\n", "sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_tianpin_product_result_df\n", "                             where ds>='20240130' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240229</td>\n", "      <td>tianpin</td>\n", "      <td>378</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ds competitor_name  recods\n", "0  20240229         tianpin     378"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "product_list_all_df['competitor']=brand_name\n", "all_products_df=product_list_all_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
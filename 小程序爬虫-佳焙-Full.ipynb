{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-08 09:57:30, date_of_now:2024-03-08, brand_name:佳焙, headers:{'uniacid': '2595', 'appType': 'mini', 'Referer': 'https://servicewechat.com/wx92c8f2cd458916b5/36/page-frame.html'}\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/佳焙/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "\n", "headers={'uniacid':'2595','appType':'mini','Referer':'https://servicewechat.com/wx92c8f2cd458916b5/36/page-frame.html',}\n", "brand_name='佳焙'\n", "competitor_name_en='jiabei'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, headers:{headers}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**************:42040\n", "**************:40274\n", "*************:31908\n", "************:34097\n", "*************:32860\n", "**************:46421\n", "***************:49323\n", "***************:45181\n", "*************:48912\n", "*************:49460\n", "['**************:42040', '**************:40274', '*************:31908', '************:34097', '*************:32860', '**************:46421', '***************:49323', '***************:45181', '*************:48912', '*************:49460']\n"]}], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url, headers, cookies):\n", "    max_retries=3;\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, data=None, headers=headers, proxies=proxies, cookies=cookies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None\n", "def post_remote_data_with_proxy(url, data, headers):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, data=data, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["根据一级、二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: *************************************************\n"]}, {"data": {"text/plain": ["{'rt': -23, 'success': False, 'msg': 'no login', 'noMemberAuth': True}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["cookies={'_siteStatVisitorType':'visitorType_28384605',\n", "'_siteStatId':'67323e1c-a023-4175-be4a-214696daf9aa',\n", "'_siteStatDay':'20240222',\n", "'_siteStatRedirectUv':'redirectUv_28384605',\n", "'_filterVisitTime':'fehfhklmjsts',\n", "'loginIntegralTip283846051191':'true',\n", "'lastLoginTime283846051191':'2024-02-22',\n", "'_FSESSIONID':'s_7JPiOQA7JnQkMm',\n", "'loginMemberAcct':'xcx_LV9NRvzKUoSk60',\n", "'_siteStatReVisit':'reVisit_28384605',\n", "'_siteStatVisit':'visit_28384605',\n", "'_siteStatVisitTime':'1708580460796',\n", "'_cliid':'f4-jnyO454huOSJb',\n", "'_FSESSIONID':'s_7JPiOQA7JnQkMm',}\n", "\n", "cookies={'_siteStatVisitorType':'visitorType_28384605',\n", "'_siteStatId':'67323e1c-a023-4175-be4a-214696daf9aa',\n", "'_siteStatDay':'20240222',\n", "'_siteStatRedirectUv':'redirectUv_28384605',\n", "'_filterVisitTime':'fehfhklmjsts',\n", "'loginIntegralTip283846051191':'true',\n", "'lastLoginTime283846051191':'2024-03-08',\n", "'_FSESSIONID':'s_7JPiOQA7JnQkMm',\n", "'loginMemberAcct':'xcx_LV9NRvzKUoSk60',\n", "'_siteStatReVisit':'reVisit_28384605',\n", "'_siteStatVisit':'visit_28384605',\n", "'_siteStatVisitTime':'1708580460796',\n", "'_cliid':'f4-jnyO454huOSJb',\n", "'_FSESSIONID':'s_7JPiOQA7JnQkMm',}\n", "\n", "\n", "url=\"https://wx5.jzapp.yswebportal.cc/28384605/1//api/guest/col/getModuleDataFromColV3?isFrom=mp-weixin&colId=19&posCtrl=\"\n", "response=json.loads(get_remote_data_with_proxy(url=url, headers=headers, cookies=cookies).text)\n", "response\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'moduleList'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 8\u001b[0m\n\u001b[0;32m      5\u001b[0m     goods\u001b[38;5;241m=\u001b[39mgoods[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mrtPdList\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m      6\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m goods\n\u001b[1;32m----> 8\u001b[0m moduleList\u001b[38;5;241m=\u001b[39m\u001b[43mresponse\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mmoduleList\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[0;32m      9\u001b[0m catelist\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m     10\u001b[0m product_list_all\u001b[38;5;241m=\u001b[39m[]\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'moduleList'"]}], "source": ["def get_products_by_category_id(groupId=9):\n", "    url=f'https://wx5.jzapp.yswebportal.cc/28384605/1//api/guest/product/getProductByGroupV2?isFrom=mp-weixin&groupId={groupId}'+'&st=desc&sn=sales&size=200&sc=[\"pdName\",\"mallPrice\",\"marketingPrice\",\"pdProp\",\"saleNum\",\"stock\"]&lng=&lat=&selfTakeId=0&merchantId=0&addrInfo={\"prc\":\"\",\"cic\":\"\",\"coc\":\"\"}'\n", "    goods=json.loads(get_remote_data_with_proxy(url=url, headers=headers, cookies=cookies).text);\n", "    print(goods)\n", "    goods=goods['rtPdList']\n", "    return goods\n", "\n", "moduleList=response['moduleList']\n", "catelist=None\n", "product_list_all=[]\n", "for module in moduleList:\n", "    if 334== module['id']:\n", "        print(module)\n", "        catelist=module['content']['pclGroup']['level1']\n", "        print(catelist)\n", "        for cate in catelist:\n", "            product_list_all.extend(get_products_by_category_id(cate['ci']))\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df.head(5)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>mallPrice</th>\n", "      <th>lid</th>\n", "      <th>addedTime</th>\n", "      <th>sales</th>\n", "      <th>mallAmount</th>\n", "      <th>top</th>\n", "      <th>isTimedAdded</th>\n", "      <th>realPrice</th>\n", "      <th>...</th>\n", "      <th>max<PERSON><PERSON></th>\n", "      <th>minAmount</th>\n", "      <th>existOptionAmount</th>\n", "      <th>vipName</th>\n", "      <th>enableAmount</th>\n", "      <th>enableSales</th>\n", "      <th>enableMarketPrice</th>\n", "      <th>enablePromotion</th>\n", "      <th>props</th>\n", "      <th>isProductAuthHiddenPrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>589</td>\n", "      <td>琪雷萨马斯卡彭芝士500g*6盒 整箱</td>\n", "      <td>295.0</td>\n", "      <td>1</td>\n", "      <td>1639984217000</td>\n", "      <td>917</td>\n", "      <td>10</td>\n", "      <td>1705499080</td>\n", "      <td>False</td>\n", "      <td>280.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>会员价</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>[]</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>294</td>\n", "      <td>安佳淡奶油1L/瓶整箱12瓶</td>\n", "      <td>595.0</td>\n", "      <td>1</td>\n", "      <td>1639984217000</td>\n", "      <td>4951</td>\n", "      <td>960</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>515.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>会员价</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>[]</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>230</td>\n", "      <td>铁塔淡奶油1L/瓶整箱12瓶</td>\n", "      <td>620.0</td>\n", "      <td>1</td>\n", "      <td>1639984217000</td>\n", "      <td>4105</td>\n", "      <td>9691</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>568.0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>会员价</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>[]</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 31 columns</p>\n", "</div>"], "text/plain": ["    id                 name  mallPrice  lid      addedTime  sales  mallAmount  \\\n", "0  589  琪雷萨马斯卡彭芝士500g*6盒 整箱      295.0    1  1639984217000    917          10   \n", "1  294       安佳淡奶油1L/瓶整箱12瓶      595.0    1  1639984217000   4951         960   \n", "2  230       铁塔淡奶油1L/瓶整箱12瓶      620.0    1  1639984217000   4105        9691   \n", "\n", "          top  isTimedAdded  realPrice  ...  maxAmount minAmount  \\\n", "0  1705499080         False      280.0  ...          0         1   \n", "1           0         False      515.0  ...          0         1   \n", "2           0         False      568.0  ...          0         1   \n", "\n", "   existOptionAmount vipName  enableAmount enableSales enableMarketPrice  \\\n", "0              False     会员价          True        True              True   \n", "1              False     会员价          True        True              True   \n", "2              False     会员价          True        True              True   \n", "\n", "   enablePromotion  props isProductAuthHiddenPrice  \n", "0            False     []                    False  \n", "1            False     []                    False  \n", "2            False     []                    False  \n", "\n", "[3 rows x 31 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(product_list_all_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:琪雷萨马斯卡彭芝士500g*6盒 整箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:760.1454257965088ms\n", "inputText:安佳淡奶油1L/瓶整箱12瓶, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:703.7873268127441ms\n", "inputText:铁塔淡奶油1L/瓶整箱12瓶, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:891.2839889526367ms\n", "inputText:蓝风车淡奶油1L/瓶 整箱12瓶, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:681.4839839935303ms\n", "inputText:琪雷萨马斯卡彭布尼500g圆盒, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:704.521894454956ms\n", "inputText:安佳奶油奶酪1kg 奶油芝士 芝士蛋糕, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:560.5769157409668ms\n", "inputText:澳醇牧场马苏里拉芝士碎3kg*4包 整箱 新老包装随机发, usage:{\"prompt_tokens\": 37, \"total_tokens\": 37}, time cost:880.2704811096191ms\n", "inputText:雀巢全脂调制奶粉500g/包, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:569.4770812988281ms\n", "inputText:法国进口凯瑞kiri奶油奶酪1kg , usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:862.4732494354248ms\n", "inputText:安佳奶油奶酪1kg整箱12盒, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:669.4576740264893ms\n", "inputText:安佳马苏里拉芝士碎1KG, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:713.7258052825928ms\n", "inputText:安佳淡味黄油 大黄油5kg, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:815.4661655426025ms\n", "inputText:伊利环球甄选淡奶油1L/瓶整箱12瓶（原牧恩淡奶油）, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:566.9388771057129ms\n", "inputText:爱真38淡奶油1L整箱12瓶, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:631.3526630401611ms\n", "inputText:肯迪雅淡奶油1L 整箱12瓶, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:552.5956153869629ms\n", "inputText:雀巢炼乳 鹰唛炼奶 350g 整箱48瓶, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:1963.7689590454102ms\n", "inputText:伊利东方灵感淡奶油1L 整箱6瓶, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:1461.0717296600342ms\n", "inputText:进口安佳奶油芝士5kg奶油奶酪干酪乳酪蛋糕提拉米苏烘焙商用原料, usage:{\"prompt_tokens\": 54, \"total_tokens\": 54}, time cost:790.4884815216064ms\n", "inputText:莫奈花园全脂牛奶1L, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:741.1811351776123ms\n", "inputText:辛尼琪马斯卡彭 500g*6盒  整箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:618.5047626495361ms\n", "inputText:美蒂雅乳脂植脂奶油1L 12瓶, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:646.0819244384766ms\n", "inputText:安佳淡奶油1L/瓶, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:618.7326908111572ms\n", "inputText:安佳马苏里拉芝士碎12kg 新款 老款, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:1015.0341987609863ms\n", "inputText:安佳奶油奶酪芝士5KG*4块整箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:850.2018451690674ms\n", "inputText:荷兰进口黑白淡奶400g整箱48瓶, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:653.5613536834717ms\n", "inputText:澳醇牧场马苏里拉芝士碎3kg, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:704.387903213501ms\n", "inputText:乔艺800侨艺淡奶油1L 整箱12瓶, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:997.4167346954346ms\n", "inputText:琪雷萨意大利进口方盒马斯卡彭500g*1盒, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:861.7494106292725ms\n", "inputText:维益金钻含乳脂奶油 907g  整箱12瓶, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:684.180736541748ms\n", "inputText:妙可蓝多车达芝士橙片984g整箱8包, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:616.8911457061768ms\n", "inputText:辛尼琪马斯卡彭 500g/盒, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:538.1736755371094ms\n", "inputText:法瑞芙无盐动物黄油25kg蛋糕甜品烘焙原料商用家用 大包装, usage:{\"prompt_tokens\": 41, \"total_tokens\": 41}, time cost:847.0785617828369ms\n", "inputText:妙可蓝多马斯卡彭500g , usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:658.4277153015137ms\n", "inputText:金钻甜点植物淡奶油1L/瓶, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:826.7974853515625ms\n", "inputText:伊利环球甄选牧恩淡味黄油454g/块, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:653.599739074707ms\n", "inputText:妙可蓝多马斯卡彭500g  整箱6盒, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:1113.56782913208ms\n", "inputText:法瑞芙无盐动物黄油5kg做蛋糕烘焙原料商用煎牛排家用大包装, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:567.4347877502441ms\n", "inputText:新西兰全脂奶粉25kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:714.4010066986084ms\n", "inputText:安佳再制干酪片奶酪片84片1040g 米色色, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:815.2055740356445ms\n", "inputText:铁塔淡奶油1L, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:882.0650577545166ms\n", "inputText:妙可蓝多奶油芝士2kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:697.8991031646729ms\n", "inputText:蓝风车淡奶油1L/瓶, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:793.6062812805176ms\n", "inputText:妙可蓝多马苏里拉芝士碎450g, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:567.9728984832764ms\n", "inputText:澳大利亚进口大利年奶油芝士2kg/块, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:764.0411853790283ms\n", "inputText:奈特兰淡味动物黄油 454g/块, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:555.4308891296387ms\n", "inputText:安佳黄油块454g, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:542.0963764190674ms\n", "inputText:新西兰全脂奶粉分装 500g/包, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:703.2935619354248ms\n", "inputText:熊猫牌调制甜炼乳 350g整箱48瓶, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:591.808557510376ms\n", "inputText:琪雷萨意大利进口方盒马斯卡彭500g*6盒, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:584.2728614807129ms\n", "inputText:安佳大黄油25kg 安佳黄油 大牛油, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:542.8779125213623ms\n", "inputText:妙可蓝多马苏里拉芝士碎3kg , usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:592.9534435272217ms\n", "inputText:维益和牧动植脂混合奶油 907g/瓶 整箱12瓶, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:722.3057746887207ms\n", "inputText:总统淡味黄油卷1kg , usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:591.4685726165771ms\n", "inputText:琪雷萨 动物性淡奶油1L*12盒  整箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:648.850679397583ms\n", "inputText:安佳再制干酪片奶酪片84片1040g 橙色, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:771.8520164489746ms\n", "inputText:卡夫芝士粉85g, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:747.3952770233154ms\n", "inputText:伊利环球甄选淡奶油1L/瓶（原牧恩奶油）, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:738.3790016174316ms\n", "inputText:妙可蓝多马苏里拉芝士碎3kg *4包 整箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:679.6779632568359ms\n", "inputText:爱真35淡奶油1L 整箱12瓶, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:548.5210418701172ms\n", "inputText:总统黄油卷250g, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:570.8365440368652ms\n", "inputText:维益奶油味布蕾布丁420g*24盒 整箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:1370.56565284729ms\n", "inputText:新西兰进口恩蓓可淡味黄油454g/块, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:767.6618099212646ms\n", "inputText:南侨烤焙奶油10kg/箱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:707.9837322235107ms\n", "inputText:南桥液态酥油20kg/桶, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:663.9754772186279ms\n", "inputText:MG奶油奶酪2kg 整箱6盒, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:546.1900234222412ms\n", "inputText:法国总统淡奶油6瓶*1L, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:689.5372867584229ms\n", "inputText:琪雷萨 动物性淡奶油1L*1盒, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:797.30224609375ms\n", "inputText:妙可蓝多纯牛奶全脂牛奶1L*12盒整箱 成人早餐烘焙茶饮酸奶用, usage:{\"prompt_tokens\": 53, \"total_tokens\": 53}, time cost:674.9353408813477ms\n", "inputText:雀巢全脂奶粉500g 整箱24包, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:640.0480270385742ms\n", "inputText:南侨维佳液态20kg, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:569.9739456176758ms\n", "inputText:澳洲EVA无盐大黄油 25kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:604.4201850891113ms\n", "inputText:南侨丹麦面包专用油10kg /箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:718.5254096984863ms\n", "inputText:南侨王牌玛琪琳10kg/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:590.2841091156006ms\n", "inputText:澳醇芝士片12片200g/包, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:678.6403656005859ms\n", "inputText:南侨片状甜奶油炼乳味10kg, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:678.3902645111084ms\n", "inputText:南侨玉峰牌高级雪白乳化油15kg, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:588.5505676269531ms\n", "inputText:堡兰特黄油植物黄油500g, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:673.8057136535645ms\n", "inputText:维益马斯卡彭风味淡奶油1L*1瓶  整箱12瓶, usage:{\"prompt_tokens\": 37, \"total_tokens\": 37}, time cost:735.1830005645752ms\n", "inputText:妙可蓝多车达芝士白片984g整箱8包, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:734.1659069061279ms\n", "inputText:南侨维佳夹心奶油10kg/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:677.8817176818848ms\n", "inputText:阿斯图利雅淡奶油1L, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:881.0362815856934ms\n", "inputText:阿斯图利雅淡奶油1L*6瓶 整箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:923.9258766174316ms\n", "inputText:百吉福马苏里拉芝士碎  3kg/包, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:653.7110805511475ms\n", "inputText:妙可蓝多奶油芝士240g, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:776.9036293029785ms\n", "inputText:安佳有盐黄油227g/块, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:604.4180393218994ms\n", "inputText:南侨固态酥油16kg 铁桶, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:614.0596866607666ms\n", "inputText:OCD马苏里拉芝士碎 3kg/包  整箱4包, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:571.967601776123ms\n", "cache matched:安佳有盐黄油227g/块\n", "inputText:安佳马苏里拉芝士块10kg 整箱2块, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:543.8046455383301ms\n", "inputText:新悦纯牧淡奶油1L 整箱12瓶, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:569.6260929107666ms\n", "inputText:法国总统无盐发酵黄油10kg动物性淡味大黄油西餐甜点烘焙原料商用, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:556.0133457183838ms\n", "inputText:妙可蓝多高熔点奶酪丁3kg耐高温大粒再制干酪面包夹心芝士粒烘焙, usage:{\"prompt_tokens\": 53, \"total_tokens\": 53}, time cost:564.6843910217285ms\n", "inputText:百吉福马苏里拉芝士碎3kg*4包 整箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:666.5232181549072ms\n", "inputText:总统黄油  200g/块, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:707.6170444488525ms\n", "inputText:易小焙黄油400g/盒, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:591.8588638305664ms\n", "inputText:达诺全脂烘焙奶粉100g, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:555.9766292572021ms\n", "inputText:安佳淡味小黄油粒7克 288粒/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:702.12721824646ms\n", "inputText:韩国幼砂糖30kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:558.3558082580566ms\n", "inputText:王后低筋面粉25kg  蛋糕粉, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:664.4201278686523ms\n", "inputText:三象牌水磨糯米粉500g, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:574.4500160217285ms\n", "inputText:王后高筋面粉25kg 面包粉, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:610.6593608856201ms\n", "inputText:家乐玉米淀粉鹰粟粉1kg, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:793.1811809539795ms\n", "inputText:日清山茶花面粉25kg 高筋面粉, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:587.7630710601807ms\n", "inputText: 新良面包粉高筋粉500g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:533.069372177124ms\n", "inputText:美玫低筋面粉22.7kg 布袋, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:578.1428813934326ms\n", "inputText:南顺金象a面粉22.7kg  布袋 面包粉, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:729.6264171600342ms\n", "inputText:日清紫罗兰面粉25kg 低筋面粉, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:651.9269943237305ms\n", "inputText:科麦防潮糖粉1kg 防潮糖霜 , usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:665.3232574462891ms\n", "inputText:新良中筋粉500g, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:904.5853614807129ms\n", "inputText:科麦超软麻薯面包预拌粉 5kg/包 麻糬面包预拌粉 烘焙原料, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:599.4420051574707ms\n", "inputText:维益泡芙预拌粉2.5kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:792.952299118042ms\n", "inputText:南顺金象b25kg 面包粉, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:610.5332374572754ms\n", "inputText:韩国幼砂糖分装  1.5kg/包 3kg/包, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:558.3367347717285ms\n", "inputText:包邮韩国清净园糖稀糖浆麦芽糖玉米糖浆水怡果糖牛轧糖用烘焙原料, usage:{\"prompt_tokens\": 59, \"total_tokens\": 59}, time cost:551.8531799316406ms\n", "inputText:水妈妈木薯粉500g, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:604.9039363861084ms\n", "inputText:泰国进口三象粘米粉500g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:741.8749332427979ms\n", "inputText:舒可曼防潮糖粉100g/包, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:677.1266460418701ms\n", "inputText:太古一级糖霜糖粉1kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:992.0077323913574ms\n", "inputText:新良玉米淀粉200g, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:654.9234390258789ms\n", "inputText:太古糖霜蓝标糖粉13.62kg   幼滑糖霜, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:568.5067176818848ms\n", "inputText:易小焙雪媚娘预拌粉 200g/包, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:584.099292755127ms\n", "inputText:新良魔堡蛋糕粉 低筋粉 2.5kg, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:654.6792984008789ms\n", "inputText:新良低筋面粉500g, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:691.4470195770264ms\n", "inputText:福加德33全优面粉 25kg 面包粉, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:616.5640354156494ms\n", "inputText:焙考林防潮糖粉1kg/包, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:595.3412055969238ms\n", "inputText:新良澄面小麦淀粉  1kg/包, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:812.4794960021973ms\n", "inputText:浪辰防潮糖粉500g/包, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:560.2996349334717ms\n", "inputText:山东恒仁原装食用玉米淀粉生粉25kg 豆花豆腐 烧菜 饼干可用, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:578.127384185791ms\n", "inputText:清净园玉米糖浆700g, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:816.3833618164062ms\n", "inputText:焙之玺焙尔特泡芙预拌粉卡仕达粉全麦面包海绵红丝绒牛奶蛋糕原料, usage:{\"prompt_tokens\": 52, \"total_tokens\": 52}, time cost:718.2879447937012ms\n", "inputText:焙小姐冰皮月饼预拌粉 300g/包, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:644.4635391235352ms\n", "inputText:法国乐斯福师傅300 燕子牌面包改良剂300g 改善口感 原包装乐斯福, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:806.5705299377441ms\n", "inputText:新良全麦粉 5kg/包, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:610.9323501586914ms\n", "inputText:易小焙冰皮月饼粉300g制作材料, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:1154.2325019836426ms\n", "inputText:舒可曼糖霜250g, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:772.2644805908203ms\n", "inputText:新良木薯粉200g, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:610.4803085327148ms\n", "inputText:易小焙广东肠粉专用粉 260g/包, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:733.452320098877ms\n", "inputText:新良豌豆淀粉 500g/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:603.6922931671143ms\n", "inputText:伯爵T65高筋粉25kg, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:558.0642223358154ms\n", "inputText:新良粘米粉  500g/包, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:774.8711109161377ms\n", "inputText:新良大师系列硬红高筋面粉  1kg/包, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:600.104570388794ms\n", "inputText:麦穗牌麦芽糖稀230g , usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:882.457971572876ms\n", "inputText:易小焙泡芙预拌粉 200g/包, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:529.749870300293ms\n", "inputText:新良大师系列软白低筋面粉  1kg/包, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:546.6780662536621ms\n", "inputText:新良黑全麦粉500g/包  适用于馒头，花卷，面包, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:597.2356796264648ms\n", "inputText:新良原味面包粉2.5kg/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:533.2207679748535ms\n", "inputText:新良玉米饺子粉 1kg/包, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:553.8756847381592ms\n", "inputText:清净园玉米糖浆2.45kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:793.5442924499512ms\n", "inputText:焙乐道麻薯预拌粉5kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:602.7321815490723ms\n", "inputText:舒可曼细砂糖400g, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:512.9988193511963ms\n", "inputText:保软酶 烘焙复配保软剂60g, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:572.1654891967773ms\n", "inputText:新良全麦面包粉500g/包, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:775.8727073669434ms\n", "inputText:新良馒头自发粉 1kg/包, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:1049.5688915252686ms\n", "inputText: 芝焙班戟粉150g , usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:631.8004131317139ms\n", "inputText:易小焙果蔬面粉 多口味 500g/包, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:816.3959980010986ms\n", "inputText:新良全麦面粉自发粉  1kg/包, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:700.3774642944336ms\n", "inputText:新良澄面小麦淀粉  200g/包, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:583.2405090332031ms\n", "inputText:新良馒头自发粉 2.5kg/包, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:716.5944576263428ms\n", "inputText:迪吉福DGF葡萄糖浆  7kg/桶, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:518.4659957885742ms\n", "inputText:新良水磨糯米粉500g, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:523.0767726898193ms\n", "inputText:新良粘米粉500g , usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:581.9008350372314ms\n", "inputText:太古白砂糖 454g, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:562.3705387115479ms\n", "inputText:太古白砂糖1kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:582.8709602355957ms\n", "inputText:新鲜草莓 28粒, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:604.1107177734375ms\n", "inputText:百利番茄酱1kg, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:675.6460666656494ms\n", "inputText:安德鲁颗粒果酱1kg白桃茉莉乐桃桃青葡萄草莓果酱西点奶茶andros, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:547.487735748291ms\n", "inputText:奥利奥饼干碎400g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:549.2243766784668ms\n", "inputText:乔康麦苗汁 麦青汁500ml 青团艾草汁, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:535.0446701049805ms\n", "inputText:燕子牌鲜酵母500g, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:602.7615070343018ms\n", "inputText:乐芙娜西西里柠檬汁200ml/1瓶, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:1333.5299491882324ms\n", "inputText:马来西亚新鲜无核苏丹王速冻d24榴莲泥2kg袋, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:595.6723690032959ms\n", "inputText:泰国艾可榴莲肉冷冻无核榴莲3a级 3kg/包 , usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:585.6237411499023ms\n", "inputText:味斯美海苔肉松2kg3A级, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:610.5833053588867ms\n", "inputText:金枕榴莲A级 3kg 1包, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:673.7115383148193ms\n", "inputText:吴香香黑芝麻 100g/包, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:558.7418079376221ms\n", "inputText:味斯美海苔肉松2kg3A级整箱8包, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:582.6923847198486ms\n", "inputText:味斯美原味肉松1kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:601.2363433837891ms\n", "inputText:泰国艾可榴莲肉冷冻无核榴莲3a级 3kg/包    整箱6包 , usage:{\"prompt_tokens\": 41, \"total_tokens\": 41}, time cost:565.4101371765137ms\n", "inputText:高达椰浆椰汁400ml, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:620.4578876495361ms\n", "inputText:嘉利宝牛奶巧克力豆33.6%巧克力粒  2.5kg/包, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:704.8687934875488ms\n", "inputText:法瑞芙杏仁片1kg, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:673.1188297271729ms\n", "inputText:嘉利宝黑巧克力豆70.5%巧克力粒  2.5kg/包, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:580.9822082519531ms\n", "inputText:脆波波 寒天晶球多种水果口味免煮脆啵啵 500g/斤, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:736.2911701202393ms\n", "inputText:金太阳咸蛋黄 20粒装, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:883.5771083831787ms\n", "inputText:梵豪登黑巧克力砖大块1kg, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:621.9935417175293ms\n", "inputText:嘉利宝黑巧克力豆54.5%巧克力粒  2.5kg/包, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:713.2787704467773ms\n", "inputText:谷优玛利亚饼干200g, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:968.9850807189941ms\n", "inputText:吴香香白芝麻 100g/包, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:554.8810958862305ms\n", "inputText:百利沙拉酱烘焙专用型900g, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:561.9649887084961ms\n", "inputText:新良吉利丁片50g*10 鱼胶明胶, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:617.8755760192871ms\n", "inputText:台创蓝黛高脂可可粉1kg, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:613.1696701049805ms\n", "inputText:大成30克德式香肠（烘焙专用）1kg*12包整箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:683.6965084075928ms\n", "inputText:梵豪登无糖碱化高脂烘焙可可粉1kg马来西亚进口, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:682.3558807373047ms\n", "inputText:嘉利宝白巧克力豆28 %巧克力粒  2.5kg/包, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:616.0335540771484ms\n", "inputText:KARA佳乐椰汁椰浆200ml/400ml/1000ml, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:664.297342300415ms\n", "inputText:科麦卡士达克林姆粉 吉士粉 烘焙原料 5kg/包西点中式点心增香, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:875.6287097930908ms\n", "inputText:乐斯福燕子酵母500g 耐高糖, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:940.2639865875244ms\n", "inputText:KARA佳乐椰汁椰浆400ml/盒, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:539.5214557647705ms\n", "inputText:梵豪登黑巧克力币可可含量65% 1.5kg/包, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:542.5534248352051ms\n", "inputText:宇治 若竹 青岚 五十铃 白莲    天然日式抹茶粉 50g, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:770.1563835144043ms\n", "inputText:新良无铝双效泡打粉50g, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:531.5036773681641ms\n", "inputText:百利吉利丁片凝胶片100g   5g /片, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:532.4718952178955ms\n", "inputText:大成30克德式香肠1kg-烘焙用, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:535.1412296295166ms\n", "inputText:烘焙原料佳杰红曲粉 食用色素红曲粉 红丝绒蛋糕原料10g, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:566.6768550872803ms\n", "inputText:梵豪登34.8%纯可可脂牛奶巧克力豆币1.5kg, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:648.1878757476807ms\n", "inputText:梵豪登30.5%纯可可脂白巧克力币 1.5KG/包, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:722.92160987854ms\n", "inputText:芝焙椰蓉100g, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:628.1247138977051ms\n", "inputText:新良食用小苏打  200g/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:596.874475479126ms\n", "inputText:瑞娜香草精500g英国进口精面包蛋糕增香原料香料戚风蛋糕, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:550.2185821533203ms\n", "inputText:焙小姐速冻调制芋泥500g/包, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:585.7350826263428ms\n", "inputText:瑞娜香草精油28ml, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:600.5957126617432ms\n", "inputText:味斯美辣味肉松1kg, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:609.8716259002686ms\n", "inputText:法芙娜可可粉1kg, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:577.7218341827393ms\n", "inputText:大成台畜超值培根肉片1kg/包, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:661.3078117370605ms\n", "inputText:大成香薰鸡肉片  1kg/包, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:1424.9842166900635ms\n", "inputText:大成三明治切片火腿500g/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:707.0016860961914ms\n", "inputText:大成奥尔良风味早餐腿排1kg/包  整箱10包, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:588.1233215332031ms\n", "inputText:百利牌吉士粉300克 , usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:689.579963684082ms\n", "inputText:大成烘焙肉扒 1.3kg/包, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:794.0983772277832ms\n", "inputText:百凝明胶吉利丁鱼胶片 1kg, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:657.3762893676758ms\n", "inputText:KARA佳乐椰汁椰浆1L/瓶, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:572.2954273223877ms\n", "inputText:梵豪登耐烘烤巧克力豆1.5kg, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:570.0802803039551ms\n", "inputText:早苗塔塔粉复配糕点酸度调节剂1.35kg/罐, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:588.7043476104736ms\n", "inputText:冷冻椰子水喜茶同款咖啡餐饮奶茶店原料包邮, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:695.5177783966064ms\n", "inputText:咖啡甘露原装墨西哥甘露咖啡力娇酒700ml/瓶, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:655.2956104278564ms\n", "inputText:大成香薰鸡肉片  1kg*12包整箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:555.2253723144531ms\n", "inputText:大成烘焙肉扒 1.3kg*8包整箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:547.4791526794434ms\n", "inputText:蓝钻杏仁片分装美国进口烘焙原料 500g/包, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:531.4083099365234ms\n", "inputText:大卫吉利丁片1kg, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:604.3760776519775ms\n", "inputText:焙小姐纯白棉花糖 烘焙奶枣雪花酥牛轧糖专用低甜即食棉花糖, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:492.42281913757324ms\n", "inputText:吴香香脱皮绿豆 400g/包, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:562.1023178100586ms\n", "inputText:丘比沙拉酱香甜味  1kg/1包, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:524.946928024292ms\n", "inputText:法瑞芙果蔬粉100g 多口味, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:729.3531894683838ms\n", "inputText:大成德式迷你烤肠原味1kg/包, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:658.4727764129639ms\n", "inputText:法瑞芙杏仁粉1kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:695.0235366821289ms\n", "inputText:梵豪登57.6%纯可可脂黑巧克力币 1.5KG/包, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:560.253381729126ms\n", "inputText:味客吉伯爵红茶粉 50g/包, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:522.1104621887207ms\n", "inputText:宝茸速冻果泥草莓味1kg/盒, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:528.6798477172852ms\n", "inputText:吴香香披萨草 16g/瓶, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:551.6901016235352ms\n", "inputText:好时可可粉226g, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:541.623592376709ms\n", "inputText:百利美味沙拉酱900g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:538.7394428253174ms\n", "inputText:大成台畜德式香肠50g经典版 1kg/包, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:557.7945709228516ms\n", "inputText:不二欧喜可丝达馅料 卡仕达酱奶酪MPCN原味巧克力酸奶麻花馅1kg*6, usage:{\"prompt_tokens\": 53, \"total_tokens\": 53}, time cost:552.0613193511963ms\n", "inputText:优鲜沛蔓越莓干八分之一切片11.34公斤/箱, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:633.4507465362549ms\n", "inputText:宝茸速冻果泥芒果味1kg/盒, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:680.4237365722656ms\n", "inputText:克拉农场扁桃仁片100g, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:535.0151062011719ms\n", "inputText:克拉农场蔓越莓干100g/500g, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:538.1026268005371ms\n", "inputText:梵豪登黑巧克力豆耐烘焙10kg, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:546.0143089294434ms\n", "inputText:冷冻金枕头榴莲果肉3kg（生果包售后）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:539.9110317230225ms\n", "inputText:豫吉小奇福饼250g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:676.9919395446777ms\n", "inputText:台湾进口台贺小奇福饼牛奶岩盐 原味岩盐  500g/包, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:574.6047496795654ms\n", "inputText:大成香酥琵琶腿1kg/包, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:568.1567192077637ms\n", "inputText:早苗炮打粉2.7kg, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:530.8256149291992ms\n", "inputText:豫吉小奇福饼干250g , usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:585.317850112915ms\n", "inputText:豫吉小葫芦饼干170, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:527.6687145233154ms\n", "inputText:好时巧克力酱650g, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:541.5747165679932ms\n", "inputText:穗城皇冠小西米100克 白西米 甜品原料椰浆西米露 家用小包小西米, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:645.0872421264648ms\n", "inputText:法国安德鲁树莓速冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:559.6902370452881ms\n", "inputText:圣诞节雪花酥包装袋 饼干牛轧糖ins风饼干袋 机封袋100只, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:715.7533168792725ms\n", "inputText:法国安德鲁果溶芒果草莓树莓西番莲蓝莓椰子荔枝果茸速冻果泥果酱, usage:{\"prompt_tokens\": 56, \"total_tokens\": 56}, time cost:848.7198352813721ms\n", "inputText:新良面包糠（白糠/黄糠） 200g/包, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:717.6492214202881ms\n", "inputText:KARA佳乐椰汁椰浆1L*12瓶 整箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:698.9998817443848ms\n", "inputText:洪泰 新奥尔良 烤翅腌料80g/瓶, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:582.9355716705322ms\n", "inputText:大成十三香地道肠20cm 1kg/包, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:534.1596603393555ms\n", "inputText:焙小姐红西柚粒800g*罐, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:556.748628616333ms\n", "inputText:哈瓦那3年陈酿俱乐部朗姆酒基酒700ml/瓶, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:533.4384441375732ms\n", "inputText:法国安德鲁芒果速冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:616.771936416626ms\n", "inputText:宝茸速冻果泥覆盆子1kg/盒, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:549.8628616333008ms\n", "inputText:七哥披萨酱200g , usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:833.7774276733398ms\n", "inputText:美国进口好时纯可可粉226g/罐, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:519.3445682525635ms\n", "inputText:宝茸速冻果泥百香果味1kg/盒, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:515.352725982666ms\n", "inputText:科麦抹茶粉A级500g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:513.4260654449463ms\n", "inputText:宝茸速冻果泥菠萝味1kg/盒, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:600.4931926727295ms\n", "inputText:法国安德鲁荔枝速冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:582.86452293396ms\n", "inputText:法国安德鲁水蜜桃速冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:568.5691833496094ms\n", "inputText:大成奥尔良口味香雪鸡排1kg/包, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:535.0918769836426ms\n", "inputText:味客吉抹茶粉  100g, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:597.0785617828369ms\n", "inputText:宝茸速冻果椰子果泥1kg/盒, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:530.8942794799805ms\n", "inputText:敬松庄园扁桃仁片 100g/包, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:550.4560470581055ms\n", "inputText:焙小姐冻干草莓丁100g 即食牛轧糖雪花酥材料烘焙用专, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:539.8333072662354ms\n", "inputText:英雄栗子泥900g, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:577.0394802093506ms\n", "inputText:可可百利法国进口2号深咖碱化高脂可可粉1kg, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:633.683443069458ms\n", "inputText:KARA佳乐椰汁椰浆400ml*24盒 整箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:567.2605037689209ms\n", "inputText:碧威蛋白稳定剂1kg/瓶, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:547.3957061767578ms\n", "inputText:利宝70.5%黑巧克力币粒豆500g/包, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:749.9325275421143ms\n", "inputText:大成奥尔良口味香雪鸡排1kg*10包 /箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:592.8075313568115ms\n", "inputText:甘露咖啡力娇酒50ml, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:556.1342239379883ms\n", "inputText:百加得白朗姆酒50ml 芝士蛋糕调味提拉米苏烘焙原料 原装小瓶酒版, usage:{\"prompt_tokens\": 53, \"total_tokens\": 53}, time cost:648.3883857727051ms\n", "inputText:欧焙芝手指饼干200g整箱意大利进口烘焙装饰材料提拉米苏蛋糕, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:619.0130710601807ms\n", "inputText:敬松庄园扁桃仁粉 100g/包, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:597.1448421478271ms\n", "inputText:丘比沙拉酱香甜味1kg*10袋  整箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:635.3814601898193ms\n", "inputText:吾双油醋汁0脂 250g/瓶, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:824.6967792510986ms\n", "inputText:吾双零脂蒜蓉酱  250g/瓶, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:653.5866260528564ms\n", "inputText:百利甜酒奶油利口酒700ml/瓶, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:617.6574230194092ms\n", "inputText:法国安德鲁阿方索芒果速冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:607.9623699188232ms\n", "inputText:法国安德鲁草莓速冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:1131.3056945800781ms\n", "inputText:焙小姐熟黑/白芝麻罐装熟份250g, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:711.4677429199219ms\n", "inputText:京日JC45红小豆甘纳豆5kg*2 京日蜜豆 烘焙原料面包月饼馅料, usage:{\"prompt_tokens\": 43, \"total_tokens\": 43}, time cost:637.3343467712402ms\n", "inputText:复配增稠剂（苹果果胶）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:567.0487880706787ms\n", "inputText:摩根船长黑标黑朗姆调酒基酒700ml/1瓶, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:691.7529106140137ms\n", "inputText:法国安德鲁桃子速冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:643.162727355957ms\n", "inputText:法国安德鲁西番莲 百香果速 冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:636.8615627288818ms\n", "inputText:法国安德鲁菠萝速冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:1245.2785968780518ms\n", "inputText:豫吉小奇福饼干 250g*20包 整箱5kg, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:602.0774841308594ms\n", "inputText:智力进口火焰无核提子干 10kg/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:1353.5456657409668ms\n", "inputText:水妈妈牌白西米 烧仙草奶茶原料 泰国进口椰浆西米露芋圆配料500g, usage:{\"prompt_tokens\": 48, \"total_tokens\": 48}, time cost:588.1834030151367ms\n", "inputText:鑫恰巧海苔肉松脆酥松2A级酥松1KG寿司小贝面包烘焙原料肉松儿童, usage:{\"prompt_tokens\": 56, \"total_tokens\": 56}, time cost:663.7561321258545ms\n", "inputText:鑫恰巧168金丝肉松粉2.5kg寿司饭团拔丝蛋糕烘培专用材料商用整箱, usage:{\"prompt_tokens\": 53, \"total_tokens\": 53}, time cost:619.07958984375ms\n", "inputText:京日JB5\t/JB16  红豆沙5kg*4整箱 JBO70M油红豆沙烘焙原料, usage:{\"prompt_tokens\": 41, \"total_tokens\": 41}, time cost:519.801139831543ms\n", "inputText:梵豪登（VAN HOUTEN） 梵豪登高脂可可粉1kg装, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:691.7390823364258ms\n", "inputText:法国进口法芙娜香脆珍珠空心巧克力球55%34%脆脆珠蛋糕装饰巧克力, usage:{\"prompt_tokens\": 54, \"total_tokens\": 54}, time cost:586.8940353393555ms\n", "inputText:香芋馅  芋泥馅  2.5kg, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:757.1322917938232ms\n", "inputText:可可百利可可脂3kg/桶 比利时原装进口, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:639.3194198608398ms\n", "inputText:法国安德鲁青柠速冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:1084.7506523132324ms\n", "inputText:法国安德鲁椰子速冻果溶果酱1kg/盒, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:643.4400081634521ms\n", "inputText:荷美尔 惠选培根（薄切）500g 早餐西餐烘焙培根, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:533.1764221191406ms\n", "inputText:<PERSON><PERSON><PERSON>荷美尔 黑椒风味牛排 900g 西餐牛排 烤牛排 西餐原料, usage:{\"prompt_tokens\": 43, \"total_tokens\": 43}, time cost:661.3984107971191ms\n", "inputText:荷美尔惠选培根2kg烧烤手抓饼披萨意面三明治汉堡材料商用约70片, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:627.8424263000488ms\n", "inputText:荷美尔午餐肉罐头即食肉火腿下饭菜熟食三明治专用商用整箱非梅林, usage:{\"prompt_tokens\": 48, \"total_tokens\": 48}, time cost:730.7324409484863ms\n", "inputText:成功烘焙艾草粉50g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:635.291576385498ms\n", "inputText:易小焙吉利丁片10片25g, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:555.8173656463623ms\n", "inputText:全透明生日蛋糕盒子 6寸8寸10寸12寸双层加高包装盒, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:614.797830581665ms\n", "inputText:春节新年快乐纸杯蛋糕小号马芬杯, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:723.1879234313965ms\n", "inputText:圣诞卡通雪花酥透明手提盒糖果糯米船包装盒伴手礼, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:598.6142158508301ms\n", "inputText:新年抱抱桶礼物盒 高档喜庆春节礼物包装盒子 网红糖果盒礼盒空盒, usage:{\"prompt_tokens\": 50, \"total_tokens\": 50}, time cost:589.9064540863037ms\n", "inputText:卡通马口铁盒曲奇饼干盒伴手礼9/16格, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:516.5536403656006ms\n", "inputText:蛋糕刀叉盘 5刀5叉一刀  一次性高档水滴云朵生日蛋糕叉碟组合甜品餐盘, usage:{\"prompt_tokens\": 59, \"total_tokens\": 59}, time cost:862.7502918243408ms\n", "inputText:三明治包装纸 吐司面包打包袋午餐晚餐包盒子烘培泡芙牛油排包装, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:719.4697856903076ms\n", "inputText:情人节透明礼品袋 PVC袋手提无纺布塑料方形伴手礼袋子 镭射袋1只, usage:{\"prompt_tokens\": 42, \"total_tokens\": 42}, time cost:978.302001953125ms\n", "inputText:绿豆糕封套包装盒 牛皮纸  小清新创意包装甜品小吃打包专用盒子, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:523.9372253417969ms\n", "inputText:美涤卷口纸杯纸杯蛋糕杯子耐烤耐高温杯50只入, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:642.5068378448486ms\n", "inputText:雪媚娘纸托 油纸托125只装甜品点心蛋糕纸托纸杯托饼干蛋黄酥包装, usage:{\"prompt_tokens\": 58, \"total_tokens\": 58}, time cost:992.3107624053955ms\n", "inputText:手提透明蛋糕盒子 4寸方形烘焙包装爆浆慕斯芝士蛋糕小包装盒, usage:{\"prompt_tokens\": 51, \"total_tokens\": 51}, time cost:977.6890277862549ms\n", "inputText:平安夜马口铁盒曲奇饼干圣诞节糖果金铁罐, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:603.5113334655762ms\n", "inputText:圣诞节雪媚娘纸托 125只装饼干蛋黄酥包装纸托, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:503.1564235687256ms\n", "inputText:生日蛋糕餐盘餐具套装塑料一次性, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:600.2659797668457ms\n", "inputText:面包自封袋 一次性面包袋子透明高档塑料自粘吐司饼干烘焙食品袋, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:582.0474624633789ms\n", "inputText:生日蛋糕盒包装丝带 装扮花束彩带装, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:550.217866897583ms\n", "inputText:加厚6 8寸戚风蛋糕包装盒包装袋10只送金扎丝, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:1060.7554912567139ms\n", "inputText:烘易美麦芬烤杯 马芬蛋糕耐烤防油纸杯 马芬杯礼帽托小蛋糕30个装, usage:{\"prompt_tokens\": 62, \"total_tokens\": 62}, time cost:645.7176208496094ms\n", "inputText:生日蛋糕餐盘刀叉蜡烛套装, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:684.6070289611816ms\n", "inputText:小方盒慕斯杯子塑料小方盒18只装, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:563.9634132385254ms\n", "inputText:三明治包装纸 饭团汉堡纸淋膜纸面包 油蜡纸烘焙食品油纸, usage:{\"prompt_tokens\": 47, \"total_tokens\": 47}, time cost:539.421796798706ms\n", "inputText:生日餐具叉勺盘子一次性餐盘刀叉盘蜡烛纸盘碟子蛋糕盘叉组合套装, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:550.4245758056641ms\n", "inputText:透明塑料罐 铝盖手指饼干曲奇罐食品塑料罐子圆形6只装, usage:{\"prompt_tokens\": 41, \"total_tokens\": 41}, time cost:502.3477077484131ms\n", "inputText:六一儿童节礼物 透明糖果包装盒儿童饼干烘焙创意礼物盒 金属罐子, usage:{\"prompt_tokens\": 54, \"total_tokens\": 54}, time cost:553.452730178833ms\n", "inputText:透明奶茶瓶 一次性塑料瓶 pet杨枝甘露饮品瓶子 玉油柑饮料瓶, usage:{\"prompt_tokens\": 51, \"total_tokens\": 51}, time cost:688.31467628479ms\n", "inputText:网红蛋糕卷包装盒长条透明手提西点盒, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:657.3753356933594ms\n", "inputText:网红屁屁桃包装盒 蜜桃雪媚娘盒子, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:540.391206741333ms\n", "inputText:蛋糕盒子牛皮纸色 10个/50个, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:792.7167415618896ms\n", "inputText:透明二三四层纸杯蛋糕打包盒 杯子蛋糕马芬杯手提盒子密封收纳盒, usage:{\"prompt_tokens\": 50, \"total_tokens\": 50}, time cost:766.1969661712646ms\n", "inputText:半透明塑料生日蛋糕盒子双层加高6/8/10寸方形网红包装盒, usage:{\"prompt_tokens\": 41, \"total_tokens\": 41}, time cost:586.9791507720947ms\n", "inputText:牛皮开窗自立袋雪花酥蔓越莓牛轧糖白色包装袋烘焙食品吐司包装袋, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:646.3634967803955ms\n", "inputText:手工阿胶糕牛轧糖糯米纸包装纸 500张, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:592.5662517547607ms\n", "inputText:月饼盒蛋黄酥吸塑盒 雪媚娘青团包装盒托盒50g-100g 天地盖盒50个, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:770.3170776367188ms\n", "inputText:网红金色生日蛋糕迷你数字蜡烛装饰烘焙派对装扮, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:823.7786293029785ms\n", "inputText:生日蛋糕盒烘焙包装盒 4寸6寸8寸手提欧式透明开窗慕斯西点盒, usage:{\"prompt_tokens\": 49, \"total_tokens\": 49}, time cost:588.6478424072266ms\n", "inputText:圣诞小号蛋糕纸杯玛芬纸杯立体蛋糕杯50只, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:634.1722011566162ms\n", "inputText:玛德琳机封袋牛轧糖枣奶透明饼干袋雪茄装订袋磨砂包装袋, usage:{\"prompt_tokens\": 51, \"total_tokens\": 51}, time cost:614.9168014526367ms\n", "inputText:美涤雪花酥机封自封牛轧糖曲奇饼干包装透明包装袋100枚, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:538.276195526123ms\n", "inputText:网红生日蛋糕用装饰蜡烛摆件复古曲线派对烘焙螺纹创意蜡烛, usage:{\"prompt_tokens\": 53, \"total_tokens\": 53}, time cost:537.4295711517334ms\n", "inputText:塑料手提蛋糕盒 纸杯蛋糕便携式打包盒4寸8寸10寸, usage:{\"prompt_tokens\": 43, \"total_tokens\": 43}, time cost:964.3311500549316ms\n", "inputText:网红透明塑料冰淇淋杯 烧仙草奶茶塑料杯子 慕斯蛋糕盒布丁果冻盒, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:714.7107124328613ms\n", "inputText:新品蛋黄酥铝箔纸包装纸食品级包装纸, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:944.9906349182129ms\n", "inputText:蛋糕底托垫片 重复使用4/6/8/10寸 5只 家用生日蛋糕盒塑料硬垫片, usage:{\"prompt_tokens\": 52, \"total_tokens\": 52}, time cost:530.127763748169ms\n", "inputText:生日帽子儿童派对帽皇冠儿童大人一次性烘焙生日派对装饰摆件, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:1109.6348762512207ms\n", "inputText:吐司袋面包包装袋麻薯牛角包铁丝卷边封口袋子牛皮纸绵纸烘焙包装, usage:{\"prompt_tokens\": 52, \"total_tokens\": 52}, time cost:870.8627223968506ms\n", "inputText:蛋黄酥封口贴不干胶雪花奶酥花酥封口贴雪120枚入  , usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:669.2705154418945ms\n", "inputText:耐高温一次性蛋糕纸托戚风蛋糕油纸托6/8/10寸戚风翻边防油纸托, usage:{\"prompt_tokens\": 58, \"total_tokens\": 58}, time cost:583.5363864898682ms\n", "inputText:美涤一次性手套TPE食品级加厚防水橡胶100支/盒, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:577.9750347137451ms\n", "inputText:新年巨型草莓塔打包杯网红奶油蛋糕杯子打包盒装饰丝带竹签包装盒, usage:{\"prompt_tokens\": 51, \"total_tokens\": 51}, time cost:537.8344058990479ms\n", "inputText:瑞娜香草精500ml, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:712.9113674163818ms\n", "inputText:木质甜品盒 4粒装10套/包, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:495.4543113708496ms\n", "inputText:仙妮贝儿防潮糖牌干佩斯 454g /包, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:541.8751239776611ms\n", "inputText:仙妮贝儿防潮糖牌干佩斯 柔软型 454g /包 , usage:{\"prompt_tokens\": 37, \"total_tokens\": 37}, time cost:556.788444519043ms\n", "inputText:焙小姐蛋糕装饰糖珠彩色糖果2-14mm多规格多色可选, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:650.2165794372559ms\n", "inputText:americolor美国进口ac色素, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:815.4511451721191ms\n", "inputText:立体土豪金 数字蜡烛 生日蛋糕蜡烛 金色创意土豪金蜡烛, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:742.0525550842285ms\n", "inputText:ac可食用色素美国进口食品级蛋糕调色Americolor128g, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:1694.8227882385254ms\n", "inputText:仙妮贝儿 原味翻糖膏908g/包, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:756.5386295318604ms\n", "inputText:仙妮贝儿 防潮人偶干佩斯454g/包, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:581.0112953186035ms\n", "inputText:仙妮贝儿 花卉干佩斯 454g /包, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:1196.3839530944824ms\n", "cache matched:烘焙原料佳杰红曲粉 食用色素红曲粉 红丝绒蛋糕原料10g\n", "inputText:仙妮贝儿 防潮花卉干佩斯454g/包, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:646.4126110076904ms\n", "inputText:仙妮贝儿翻糖专用沾合剂  30ml/瓶, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:911.64231300354ms\n", "inputText:圣诞雪花翻糖切模花样蛋糕装饰模具立体印花模具, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:759.1598033905029ms\n", "inputText:HB字母生日快乐happybirthday蜡烛创意蛋糕字母表白派对, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:524.8267650604248ms\n", "inputText:蛋糕摆件底座固定器 蛋糕装饰摆件底座底托插件透明塑情景配件, usage:{\"prompt_tokens\": 50, \"total_tokens\": 50}, time cost:538.2785797119141ms\n", "inputText:斑斓粉可食用色素100g/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:600.4996299743652ms\n", "inputText:网红歌剧院风格生日蛋糕金色数字蜡烛节庆装扮贝壳香槟色蜡烛, usage:{\"prompt_tokens\": 50, \"total_tokens\": 50}, time cost:659.2590808868408ms\n", "inputText:舒可曼食用色素30ml 烘焙原料 彩虹蛋糕翻糖裱花 彩色马卡龙, usage:{\"prompt_tokens\": 48, \"total_tokens\": 48}, time cost:642.1194076538086ms\n", "inputText:翻糖蛋糕装饰巧克力硅胶模具, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:591.3081169128418ms\n", "inputText:仙妮贝儿翻糖专用白油  160g/盒, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:607.7029705047607ms\n", "inputText:仙妮贝儿 人偶干佩斯 454g /包, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:770.183801651001ms\n", "inputText:可食用糯米纸蝴蝶蛋糕装饰摆件生日派对甜品台装扮彩色蝴蝶插件, usage:{\"prompt_tokens\": 53, \"total_tokens\": 53}, time cost:579.6167850494385ms\n", "inputText:旺林竹质植物竹炭粉20g/包, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:563.0364418029785ms\n", "inputText:彩色拉菲草碎纸丝 礼品盒喜糖盒防压填充物装饰品, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:613.3472919464111ms\n", "inputText:食用糯米纸 3D打印立体手绘蛋糕转印 转印蛋糕用 50张入, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:608.1838607788086ms\n", "inputText:生日帽可折叠烫金镭射头饰英文儿童派对帽子金色皇冠, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:603.3260822296143ms\n", "inputText:网红羽毛蛋糕装饰摆件小仙女羽毛蛋糕装饰情人节派对摆, usage:{\"prompt_tokens\": 46, \"total_tokens\": 46}, time cost:637.8419399261475ms\n", "inputText:烘焙裱花袋 大号中号小号 奶油袋 挤花袋 100只 蛋糕奶油袋, usage:{\"prompt_tokens\": 47, \"total_tokens\": 47}, time cost:578.6120891571045ms\n", "inputText:翻糖diy基础工具合集雕刻板擀面杖塑形压边滚轮刀凹造型工具, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:655.1501750946045ms\n", "inputText:蛋糕分片层器2只装, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:640.4201984405518ms\n", "inputText:不锈钢304曲奇蛋糕面包裱花嘴, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:644.5717811584473ms\n", "inputText:无粉PVC一次性手套 1盒20只, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:675.0655174255371ms\n", "inputText:2PC果酱瓶大中小号尖嘴调料瓶沙拉番茄酱挤压瓶厨房用品, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:574.5835304260254ms\n", "inputText:爱满屋烘焙工具黑柄不锈钢曲吻刀蛋糕奶油抺刀6寸8寸10寸, usage:{\"prompt_tokens\": 50, \"total_tokens\": 50}, time cost:678.7755489349365ms\n", "inputText:美涤高硼硅玻璃盆家用食品级厨房打蛋果蔬盆沙拉碗加深加厚和面盆, usage:{\"prompt_tokens\": 50, \"total_tokens\": 50}, time cost:642.3516273498535ms\n", "inputText:耐高温加厚油布烘焙家用不粘反复使用33cm*33cm, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:577.7528285980225ms\n", "inputText:烘焙翻糖蛋糕模具逢考必过资产过亿生日蛋糕装饰烘焙巧克力硅胶模, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:512.3574733734131ms\n", "inputText:爱满屋烤箱专用温度表 温度计双刻度带包装, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:645.4532146453857ms\n", "inputText:一次性无粉pvc手套食品级 20只一盒, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:663.1312370300293ms\n", "inputText:厨师机冰桶降温冰袋 奶油打发专用打面机揉面机面包机可重复使用, usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:540.6653881072998ms\n", "inputText:【爱满屋】6寸8寸不粘平底锅 千层班戟皮麦饭石煎锅 电磁炉家用, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:558.6888790130615ms\n", "inputText:美涤多功能电子秤台秤 烘焙厨房家用食品小型高精度工具0.1g, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:807.424783706665ms\n", "inputText:有漏嘴带手柄不锈钢打蛋盆 防滑硅胶底打蛋盆加深盆防溅盖, usage:{\"prompt_tokens\": 50, \"total_tokens\": 50}, time cost:529.4466018676758ms\n", "inputText:不锈钢慕斯圈盒装带推板饼干切模具爱满屋烘焙工具, usage:{\"prompt_tokens\": 41, \"total_tokens\": 41}, time cost:883.6534023284912ms\n", "inputText:面包冷却架长方形大小号, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:714.8540019989014ms\n", "inputText:食品夹不锈钢红柄食物夹防烫面包夹酒店牛排烤肉夹9寸~14寸, usage:{\"prompt_tokens\": 49, \"total_tokens\": 49}, time cost:564.5577907562256ms\n", "inputText:爱满屋不锈钢304散装裱花嘴无缝裱花嘴(#45～#352可选), usage:{\"prompt_tokens\": 40, \"total_tokens\": 40}, time cost:548.0186939239502ms\n", "inputText:翻糖工具奶油抹刀带手柄面抺蛋糕奶油抹平器烘焙用具, usage:{\"prompt_tokens\": 45, \"total_tokens\": 45}, time cost:537.9586219787598ms\n", "inputText:爱满屋韩式裱花嘴芭比娃娃裙边裙摆裱花嘴无缝奶油嘴, usage:{\"prompt_tokens\": 51, \"total_tokens\": 51}, time cost:549.8642921447754ms\n", "inputText:爱满屋3寸~10寸表面阳极处理铝合金爱心形活底蛋糕模, usage:{\"prompt_tokens\": 37, \"total_tokens\": 37}, time cost:646.7084884643555ms\n", "inputText:一次性空气炸锅铝箔盘空气炸锅专用纸硅油纸盘纸托圆形, usage:{\"prompt_tokens\": 44, \"total_tokens\": 44}, time cost:677.4148941040039ms\n", "inputText:美涤长方形U型不粘饼干蔓越莓曲奇塑形器24.6cm, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:569.594144821167ms\n", "inputText:祈和KS-930打蛋器 电动家用手持式, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:701.8148899078369ms\n", "inputText:便携封口机雪花酥包装袋糖纸塑封机平口电热式, usage:{\"prompt_tokens\": 37, \"total_tokens\": 37}, time cost:805.260419845581ms\n", "inputText:烘培曲艺纹路印花模具饼干压膜爱心菱形纹理翻糖围边塑料模具切模, usage:{\"prompt_tokens\": 55, \"total_tokens\": 55}, time cost:562.6013278961182ms\n", "inputText:猫爪夹食物夹厨房食品夹子, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:611.7856502532959ms\n", "inputText:食品级硅胶蒸笼垫圆形耐高温加厚蒸笼布蒸包子蒸馒头不粘家用屉布, usage:{\"prompt_tokens\": 52, \"total_tokens\": 52}, time cost:579.9863338470459ms\n", "inputText:裱花嘴套装 中号, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:634.4432830810547ms\n", "inputText:奥昆菠萝包  6个/袋, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:683.1371784210205ms\n", "inputText:倍成8寸抹茶红豆慕斯1000g/盒, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:566.2574768066406ms\n", "inputText:倍成小罐蛋糕 提拉米苏 酷脆奥巧 爱上芝士, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:633.1787109375ms\n", "inputText:奥昆大佬强牛角包（原味） 16个/包 288g, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:649.817943572998ms\n", "inputText:倍成 4寸 红丝绒咸蛋黄慕斯140g/块, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:750.0481605529785ms\n", "inputText:倍成8寸白桃乌龙慕斯1000g/盒, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:600.1062393188477ms\n", "inputText:倍成小罐蛋糕 榴莲 抹茶 芝士 奥利奥 白桃 提拉米苏六个口味, usage:{\"prompt_tokens\": 47, \"total_tokens\": 47}, time cost:570.5623626708984ms\n", "inputText:倍成自助餐慕斯蛋糕720g/盒 54小块, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:546.0224151611328ms\n", "inputText:倍成8寸双莓慕斯1000g/盒, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:572.8695392608643ms\n", "inputText:倍成4寸方形黑森林方块慕斯140g/块, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:761.6565227508545ms\n", "inputText:倍成4寸白桃方块140g/块, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:594.1088199615479ms\n", "inputText:倍成8寸法式黑森林慕斯1000g/盒, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:698.6730098724365ms\n", "inputText:倍成4寸提拉米苏方块 140g/块, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:546.6876029968262ms\n", "inputText:倍成8寸法式榴莲千层慕斯900g/盒, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:617.2630786895752ms\n", "inputText:倍成匠心手工蛋挞皮 28个/包 , usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:565.5517578125ms\n", "inputText:奥昆速冻盘挞皮（原味）24个  黄油布丁杯   , usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:519.1178321838379ms\n", "inputText:倍成8寸多肉葡萄慕斯1000g/盒, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:610.4555130004883ms\n", "inputText:倍成8寸法式芒果千层慕斯900g/盒, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:519.3893909454346ms\n", "inputText:倍成8寸芝士咸蛋黄慕斯1000g/盒, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:617.1755790710449ms\n", "inputText:倍成蛋挞皮207中号（带锡底托）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:716.6707515716553ms\n", "inputText:倍成8寸彩虹慕斯 1000g/盒, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:617.5177097320557ms\n", "inputText:倍成匠心手工蛋挞皮 28个/包 整箱10包, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:550.6038665771484ms\n", "inputText:倍成206迷你葡式蛋挞皮, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:484.07459259033203ms\n", "inputText:倍成蛋挞液907g 整箱12瓶, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:842.3991203308105ms\n", "inputText:倍成4寸迷你彩虹慕斯210g/盒, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:838.0417823791504ms\n", "inputText:倍成8寸日式提拉米苏  芒果千层 半成品慕斯蛋糕, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:647.1436023712158ms\n", "inputText:倍成8寸日式提拉米苏1000g/盒, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:630.756139755249ms\n", "inputText:七哥葡式蛋挞皮24个整箱30包, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:587.3863697052002ms\n", "inputText:倍成8寸海盐奥利奥慕斯1000g/盒, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:635.9238624572754ms\n", "inputText:奥昆207葡式蛋挞皮30个/包  , usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:860.4936599731445ms\n", "inputText:安德鲁七哥无蔗糖蛋挞液400g, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:867.3653602600098ms\n", "inputText:奥昆好禧坊牛角包（流沙蛋黄味）12个/包 360g, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:604.975700378418ms\n", "inputText:奥昆206葡式蛋挞皮60个/包   整箱11包, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:934.6292018890381ms\n", "inputText:奥昆迷你甜甜圈原味 18个/袋（360g）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:654.059648513794ms\n", "inputText:奥昆精装蛋挞皮30个, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:717.444658279419ms\n", "inputText:奥昆蛋挞液907g*12瓶 整箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:564.1393661499023ms\n", "inputText:奥昆葡式蛋挞液好禧坊907g*12盒, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:949.660062789917ms\n", "inputText:立高奥昆葡式蛋挞液907g/盒, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:686.8619918823242ms\n", "inputText:安特鲁七哥德式布丁蛋挞皮36g*8个, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:950.3395557403564ms\n", "inputText:奥昆大佬强榴莲酥  24个*9层/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:1155.9691429138184ms\n", "inputText:奥昆迷你甜甜圈草莓味 18个/袋（360g）, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:542.7582263946533ms\n", "inputText:奥昆速冻老婆饼   20个*13层/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:534.6598625183105ms\n", "inputText:安特鲁七哥安佳黄油蛋挞皮234g/盒, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:563.49778175354ms\n", "inputText:6寸榴莲千层芒果千层, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:531.3189029693604ms\n", "inputText:倍成Q软香嫩麻薯慕斯 白桃 青柠 彩虹 芋泥四个口味, usage:{\"prompt_tokens\": 43, \"total_tokens\": 43}, time cost:567.6255226135254ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['name'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "获取了新的代理列表:*************:31276\n", "**************:47685\n", "***************:44013\n", "***************:45152\n", "************:39410\n", "**************:45312\n", "*************:33978\n", "**************:47632\n", "************:48310\n", "**************:33751\n", "获取了新的代理列表:**************:32654\n", "************:42912\n", "*************:33772\n", "**************:49830\n", "************:48433\n", "**************:32274\n", "************:40801\n", "*************:47933\n", "*************:41555\n", "**************:34313\n", "['*************:31276', '**************:47685', '***************:44013', '***************:45152', '************:39410', '**************:45312', '*************:33978', '**************:47632', '************:48310', '**************:33751', '**************:32654', '************:42912', '*************:33772', '**************:49830', '************:48433', '**************:32274', '************:40801', '*************:47933', '*************:41555', '**************:34313']\n", "1715753667235, headers:{'Accept-Encoding': 'gzip, deflate', 'Content-Type': 'application\\\\/x-www-form-urlencoded', 'Accept-Language': 'en-CN;q=1, zh-Hans-C<PERSON>;q=0.9', 'Accept': '*\\\\/*', 'User-Agent': '<PERSON><PERSON><PERSON><PERSON>\\\\/1.0.9 (iPhone; iOS 17.3.1; Scale\\\\/3.00)', 'Content-Length': '40', 'Host': 'fggapi.fengguog.com', 'Connection': 'keep-alive'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"Accept-Encoding\": \"gzip, deflate\",\n", "    \"Content-Type\": \"application\\/x-www-form-urlencoded\",\n", "    \"Accept-Language\": \"en-CN;q=1, zh-Hans-CN;q=0.9\",\n", "    \"Accept\": \"*\\/*\",\n", "    \"User-Agent\": \"Feng<PERSON>uogong\\/1.0.9 (iPhone; iOS 17.3.1; Scale\\/3.00)\",\n", "    \"Content-Length\": \"40\",\n", "    \"Host\": \"fggapi.fengguog.com\",\n", "    \"Connection\": \"keep-alive\",\n", "}\n", "brand_name = \"蜂果供\"\n", "competitor_name_en = \"fengguogong\"\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机的使用一个代理IP:6\n", "url:http://fggapi.fengguog.com/cuser/login?mobile=13376812870&passwd=d699ef2f61f04f981e8fdd1e1d58d1af, using proxy: http://***********:8gTcEKLs@*************:33978, retry_cnt:0, headers:{}\n", "response status:200, proxy used:http://***********:8gTcEKLs@*************:33978\n"]}, {"data": {"text/plain": ["{'statusCode': '100000',\n", " 'statusMsg': '请求成功',\n", " 'data': {'shopInfo': {'id': 5292,\n", "   'shopName': '穆家塘12号水果店',\n", "   'linkMan': '练',\n", "   'linkTel': '13376812870',\n", "   'faceImgUrl': 'http://fggimg.fengguog.com/img_root/img_shop_face/5292/202311292113292632.jpg',\n", "   'province': '33',\n", "   'city': '3301',\n", "   'county': '330110',\n", "   'address': '七贤桥村穆家塘12号',\n", "   'street': '33011011',\n", "   'websiteNode': '3301',\n", "   'receiverTime': '08:00-08:30',\n", "   'latitude': '30.44099175347222',\n", "   'longitude': '119.8135592990451',\n", "   'desc': '',\n", "   'signMan': '',\n", "   'signTime': '2023-11-29',\n", "   'staffId': None,\n", "   'createTime': '2023-11-29 21:13:29',\n", "   'lastPickUpTime': '2023-11-29 21:32:21',\n", "   'sysNote': '',\n", "   'shopGrade': 'LV1',\n", "   'exp': 0,\n", "   'authStatus': 1,\n", "   'status': 1,\n", "   'doOper': None,\n", "   'reason': None,\n", "   'shopFrom': 1,\n", "   'shopType': 1,\n", "   'invitationCode': 'L5EL',\n", "   'invitedCode': '',\n", "   'websiteName': '杭州站',\n", "   'staffName': None,\n", "   'staffTel': None,\n", "   'expand': 1,\n", "   'closeTime': None,\n", "   'logoff': None,\n", "   'messageIsRead': None,\n", "   'couponCount': None,\n", "   'cuserInfoList': None,\n", "   'provinceName': None,\n", "   'cityName': None,\n", "   'countyName': None,\n", "   'streetName': None,\n", "   'statusName': None,\n", "   'shopTypeName': None},\n", "  'cuserInfo': {'id': 5102,\n", "   'mobile': '13376812870',\n", "   'password': 'd699ef2f61f04f981e8fdd1e1d58d1af',\n", "   'realName': '老板',\n", "   'faceImgUrl': None,\n", "   'sex': 1,\n", "   'birthday': '',\n", "   'regMethod': 1,\n", "   'regTime': '2023-11-29 21:32:37',\n", "   'status': 1,\n", "   'sysNote': '',\n", "   'isMain': 1,\n", "   'shopId': 5292,\n", "   'shopName': None,\n", "   'websiteNode': '3301',\n", "   'websiteName': '杭州站'},\n", "  'tokenId': 'dde229182795dadf29e12bf9f5255911'}}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# 先登录：\n", "\n", "url='http://fggapi.fengguog.com/cuser/login?mobile=13376812870&passwd=d699ef2f61f04f981e8fdd1e1d58d1af'\n", "token=get_remote_data_with_proxy_json(url=url, json={})\n", "token"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机的使用一个代理IP:0\n", "url:http://fggapi.fengguog.com/goods/type/first?shopId=5292&websiteNode=3301, using proxy: http://***********:8gTcEKLs@*************:31276, retry_cnt:0, headers:{}\n", "response status:200, proxy used:http://***********:8gTcEKLs@*************:31276\n"]}], "source": ["# 获取类目列表：\n", "shopId = token[\"data\"][\"shopInfo\"][\"id\"]\n", "url_root = f\"http://fggapi.fengguog.com/goods/type/first?shopId=5292&websiteNode=3301\"\n", "\n", "root_cate_list = get_remote_data_with_proxy_json(\n", "    url=\"http://fggapi.fengguog.com/goods/type/first?shopId=5292&websiteNode=3301\"\n", ")[\"data\"]\n", "\n", "# 二级类目\n", "all_second_cate_list = []\n", "for root_cate in root_cate_list:\n", "    typeCode = root_cate[\"typeCode\"]\n", "    second_url = f\"http://fggapi.fengguog.com/goods/type/second?shopId={shopId}&typeCode={typeCode}&websiteNode=3301\"\n", "    second_cate_list = requests.get(url=second_url).json()[\"data\"]\n", "    for second_cate in second_cate_list:\n", "        second_cate[\"parentTypeCode\"] = typeCode\n", "        second_cate[\"parentTypeName\"] = root_cate[\"typeName\"]\n", "        second_cate[\"parentCategoryId\"] = root_cate[\"id\"]\n", "    all_second_cate_list.extend(second_cate_list)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["爆款单品:0501\n", "放心购:0527\n", "源于产地:0509\n", "鲜花专场:0529\n", "水果礼盒:0523\n", "香蕉:0308\n", "火龙果:0303\n", "菠萝蜜:0310\n", "椰子:0312\n", "桃李:0316\n", "凤梨:0304\n", "龙眼:0315\n", "山竹:0305\n", "榴莲:0301\n", "葡提:0302\n", "柑橘橙柚:0309\n", "猕猴桃:0306\n", "苹果梨:0313\n", "牛油果石榴:0311\n", "甜瓜:0205\n", "葡提:0222\n", "枇杷:0246\n", "芒果:0214\n", "西瓜:0204\n", "蓝莓:0236\n", "桃李:0209\n", "苹果:0202\n", "蔬果类:0212\n", "荔枝:0203\n", "橙:0235\n", "杨梅:0208\n", "草莓:0218\n", "凤梨菠萝:0242\n", "梨:0207\n", "樱桃:0220\n", "香蕉:0201\n", "柑橘:0215\n", "柚:0228\n", "火龙果椰子:0224\n", "其他:0249\n", "甜瓜:6002\n", "葡萄:6005\n", "凤梨:6009\n", "梨:6016\n", "芒果:6004\n", "柚:6007\n", "苹果:6008\n", "蔬果:6018\n", "柑橘:6003\n", "桃:6017\n", "休闲食品:0701\n", "一木一果:0714\n", "超市用品:0101\n", "果切专用盒:0110\n", "水果刀具:0102\n", "精品果篮:0103\n", "礼盒包装:0106\n", "水果标签:0105\n", "水果包装:0104\n", "证件信息:5501\n"]}], "source": ["for c in all_second_cate_list:\n", "    print(f\"{c['typeName']}:{c['typeCode']}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["即将获取类目数据:爆款单品\n", "typeCode:0501, pageNo:1, length:20\n", "typeCode:0501, pageNo:2, length:20\n", "typeCode:0501, pageNo:3, length:12\n", "即将获取类目数据:放心购\n", "typeCode:0527, pageNo:1, length:15\n", "即将获取类目数据:源于产地\n", "typeCode:0509, pageNo:1, length:12\n", "即将获取类目数据:鲜花专场\n", "typeCode:0529, pageNo:1, length:1\n", "即将获取类目数据:水果礼盒\n", "typeCode:0523, pageNo:1, length:9\n", "即将获取类目数据:香蕉\n", "typeCode:0308, pageNo:1, length:11\n", "即将获取类目数据:火龙果\n", "typeCode:0303, pageNo:1, length:7\n", "即将获取类目数据:菠萝蜜\n", "typeCode:0310, pageNo:1, length:8\n", "即将获取类目数据:椰子\n", "typeCode:0312, pageNo:1, length:4\n", "即将获取类目数据:桃李\n", "typeCode:0316, pageNo:1, length:1\n", "即将获取类目数据:凤梨\n", "typeCode:0304, pageNo:1, length:4\n", "即将获取类目数据:龙眼\n", "typeCode:0315, pageNo:1, length:1\n", "即将获取类目数据:山竹\n", "typeCode:0305, pageNo:1, length:4\n", "即将获取类目数据:榴莲\n", "typeCode:0301, pageNo:1, length:14\n", "即将获取类目数据:葡提\n", "typeCode:0302, pageNo:1, length:1\n", "即将获取类目数据:柑橘橙柚\n", "typeCode:0309, pageNo:1, length:5\n", "即将获取类目数据:猕猴桃\n", "typeCode:0306, pageNo:1, length:4\n", "即将获取类目数据:苹果梨\n", "typeCode:0313, pageNo:1, length:3\n", "即将获取类目数据:牛油果石榴\n", "typeCode:0311, pageNo:1, length:1\n", "即将获取类目数据:甜瓜\n", "typeCode:0205, pageNo:1, length:20\n", "typeCode:0205, pageNo:2, length:7\n", "即将获取类目数据:葡提\n", "typeCode:0222, pageNo:1, length:17\n", "即将获取类目数据:枇杷\n", "typeCode:0246, pageNo:1, length:6\n", "即将获取类目数据:芒果\n", "typeCode:0214, pageNo:1, length:11\n", "即将获取类目数据:西瓜\n", "typeCode:0204, pageNo:1, length:20\n", "typeCode:0204, pageNo:2, length:20\n", "typeCode:0204, pageNo:3, length:4\n", "即将获取类目数据:蓝莓\n", "typeCode:0236, pageNo:1, length:10\n", "即将获取类目数据:桃李\n", "typeCode:0209, pageNo:1, length:20\n", "typeCode:0209, pageNo:2, length:15\n", "即将获取类目数据:苹果\n", "typeCode:0202, pageNo:1, length:17\n", "即将获取类目数据:蔬果类\n", "typeCode:0212, pageNo:1, length:20\n", "typeCode:0212, pageNo:2, length:14\n", "即将获取类目数据:荔枝\n", "typeCode:0203, pageNo:1, length:13\n", "即将获取类目数据:橙\n", "typeCode:0235, pageNo:1, length:7\n", "即将获取类目数据:杨梅\n", "typeCode:0208, pageNo:1, length:14\n", "即将获取类目数据:草莓\n", "typeCode:0218, pageNo:1, length:3\n", "即将获取类目数据:凤梨菠萝\n", "typeCode:0242, pageNo:1, length:5\n", "即将获取类目数据:梨\n", "typeCode:0207, pageNo:1, length:11\n", "即将获取类目数据:樱桃\n", "typeCode:0220, pageNo:1, length:13\n", "即将获取类目数据:香蕉\n", "typeCode:0201, pageNo:1, length:5\n", "即将获取类目数据:柑橘\n", "typeCode:0215, pageNo:1, length:20\n", "typeCode:0215, pageNo:2, length:13\n", "即将获取类目数据:柚\n", "typeCode:0228, pageNo:1, length:0\n", "即将获取类目数据:火龙果椰子\n", "typeCode:0224, pageNo:1, length:2\n", "即将获取类目数据:其他\n", "typeCode:0249, pageNo:1, length:1\n", "即将获取类目数据:甜瓜\n", "typeCode:6002, pageNo:1, length:2\n", "即将获取类目数据:葡萄\n", "typeCode:6005, pageNo:1, length:2\n", "即将获取类目数据:凤梨\n", "typeCode:6009, pageNo:1, length:2\n", "即将获取类目数据:梨\n", "typeCode:6016, pageNo:1, length:1\n", "即将获取类目数据:芒果\n", "typeCode:6004, pageNo:1, length:2\n", "即将获取类目数据:柚\n", "typeCode:6007, pageNo:1, length:1\n", "即将获取类目数据:苹果\n", "typeCode:6008, pageNo:1, length:2\n", "即将获取类目数据:蔬果\n", "typeCode:6018, pageNo:1, length:1\n", "即将获取类目数据:柑橘\n", "typeCode:6003, pageNo:1, length:1\n", "即将获取类目数据:桃\n", "typeCode:6017, pageNo:1, length:1\n", "即将获取类目数据:休闲食品\n", "typeCode:0701, pageNo:1, length:11\n", "即将获取类目数据:一木一果\n", "typeCode:0714, pageNo:1, length:20\n", "typeCode:0714, pageNo:2, length:20\n", "typeCode:0714, pageNo:3, length:17\n", "即将获取类目数据:超市用品\n", "typeCode:0101, pageNo:1, length:20\n", "typeCode:0101, pageNo:2, length:4\n", "即将获取类目数据:果切专用盒\n", "typeCode:0110, pageNo:1, length:14\n", "即将获取类目数据:水果刀具\n", "typeCode:0102, pageNo:1, length:16\n", "即将获取类目数据:精品果篮\n", "typeCode:0103, pageNo:1, length:5\n", "即将获取类目数据:礼盒包装\n", "typeCode:0106, pageNo:1, length:20\n", "typeCode:0106, pageNo:2, length:11\n", "即将获取类目数据:水果标签\n", "typeCode:0105, pageNo:1, length:20\n", "typeCode:0105, pageNo:2, length:20\n", "typeCode:0105, pageNo:3, length:20\n", "typeCode:0105, pageNo:4, length:20\n", "typeCode:0105, pageNo:5, length:20\n", "typeCode:0105, pageNo:6, length:17\n", "即将获取类目数据:水果包装\n", "typeCode:0104, pageNo:1, length:20\n", "typeCode:0104, pageNo:2, length:20\n", "typeCode:0104, pageNo:3, length:1\n", "即将获取类目数据:证件信息\n", "typeCode:5501, pageNo:1, length:1\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>goodsCode</th>\n", "      <th>goodsName</th>\n", "      <th>goodsLogo</th>\n", "      <th>goodsPics</th>\n", "      <th>goodsContext</th>\n", "      <th>attentions</th>\n", "      <th>goodsShows</th>\n", "      <th>planOfOrigin</th>\n", "      <th>goodTypes</th>\n", "      <th>...</th>\n", "      <th>buyCount</th>\n", "      <th>onStartTime</th>\n", "      <th>onEndTime</th>\n", "      <th>offStartTime</th>\n", "      <th>offEndTime</th>\n", "      <th>sourceType</th>\n", "      <th>packNum</th>\n", "      <th>ids</th>\n", "      <th>shopTypeName</th>\n", "      <th>categoryName</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>26855</td>\n", "      <td>33011002014029022</td>\n", "      <td>【小张】8424麒麟瓜(有籽.1+).东台.箱装</td>\n", "      <td>http://fggimg.fengguog.com/img_root/img_goods/...</td>\n", "      <td>http://fggimg.fengguog.com/img_root/img_goods/...</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>东台</td>\n", "      <td>0204@0501</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>爆款单品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>26856</td>\n", "      <td>33011002014029022</td>\n", "      <td>【小张】8424麒麟瓜(有籽.1号).东台.箱装</td>\n", "      <td>http://fggimg.fengguog.com/img_root/img_goods/...</td>\n", "      <td>http://fggimg.fengguog.com/img_root/img_goods/...</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>东台</td>\n", "      <td>0204@0501</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>爆款单品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>26857</td>\n", "      <td>33011002045070622</td>\n", "      <td>【小张】8424麒麟瓜(有籽.2号).东台.箱装</td>\n", "      <td>http://fggimg.fengguog.com/img_root/img_goods/...</td>\n", "      <td>http://fggimg.fengguog.com/img_root/img_goods/...</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>东台</td>\n", "      <td>0204@0501</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>爆款单品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>26947</td>\n", "      <td>33011002548085622</td>\n", "      <td>【引流】小台芒.广西.箱装</td>\n", "      <td>http://fggimg.fengguog.com/img_root/img_goods/...</td>\n", "      <td>http://fggimg.fengguog.com/img_root/img_goods/...</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>广西</td>\n", "      <td>0214@0501</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>爆款单品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>26943</td>\n", "      <td>33011002548084722</td>\n", "      <td>【秒杀】盛通荔枝(妃子笑.中果).海南.泡沫箱</td>\n", "      <td>http://fggimg.fengguog.com/img_root/img_goods/...</td>\n", "      <td>http://fggimg.fengguog.com/img_root/img_goods/...</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>海南</td>\n", "      <td>0203@0501</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>爆款单品</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 48 columns</p>\n", "</div>"], "text/plain": ["      id          goodsCode                 goodsName  \\\n", "0  26855  33011002014029022  【小张】8424麒麟瓜(有籽.1+).东台.箱装   \n", "1  26856  33011002014029022  【小张】8424麒麟瓜(有籽.1号).东台.箱装   \n", "2  26857  33011002045070622  【小张】8424麒麟瓜(有籽.2号).东台.箱装   \n", "3  26947  33011002548085622             【引流】小台芒.广西.箱装   \n", "4  26943  33011002548084722   【秒杀】盛通荔枝(妃子笑.中果).海南.泡沫箱   \n", "\n", "                                           goodsLogo  \\\n", "0  http://fggimg.fengguog.com/img_root/img_goods/...   \n", "1  http://fggimg.fengguog.com/img_root/img_goods/...   \n", "2  http://fggimg.fengguog.com/img_root/img_goods/...   \n", "3  http://fggimg.fengguog.com/img_root/img_goods/...   \n", "4  http://fggimg.fengguog.com/img_root/img_goods/...   \n", "\n", "                                           goodsPics goodsContext attentions  \\\n", "0  http://fggimg.fengguog.com/img_root/img_goods/...                    None   \n", "1  http://fggimg.fengguog.com/img_root/img_goods/...                    None   \n", "2  http://fggimg.fengguog.com/img_root/img_goods/...                    None   \n", "3  http://fggimg.fengguog.com/img_root/img_goods/...                    None   \n", "4  http://fggimg.fengguog.com/img_root/img_goods/...                    None   \n", "\n", "  goodsShows planOfOrigin  goodTypes  ... buyCount onStartTime onEndTime  \\\n", "0       None           东台  0204@0501  ...        0        None      None   \n", "1       None           东台  0204@0501  ...        0        None      None   \n", "2       None           东台  0204@0501  ...        0        None      None   \n", "3       None           广西  0214@0501  ...        0        None      None   \n", "4       None           海南  0203@0501  ...        0        None      None   \n", "\n", "  offStartTime offEndTime  sourceType  packNum   ids  shopTypeName  \\\n", "0         None       None        None     None  None          None   \n", "1         None       None        None     None  None          None   \n", "2         None       None        None     None  None          None   \n", "3         None       None        None     None  None          None   \n", "4         None       None        None     None  None          None   \n", "\n", "  categoryName  \n", "0         爆款单品  \n", "1         爆款单品  \n", "2         爆款单品  \n", "3         爆款单品  \n", "4         爆款单品  \n", "\n", "[5 rows x 48 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_product_by_second_cate(second_cate={}, pageNo=1):\n", "    # 商品列表：\n", "    pageSize = 20\n", "    typeCode = second_cate[\"typeCode\"]\n", "    url = f\"http://fggapi.fengguog.com/goods/list?pageNo={pageNo}&pageSize={pageSize}&shopId={shopId}&typeCode={typeCode}&websiteNode=3301\"\n", "    products = requests.get(url=url).json()[\"data\"][\"list\"]\n", "    print(f\"typeCode:{typeCode}, pageNo:{pageNo}, length:{len(products)}\")\n", "    if len(products) < pageSize:\n", "        return products\n", "    else:\n", "        nested_list = get_product_by_second_cate(\n", "            second_cate=second_cate, pageNo=pageNo + 1\n", "        )\n", "        products.extend(nested_list)\n", "        return products\n", "\n", "\n", "all_product_list = []\n", "for second_cate in all_second_cate_list:\n", "    typeName = second_cate[\"typeName\"]\n", "    print(f\"即将获取类目数据:{typeName}\")\n", "    product_of_cate = get_product_by_second_cate(second_cate)\n", "    for product in product_of_cate:\n", "        product[\"categoryName\"] = typeName\n", "    all_product_list.extend(product_of_cate)\n", "\n", "all_product_list_df = pd.DataFrame(all_product_list)\n", "all_product_list_df.head(5)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>goodsName</th>\n", "      <th>goodsCode</th>\n", "      <th>priceDesc</th>\n", "      <th>wholeGgguoPrice</th>\n", "      <th>wholePriceSize</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>【引流】小台芒.广西.箱装</td>\n", "      <td>33011002548085622</td>\n", "      <td>约54.15元/箱</td>\n", "      <td>54.15</td>\n", "      <td>箱</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>【引流】小台芒.广西.箱装</td>\n", "      <td>33011002548085622</td>\n", "      <td>约54.15元/箱</td>\n", "      <td>54.15</td>\n", "      <td>箱</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>小台芒.广西.箱装</td>\n", "      <td>33011002551058122</td>\n", "      <td>约65.55元/箱</td>\n", "      <td>65.55</td>\n", "      <td>箱</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>大台芒.海南.箱装</td>\n", "      <td>33011002548071022</td>\n", "      <td>约99.80元/箱</td>\n", "      <td>99.80</td>\n", "      <td>箱</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       goodsName          goodsCode  priceDesc wholeGgguoPrice wholePriceSize\n", "0  【引流】小台芒.广西.箱装  33011002548085622  约54.15元/箱           54.15              箱\n", "1  【引流】小台芒.广西.箱装  33011002548085622  约54.15元/箱           54.15              箱\n", "2      小台芒.广西.箱装  33011002551058122  约65.55元/箱           65.55              箱\n", "3      大台芒.海南.箱装  33011002548071022  约99.80元/箱           99.80              箱"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandasql\n", "\n", "df=pandasql.sqldf('select goodsName,goodsCode,pricedesc,wholeggguoprice,wholepricesize from all_product_list_df where goodsName like \"%台芒%\"')\n", "df"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DaraFrame字段合集:\n", "\n", "attentions,\n", "packnum,\n", "sourcetype,\n", "wholepricesize,\n", "categoryname,\n", "goodssort,\n", "shoptype,\n", "spider_fetch_time,\n", "websitenode,\n", "buycount,\n", "onstarttime,\n", "suppliermobile,\n", "shoptypename,\n", "goodsge,\n", "offtime,\n", "maxcount,\n", "singlenutweight,\n", "ontime,\n", "ids,\n", "ggguo<PERSON><PERSON>,\n", "sizedesc,\n", "websitename,\n", "goodsname,\n", "surplusnum,\n", "netweight,\n", "goodspics,\n", "roughweight,\n", "outpeel,\n", "goodtypes,\n", "goodscode,\n", "planoforigin,\n", "competitor_name,\n", "goodscontext,\n", "offendtime,\n", "ds,\n", "goodstypename,\n", "goodslogo,\n", "initnum,\n", "id,\n", "orderno,\n", "goodsshows,\n", "goodsgeunit,\n", "pricedesc,\n", "wholeggguoprice,\n", "offstarttime,\n", "createtime,\n", "competitor,\n", "priceunit,\n", "status,\n", "goodswholecount,\n", "salenum,\n", "onendtime\n", "成功写入odps:summerfarm_ds.spider_fengguogong_product_result_df, partition_spec:ds=20240515,competitor_name=fengguogong, attemp:0\n", "sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_fengguogong_product_result_df\n", "                             where ds>='20240415' group by ds,competitor_name  order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240515</td>\n", "      <td>fengguogong</td>\n", "      <td>1596</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20240514</td>\n", "      <td>fengguogong</td>\n", "      <td>2410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20240513</td>\n", "      <td>fengguogong</td>\n", "      <td>2429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20240512</td>\n", "      <td>fengguogong</td>\n", "      <td>2434</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20240511</td>\n", "      <td>fengguogong</td>\n", "      <td>2469</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>20240510</td>\n", "      <td>fengguogong</td>\n", "      <td>2633</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>20240509</td>\n", "      <td>fengguogong</td>\n", "      <td>1661</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>20240508</td>\n", "      <td>fengguogong</td>\n", "      <td>1611</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>20240507</td>\n", "      <td>fengguogong</td>\n", "      <td>1521</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>20240506</td>\n", "      <td>fengguogong</td>\n", "      <td>1474</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>20240505</td>\n", "      <td>fengguogong</td>\n", "      <td>1468</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>20240504</td>\n", "      <td>fengguogong</td>\n", "      <td>1617</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>20240503</td>\n", "      <td>fengguogong</td>\n", "      <td>1630</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>20240502</td>\n", "      <td>fengguogong</td>\n", "      <td>1584</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>20240501</td>\n", "      <td>fengguogong</td>\n", "      <td>1636</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>20240430</td>\n", "      <td>fengguogong</td>\n", "      <td>1663</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>20240429</td>\n", "      <td>fengguogong</td>\n", "      <td>1664</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>20240428</td>\n", "      <td>fengguogong</td>\n", "      <td>1652</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>20240427</td>\n", "      <td>fengguogong</td>\n", "      <td>1666</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>20240426</td>\n", "      <td>fengguogong</td>\n", "      <td>1710</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>20240425</td>\n", "      <td>fengguogong</td>\n", "      <td>1686</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>20240424</td>\n", "      <td>fengguogong</td>\n", "      <td>1679</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>20240423</td>\n", "      <td>fengguogong</td>\n", "      <td>2536</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>20240422</td>\n", "      <td>fengguogong</td>\n", "      <td>2502</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>20240421</td>\n", "      <td>fengguogong</td>\n", "      <td>1629</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>20240420</td>\n", "      <td>fengguogong</td>\n", "      <td>1639</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>20240419</td>\n", "      <td>fengguogong</td>\n", "      <td>2560</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>20240418</td>\n", "      <td>fengguogong</td>\n", "      <td>1688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>20240417</td>\n", "      <td>fengguogong</td>\n", "      <td>1695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>20240416</td>\n", "      <td>fengguogong</td>\n", "      <td>1683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>20240415</td>\n", "      <td>fengguogong</td>\n", "      <td>1659</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          ds competitor_name  recods\n", "0   20240515     fengguogong    1596\n", "1   20240514     fengguogong    2410\n", "2   20240513     fengguogong    2429\n", "3   20240512     fengguogong    2434\n", "4   20240511     fengguogong    2469\n", "5   20240510     fengguogong    2633\n", "6   20240509     fengguogong    1661\n", "7   20240508     fengguogong    1611\n", "8   20240507     fengguogong    1521\n", "9   20240506     fengguogong    1474\n", "10  20240505     fengguogong    1468\n", "11  20240504     fengguogong    1617\n", "12  20240503     fengguogong    1630\n", "13  20240502     fengguogong    1584\n", "14  20240501     fengguogong    1636\n", "15  20240430     fengguogong    1663\n", "16  20240429     fengguogong    1664\n", "17  20240428     fengguogong    1652\n", "18  20240427     fengguogong    1666\n", "19  20240426     fengguogong    1710\n", "20  20240425     fengguogong    1686\n", "21  20240424     fengguogong    1679\n", "22  20240423     fengguogong    2536\n", "23  20240422     fengguogong    2502\n", "24  20240421     fengguogong    1629\n", "25  20240420     fengguogong    1639\n", "26  20240419     fengguogong    2560\n", "27  20240418     fengguogong    1688\n", "28  20240417     fengguogong    1695\n", "29  20240416     fengguogong    1683\n", "30  20240415     fengguogong    1659"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "all_product_list_df['competitor']=brand_name\n", "all_products_df=all_product_list_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name  order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
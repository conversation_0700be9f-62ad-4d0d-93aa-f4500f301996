FROM python:3.8-slim

# Use Tsinghua University mirror for Debian
# And then install ffmpeg
RUN sed -i 's|http://deb.debian.org/debian|https://mirrors.tuna.tsinghua.edu.cn/debian|g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|http://security.debian.org/debian-security|https://mirrors.tuna.tsinghua.edu.cn/debian-security|g' /etc/apt/sources.list.d/debian.sources && \
    apt update && apt install -y ffmpeg

WORKDIR /app/spider

COPY ./requirements.txt ./requirements.txt

RUN pip install --index-url=https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host=pypi.tuna.tsinghua.edu.cn --default-timeout=30 -r requirements.txt
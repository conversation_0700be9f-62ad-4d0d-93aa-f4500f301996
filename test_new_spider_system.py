#!/usr/bin/env python3
"""
测试新的爬虫结构化输出系统
"""

import sys
import os
import tempfile
import subprocess
import json
import re
from pathlib import Path

# 添加scripts目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

def test_spider_reporter():
    """测试SpiderResultReporter类"""
    print("=== 测试SpiderResultReporter类 ===")
    
    try:
        from proxy_setup import create_spider_reporter
        
        # 测试成功情况
        print("1. 测试成功报告...")
        reporter = create_spider_reporter("test_spider.py", "测试品牌")
        
        # 捕获输出
        import io
        from contextlib import redirect_stdout
        
        captured_output = io.StringIO()
        with redirect_stdout(captured_output):
            result = reporter.report_success(
                product_count=100,
                additional_info={"test_field": "test_value"}
            )
        
        output = captured_output.getvalue()
        print(f"  输出: {output.strip()}")
        
        # 检查JSON输出
        if "SPIDER_RESULT_JSON:" in output:
            json_part = output.split("SPIDER_RESULT_JSON:")[1].strip()
            try:
                parsed = json.loads(json_part)
                print(f"  ✅ JSON解析成功: {parsed}")
                assert parsed["status"] == "success"
                assert parsed["product_count"] == 100
                assert parsed["brand_name"] == "测试品牌"
                print("  ✅ 成功报告测试通过")
            except json.JSONDecodeError as e:
                print(f"  ❌ JSON解析失败: {e}")
                return False
        else:
            print("  ❌ 未找到JSON输出")
            return False
        
        # 测试失败情况
        print("2. 测试失败报告...")
        captured_output = io.StringIO()
        with redirect_stdout(captured_output):
            result = reporter.report_failure(
                error_message="测试错误",
                error_type="test_error"
            )
        
        output = captured_output.getvalue()
        if "SPIDER_RESULT_JSON:" in output:
            json_part = output.split("SPIDER_RESULT_JSON:")[1].strip()
            try:
                parsed = json.loads(json_part)
                print(f"  ✅ 失败JSON解析成功: {parsed}")
                assert parsed["status"] == "failure"
                assert parsed["error_message"] == "测试错误"
                print("  ✅ 失败报告测试通过")
            except json.JSONDecodeError as e:
                print(f"  ❌ 失败JSON解析失败: {e}")
                return False
        
        # 测试部分成功情况
        print("3. 测试部分成功报告...")
        captured_output = io.StringIO()
        with redirect_stdout(captured_output):
            result = reporter.report_partial_success(
                product_count=50,
                warning_message="部分数据缺失"
            )
        
        output = captured_output.getvalue()
        if "SPIDER_RESULT_JSON:" in output:
            json_part = output.split("SPIDER_RESULT_JSON:")[1].strip()
            try:
                parsed = json.loads(json_part)
                print(f"  ✅ 部分成功JSON解析成功: {parsed}")
                assert parsed["status"] == "partial_success"
                assert parsed["product_count"] == 50
                print("  ✅ 部分成功报告测试通过")
            except json.JSONDecodeError as e:
                print(f"  ❌ 部分成功JSON解析失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ SpiderResultReporter测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_run_all_docker_parsing():
    """测试run_all_docker.sh的JSON解析功能"""
    print("\n=== 测试run_all_docker.sh的JSON解析功能 ===")
    
    # 创建测试日志文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
        test_log_content = '''
[2025-07-02 10:00:00] 开始执行测试爬虫
SPIDER_RESULT_JSON:{"status": "success", "spider_name": "test_spider.py", "brand_name": "测试品牌", "product_count": 123, "start_time": "2025-07-02 10:00:00", "end_time": "2025-07-02 10:05:00", "duration_seconds": 300, "timestamp": "2025-07-02 10:05:00"}
[2025-07-02 10:05:00] 爬虫执行完成
'''
        f.write(test_log_content)
        test_log_path = f.name
    
    try:
        # 测试bash脚本的JSON解析函数
        bash_test_script = f'''
#!/bin/bash
source ./run_all_docker.sh

# 测试parse_spider_json_result函数
result=$(parse_spider_json_result "{test_log_path}")
echo "解析结果: $result"

# 测试extract_json_field函数
if [ -n "$result" ]; then
    status=$(extract_json_field "$result" "status" "unknown")
    brand_name=$(extract_json_field "$result" "brand_name" "unknown")
    product_count=$(extract_json_field "$result" "product_count" "0")
    
    echo "状态: $status"
    echo "品牌: $brand_name"
    echo "数量: $product_count"
    
    if [ "$status" = "success" ] && [ "$brand_name" = "测试品牌" ] && [ "$product_count" = "123" ]; then
        echo "✅ JSON解析测试通过"
        exit 0
    else
        echo "❌ JSON解析测试失败"
        exit 1
    fi
else
    echo "❌ 未找到JSON结果"
    exit 1
fi
'''
        
        # 写入临时bash脚本
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False) as f:
            f.write(bash_test_script)
            bash_script_path = f.name
        
        # 执行bash测试
        os.chmod(bash_script_path, 0o755)
        result = subprocess.run(['bash', bash_script_path], 
                              capture_output=True, text=True, 
                              cwd=Path(__file__).parent)
        
        print(f"Bash测试输出: {result.stdout}")
        if result.stderr:
            print(f"Bash测试错误: {result.stderr}")
        
        if result.returncode == 0:
            print("✅ run_all_docker.sh JSON解析测试通过")
            return True
        else:
            print("❌ run_all_docker.sh JSON解析测试失败")
            return False
            
    except Exception as e:
        print(f"❌ Bash脚本测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(test_log_path)
            os.unlink(bash_script_path)
        except:
            pass

def test_feishu_notification():
    """测试飞书通知脚本"""
    print("\n=== 测试飞书通知脚本 ===")
    
    try:
        # 测试成功结果解析
        test_success_result = "测试品牌:::123, 2025-07-02 10:05:00__EOF__"
        test_failed_result = "test_spider.py:::执行失败, 2025-07-02 10:05:00|||测试错误日志__EOF__"
        
        # 运行飞书通知脚本
        result = subprocess.run([
            'python', 'send_feishu_notification.py',
            '--all_success_result', test_success_result,
            '--all_failed_result', test_failed_result,
            '--time_cost_in_seconds', '300'
        ], capture_output=True, text=True, cwd=Path(__file__).parent)
        
        print(f"飞书通知脚本输出: {result.stdout}")
        if result.stderr:
            print(f"飞书通知脚本错误: {result.stderr}")
        
        # 检查输出是否包含预期内容
        if "测试品牌, 记录数:123" in result.stdout and "test_spider.py" in result.stdout:
            print("✅ 飞书通知脚本测试通过")
            return True
        else:
            print("❌ 飞书通知脚本测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 飞书通知脚本测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试新的爬虫结构化输出系统...\n")
    
    tests = [
        ("SpiderResultReporter类", test_spider_reporter),
        ("run_all_docker.sh JSON解析", test_run_all_docker_parsing),
        ("飞书通知脚本", test_feishu_notification)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print(f"=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！新的结构化输出系统工作正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

{"cells": [{"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["70b9bbf5d3a61f537138dd684a7cca61\n"]}], "source": ["import hashlib\n", "\n", "# The string you want to hash\n", "input_string = \"open-id=oMwzt0BqvI2kk5EeTn-D5M9sxVk4&timestamp=*************&page=1&size=20&area_second_id=3a182727-4364-4cfc-b12f-bc29531b6853&sort=0&query_by_category=false&third_navigation_type=10&navigation_index=0\"\n", "\n", "# Create a new MD5 hash object\n", "md5_hash = hashlib.md5()\n", "\n", "# Update the hash object with the bytes of the string\n", "md5_hash.update(input_string.encode('utf-8'))\n", "\n", "# Get the hexadecimal representation of the hash\n", "md5_hex = md5_hash.hexdigest()\n", "\n", "\"dac56d4d3e06c6d4806dab81b1c501d1\"\n", "\"4663f735462e50d211fafa92df3c32d8\"\n", "\n", "\n", "print(md5_hex)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1712819674235, headers:{'pp-suid': '0228dff8-9e66-4c94-aa70-8502e257d242', 'pp_storeid': 'b565ec67-fd76-4195-888e-b6ff156b2adc', 'pp-userid': '1e8382ff-662f-483d-8c6a-a733195683a6', 'timestamp': '*************', 'content-type': 'application/json', 'pp-version': '**********', 'seal': '{\"a\":\"ef3e8b510806e8264a665bd6a5b9ac07HQzfGAss\",\"b\":\"msRn9fs8\",\"c\":\"D4rg64+H\",\"d\":\"HwJ/Bhm8\",\"f\":\"fq9WJjlrP5lW03DeqQBlxa6QCnVVcN/a6TDgZdYOVuTaHeDuC/DolVEp4AARwktgHOMjoY\"}', 'pp-os': '0', 'Host': 'j1.pupuapi.com', 'Connection': 'keep-alive', 'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN', 'pp_store_city_zip': '350100', 'Accept-Encoding': 'gzip,compress,br,deflate', 'Accept': 'application/json', 'sign': 'dac56d4d3e06c6d4806dab81b1c501d1', 'Referer': 'https://servicewechat.com/wx122ef876a7132eb4/368/page-frame.html', 'pp-seqid': 'hzbXEaw8Xs8cGTLGMcykYBGuBjD9Y8fJu3CUEdLoqW0=', 'pp-placeid': 'f47030ca-fc00-4736-b62a-e067a3ba747a', 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIiLCJhdWQiOiJodHRwczovL3VjLnB1cHVhcGkuY29tIiwiaXNfbm90X25vdmljZSI6IjAiLCJpc3MiOiJodHRwczovL3VjLnB1cHVhcGkuY29tIiwiZ2l2ZW5fbmFtZSI6IueQhuaZuueahOmdkuaPkCIsImV4cCI6MTcxMjgyNTI1OCwidmVyc2lvbiI6IjIuMCIsImp0aSI6IjFlODM4MmZmLTY2MmYtNDgzZC04YzZhLWE3MzMxOTU2ODNhNiJ9.lllnMQWa_D0Nx5sWU_4FeCGMnOBXia3pPCdCn37jCZI'}\n"]}, {"data": {"text/plain": ["{'errcode': 0, 'errmsg': '', 'data': {}}"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# 写入odps\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"pp-suid\": \"0228dff8-9e66-4c94-aa70-8502e257d242\",\n", "    \"pp_storeid\": \"b565ec67-fd76-4195-888e-b6ff156b2adc\",\n", "    \"open-id\": \"oMwzt0BqvI2kk5EeTn-D5M9sxVk4\",\n", "    \"pp-userid\": \"1e8382ff-662f-483d-8c6a-a733195683a6\",\n", "    \"timestamp\": \"*************\",\n", "    \"content-type\": \"application/json\",\n", "    \"pp-version\": \"**********\",\n", "    \"seal\": '{\"a\":\"ef3e8b510806e8264a665bd6a5b9ac07HQzfGAss\",\"b\":\"msRn9fs8\",\"c\":\"D4rg64+H\",\"d\":\"HwJ/Bhm8\",\"f\":\"fq9WJjlrP5lW03DeqQBlxa6QCnVVcN/a6TDgZdYOVuTaHeDuC/DolVEp4AARwktgHOMjoY\"}',\n", "    \"pp-os\": \"0\",\n", "    \"Host\": \"j1.pupuapi.com\",\n", "    \"Connection\": \"keep-alive\",\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN\",\n", "    \"pp_store_city_zip\": \"350100\",\n", "    \"Accept-Encoding\": \"gzip,compress,br,deflate\",\n", "    \"Accept\": \"application/json\",\n", "    \"sign\": \"dac56d4d3e06c6d4806dab81b1c501d1\",\n", "    \"Referer\": \"https://servicewechat.com/wx122ef876a7132eb4/368/page-frame.html\",\n", "    \"pp-seqid\": \"hzbXEaw8Xs8cGTLGMcykYBGuBjD9Y8fJu3CUEdLoqW0=\",\n", "    \"pp-placeid\": \"f47030ca-fc00-4736-b62a-e067a3ba747a\",\n", "    \"Authorization\": \"Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIiLCJhdWQiOiJodHRwczovL3VjLnB1cHVhcGkuY29tIiwiaXNfbm90X25vdmljZSI6IjAiLCJpc3MiOiJodHRwczovL3VjLnB1cHVhcGkuY29tIiwiZ2l2ZW5fbmFtZSI6IueQhuaZuueahOmdkuaPkCIsImV4cCI6MTcxMjgyNTI1OCwidmVyc2lvbiI6IjIuMCIsImp0aSI6IjFlODM4MmZmLTY2MmYtNDgzZC04YzZhLWE3MzMxOTU2ODNhNiJ9.lllnMQWa_D0Nx5sWU_4FeCGMnOBXia3pPCdCn37jCZI\",\n", "}\n", "brand_name = \"扑扑超市-福州\"\n", "competitor_name_en = \"pupu_supermarket\"\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")\n", "\n", "import requests\n", "url=\"https://j1.pupuapi.com/client/marketing/classification/product/recall/v3?page=1&size=20&area_second_id=3a182727-4364-4cfc-b12f-bc29531b6853&sort=0&query_by_category=false&third_navigation_type=10&navigation_index=0\"\n", "products=requests.get(url=url, headers=headers).json()\n", "products"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "all_products=[]\n", "def get_products_by_page(page=1):\n", "    url = \"https://weixin.dinghuo365.com/biz/std_mendian/pd/client/v1/queryPds.action\"\n", "    data = {\n", "        \"orderType\": \"MERP\",\n", "        \"page\": page,\n", "        \"rows\": 20,\n", "        \"class_id\": \"-1\",\n", "        \"period\": \"all\",\n", "        \"brand\": \"\",\n", "        \"brandIds\": \"\",\n", "        \"orderByField\": \"\",\n", "        \"sortWay\": \"asc\",\n", "    }\n", "    cookies = {\n", "        \"sourceType\": \"CLIENT\",\n", "        \"WQSESSIONID\": \"27E4A77DA514A0ACFBC79B217CA0A03D.10\",\n", "        \"x-token\": jwtT<PERSON>,\n", "        \"tenantId\": \"6489320774649852103\",\n", "    }\n", "\n", "    products = get_remote_data_with_proxy_json(url=url, json=data, cookies=cookies)[\n", "        \"data\"\n", "    ][\"products\"]\n", "    if products is not None and len(products) >= 20:\n", "        print(f\"未取完:{page},继续...\")\n", "        sub_list = get_products_by_page(page=page + 1)\n", "        if len(sub_list) > 0:\n", "            products.extend(sub_list)\n", "    return products\n", "\n", "all_products=get_products_by_page(1)\n", "all_sku_list=[]\n", "for spu in all_products:\n", "    for unit_info in spu['unit_info']:\n", "        print(unit_info)\n", "        sku_info={}\n", "        for key,value in unit_info.items():\n", "            sku_info[f\"sku_{key}\"]=value\n", "        sku_info.update(spu)\n", "        del sku_info['unit_info']\n", "        all_sku_list.append(sku_info)\n", "\n", "len(all_sku_list)\n", "all_sku_list_df=pd.DataFrame(all_sku_list)\n", "all_sku_list_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "all_sku_list_df['competitor']=brand_name\n", "all_products_df=all_sku_list_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
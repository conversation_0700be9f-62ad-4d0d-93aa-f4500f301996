{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "1708480191235, headers:{'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q', 'appcodenew': '7798c1f4306b4f89a9fc2a4c2cdc47ac', 'uid': '712451', 'time': '*************'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "THREAD_CNT = int(os.environ.get('THREAD_CNT', 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "hints={'odps.sql.hive.compatible':True,'odps.sql.type.system.odps2':True}\n", "def get_odps_sql_result_as_df(sql):\n", "    instance=odps.execute_sql(sql, hints=hints)\n", "    instance.wait_for_success()\n", "    pd_df=None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={'token':'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q',\n", "'appcodenew':'7798c1f4306b4f89a9fc2a4c2cdc47ac',\n", "'uid':'712451',\n", "'time':'*************',}\n", "brand_name='藏珍'\n", "competitor_name_en='cangzhen'\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 先登录\n", "\n", "获取token并保存"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'token': 'fkUDvjW1Ni0oq4cEhTv5xpOrPY/obuIyZ22WY6bo+9KWTNDMQl5PVUvyVmLKU5MMKTYUm0V5EPUqFLCYs0PXpzATdKi5gazuQ7B/nQ9s0imUphXBk1rby1wRvPzggAtJHHu9avApNcEihmKEx+hFEnbTpUJKw+r0qHPKA8njNwY='}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data={'accounts_name':'***********',\n", "'accounts_pass':'767776326c6433397474576a456271314146394346504c55304974353045764b7761472f687330775870706c495845303435463048505153394b734a44326d76695a6b732f654b4e4c4a4b5937744f6462302b39667755445263637a72436a65376c483641694a30715a6b33314936497547666b4c5838364f7372345a6c583070493144313937727358504b4a6d71684c5963354e4f474e6d646564314f574330505773616369434d41343d',\n", "'login_type':'xcx',}\n", "r=requests.post(f'https://passport.dhb168.com/login/mUserLogin',data=data, headers=headers)\n", "\n", "login_res=r.json()\n", "login_res"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'status': 'T', 'data': {'access_token': '6kYLCHw6KtRO61itqGZ4TMzP4FAqag3Yglhxz49b', 'token_type': 'Bearer', 'expires_in': 2592000, 'refresh_token': 'OxhzpqCMMDkWOAsqz8YDVWLpA4UH1XMZ1bplJdRm', 'skey': '09f8f324d86cd1fb31c0c6cfae5f6555', 'auth_token': 'a0efb6d5614d587571cf715f12273efd', 'is_more': 'T', 'isShare': True, 'company_id': 198679, 'component_appid': 'wxd2c2555c5732b86f', 'wid': 'wx61a903d0ed3feccf', 'is_dhb': 0, 'registration_code': '6968571,198679', 'registration_code_signature': '024b4ec21b5fe3714e58d73a9a3c795a', 'is_open_safe_login': False, 'forbidden_bind_multiple_wechat': 'F'}, 'code': 200}\n"]}], "source": ["import urllib.parse\n", "\n", "token=login_res['token']\n", "token_data={'client_id':'3',\n", "'client_secret':'1a164178e7a4c824303318b2b6f4a0df337b68d6',\n", "'need_skey':'1',\n", "'scope':'',\n", "'code':'undefined',\n", "'appid':'wx61a903d0ed3feccf',\n", "'grant_type':'agency_password_sso',\n", "'token':f'{token}',\n", "'company_id':'198679',}\n", "\n", "r=requests.post(f'https://admin.dhb168.com/Auth/oauth/token', data=token_data).json()\n", "print(r)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6kYLCHw6KtRO61itqGZ4TMzP4FAqag3Yglhxz49b 09f8f324d86cd1fb31c0c6cfae5f6555\n", "{'first_category': [{'category_id': '27817', 'company_id': '198679', 'parent_id': '0', 'order_num': '499', 'category_pnum': '0.27817.', 'category_num': '1121', 'category_name': '牛油果/龙眼', 'level_num': '1', 'category_count': '2', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 1, 'calc_goods_count': '1'}, {'category_id': '21554', 'company_id': '198679', 'parent_id': '0', 'order_num': '498', 'category_pnum': '0.21554.', 'category_num': '1001', 'category_name': '芒果类', 'level_num': '1', 'category_count': '3', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 2, 'calc_goods_count': '1'}, {'category_id': '27450', 'company_id': '198679', 'parent_id': '0', 'order_num': '497', 'category_pnum': '0.27450.', 'category_num': '1065', 'category_name': '瓜类', 'level_num': '1', 'category_count': '4', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 1, 'calc_goods_count': '1'}, {'category_id': '27437', 'company_id': '198679', 'parent_id': '0', 'order_num': '496', 'category_pnum': '0.27437.', 'category_num': '1052', 'category_name': '柑橘橙', 'level_num': '1', 'category_count': '3', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/11/1667446444893_18797388334843856.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/11/1667446444893_18797388334843856.png', 'custom_sub_level': 3, 'calc_goods_count': '1'}, {'category_id': '21626', 'company_id': '198679', 'parent_id': '0', 'order_num': '495', 'category_pnum': '0.21626.', 'category_num': '1006', 'category_name': '凤梨/菠萝', 'level_num': '1', 'category_count': '2', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 2, 'calc_goods_count': '1'}, {'category_id': '27468', 'company_id': '198679', 'parent_id': '0', 'order_num': '494', 'category_pnum': '0.27468.', 'category_num': '1083', 'category_name': '草莓/李子/猕猴桃', 'level_num': '1', 'category_count': '3', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 2, 'calc_goods_count': '1'}, {'category_id': '21627', 'company_id': '198679', 'parent_id': '0', 'order_num': '491', 'category_pnum': '0.21627.', 'category_num': '1007', 'category_name': '葡萄/提子', 'level_num': '1', 'category_count': '5', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 2, 'calc_goods_count': '1'}, {'category_id': '27814', 'company_id': '198679', 'parent_id': '0', 'order_num': '489', 'category_pnum': '0.27814.', 'category_num': '1118', 'category_name': '其他', 'level_num': '1', 'category_count': '5', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 1, 'calc_goods_count': '1'}], 'second_category': {'21554': [{'category_id': '27455', 'company_id': '198679', 'parent_id': '21554', 'order_num': '500', 'category_pnum': '0.21554.27455.', 'category_num': '1070', 'category_name': '台芒', 'level_num': '2', 'category_count': '3', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1673749698080_6429771523409247.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1673749698080_6429771523409247.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}], '21626': [{'category_id': '25486', 'company_id': '198679', 'parent_id': '21626', 'order_num': '500', 'category_pnum': '0.21626.25486.', 'category_num': '1020', 'category_name': '有冠凤梨', 'level_num': '2', 'category_count': '2', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/17/1669714366465_84472187520813.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/17/1669714366465_84472187520813.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}], '21627': [{'category_id': '25485', 'company_id': '198679', 'parent_id': '21627', 'order_num': '499', 'category_pnum': '0.21627.25485.', 'category_num': '1019', 'category_name': '夏黑葡萄', 'level_num': '2', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/9/1667524998894_4642430645843063.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/9/1667524998894_4642430645843063.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}, {'category_id': '26789', 'company_id': '198679', 'parent_id': '21627', 'order_num': '498', 'category_pnum': '0.21627.26789.', 'category_num': '1035', 'category_name': '青提', 'level_num': '2', 'category_count': '2', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/14/1666766580158_664671174206056.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/14/1666766580158_664671174206056.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}], '25507': [], '27400': [], '27437': [{'category_id': '27438', 'company_id': '198679', 'parent_id': '27437', 'order_num': '500', 'category_pnum': '0.27437.27438.', 'category_num': '1053', 'category_name': '柠檬', 'level_num': '2', 'category_count': '5', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 1, 'calc_goods_count': '1'}, {'category_id': '27444', 'company_id': '198679', 'parent_id': '27437', 'order_num': '500', 'category_pnum': '0.27437.27444.', 'category_num': '1059', 'category_name': '柑桔', 'level_num': '2', 'category_count': '3', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/9/1708133570828_8726460989979559.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/9/1708133570828_8726460989979559.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}, {'category_id': '27447', 'company_id': '198679', 'parent_id': '27437', 'order_num': '500', 'category_pnum': '0.27437.27447.', 'category_num': '1062', 'category_name': '橙类', 'level_num': '2', 'category_count': '2', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 2, 'calc_goods_count': '1'}], '27450': [{'category_id': '27451', 'company_id': '198679', 'parent_id': '27450', 'order_num': '500', 'category_pnum': '0.27450.27451.', 'category_num': '1066', 'category_name': '麒麟西瓜', 'level_num': '2', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/17/1669714095153_17892891133953248.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/17/1669714095153_17892891133953248.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27468': [{'category_id': '27820', 'company_id': '198679', 'parent_id': '27468', 'order_num': '500', 'category_pnum': '0.27468.27820.', 'category_num': '1125', 'category_name': '草莓', 'level_num': '2', 'category_count': '5', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/9/1670722916447_31578669828737227.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/9/1670722916447_31578669828737227.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}, {'category_id': '29135', 'company_id': '198679', 'parent_id': '27468', 'order_num': '500', 'category_pnum': '0.27468.29135.', 'category_num': '1157', 'category_name': '猕猴桃', 'level_num': '2', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/17/1670317625960_9209301420663378.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/17/1670317625960_9209301420663378.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27478': [], '27804': [], '27808': [], '27814': [{'category_id': '29433', 'company_id': '198679', 'parent_id': '27814', 'order_num': '500', 'category_pnum': '0.27814.29433.', 'category_num': '1158', 'category_name': '车厘子', 'level_num': '2', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1672455389850_4193221454637821.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1672455389850_4193221454637821.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27817': [{'category_id': '28730', 'company_id': '198679', 'parent_id': '27817', 'order_num': '500', 'category_pnum': '0.27817.28730.', 'category_num': '1133', 'category_name': '龙眼', 'level_num': '2', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/9/1673745679979_24491040254924368.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/9/1673745679979_24491040254924368.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '29132': []}, 'third_category': {'25486': [{'category_id': '27460', 'company_id': '198679', 'parent_id': '25486', 'order_num': '500', 'category_pnum': '0.21626.25486.27460.', 'category_num': '1075', 'category_name': '佳农凤梨有冠6-9', 'level_num': '3', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/17/1669714400572_6889506306393953.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/17/1669714400572_6889506306393953.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '25487': [], '26789': [{'category_id': '28737', 'company_id': '198679', 'parent_id': '26789', 'order_num': '500', 'category_pnum': '0.21627.26789.28737.', 'category_num': '1139', 'category_name': '阳光玫瑰', 'level_num': '3', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1670552076667_4852683702494722.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1670552076667_4852683702494722.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27438': [{'category_id': '27439', 'company_id': '198679', 'parent_id': '27438', 'order_num': '500', 'category_pnum': '0.27437.27438.27439.', 'category_num': '1054', 'category_name': '香水柠檬一级', 'level_num': '3', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/11/1667531809825_8890380387623855.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/11/1667531809825_8890380387623855.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}, {'category_id': '27442', 'company_id': '198679', 'parent_id': '27438', 'order_num': '500', 'category_pnum': '0.27437.27438.27442.', 'category_num': '1057', 'category_name': '黄柠檬', 'level_num': '3', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/16/1670317030861_8522933714041201.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/16/1670317030861_8522933714041201.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27444': [{'category_id': '27445', 'company_id': '198679', 'parent_id': '27444', 'order_num': '500', 'category_pnum': '0.27437.27444.27445.', 'category_num': '1060', 'category_name': '青金桔', 'level_num': '3', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1672193366225_8108435662280824.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1672193366225_8108435662280824.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27447': [{'category_id': '27449', 'company_id': '198679', 'parent_id': '27447', 'order_num': '500', 'category_pnum': '0.27437.27447.27449.', 'category_num': '1064', 'category_name': '国产橙', 'level_num': '3', 'category_count': '1', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1669776504504_5694896628822617.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1669776504504_5694896628822617.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}], '27455': [], '27479': [], '27820': [], '27823': []}, 'four_category': {'27448': [], '27449': [{'category_id': '27711', 'company_id': '198679', 'parent_id': '27449', 'order_num': '500', 'category_pnum': '0.27437.27447.27449.27711.', 'category_num': '1103', 'category_name': '赣南脐橙', 'level_num': '4', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1669776545916_7684729528982541.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1669776545916_7684729528982541.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}]}, 'level_num': 4, 'debug': []}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>category_id</th>\n", "      <th>company_id</th>\n", "      <th>parent_id</th>\n", "      <th>order_num</th>\n", "      <th>category_pnum</th>\n", "      <th>category_num</th>\n", "      <th>category_name</th>\n", "      <th>level_num</th>\n", "      <th>category_count</th>\n", "      <th>goods_count</th>\n", "      <th>is_default</th>\n", "      <th>category_image</th>\n", "      <th>has_child</th>\n", "      <th>goods_picture</th>\n", "      <th>custom_sub_level</th>\n", "      <th>calc_goods_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>27817</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>499</td>\n", "      <td>0.27817.</td>\n", "      <td>1121</td>\n", "      <td>牛油果/龙眼</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21554</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>498</td>\n", "      <td>0.21554.</td>\n", "      <td>1001</td>\n", "      <td>芒果类</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27450</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>497</td>\n", "      <td>0.27450.</td>\n", "      <td>1065</td>\n", "      <td>瓜类</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>27437</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>496</td>\n", "      <td>0.27437.</td>\n", "      <td>1052</td>\n", "      <td>柑橘橙</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td>000198/*********/11/1667446444893_187973883348...</td>\n", "      <td>T</td>\n", "      <td>https://img.dhb168.com/000198/*********/11/166...</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21626</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>495</td>\n", "      <td>0.21626.</td>\n", "      <td>1006</td>\n", "      <td>凤梨/菠萝</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>27468</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>494</td>\n", "      <td>0.27468.</td>\n", "      <td>1083</td>\n", "      <td>草莓/李子/猕猴桃</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>21627</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>491</td>\n", "      <td>0.21627.</td>\n", "      <td>1007</td>\n", "      <td>葡萄/提子</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>27814</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>489</td>\n", "      <td>0.27814.</td>\n", "      <td>1118</td>\n", "      <td>其他</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  category_id company_id parent_id order_num category_pnum category_num  \\\n", "0       27817     198679         0       499      0.27817.         1121   \n", "1       21554     198679         0       498      0.21554.         1001   \n", "2       27450     198679         0       497      0.27450.         1065   \n", "3       27437     198679         0       496      0.27437.         1052   \n", "4       21626     198679         0       495      0.21626.         1006   \n", "5       27468     198679         0       494      0.27468.         1083   \n", "6       21627     198679         0       491      0.21627.         1007   \n", "7       27814     198679         0       489      0.27814.         1118   \n", "\n", "  category_name level_num category_count goods_count is_default  \\\n", "0        牛油果/龙眼         1              2           0          F   \n", "1           芒果类         1              3           0          F   \n", "2            瓜类         1              4           0          F   \n", "3           柑橘橙         1              3           0          F   \n", "4         凤梨/菠萝         1              2           0          F   \n", "5     草莓/李子/猕猴桃         1              3           0          F   \n", "6         葡萄/提子         1              5           0          F   \n", "7            其他         1              5           0          F   \n", "\n", "                                      category_image has_child  \\\n", "0                                                            T   \n", "1                                                            T   \n", "2                                                            T   \n", "3  000198/*********/11/1667446444893_187973883348...         T   \n", "4                                                            T   \n", "5                                                            T   \n", "6                                                            T   \n", "7                                                            T   \n", "\n", "                                       goods_picture  custom_sub_level  \\\n", "0                                                                    1   \n", "1                                                                    2   \n", "2                                                                    1   \n", "3  https://img.dhb168.com/000198/*********/11/166...                 3   \n", "4                                                                    2   \n", "5                                                                    2   \n", "6                                                                    2   \n", "7                                                                    1   \n", "\n", "  calc_goods_count  \n", "0                1  \n", "1                1  \n", "2                1  \n", "3                1  \n", "4                1  \n", "5                1  \n", "6                1  \n", "7                1  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["access_token = r['data']['access_token']\n", "skey = r['data']['skey']\n", "\n", "skey_var=f'{{\"skey\":\"{skey}\",\"version\":\"10.9\"}}'\n", "data={\n", "    'a':'goodsNewCategory',\n", "    'val':f'{skey_var}',\n", "    'c':'Ding<PERSON><PERSON>',\n", "}\n", "print(access_token, skey)\n", "\n", "headers = {\n", "    \"Authorization\": f\"Bearer {access_token}\"\n", "}\n", "\n", "url=f'https://api.dhb168.com/api.php'\n", "\n", "cate_list = requests.post(url, data=data, headers=headers).json()['data']\n", "print(cate_list)\n", "first_category_df=pd.DataFrame(cate_list['first_category'])\n", "first_category_df"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["first category id:21554\n", "first category id:21626\n", "first category id:21627\n", "first category id:25507\n", "first category id:27400\n", "first category id:27437\n", "first category id:27450\n", "first category id:27468\n", "first category id:27478\n", "first category id:27804\n", "first category id:27808\n", "first category id:27814\n", "first category id:27817\n", "first category id:29132\n", "category_id:27455, product count:1\n", "category_id:25486, product count:2\n", "category_id:25485, product count:1\n", "category_id:26789, product count:1\n", "category_id:27438, product count:2\n", "category_id:27444, product count:1\n", "category_id:27447, product count:1\n", "category_id:27451, product count:1\n", "category_id:27820, product count:1\n", "category_id:29135, product count:1\n", "category_id:29433, product count:1\n", "category_id:28730, product count:1\n"]}, {"data": {"text/plain": ["[{'goods_id': '278399',\n", "  'multi_id': '0',\n", "  'goods_num': 'cyyk100715',\n", "  'goods_type': '',\n", "  'goods_name': '台农芒果/中台 净重12斤/份±100g',\n", "  'goods_model': '净重12斤±100g',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/10/1673749698080_6429771523409247.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '93.60',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '27455',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果100克起',\n", "  'field_2': '净重12斤±100g',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '海南/广西',\n", "  'field_5': '',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果100克起'},\n", "   {'name': '规格', 'value': '净重12斤±100g'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '海南/广西'},\n", "   {'name': '售后标准', 'value': ''}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '45.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '',\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '93.60',\n", "  'options_id': '0',\n", "  'price_id': '309079',\n", "  'multi_price_id': '309079',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '93.60',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '274939',\n", "  'multi_id': '0',\n", "  'goods_num': 'CSK100510',\n", "  'goods_type': '',\n", "  'goods_name': '佳农sw有冠凤梨 4粒装',\n", "  'goods_model': '4粒装',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/17/1669714350796_4663404515140661.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '82.50',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '27460',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果2.2斤左右',\n", "  'field_2': '4粒/份',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '菲律宾',\n", "  'field_5': '收货24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果2.2斤左右'},\n", "   {'name': '规格', 'value': '4粒/份'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '菲律宾'},\n", "   {'name': '售后标准', 'value': '收货24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '23.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '',\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '82.50',\n", "  'options_id': '0',\n", "  'price_id': '303990',\n", "  'multi_price_id': '303990',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '82.50',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '274688',\n", "  'multi_id': '0',\n", "  'goods_num': 'CYK100468',\n", "  'goods_type': '',\n", "  'goods_name': '佳农sw有冠凤梨 6-9粒装',\n", "  'goods_model': '有冠6-9粒',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/17/1669714366465_84472187520813.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '165.00',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '25486',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果2.2斤起',\n", "  'field_2': '6-9粒/份',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '菲律宾',\n", "  'field_5': '到货24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果2.2斤起'},\n", "   {'name': '规格', 'value': '6-9粒/份'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '菲律宾'},\n", "   {'name': '售后标准', 'value': '到货24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '40.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '',\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '165.00',\n", "  'options_id': '0',\n", "  'price_id': '303583',\n", "  'multi_price_id': '303583',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '165.00',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '274953',\n", "  'multi_id': '0',\n", "  'goods_num': 'CYS100511',\n", "  'goods_type': '2,3',\n", "  'goods_name': '夏黑葡萄 净重10斤/份±100g',\n", "  'goods_model': '10斤/份±100g',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/9/1667524998894_4642430645843063.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '117.00',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '25485',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果6g起',\n", "  'field_2': '10斤/份',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '云南',\n", "  'field_5': '收货24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果6g起'},\n", "   {'name': '规格', 'value': '10斤/份'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '云南'},\n", "   {'name': '售后标准', 'value': '收货24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '20.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '2,3',\n", "  'goods_new_type': [],\n", "  'goods_type_name': ['荐', '热'],\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '117.00',\n", "  'options_id': '0',\n", "  'price_id': '304012',\n", "  'multi_price_id': '304012',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '117.00',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '277067',\n", "  'multi_id': '0',\n", "  'goods_num': 'CYK100675',\n", "  'goods_type': '',\n", "  'goods_name': '阳光玫瑰青提  净重8斤±50g',\n", "  'goods_model': '8斤/份±50g',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/10/1670552076667_4852683702494722.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '224.00',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '28737',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果6g起',\n", "  'field_2': '8斤/份',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '云南',\n", "  'field_5': '收货24小时内（部分有籽不售后）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果6g起'},\n", "   {'name': '规格', 'value': '8斤/份'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '云南'},\n", "   {'name': '售后标准', 'value': '收货24小时内（部分有籽不售后）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '25.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '187',\n", "  'goods_new_type': [{'tags_name': '惠', 'tags_color': ''}],\n", "  'goods_type_name': [],\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '224.00',\n", "  'options_id': '0',\n", "  'price_id': '307429',\n", "  'multi_price_id': '307429',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '224.00',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '294204',\n", "  'multi_id': '0',\n", "  'goods_num': 'CSK100768',\n", "  'goods_type': '2,1',\n", "  'goods_name': '应季香水柠檬保鲜装  净果10斤±100g',\n", "  'goods_model': '10斤±100g',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/11/1667531809825_8890380387623855.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '125.00',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '27439',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '1',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果60g起',\n", "  'field_2': '10斤±100g',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '广东',\n", "  'field_5': '收货24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果60g起'},\n", "   {'name': '规格', 'value': '10斤±100g'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '广东'},\n", "   {'name': '售后标准', 'value': '收货24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '42.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '2,1',\n", "  'goods_new_type': [],\n", "  'goods_type_name': ['荐', '新'],\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '125.00',\n", "  'options_id': '0',\n", "  'price_id': '326259',\n", "  'multi_price_id': '326259',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '125.00',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '274695',\n", "  'multi_id': '0',\n", "  'goods_num': 'SK100474',\n", "  'goods_type': '',\n", "  'goods_name': '黄柠檬 净果5斤±50g',\n", "  'goods_model': '5斤±50g',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/16/1670317008607_34898985663649285.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '15.00',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '27442',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '1',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果80g起',\n", "  'field_2': '5斤±50g',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '四川',\n", "  'field_5': '收货24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果80g起'},\n", "   {'name': '规格', 'value': '5斤±50g'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '四川'},\n", "   {'name': '售后标准', 'value': '收货24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '18.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '',\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '15.00',\n", "  'options_id': '0',\n", "  'price_id': '303590',\n", "  'multi_price_id': '303590',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '15.00',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '274690',\n", "  'multi_id': '0',\n", "  'goods_num': 'SYK100469',\n", "  'goods_type': '',\n", "  'goods_name': '青金桔 净重3斤/份±30g',\n", "  'goods_model': '3斤/份±30g',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/15/1667289414898_2993578188916488.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '47.00',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '27445',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '1',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果8g起',\n", "  'field_2': '3斤/份',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '海南',\n", "  'field_5': '到货24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果8g起'},\n", "   {'name': '规格', 'value': '3斤/份'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '海南'},\n", "   {'name': '售后标准', 'value': '到货24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '23.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '',\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '47.00',\n", "  'options_id': '0',\n", "  'price_id': '303585',\n", "  'multi_price_id': '303585',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '47.00',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '275933',\n", "  'multi_id': '0',\n", "  'goods_num': 'YYZK100657',\n", "  'goods_type': '',\n", "  'goods_name': '脐橙  净果10斤/份±50g',\n", "  'goods_model': '10斤/份±50g',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/10/1669776545916_7684729528982541.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '45.00',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '27711',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果180g起',\n", "  'field_2': '10斤/份',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '江西/湖北',\n", "  'field_5': '收货24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果180g起'},\n", "   {'name': '规格', 'value': '10斤/份'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '江西/湖北'},\n", "   {'name': '售后标准', 'value': '收货24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '35.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '',\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '45.00',\n", "  'options_id': '0',\n", "  'price_id': '305547',\n", "  'multi_price_id': '305547',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '45.00',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '283809',\n", "  'multi_id': '0',\n", "  'goods_num': 'S100734',\n", "  'goods_type': '',\n", "  'goods_name': '无籽麒麟西瓜 毛重15-17斤±500g（快递）',\n", "  'goods_model': '15-17斤±500g',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/15/1669966991071_6673272296329831.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '112.20',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '27451',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果5斤起',\n", "  'field_2': '2粒/份',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '云南',\n", "  'field_5': '收货后24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果5斤起'},\n", "   {'name': '规格', 'value': '2粒/份'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '云南'},\n", "   {'name': '售后标准', 'value': '收货后24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '5.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '',\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '112.20',\n", "  'options_id': '0',\n", "  'price_id': '315353',\n", "  'multi_price_id': '315353',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '112.20',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '277193',\n", "  'multi_id': '0',\n", "  'goods_num': 'CYK100680',\n", "  'goods_type': '2,3',\n", "  'goods_name': '红颜草莓盒装  14盒/份 250g/盒',\n", "  'goods_model': '14盒/份  250g/盒',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/9/1672708872881_012889038826103372.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '165.20',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '27820',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果7g起',\n", "  'field_2': '14盒/份',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '辽宁',\n", "  'field_5': '收货24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果7g起'},\n", "   {'name': '规格', 'value': '14盒/份'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '辽宁'},\n", "   {'name': '售后标准', 'value': '收货24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '36.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '2,3',\n", "  'goods_new_type': [],\n", "  'goods_type_name': ['荐', '热'],\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '165.20',\n", "  'options_id': '0',\n", "  'price_id': '307597',\n", "  'multi_price_id': '307597',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '165.20',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '274888',\n", "  'multi_id': '0',\n", "  'goods_num': 'YK100494',\n", "  'goods_type': '',\n", "  'goods_name': '猕猴桃 净重8斤±100g（快递）',\n", "  'goods_model': '8斤±100g',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/17/1670317652280_363534901460449.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '40.80',\n", "  'base_units': '份',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '29135',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果80g起',\n", "  'field_2': '8斤/份',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '陕西',\n", "  'field_5': '收货24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果80g起'},\n", "   {'name': '规格', 'value': '8斤/份'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '陕西'},\n", "   {'name': '售后标准', 'value': '收货24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '39.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '',\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '40.80',\n", "  'options_id': '0',\n", "  'price_id': '303926',\n", "  'multi_price_id': '303926',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '40.80',\n", "    'units_name': '份',\n", "    'discount_price': ''}]},\n", " {'goods_id': '277844',\n", "  'multi_id': '0',\n", "  'goods_num': '100698',\n", "  'goods_type': '3,2',\n", "  'goods_name': '桑提娜车厘子/XL/净重10斤/份',\n", "  'goods_model': 'XL',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/10/1672455389850_4193221454637821.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '265.00',\n", "  'base_units': '盒',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '29433',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '净果10斤/份',\n", "  'field_2': '净果10斤/份',\n", "  'field_3': '',\n", "  'field_4': '',\n", "  'field_5': '',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '净果10斤/份'},\n", "   {'name': '规格', 'value': '净果10斤/份'},\n", "   {'name': '储存方式', 'value': ''},\n", "   {'name': '产地', 'value': ''},\n", "   {'name': '售后标准', 'value': ''}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '27.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '3,2',\n", "  'goods_new_type': [],\n", "  'goods_type_name': ['热', '荐'],\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '265.00',\n", "  'options_id': '0',\n", "  'price_id': '308396',\n", "  'multi_price_id': '308396',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '265.00',\n", "    'units_name': '盒',\n", "    'discount_price': ''}]},\n", " {'goods_id': '276999',\n", "  'multi_id': '0',\n", "  'goods_num': 'YK100670',\n", "  'goods_type': '1,2',\n", "  'goods_name': '龙眼  净重5斤/份±50g',\n", "  'goods_model': '5斤/份±50g',\n", "  'goods_picture': 'https://img.dhb168.com/000198/*********/9/1673745679979_24491040254924368.png?x-oss-process=image/resize,m_pad,w_250,h_250',\n", "  'selling_price': '0.00',\n", "  'whole_price': '51.50',\n", "  'base_units': '盒',\n", "  'container_units': '',\n", "  'conversion_number': '1.00',\n", "  'order_units': 'base_units',\n", "  'min_order': '1.00',\n", "  'limit_order': '0.00',\n", "  'limit_units': 'base_units',\n", "  'max_order': '0.0000',\n", "  'price_count': '1',\n", "  'translation': '1',\n", "  'inventory_safety': '0',\n", "  'keywords': '',\n", "  'category_id': '28730',\n", "  'brand_id': '7740',\n", "  'base_barcode': '',\n", "  'middle_units': '',\n", "  'is_double_sell': '0',\n", "  'base2middle_unit_rate': '0.00',\n", "  'version': '1',\n", "  'collaborator_id': '0',\n", "  'field_1': '单果7g起',\n", "  'field_2': '5斤/份',\n", "  'field_3': '冷藏2-4°',\n", "  'field_4': '泰国',\n", "  'field_5': '收货24小时内（个人口感喜好不在赔付范围内）',\n", "  'field_6': '',\n", "  'type': '1',\n", "  'in_cart': 'F',\n", "  'number': '0.00',\n", "  'units': 'base_units',\n", "  'field_data': [{'name': '净重', 'value': '单果7g起'},\n", "   {'name': '规格', 'value': '5斤/份'},\n", "   {'name': '储存方式', 'value': '冷藏2-4°'},\n", "   {'name': '产地', 'value': '泰国'},\n", "   {'name': '售后标准', 'value': '收货24小时内（个人口感喜好不在赔付范围内）'}],\n", "  'inventory_control': 'N',\n", "  'available_number': '',\n", "  'stock': '40.00',\n", "  'show_number': '',\n", "  'is_out_of_stock': <PERSON>alse,\n", "  'goods_type_old': '1,2',\n", "  'goods_new_type': [],\n", "  'goods_type_name': ['新', '荐'],\n", "  'base_multi_name': '',\n", "  'promotion_limit_msg': '',\n", "  'number_price': [],\n", "  'price': '51.50',\n", "  'options_id': '0',\n", "  'price_id': '307351',\n", "  'multi_price_id': '307351',\n", "  'options': 'F',\n", "  'units_list': [{'units_type': 'base_units',\n", "    'rate_number': '1.00',\n", "    'whole_price': '51.50',\n", "    'units_name': '盒',\n", "    'discount_price': ''}]}]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["data={'a':'goodsList',\n", "      'c':'Ding<PERSON><PERSON>',\n", "      'val':f'{{\"page\":1,\"step\":30,\"category_id\":\"27460\",\"type\":0,\"skey\":\"{skey}\",\"version\":\"10.9\"}}'}\n", "\n", "second_category=cate_list['second_category']\n", "second_category_list=[]\n", "for category_list in second_category:\n", "    print(f\"first category id:{category_list}\")\n", "    second_category_list.extend(second_category[category_list])\n", "\n", "product_list_all=[]\n", "for cate in second_category_list:\n", "    category_id=cate[\"category_id\"]\n", "    data={'a':'goodsList',\n", "      'c':'Ding<PERSON><PERSON>',\n", "      'val':f'{{\"page\":1,\"step\":30,\"category_id\":\"{category_id}\",\"type\":0,\"skey\":\"{skey}\",\"version\":\"10.9\"}}'}\n", "\n", "    # print(data)\n", "    product_list = requests.post(url, data=data, headers=headers).json()['data']['list']\n", "    print(f'category_id:{category_id}, product count:{len(product_list)}')\n", "    product_list_all.extend(product_list)\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功写入odps:summerfarm_ds.spider_cangzhen_product_result_df, partition_spec:ds=20240221,competitor_name=cangzhen, attemp:0\n", "sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_cangzhen_product_result_df\n", "                             where ds>='20240122' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240221</td>\n", "      <td>cangzhen</td>\n", "      <td>14</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ds competitor_name  recods\n", "0  20240221        cangzhen      14"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "product_list_all_df['competitor']=brand_name\n", "all_products_df=product_list_all_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
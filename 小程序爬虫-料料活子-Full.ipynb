{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-02-01 10:33:15, date_of_now:2024-02-01, brand_name:料料活子\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = '/Users/<USER>/Documents/github/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=os.environ['AZURE_API_KEY_XM']\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "brand_name=\"料料活子\"\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**************:33638\n", "**************:41471\n", "***************:48646\n", "***************:44559\n", "***************:49478\n", "**************:38800\n", "**************:40158\n", "***************:45037\n", "**************:38630\n", "**************:33995\n", "['**************:33638', '**************:41471', '***************:48646', '***************:44559', '***************:49478', '**************:38800', '**************:40158', '***************:45037', '**************:38630', '**************:33995']\n"]}], "source": ["def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "import requests\n", "import random\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url, max_retries=3):\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response.text\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 爬取类目树"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"ucxcweqvk4\",\"attributes\":\"\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"type\":\"config\",\"title\":\"全部商品\",\"category\":[],\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"ucxcweqvk4\"},{\"component\":\"dc-search\",\"type\":\"search\",\"placeholder\":\"搜索商品\",\"hotWords\":[],\"position\":0,\"showMode\":0,\"showScan\":false,\"borderRadius\":8,\"textAlign\":\"left\",\"height\":40,\"color\":\"#999\",\"bgColor\":\"#f9f9f9\",\"borderColor\":\"#fff\",\"zIndex\":110,\"uuid\":\"\",\"showSearchComponent\":0,\"minHeightOpt\":null,\"risk_type\":0,\"risk_alias\":\"ucxcweqvk4\"},{\"show_method\":\"3\",\"background_color\":\"#fff\",\"color\":\"#000\",\"count\":5,\"image_fill_style\":\"2\",\"sub_entry\":[{\"image_url\":\"\",\"link_title\":\"水果\",\"image_width\":0,\"title\":\"水果\",\"type\":\"image_ad_selection\",\"image_thumb_url\":\"\",\"link_id\":103346040,\"link_type\":\"feature\",\"image_height\":0,\"alias\":\"Js2HnsYHeV\",\"link_url\":\"https://h5.youzan.com/v2/showcase/feature?alias=Js2HnsYHeV\",\"template_id\":1,\"image_id\":\"0\"},{\"image_url\":\"\",\"link_title\":\"乳制品\",\"image_width\":0,\"title\":\"乳制品\",\"type\":\"image_ad_selection\",\"image_thumb_url\":\"\",\"link_id\":103346064,\"link_type\":\"feature\",\"image_height\":0,\"alias\":\"ZlZDIQVOT8\",\"link_url\":\"https://h5.youzan.com/v2/showcase/feature?alias=ZlZDIQVOT8\",\"template_id\":1,\"image_id\":\"0\"},{\"image_url\":\"\",\"link_title\":\"面粉\",\"image_width\":0,\"title\":\"面粉\",\"type\":\"image_ad_selection\",\"image_thumb_url\":\"\",\"link_id\":103346018,\"link_type\":\"feature\",\"image_height\":0,\"alias\":\"GpnyHaen0h\",\"link_url\":\"https://h5.youzan.com/v2/showcase/feature?alias=GpnyHaen0h\",\"template_id\":1,\"image_id\":\"0\"},{\"image_url\":\"\",\"link_title\":\"半成品\",\"image_width\":0,\"title\":\"成/半成品\",\"type\":\"image_ad_selection\",\"image_thumb_url\":\"\",\"link_id\":103346095,\"link_type\":\"feature\",\"image_height\":0,\"alias\":\"MQjbS8pvJZ\",\"link_url\":\"https://h5.youzan.com/v2/showcase/feature?alias=MQjbS8pvJZ\",\"template_id\":1,\"image_id\":\"0\"},{\"image_url\":\"\",\"link_title\":\"烘焙辅料\",\"image_width\":0,\"title\":\"烘焙辅料\",\"type\":\"image_ad_selection\",\"image_thumb_url\":\"\",\"link_id\":103346096,\"link_type\":\"feature\",\"image_height\":0,\"alias\":\"4DahSDpGH7\",\"link_url\":\"https://h5.youzan.com/v2/showcase/feature?alias=4DahSDpGH7\",\"template_id\":1,\"image_id\":\"0\"},{\"image_url\":\"\",\"link_title\":\"水吧咖啡\",\"image_width\":0,\"title\":\"水吧咖啡\",\"type\":\"image_ad_selection\",\"image_thumb_url\":\"\",\"link_id\":103346087,\"link_type\":\"feature\",\"image_height\":0,\"alias\":\"hDXz72qOC8\",\"link_url\":\"https://h5.youzan.com/v2/showcase/feature?alias=hDXz72qOC8\",\"template_id\":1,\"image_id\":\"0\"},{\"image_url\":\"\",\"link_title\":\"油脂类\",\"image_width\":0,\"title\":\"油脂类\",\"type\":\"image_ad_selection\",\"image_thumb_url\":\"\",\"link_id\":103346019,\"link_type\":\"feature\",\"image_height\":0,\"alias\":\"x3OdzbTYI2\",\"link_url\":\"https://h5.youzan.com/v2/showcase/feature?alias=x3OdzbTYI2\",\"template_id\":1,\"image_id\":\"0\"},{\"image_url\":\"\",\"link_title\":\"冷冻蛋糕\",\"image_width\":0,\"title\":\"冷冻蛋糕\",\"type\":\"image_ad_selection\",\"image_thumb_url\":\"\",\"link_id\":104349822,\"link_type\":\"feature\",\"image_height\":0,\"alias\":\"TBvPHOkmw0\",\"link_url\":\"https://h5.youzan.com/v2/showcase/feature?alias=TBvPHOkmw0\",\"template_id\":1,\"image_id\":\"0\"}],\"slide_setting\":\"1\",\"type\":\"top_nav\",\"risk_type\":0,\"risk_alias\":\"ucxcweqvk4\"}],\"createdTime\":1655395369000,\"goodsNum\":0,\"id\":103346086,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":98595742,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":2,\"templateId\":83,\"title\":\"全部商品\",\"type\":0,\"updateTime\":1688104550000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"chainOnlineShopMode\":2,\"joinType\":1,\"kdtId\":98595742,\"lockStatus\":0,\"offlineShopOpen\":false,\"onlineShopOpen\":true,\"parentKdtId\":98675540,\"rootKdtId\":98675540,\"saasSolution\":2,\"shopName\":\"料料活子\",\"shopRole\":2,\"shopTopic\":0,\"shopType\":7,\"subSolution\":2},\"themeAndColors\":{\"type\":13,\"colors\":{\"general\":\"#EE0A24\",\"main-bg\":\"#EE0A24\",\"main-bg-gradient\":\"linear-gradient(to right, #FF6034, #EE0A24)\",\"main-text\":\"#ffffff\",\"vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"vice-text\":\"#ffffff\",\"icon\":\"#EE0A24\",\"price\":\"#EE0A24\",\"tag-text\":\"#EE0A24\",\"tag-bg\":\"#FDE6E9\",\"start-bg\":\"#FF6034\",\"end-bg\":\"#EE0A24\",\"ump-main-bg\":\"#EE0A24\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#EE0A24\",\"ump-price\":\"#EE0A24\",\"ump-tag-text\":\"#EE0A24\",\"ump-tag-bg\":\"#FDE6E9\",\"ump-coupon-bg\":\"#FFF2F4\",\"ump-border\":\"#FCCED3\",\"ump-start-bg\":\"#FF6034\",\"ump-end-bg\":\"#EE0A24\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":false,\"shopInfo\":{\"address\":\"杭州市临平区杭州迪安家具有限公司(西北门)\",\"area\":\"临平区\",\"business\":\"41\",\"businessName\":\"综合食品\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"183******99\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2021-12-10 11:10:19\",\"intro\":\"精耕杭城，专注烘焙水吧原材料批发30年，欢迎使用！\",\"kdtId\":98675540,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2021/12/24/FlKuRT9zyINsPE1ZRX5kQBhg3ul-.jpg\",\"province\":\"浙江省\",\"shopId\":68492016,\"shopName\":\"杭州琛宝网络科技有限公司-总部\",\"shopType\":7},\"shopConfig\":{\"sold_out_goods_flag\":\"https://img01.yzcdn.cn/upload_files/2021/12/28/FlpOM8X9HUrWy2QumzIip8-FGn79.png\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "search\n", "top_nav\n", "[{'image_url': '', 'link_title': '水果', 'image_width': 0, 'title': '水果', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346040, 'link_type': 'feature', 'image_height': 0, 'alias': 'Js2HnsYHeV', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=Js2HnsYHeV', 'template_id': 1, 'image_id': '0'}, {'image_url': '', 'link_title': '乳制品', 'image_width': 0, 'title': '乳制品', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346064, 'link_type': 'feature', 'image_height': 0, 'alias': 'ZlZDIQVOT8', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=ZlZDIQVOT8', 'template_id': 1, 'image_id': '0'}, {'image_url': '', 'link_title': '面粉', 'image_width': 0, 'title': '面粉', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346018, 'link_type': 'feature', 'image_height': 0, 'alias': 'GpnyHaen0h', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=GpnyHaen0h', 'template_id': 1, 'image_id': '0'}, {'image_url': '', 'link_title': '半成品', 'image_width': 0, 'title': '成/半成品', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346095, 'link_type': 'feature', 'image_height': 0, 'alias': 'MQjbS8pvJZ', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=MQjbS8pvJZ', 'template_id': 1, 'image_id': '0'}, {'image_url': '', 'link_title': '烘焙辅料', 'image_width': 0, 'title': '烘焙辅料', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346096, 'link_type': 'feature', 'image_height': 0, 'alias': '4DahSDpGH7', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=4DahSDpGH7', 'template_id': 1, 'image_id': '0'}, {'image_url': '', 'link_title': '水吧咖啡', 'image_width': 0, 'title': '水吧咖啡', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346087, 'link_type': 'feature', 'image_height': 0, 'alias': 'hDXz72qOC8', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=hDXz72qOC8', 'template_id': 1, 'image_id': '0'}, {'image_url': '', 'link_title': '油脂类', 'image_width': 0, 'title': '油脂类', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346019, 'link_type': 'feature', 'image_height': 0, 'alias': 'x3OdzbTYI2', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=x3OdzbTYI2', 'template_id': 1, 'image_id': '0'}, {'image_url': '', 'link_title': '冷冻蛋糕', 'image_width': 0, 'title': '冷冻蛋糕', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 104349822, 'link_type': 'feature', 'image_height': 0, 'alias': 'TBvPHOkmw0', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=TBvPHOkmw0', 'template_id': 1, 'image_id': '0'}]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>image_url</th>\n", "      <th>link_title</th>\n", "      <th>image_width</th>\n", "      <th>title</th>\n", "      <th>type</th>\n", "      <th>image_thumb_url</th>\n", "      <th>link_id</th>\n", "      <th>link_type</th>\n", "      <th>image_height</th>\n", "      <th>alias</th>\n", "      <th>link_url</th>\n", "      <th>template_id</th>\n", "      <th>image_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>水果</td>\n", "      <td>0</td>\n", "      <td>水果</td>\n", "      <td>image_ad_selection</td>\n", "      <td></td>\n", "      <td>103346040</td>\n", "      <td>feature</td>\n", "      <td>0</td>\n", "      <td>Js2HnsYHeV</td>\n", "      <td>https://h5.youzan.com/v2/showcase/feature?alia...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td></td>\n", "      <td>乳制品</td>\n", "      <td>0</td>\n", "      <td>乳制品</td>\n", "      <td>image_ad_selection</td>\n", "      <td></td>\n", "      <td>103346064</td>\n", "      <td>feature</td>\n", "      <td>0</td>\n", "      <td>ZlZDIQVOT8</td>\n", "      <td>https://h5.youzan.com/v2/showcase/feature?alia...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td></td>\n", "      <td>面粉</td>\n", "      <td>0</td>\n", "      <td>面粉</td>\n", "      <td>image_ad_selection</td>\n", "      <td></td>\n", "      <td>103346018</td>\n", "      <td>feature</td>\n", "      <td>0</td>\n", "      <td>GpnyHaen0h</td>\n", "      <td>https://h5.youzan.com/v2/showcase/feature?alia...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td></td>\n", "      <td>半成品</td>\n", "      <td>0</td>\n", "      <td>成/半成品</td>\n", "      <td>image_ad_selection</td>\n", "      <td></td>\n", "      <td>103346095</td>\n", "      <td>feature</td>\n", "      <td>0</td>\n", "      <td>MQjbS8pvJZ</td>\n", "      <td>https://h5.youzan.com/v2/showcase/feature?alia...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td></td>\n", "      <td>烘焙辅料</td>\n", "      <td>0</td>\n", "      <td>烘焙辅料</td>\n", "      <td>image_ad_selection</td>\n", "      <td></td>\n", "      <td>103346096</td>\n", "      <td>feature</td>\n", "      <td>0</td>\n", "      <td>4DahSDpGH7</td>\n", "      <td>https://h5.youzan.com/v2/showcase/feature?alia...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td></td>\n", "      <td>水吧咖啡</td>\n", "      <td>0</td>\n", "      <td>水吧咖啡</td>\n", "      <td>image_ad_selection</td>\n", "      <td></td>\n", "      <td>103346087</td>\n", "      <td>feature</td>\n", "      <td>0</td>\n", "      <td>hDXz72qOC8</td>\n", "      <td>https://h5.youzan.com/v2/showcase/feature?alia...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td></td>\n", "      <td>油脂类</td>\n", "      <td>0</td>\n", "      <td>油脂类</td>\n", "      <td>image_ad_selection</td>\n", "      <td></td>\n", "      <td>103346019</td>\n", "      <td>feature</td>\n", "      <td>0</td>\n", "      <td>x3OdzbTYI2</td>\n", "      <td>https://h5.youzan.com/v2/showcase/feature?alia...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td></td>\n", "      <td>冷冻蛋糕</td>\n", "      <td>0</td>\n", "      <td>冷冻蛋糕</td>\n", "      <td>image_ad_selection</td>\n", "      <td></td>\n", "      <td>104349822</td>\n", "      <td>feature</td>\n", "      <td>0</td>\n", "      <td>TBvPHOkmw0</td>\n", "      <td>https://h5.youzan.com/v2/showcase/feature?alia...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  image_url link_title  image_width  title                type  \\\n", "0                   水果            0     水果  image_ad_selection   \n", "1                  乳制品            0    乳制品  image_ad_selection   \n", "2                   面粉            0     面粉  image_ad_selection   \n", "3                  半成品            0  成/半成品  image_ad_selection   \n", "4                 烘焙辅料            0   烘焙辅料  image_ad_selection   \n", "5                 水吧咖啡            0   水吧咖啡  image_ad_selection   \n", "6                  油脂类            0    油脂类  image_ad_selection   \n", "7                 冷冻蛋糕            0   冷冻蛋糕  image_ad_selection   \n", "\n", "  image_thumb_url    link_id link_type  image_height       alias  \\\n", "0                  103346040   feature             0  Js2HnsYHeV   \n", "1                  103346064   feature             0  ZlZDIQVOT8   \n", "2                  103346018   feature             0  GpnyHaen0h   \n", "3                  103346095   feature             0  MQjbS8pvJZ   \n", "4                  103346096   feature             0  4DahSDpGH7   \n", "5                  103346087   feature             0  hDXz72qOC8   \n", "6                  103346019   feature             0  x3OdzbTYI2   \n", "7                  104349822   feature             0  TBvPHOkmw0   \n", "\n", "                                            link_url  template_id image_id  \n", "0  https://h5.youzan.com/v2/showcase/feature?alia...            1        0  \n", "1  https://h5.youzan.com/v2/showcase/feature?alia...            1        0  \n", "2  https://h5.youzan.com/v2/showcase/feature?alia...            1        0  \n", "3  https://h5.youzan.com/v2/showcase/feature?alia...            1        0  \n", "4  https://h5.youzan.com/v2/showcase/feature?alia...            1        0  \n", "5  https://h5.youzan.com/v2/showcase/feature?alia...            1        0  \n", "6  https://h5.youzan.com/v2/showcase/feature?alia...            1        0  \n", "7  https://h5.youzan.com/v2/showcase/feature?alia...            1        0  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["app_and_kdt_id='app_id=wx3529eb8a969d738d&kdt_id=98595742'\n", "cate_list=get_remote_data_with_proxy('https://h5.youzan.com/wscdeco/feature-detail.json?'+app_and_kdt_id+'&access_token=1f0bfa99cd3a06af049b7b0fd27160&stage=16&check_multi_store=1&close_chainstore_webview_limit=true&check_old_home=1&hadEnterShop=true&alias=ucxcweqvk4&check_chainstore=true&version_control=%7B%22use_native_feature_page%22%3A1%2C%22feature_page_path%22%3A%22pages%2Fhome%2Ftab%2Fone%22%7D')\n", "print(cate_list)\n", "cate_list=json.loads(cate_list)\n", "\n", "root_cate_df=None\n", "root_cate_list=[]\n", "for component in cate_list['data']['components']:\n", "    print(component['type'])\n", "    if component['type']=='top_nav':\n", "        print(component['sub_entry'])\n", "        root_cate_list=component['sub_entry']\n", "        root_cate_df = pd.DataFrame(root_cate_list)\n", "\n", "root_cate_df"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_colwidth', None)\n", "root_cate_df.to_csv(f'./data/{brand_name}/{brand_name}一级类目(顶部).csv',index=False)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"Js2HnsYHeV\",\"attributes\":\"\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"水果\",\"category\":[],\"uuid\":\"78c72948-24bc-489d-98a1-2eb9d9667c4e\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"Js2HnsYHeV\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FrY3u9JfTSz81VMr9R0_maaqINgn.png\"},\"show\":0,\"title\":\"首页\",\"key\":\"home\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"custom\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"Js2HnsYHeV\"},{\"component\":\"dc-search\",\"type\":\"search\",\"placeholder\":\"搜索商品\",\"hotWords\":[],\"position\":0,\"showMode\":0,\"showScan\":false,\"borderRadius\":8,\"textAlign\":\"left\",\"height\":40,\"color\":\"#999\",\"bgColor\":\"#F9F9F9\",\"borderColor\":\"#FFF\",\"zIndex\":110,\"uuid\":\"27c7f634-70a1-4b21-8518-943c0511e49d\",\"showSearchComponent\":1,\"minHeightOpt\":null,\"risk_type\":0,\"risk_alias\":\"Js2HnsYHeV\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"芒果\",\"alias\":\"2okjw8u5sldxq\",\"number\":5,\"goodsNumber\":10},{\"loading\":true,\"title\":\"莓果\",\"alias\":\"361y2o62pqgxa\",\"number\":8,\"goodsNumber\":10},{\"loading\":true,\"title\":\"火龙果\",\"alias\":\"2olqi8cgw6a7i\",\"number\":2,\"goodsNumber\":99},{\"loading\":true,\"title\":\"柠檬/金桔\",\"alias\":\"276soicrxzz32\",\"number\":4,\"goodsNumber\":99},{\"loading\":true,\"title\":\"葡提/番茄\",\"alias\":\"36btvdij3o432\",\"number\":2,\"goodsNumber\":99},{\"loading\":true,\"title\":\"凤梨/橙橘\",\"alias\":\"2732lwy1fqq0e\",\"number\":2,\"goodsNumber\":99},{\"loading\":true,\"title\":\"瓜类\",\"alias\":\"2od558j9wvt4e\",\"number\":1,\"goodsNumber\":99},{\"loading\":true,\"title\":\"果汁|原浆|果酱\",\"alias\":\"35y91ke7563u6\",\"number\":39,\"goodsNumber\":99},{\"loading\":true,\"title\":\"鲜鸡蛋|蛋类\",\"alias\":\"2fo8hpl4xd7um\",\"number\":4,\"goodsNumber\":100}],\"uuid\":\"f41dc052-c69d-43ea-a9b2-ce0846dc03f4\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"rect\",\"imgOpt\":{\"fill\":\"cover\",\"corner\":\"rect\",\"radius\":2,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":400,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":400,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":null,\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"98595742\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"risk_type\":0,\"risk_alias\":\"Js2HnsYHeV\"}],\"createdTime\":1655395370000,\"goodsNum\":0,\"id\":103346040,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":98595742,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":2,\"templateId\":1,\"title\":\"水果\",\"type\":0,\"updateTime\":1706517113000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"chainOnlineShopMode\":2,\"joinType\":1,\"kdtId\":98595742,\"lockStatus\":0,\"offlineShopOpen\":false,\"onlineShopOpen\":true,\"parentKdtId\":98675540,\"rootKdtId\":98675540,\"saasSolution\":2,\"shopName\":\"料料活子\",\"shopRole\":2,\"shopTopic\":0,\"shopType\":7,\"subSolution\":2},\"themeAndColors\":{\"type\":13,\"colors\":{\"general\":\"#EE0A24\",\"main-bg\":\"#EE0A24\",\"main-bg-gradient\":\"linear-gradient(to right, #FF6034, #EE0A24)\",\"main-text\":\"#ffffff\",\"vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"vice-text\":\"#ffffff\",\"icon\":\"#EE0A24\",\"price\":\"#EE0A24\",\"tag-text\":\"#EE0A24\",\"tag-bg\":\"#FDE6E9\",\"start-bg\":\"#FF6034\",\"end-bg\":\"#EE0A24\",\"ump-main-bg\":\"#EE0A24\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#EE0A24\",\"ump-price\":\"#EE0A24\",\"ump-tag-text\":\"#EE0A24\",\"ump-tag-bg\":\"#FDE6E9\",\"ump-coupon-bg\":\"#FFF2F4\",\"ump-border\":\"#FCCED3\",\"ump-start-bg\":\"#FF6034\",\"ump-end-bg\":\"#EE0A24\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":true,\"shopInfo\":{\"address\":\"杭州市临平区杭州迪安家具有限公司(西北门)\",\"area\":\"临平区\",\"business\":\"41\",\"businessName\":\"综合食品\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"183******99\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2021-12-10 11:10:19\",\"intro\":\"精耕杭城，专注烘焙水吧原材料批发30年，欢迎使用！\",\"kdtId\":98675540,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2021/12/24/FlKuRT9zyINsPE1ZRX5kQBhg3ul-.jpg\",\"province\":\"浙江省\",\"shopId\":68492016,\"shopName\":\"杭州琛宝网络科技有限公司-总部\",\"shopType\":7},\"shopConfig\":{\"sold_out_goods_flag\":\"https://img01.yzcdn.cn/upload_files/2021/12/28/FlpOM8X9HUrWy2QumzIip8-FGn79.png\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "search\n", "tag_list_left\n", "[{'loading': True, 'title': '芒果', 'alias': '2okjw8u5sldxq', 'number': 5, 'goodsNumber': 10}, {'loading': True, 'title': '莓果', 'alias': '361y2o62pqgxa', 'number': 8, 'goodsNumber': 10}, {'loading': True, 'title': '火龙果', 'alias': '2olqi8cgw6a7i', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '柠檬/金桔', 'alias': '276soicrxzz32', 'number': 4, 'goodsNumber': 99}, {'loading': True, 'title': '葡提/番茄', 'alias': '36btvdij3o432', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '凤梨/橙橘', 'alias': '2732lwy1fqq0e', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '瓜类', 'alias': '2od558j9wvt4e', 'number': 1, 'goodsNumber': 99}, {'loading': True, 'title': '果汁|原浆|果酱', 'alias': '35y91ke7563u6', 'number': 39, 'goodsNumber': 99}, {'loading': True, 'title': '鲜鸡蛋|蛋类', 'alias': '2fo8hpl4xd7um', 'number': 4, 'goodsNumber': 100}]\n", "Using proxy: ************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"ZlZDIQVOT8\",\"attributes\":\"\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"乳制品\",\"category\":[],\"uuid\":\"9702eac0-8e18-4297-ac62-c6ca0cb1ee4c\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"ZlZDIQVOT8\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FrY3u9JfTSz81VMr9R0_maaqINgn.png\"},\"show\":0,\"title\":\"首页\",\"key\":\"home\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"custom\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"ZlZDIQVOT8\"},{\"component\":\"dc-search\",\"type\":\"search\",\"placeholder\":\"搜索商品\",\"hotWords\":[],\"position\":0,\"showMode\":0,\"showScan\":false,\"borderRadius\":8,\"textAlign\":\"left\",\"height\":40,\"color\":\"#999\",\"bgColor\":\"#F9F9F9\",\"borderColor\":\"#FFF\",\"zIndex\":110,\"uuid\":\"f750f1f7-5293-4a55-b989-08869a519f6b\",\"showSearchComponent\":1,\"minHeightOpt\":null,\"risk_type\":0,\"risk_alias\":\"ZlZDIQVOT8\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"稀奶油淡奶油\",\"alias\":\"2xgtec0bd5yy6\",\"number\":18,\"goodsNumber\":100},{\"loading\":true,\"title\":\"乳脂|植物|牛奶奶油\",\"alias\":\"274as0u9gphou\",\"number\":9,\"goodsNumber\":100},{\"loading\":true,\"title\":\"奶酪芝士马苏\",\"alias\":\"2x5nvlc642m9q\",\"number\":24,\"goodsNumber\":100},{\"loading\":true,\"title\":\"黄油\",\"alias\":\"3f4crzocvq1mm\",\"number\":24,\"goodsNumber\":100},{\"loading\":true,\"title\":\"常温|冷藏牛奶\",\"alias\":\"3f35nhpn0c3ku\",\"number\":10,\"goodsNumber\":100},{\"loading\":true,\"title\":\"奶粉\",\"alias\":\"2xj7u75l5eu6m\",\"number\":2,\"goodsNumber\":100},{\"loading\":true,\"title\":\"其他油脂\",\"alias\":\"3negs7uu00mf2\",\"number\":10,\"goodsNumber\":10},{\"loading\":true,\"title\":\"包材\",\"alias\":\"2fy4gh9yzl5fi\",\"number\":16,\"goodsNumber\":100}],\"uuid\":\"87827fae-f093-4d32-8d83-c6201bfa743c\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"rect\",\"imgOpt\":{\"fill\":\"cover\",\"corner\":\"rect\",\"radius\":2,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":400,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":400,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":null,\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"98595742\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"risk_type\":0,\"risk_alias\":\"ZlZDIQVOT8\"}],\"createdTime\":1655395369000,\"goodsNum\":0,\"id\":103346064,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":98595742,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":2,\"templateId\":1,\"title\":\"乳制品\",\"type\":0,\"updateTime\":1701333795000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"chainOnlineShopMode\":2,\"joinType\":1,\"kdtId\":98595742,\"lockStatus\":0,\"offlineShopOpen\":false,\"onlineShopOpen\":true,\"parentKdtId\":98675540,\"rootKdtId\":98675540,\"saasSolution\":2,\"shopName\":\"料料活子\",\"shopRole\":2,\"shopTopic\":0,\"shopType\":7,\"subSolution\":2},\"themeAndColors\":{\"type\":13,\"colors\":{\"general\":\"#EE0A24\",\"main-bg\":\"#EE0A24\",\"main-bg-gradient\":\"linear-gradient(to right, #FF6034, #EE0A24)\",\"main-text\":\"#ffffff\",\"vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"vice-text\":\"#ffffff\",\"icon\":\"#EE0A24\",\"price\":\"#EE0A24\",\"tag-text\":\"#EE0A24\",\"tag-bg\":\"#FDE6E9\",\"start-bg\":\"#FF6034\",\"end-bg\":\"#EE0A24\",\"ump-main-bg\":\"#EE0A24\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#EE0A24\",\"ump-price\":\"#EE0A24\",\"ump-tag-text\":\"#EE0A24\",\"ump-tag-bg\":\"#FDE6E9\",\"ump-coupon-bg\":\"#FFF2F4\",\"ump-border\":\"#FCCED3\",\"ump-start-bg\":\"#FF6034\",\"ump-end-bg\":\"#EE0A24\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":true,\"shopInfo\":{\"address\":\"杭州市临平区杭州迪安家具有限公司(西北门)\",\"area\":\"临平区\",\"business\":\"41\",\"businessName\":\"综合食品\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"183******99\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2021-12-10 11:10:19\",\"intro\":\"精耕杭城，专注烘焙水吧原材料批发30年，欢迎使用！\",\"kdtId\":98675540,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2021/12/24/FlKuRT9zyINsPE1ZRX5kQBhg3ul-.jpg\",\"province\":\"浙江省\",\"shopId\":68492016,\"shopName\":\"杭州琛宝网络科技有限公司-总部\",\"shopType\":7},\"shopConfig\":{\"sold_out_goods_flag\":\"https://img01.yzcdn.cn/upload_files/2021/12/28/FlpOM8X9HUrWy2QumzIip8-FGn79.png\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "search\n", "tag_list_left\n", "[{'loading': True, 'title': '稀奶油淡奶油', 'alias': '2xgtec0bd5yy6', 'number': 18, 'goodsNumber': 100}, {'loading': True, 'title': '乳脂|植物|牛奶奶油', 'alias': '274as0u9gphou', 'number': 9, 'goodsNumber': 100}, {'loading': True, 'title': '奶酪芝士马苏', 'alias': '2x5nvlc642m9q', 'number': 24, 'goodsNumber': 100}, {'loading': True, 'title': '黄油', 'alias': '3f4crzocvq1mm', 'number': 24, 'goodsNumber': 100}, {'loading': True, 'title': '常温|冷藏牛奶', 'alias': '3f35nhpn0c3ku', 'number': 10, 'goodsNumber': 100}, {'loading': True, 'title': '奶粉', 'alias': '2xj7u75l5eu6m', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '其他油脂', 'alias': '3negs7uu00mf2', 'number': 10, 'goodsNumber': 10}, {'loading': True, 'title': '包材', 'alias': '2fy4gh9yzl5fi', 'number': 16, 'goodsNumber': 100}]\n", "Using proxy: ************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"GpnyHaen0h\",\"attributes\":\"\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"面粉\",\"category\":[],\"uuid\":\"ca0ecdf5-87b5-4c34-a7b6-d4f6146c2bd0\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"GpnyHaen0h\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"custom\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"GpnyHaen0h\"},{\"component\":\"dc-search\",\"type\":\"search\",\"placeholder\":\"搜索商品\",\"hotWords\":[],\"position\":0,\"showMode\":0,\"showScan\":false,\"borderRadius\":8,\"textAlign\":\"left\",\"height\":40,\"color\":\"#999\",\"bgColor\":\"#F9F9F9\",\"borderColor\":\"#FFF\",\"zIndex\":110,\"uuid\":\"98edfc8e-0984-4df2-a50c-1882918591b5\",\"showSearchComponent\":1,\"minHeightOpt\":null,\"risk_type\":0,\"risk_alias\":\"GpnyHaen0h\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"日清面粉\",\"alias\":\"2780hz2tgqw4e\",\"number\":5,\"goodsNumber\":100},{\"loading\":true,\"title\":\"美玫面粉\",\"alias\":\"2ovmzr9jf7jlq\",\"number\":2,\"goodsNumber\":100},{\"loading\":true,\"title\":\"金像面粉\",\"alias\":\"2xkh808r83z9q\",\"number\":4,\"goodsNumber\":100},{\"loading\":true,\"title\":\"王后面粉\",\"alias\":\"2fy443sne4qha\",\"number\":5,\"goodsNumber\":100},{\"loading\":true,\"title\":\"王后柔风\",\"alias\":\"2ft7xzogeccpa\",\"number\":5,\"goodsNumber\":100},{\"loading\":true,\"title\":\"伯爵面粉\",\"alias\":\"2xj8cxhrck41a\",\"number\":6,\"goodsNumber\":100},{\"loading\":true,\"title\":\"鸟越面粉\",\"alias\":\"366wblbolj2oe\",\"number\":3,\"goodsNumber\":100},{\"loading\":true,\"title\":\"日本昭和\",\"alias\":\"2xmylyef2hmby\",\"number\":4,\"goodsNumber\":100},{\"loading\":true,\"title\":\"日本制粉 NIPPN\",\"alias\":\"1y4dmmcqjmn1a\",\"number\":3,\"goodsNumber\":100},{\"loading\":true,\"title\":\"预拌粉\",\"alias\":\"3f36v3gik0ifi\",\"number\":1,\"goodsNumber\":100},{\"loading\":true,\"title\":\"法国都梅\",\"alias\":\"2xmxk7zzd97vy\",\"number\":1,\"goodsNumber\":10},{\"loading\":true,\"title\":\"其他粉类\",\"alias\":\"3f1wm3uxd3k3i\",\"number\":4,\"goodsNumber\":100}],\"uuid\":\"5f1628ae-edc6-4b93-b7e2-5e6da329f434\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"rect\",\"imgOpt\":{\"fill\":\"cover\",\"corner\":\"rect\",\"radius\":2,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":400,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":400,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":null,\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"98595742\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"risk_type\":0,\"risk_alias\":\"GpnyHaen0h\"}],\"createdTime\":1655395369000,\"goodsNum\":0,\"id\":103346018,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":98595742,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":2,\"templateId\":1,\"title\":\"面粉\",\"type\":0,\"updateTime\":1693190564000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"chainOnlineShopMode\":2,\"joinType\":1,\"kdtId\":98595742,\"lockStatus\":0,\"offlineShopOpen\":false,\"onlineShopOpen\":true,\"parentKdtId\":98675540,\"rootKdtId\":98675540,\"saasSolution\":2,\"shopName\":\"料料活子\",\"shopRole\":2,\"shopTopic\":0,\"shopType\":7,\"subSolution\":2},\"themeAndColors\":{\"type\":13,\"colors\":{\"general\":\"#EE0A24\",\"main-bg\":\"#EE0A24\",\"main-bg-gradient\":\"linear-gradient(to right, #FF6034, #EE0A24)\",\"main-text\":\"#ffffff\",\"vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"vice-text\":\"#ffffff\",\"icon\":\"#EE0A24\",\"price\":\"#EE0A24\",\"tag-text\":\"#EE0A24\",\"tag-bg\":\"#FDE6E9\",\"start-bg\":\"#FF6034\",\"end-bg\":\"#EE0A24\",\"ump-main-bg\":\"#EE0A24\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#EE0A24\",\"ump-price\":\"#EE0A24\",\"ump-tag-text\":\"#EE0A24\",\"ump-tag-bg\":\"#FDE6E9\",\"ump-coupon-bg\":\"#FFF2F4\",\"ump-border\":\"#FCCED3\",\"ump-start-bg\":\"#FF6034\",\"ump-end-bg\":\"#EE0A24\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":true,\"shopInfo\":{\"address\":\"杭州市临平区杭州迪安家具有限公司(西北门)\",\"area\":\"临平区\",\"business\":\"41\",\"businessName\":\"综合食品\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"183******99\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2021-12-10 11:10:19\",\"intro\":\"精耕杭城，专注烘焙水吧原材料批发30年，欢迎使用！\",\"kdtId\":98675540,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2021/12/24/FlKuRT9zyINsPE1ZRX5kQBhg3ul-.jpg\",\"province\":\"浙江省\",\"shopId\":68492016,\"shopName\":\"杭州琛宝网络科技有限公司-总部\",\"shopType\":7},\"shopConfig\":{\"sold_out_goods_flag\":\"https://img01.yzcdn.cn/upload_files/2021/12/28/FlpOM8X9HUrWy2QumzIip8-FGn79.png\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "search\n", "tag_list_left\n", "[{'loading': True, 'title': '日清面粉', 'alias': '2780hz2tgqw4e', 'number': 5, 'goodsNumber': 100}, {'loading': True, 'title': '美玫面粉', 'alias': '2ovmzr9jf7jlq', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '金像面粉', 'alias': '2xkh808r83z9q', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '王后面粉', 'alias': '2fy443sne4qha', 'number': 5, 'goodsNumber': 100}, {'loading': True, 'title': '王后柔风', 'alias': '2ft7xzogeccpa', 'number': 5, 'goodsNumber': 100}, {'loading': True, 'title': '伯爵面粉', 'alias': '2xj8cxhrck41a', 'number': 6, 'goodsNumber': 100}, {'loading': True, 'title': '鸟越面粉', 'alias': '366wblbolj2oe', 'number': 3, 'goodsNumber': 100}, {'loading': True, 'title': '日本昭和', 'alias': '2xmylyef2hmby', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '日本制粉 NIPPN', 'alias': '1y4dmmcqjmn1a', 'number': 3, 'goodsNumber': 100}, {'loading': True, 'title': '预拌粉', 'alias': '3f36v3gik0ifi', 'number': 1, 'goodsNumber': 100}, {'loading': True, 'title': '法国都梅', 'alias': '2xmxk7zzd97vy', 'number': 1, 'goodsNumber': 10}, {'loading': True, 'title': '其他粉类', 'alias': '3f1wm3uxd3k3i', 'number': 4, 'goodsNumber': 100}]\n", "Using proxy: ************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"MQjbS8pvJZ\",\"attributes\":\"\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"成/半成品\",\"category\":[],\"uuid\":\"b6cd0804-9dc6-4832-b358-31e0423b4ac1\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"MQjbS8pvJZ\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"custom\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"MQjbS8pvJZ\"},{\"component\":\"dc-search\",\"type\":\"search\",\"placeholder\":\"搜索商品\",\"hotWords\":[],\"position\":0,\"showMode\":0,\"showScan\":false,\"borderRadius\":8,\"textAlign\":\"left\",\"height\":40,\"color\":\"#999\",\"bgColor\":\"#F9F9F9\",\"borderColor\":\"#FFF\",\"zIndex\":110,\"uuid\":\"321a05cb-c253-4541-811d-6540f1cd02bf\",\"showSearchComponent\":1,\"minHeightOpt\":null,\"risk_type\":0,\"risk_alias\":\"MQjbS8pvJZ\"},{\"uuid\":\"941c739e-5112-47cb-9602-ea92845e8a69\",\"type\":\"tag_list_top\",\"tabs\":[],\"tagsConfig\":{\"navStyle\":\"0\",\"sticky\":\"0\"},\"goodsListConfig\":{\"type\":\"goods\",\"buttonText\":\"\",\"cornerMarkImage\":\"\",\"list\":[],\"goods\":[],\"layout\":0,\"sizeType\":0,\"showTitle\":true,\"showSubTitle\":true,\"showPrice\":true,\"showOriginPrice\":false,\"showBuyButton\":true,\"buyButtonType\":1,\"buyBtnExpress\":false,\"showCornerMark\":false,\"showCornerMarkVal\":0,\"cornerMarkType\":0,\"imageRatio\":0,\"imageFillStyle\":2,\"pageMargin\":15,\"goodsMargin\":10,\"textAlignType\":\"left\",\"textStyleType\":1,\"borderRadiusType\":1,\"showSoldNum\":false,\"showUmpAmbience\":false,\"showGoodsCoverImage\":\"0\"},\"isShowAllGoodsTag\":false,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"vertical\",\"corner\":\"rect\",\"align\":\"left\",\"layoutType\":\"one\",\"imgOpt\":{\"ratio\":\"3-2\",\"fill\":\"contain\",\"cornerMark\":false,\"showKeyLabel\":false,\"useGoodsCoverImage\":false},\"priceOpt\":{\"fontWeight\":\"400\",\"fontSize\":18,\"tagGap\":2},\"btnOpt\":{\"kdtId\":98595742,\"type\":\"icon\",\"name\":\"cart-circle-o\"},\"oPriceOpt\":null,\"titleOpt\":{\"titleFontWeight\":\"400\",\"titleFontSize\":16,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"height\":\"22px\",\"titleLines\":1},\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"height\":\"18px\"},\"tagsOpt\":{\"textAlign\":\"left\"}},\"layoutOpt\":{\"type\":\"one\",\"layoutMargin\":15,\"itemMargin\":10},\"queryExtra\":{\"layout\":0,\"isShowPeriod\":1},\"risk_type\":0,\"risk_alias\":\"MQjbS8pvJZ\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"烘焙半成品\",\"alias\":\"26ugjjg3odq7y\",\"number\":29,\"goodsNumber\":100},{\"loading\":true,\"title\":\"肉制品\",\"alias\":\"3ezhb5qzqwi3i\",\"number\":26,\"goodsNumber\":100},{\"loading\":true,\"title\":\"糖果\",\"alias\":\"2xbw0zc9cbqbi\",\"number\":1,\"goodsNumber\":100},{\"loading\":true,\"title\":\"西餐\",\"alias\":\"3f34s8nyl9q3y\",\"number\":1,\"goodsNumber\":100}],\"uuid\":\"254d1674-b83f-44f3-add4-f6fafb80d2be\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"rect\",\"imgOpt\":{\"fill\":\"cover\",\"corner\":\"rect\",\"radius\":2,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":400,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":400,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":null,\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"98595742\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"risk_type\":0,\"risk_alias\":\"MQjbS8pvJZ\"}],\"createdTime\":1655395370000,\"goodsNum\":0,\"id\":103346095,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":98595742,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":2,\"templateId\":1,\"title\":\"成/半成品\",\"type\":0,\"updateTime\":1686715901000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"chainOnlineShopMode\":2,\"joinType\":1,\"kdtId\":98595742,\"lockStatus\":0,\"offlineShopOpen\":false,\"onlineShopOpen\":true,\"parentKdtId\":98675540,\"rootKdtId\":98675540,\"saasSolution\":2,\"shopName\":\"料料活子\",\"shopRole\":2,\"shopTopic\":0,\"shopType\":7,\"subSolution\":2},\"themeAndColors\":{\"type\":13,\"colors\":{\"general\":\"#EE0A24\",\"main-bg\":\"#EE0A24\",\"main-bg-gradient\":\"linear-gradient(to right, #FF6034, #EE0A24)\",\"main-text\":\"#ffffff\",\"vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"vice-text\":\"#ffffff\",\"icon\":\"#EE0A24\",\"price\":\"#EE0A24\",\"tag-text\":\"#EE0A24\",\"tag-bg\":\"#FDE6E9\",\"start-bg\":\"#FF6034\",\"end-bg\":\"#EE0A24\",\"ump-main-bg\":\"#EE0A24\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#EE0A24\",\"ump-price\":\"#EE0A24\",\"ump-tag-text\":\"#EE0A24\",\"ump-tag-bg\":\"#FDE6E9\",\"ump-coupon-bg\":\"#FFF2F4\",\"ump-border\":\"#FCCED3\",\"ump-start-bg\":\"#FF6034\",\"ump-end-bg\":\"#EE0A24\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":true,\"shopInfo\":{\"address\":\"杭州市临平区杭州迪安家具有限公司(西北门)\",\"area\":\"临平区\",\"business\":\"41\",\"businessName\":\"综合食品\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"183******99\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2021-12-10 11:10:19\",\"intro\":\"精耕杭城，专注烘焙水吧原材料批发30年，欢迎使用！\",\"kdtId\":98675540,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2021/12/24/FlKuRT9zyINsPE1ZRX5kQBhg3ul-.jpg\",\"province\":\"浙江省\",\"shopId\":68492016,\"shopName\":\"杭州琛宝网络科技有限公司-总部\",\"shopType\":7},\"shopConfig\":{\"sold_out_goods_flag\":\"https://img01.yzcdn.cn/upload_files/2021/12/28/FlpOM8X9HUrWy2QumzIip8-FGn79.png\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "search\n", "tag_list_top\n", "tag_list_left\n", "[{'loading': True, 'title': '烘焙半成品', 'alias': '26ugjjg3odq7y', 'number': 29, 'goodsNumber': 100}, {'loading': True, 'title': '肉制品', 'alias': '3ezhb5qzqwi3i', 'number': 26, 'goodsNumber': 100}, {'loading': True, 'title': '糖果', 'alias': '2xbw0zc9cbqbi', 'number': 1, 'goodsNumber': 100}, {'loading': True, 'title': '西餐', 'alias': '3f34s8nyl9q3y', 'number': 1, 'goodsNumber': 100}]\n", "Using proxy: *************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"4DahSDpGH7\",\"attributes\":\"\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"烘焙辅料\",\"category\":[],\"uuid\":\"78e2712a-d5bb-494e-a56f-205cbb80654d\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"4DahSDpGH7\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"custom\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"4DahSDpGH7\"},{\"component\":\"dc-search\",\"type\":\"search\",\"placeholder\":\"搜索商品\",\"hotWords\":[],\"position\":0,\"showMode\":0,\"showScan\":false,\"borderRadius\":8,\"textAlign\":\"left\",\"height\":40,\"color\":\"#999\",\"bgColor\":\"#F9F9F9\",\"borderColor\":\"#FFF\",\"zIndex\":110,\"uuid\":\"cc380466-c32f-4ed0-ace3-c4c275fa5a2d\",\"showSearchComponent\":1,\"minHeightOpt\":null,\"risk_type\":0,\"risk_alias\":\"4DahSDpGH7\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"糖类\",\"alias\":\"3nugk8hlajgry\",\"number\":13,\"goodsNumber\":100},{\"loading\":true,\"title\":\"果泥果酱果肉\",\"alias\":\"26y6m0erzq1ny\",\"number\":22,\"goodsNumber\":100},{\"loading\":true,\"title\":\"沙拉酱|奶制夹心馅\",\"alias\":\"2opg8pw5wgj9q\",\"number\":12,\"goodsNumber\":100},{\"loading\":true,\"title\":\"食用馅料|豆沙|泥制馅料\",\"alias\":\"3f34m3n9msfta\",\"number\":15,\"goodsNumber\":100},{\"loading\":true,\"title\":\"可可豆及其制品\",\"alias\":\"3etaw4q9es7xq\",\"number\":8,\"goodsNumber\":100},{\"loading\":true,\"title\":\"预拌粉\",\"alias\":\"3f36v3gik0ifi\",\"number\":1,\"goodsNumber\":100},{\"loading\":true,\"title\":\"添加剂|酵母|泡打粉\",\"alias\":\"36833rjlxycla\",\"number\":16,\"goodsNumber\":100},{\"loading\":true,\"title\":\"坚果及其制品\",\"alias\":\"2xj86td4hjg5a\",\"number\":17,\"goodsNumber\":100},{\"loading\":true,\"title\":\"装饰|撒料|淋面\",\"alias\":\"2omzq62y1v2um\",\"number\":12,\"goodsNumber\":100},{\"loading\":true,\"title\":\"肉松\",\"alias\":\"3f1xzwabdgice\",\"number\":3,\"goodsNumber\":100},{\"loading\":true,\"title\":\"调制酒\",\"alias\":\"3f36cagwcaym6\",\"number\":6,\"goodsNumber\":100},{\"loading\":true,\"title\":\"调味粉|香精|浓缩汁\",\"alias\":\"1yah9swwsr45q\",\"number\":7,\"goodsNumber\":100},{\"loading\":true,\"title\":\"鲜鸡蛋|蛋类\",\"alias\":\"2fo8hpl4xd7um\",\"number\":4,\"goodsNumber\":100},{\"loading\":true,\"title\":\"其他辅料\",\"alias\":\"1yajclybyrpta\",\"number\":12,\"goodsNumber\":100},{\"loading\":true,\"title\":\"包材\",\"alias\":\"2fy4gh9yzl5fi\",\"number\":16,\"goodsNumber\":100}],\"uuid\":\"31652410-880e-4bb8-b76a-bb5f5402a0f5\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"rect\",\"imgOpt\":{\"fill\":\"cover\",\"corner\":\"rect\",\"radius\":2,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":400,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":400,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":null,\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"98595742\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"risk_type\":0,\"risk_alias\":\"4DahSDpGH7\"}],\"createdTime\":1655395370000,\"goodsNum\":0,\"id\":103346096,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":98595742,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":2,\"templateId\":1,\"title\":\"烘焙辅料\",\"type\":0,\"updateTime\":1688097435000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"chainOnlineShopMode\":2,\"joinType\":1,\"kdtId\":98595742,\"lockStatus\":0,\"offlineShopOpen\":false,\"onlineShopOpen\":true,\"parentKdtId\":98675540,\"rootKdtId\":98675540,\"saasSolution\":2,\"shopName\":\"料料活子\",\"shopRole\":2,\"shopTopic\":0,\"shopType\":7,\"subSolution\":2},\"themeAndColors\":{\"type\":13,\"colors\":{\"general\":\"#EE0A24\",\"main-bg\":\"#EE0A24\",\"main-bg-gradient\":\"linear-gradient(to right, #FF6034, #EE0A24)\",\"main-text\":\"#ffffff\",\"vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"vice-text\":\"#ffffff\",\"icon\":\"#EE0A24\",\"price\":\"#EE0A24\",\"tag-text\":\"#EE0A24\",\"tag-bg\":\"#FDE6E9\",\"start-bg\":\"#FF6034\",\"end-bg\":\"#EE0A24\",\"ump-main-bg\":\"#EE0A24\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#EE0A24\",\"ump-price\":\"#EE0A24\",\"ump-tag-text\":\"#EE0A24\",\"ump-tag-bg\":\"#FDE6E9\",\"ump-coupon-bg\":\"#FFF2F4\",\"ump-border\":\"#FCCED3\",\"ump-start-bg\":\"#FF6034\",\"ump-end-bg\":\"#EE0A24\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":true,\"shopInfo\":{\"address\":\"杭州市临平区杭州迪安家具有限公司(西北门)\",\"area\":\"临平区\",\"business\":\"41\",\"businessName\":\"综合食品\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"183******99\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2021-12-10 11:10:19\",\"intro\":\"精耕杭城，专注烘焙水吧原材料批发30年，欢迎使用！\",\"kdtId\":98675540,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2021/12/24/FlKuRT9zyINsPE1ZRX5kQBhg3ul-.jpg\",\"province\":\"浙江省\",\"shopId\":68492016,\"shopName\":\"杭州琛宝网络科技有限公司-总部\",\"shopType\":7},\"shopConfig\":{\"sold_out_goods_flag\":\"https://img01.yzcdn.cn/upload_files/2021/12/28/FlpOM8X9HUrWy2QumzIip8-FGn79.png\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "search\n", "tag_list_left\n", "[{'loading': True, 'title': '糖类', 'alias': '3nugk8hlajgry', 'number': 13, 'goodsNumber': 100}, {'loading': True, 'title': '果泥果酱果肉', 'alias': '26y6m0erzq1ny', 'number': 22, 'goodsNumber': 100}, {'loading': True, 'title': '沙拉酱|奶制夹心馅', 'alias': '2opg8pw5wgj9q', 'number': 12, 'goodsNumber': 100}, {'loading': True, 'title': '食用馅料|豆沙|泥制馅料', 'alias': '3f34m3n9msfta', 'number': 15, 'goodsNumber': 100}, {'loading': True, 'title': '可可豆及其制品', 'alias': '3etaw4q9es7xq', 'number': 8, 'goodsNumber': 100}, {'loading': True, 'title': '预拌粉', 'alias': '3f36v3gik0ifi', 'number': 1, 'goodsNumber': 100}, {'loading': True, 'title': '添加剂|酵母|泡打粉', 'alias': '36833rjlxycla', 'number': 16, 'goodsNumber': 100}, {'loading': True, 'title': '坚果及其制品', 'alias': '2xj86td4hjg5a', 'number': 17, 'goodsNumber': 100}, {'loading': True, 'title': '装饰|撒料|淋面', 'alias': '2omzq62y1v2um', 'number': 12, 'goodsNumber': 100}, {'loading': True, 'title': '肉松', 'alias': '3f1xzwabdgice', 'number': 3, 'goodsNumber': 100}, {'loading': True, 'title': '调制酒', 'alias': '3f36cagwcaym6', 'number': 6, 'goodsNumber': 100}, {'loading': True, 'title': '调味粉|香精|浓缩汁', 'alias': '1yah9swwsr45q', 'number': 7, 'goodsNumber': 100}, {'loading': True, 'title': '鲜鸡蛋|蛋类', 'alias': '2fo8hpl4xd7um', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '其他辅料', 'alias': '1yajclybyrpta', 'number': 12, 'goodsNumber': 100}, {'loading': True, 'title': '包材', 'alias': '2fy4gh9yzl5fi', 'number': 16, 'goodsNumber': 100}]\n", "Using proxy: ************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"hDXz72qOC8\",\"attributes\":\"\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"水吧咖啡奶茶调饮\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"水吧咖啡奶茶调饮\",\"category\":[],\"uuid\":\"37f4cd27-2aa5-4173-9145-8c40ede29d84\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"hDXz72qOC8\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"custom\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"hDXz72qOC8\"},{\"component\":\"dc-search\",\"type\":\"search\",\"placeholder\":\"搜索商品\",\"hotWords\":[],\"position\":0,\"showMode\":0,\"showScan\":false,\"borderRadius\":8,\"textAlign\":\"left\",\"height\":40,\"color\":\"#999\",\"bgColor\":\"#F9F9F9\",\"borderColor\":\"#FFF\",\"zIndex\":110,\"uuid\":\"9654c842-d9bc-4de6-ac76-b5d4dda16142\",\"showSearchComponent\":1,\"minHeightOpt\":null,\"risk_type\":0,\"risk_alias\":\"hDXz72qOC8\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"鲜奶|常温牛奶|调配奶\",\"alias\":\"2xbut4vjay2zi\",\"number\":12,\"goodsNumber\":100},{\"loading\":true,\"title\":\"咖啡豆\",\"alias\":\"2xj8cs7lobu8u\",\"number\":2,\"goodsNumber\":100},{\"loading\":true,\"title\":\"成品饮料类\",\"alias\":\"3ngyidd2uvsum\",\"number\":4,\"goodsNumber\":100},{\"loading\":true,\"title\":\"果汁|原浆|果酱\",\"alias\":\"35y91ke7563u6\",\"number\":39,\"goodsNumber\":100},{\"loading\":true,\"title\":\"茶叶|茶汤\",\"alias\":\"3es213pewjo6m\",\"number\":4,\"goodsNumber\":100},{\"loading\":true,\"title\":\"饮品配料|小料\",\"alias\":\"2fuflhke0jhv2\",\"number\":15,\"goodsNumber\":100},{\"loading\":true,\"title\":\"食用冰\",\"alias\":\"277za906phs4e\",\"number\":1,\"goodsNumber\":100},{\"loading\":true,\"title\":\"植脂末|炼乳|粉类\",\"alias\":\"3equjwxfjbzzy\",\"number\":5,\"goodsNumber\":100},{\"loading\":true,\"title\":\"烘焙半成品\",\"alias\":\"26ugjjg3odq7y\",\"number\":29,\"goodsNumber\":100}],\"uuid\":\"44279e40-695f-496d-ace4-9d79f52fbeb3\",\"tagGroupOpt\":{\"goodsMargin\":7,\"pageMargin\":4,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"rect\",\"imgOpt\":{\"fill\":\"cover\",\"corner\":\"rect\",\"radius\":2,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":400,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":400,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":null,\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"98595742\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"risk_type\":0,\"risk_alias\":\"hDXz72qOC8\"}],\"createdTime\":1655395369000,\"goodsNum\":0,\"id\":103346087,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":98595742,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":2,\"templateId\":1,\"title\":\"水吧咖啡奶茶调饮\",\"type\":0,\"updateTime\":1686044631000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"chainOnlineShopMode\":2,\"joinType\":1,\"kdtId\":98595742,\"lockStatus\":0,\"offlineShopOpen\":false,\"onlineShopOpen\":true,\"parentKdtId\":98675540,\"rootKdtId\":98675540,\"saasSolution\":2,\"shopName\":\"料料活子\",\"shopRole\":2,\"shopTopic\":0,\"shopType\":7,\"subSolution\":2},\"themeAndColors\":{\"type\":13,\"colors\":{\"general\":\"#EE0A24\",\"main-bg\":\"#EE0A24\",\"main-bg-gradient\":\"linear-gradient(to right, #FF6034, #EE0A24)\",\"main-text\":\"#ffffff\",\"vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"vice-text\":\"#ffffff\",\"icon\":\"#EE0A24\",\"price\":\"#EE0A24\",\"tag-text\":\"#EE0A24\",\"tag-bg\":\"#FDE6E9\",\"start-bg\":\"#FF6034\",\"end-bg\":\"#EE0A24\",\"ump-main-bg\":\"#EE0A24\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#EE0A24\",\"ump-price\":\"#EE0A24\",\"ump-tag-text\":\"#EE0A24\",\"ump-tag-bg\":\"#FDE6E9\",\"ump-coupon-bg\":\"#FFF2F4\",\"ump-border\":\"#FCCED3\",\"ump-start-bg\":\"#FF6034\",\"ump-end-bg\":\"#EE0A24\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":true,\"shopInfo\":{\"address\":\"杭州市临平区杭州迪安家具有限公司(西北门)\",\"area\":\"临平区\",\"business\":\"41\",\"businessName\":\"综合食品\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"183******99\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2021-12-10 11:10:19\",\"intro\":\"精耕杭城，专注烘焙水吧原材料批发30年，欢迎使用！\",\"kdtId\":98675540,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2021/12/24/FlKuRT9zyINsPE1ZRX5kQBhg3ul-.jpg\",\"province\":\"浙江省\",\"shopId\":68492016,\"shopName\":\"杭州琛宝网络科技有限公司-总部\",\"shopType\":7},\"shopConfig\":{\"sold_out_goods_flag\":\"https://img01.yzcdn.cn/upload_files/2021/12/28/FlpOM8X9HUrWy2QumzIip8-FGn79.png\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "search\n", "tag_list_left\n", "[{'loading': True, 'title': '鲜奶|常温牛奶|调配奶', 'alias': '2xbut4vjay2zi', 'number': 12, 'goodsNumber': 100}, {'loading': True, 'title': '咖啡豆', 'alias': '2xj8cs7lobu8u', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '成品饮料类', 'alias': '3ngyidd2uvsum', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '果汁|原浆|果酱', 'alias': '35y91ke7563u6', 'number': 39, 'goodsNumber': 100}, {'loading': True, 'title': '茶叶|茶汤', 'alias': '3es213pewjo6m', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '饮品配料|小料', 'alias': '2fuflhke0jhv2', 'number': 15, 'goodsNumber': 100}, {'loading': True, 'title': '食用冰', 'alias': '277za906phs4e', 'number': 1, 'goodsNumber': 100}, {'loading': True, 'title': '植脂末|炼乳|粉类', 'alias': '3equjwxfjbzzy', 'number': 5, 'goodsNumber': 100}, {'loading': True, 'title': '烘焙半成品', 'alias': '26ugjjg3odq7y', 'number': 29, 'goodsNumber': 100}]\n", "Using proxy: ************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"x3OdzbTYI2\",\"attributes\":\"\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"西餐简餐\",\"category\":[],\"uuid\":\"f3ee27c2-9f83-43a8-a74d-d6e2ce320364\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"x3OdzbTYI2\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FnRECuSRF9cajPnQTMYMfdf0E-Rk.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2023/08/11/FrY3u9JfTSz81VMr9R0_maaqINgn.png\"},\"show\":0,\"title\":\"首页\",\"key\":\"home\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"custom\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"x3OdzbTYI2\"},{\"component\":\"dc-search\",\"type\":\"search\",\"placeholder\":\"搜索商品\",\"hotWords\":[],\"position\":0,\"showMode\":0,\"showScan\":false,\"borderRadius\":8,\"textAlign\":\"left\",\"height\":40,\"color\":\"#999\",\"bgColor\":\"#F9F9F9\",\"borderColor\":\"#FFF\",\"zIndex\":110,\"uuid\":\"aa7eb040-993d-4e50-8d96-fcf6df39129e\",\"showSearchComponent\":1,\"minHeightOpt\":null,\"risk_type\":0,\"risk_alias\":\"x3OdzbTYI2\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"西餐\",\"alias\":\"35vsclyzvi2ry\",\"number\":51,\"goodsNumber\":100},{\"loading\":true,\"title\":\"成品饮料类\",\"alias\":\"3ngyidd2uvsum\",\"number\":4,\"goodsNumber\":100},{\"loading\":true,\"title\":\"常温|冷藏牛奶\",\"alias\":\"3f35nhpn0c3ku\",\"number\":10,\"goodsNumber\":100},{\"loading\":true,\"title\":\"奶酪芝士马苏\",\"alias\":\"2x5nvlc642m9q\",\"number\":24,\"goodsNumber\":100},{\"loading\":true,\"title\":\"稀奶油淡奶油\",\"alias\":\"2xgtec0bd5yy6\",\"number\":18,\"goodsNumber\":100},{\"loading\":true,\"title\":\"黄油\",\"alias\":\"3f4crzocvq1mm\",\"number\":24,\"goodsNumber\":100},{\"loading\":true,\"title\":\"肉制品\",\"alias\":\"3ezhb5qzqwi3i\",\"number\":26,\"goodsNumber\":100},{\"loading\":true,\"title\":\"法甜长条造型蛋糕\",\"alias\":\"35zhqb5fd1hvi\",\"number\":24,\"goodsNumber\":100}],\"uuid\":\"e9b6be56-0e68-46d6-971a-e83a03ea74e4\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"rect\",\"imgOpt\":{\"fill\":\"cover\",\"corner\":\"rect\",\"radius\":2,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":400,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":400,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":null,\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"98595742\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"risk_type\":0,\"risk_alias\":\"x3OdzbTYI2\"}],\"createdTime\":1655395369000,\"goodsNum\":0,\"id\":103346019,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":98595742,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":2,\"templateId\":1,\"title\":\"西餐简餐\",\"type\":0,\"updateTime\":1701334516000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"chainOnlineShopMode\":2,\"joinType\":1,\"kdtId\":98595742,\"lockStatus\":0,\"offlineShopOpen\":false,\"onlineShopOpen\":true,\"parentKdtId\":98675540,\"rootKdtId\":98675540,\"saasSolution\":2,\"shopName\":\"料料活子\",\"shopRole\":2,\"shopTopic\":0,\"shopType\":7,\"subSolution\":2},\"themeAndColors\":{\"type\":13,\"colors\":{\"general\":\"#EE0A24\",\"main-bg\":\"#EE0A24\",\"main-bg-gradient\":\"linear-gradient(to right, #FF6034, #EE0A24)\",\"main-text\":\"#ffffff\",\"vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"vice-text\":\"#ffffff\",\"icon\":\"#EE0A24\",\"price\":\"#EE0A24\",\"tag-text\":\"#EE0A24\",\"tag-bg\":\"#FDE6E9\",\"start-bg\":\"#FF6034\",\"end-bg\":\"#EE0A24\",\"ump-main-bg\":\"#EE0A24\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#EE0A24\",\"ump-price\":\"#EE0A24\",\"ump-tag-text\":\"#EE0A24\",\"ump-tag-bg\":\"#FDE6E9\",\"ump-coupon-bg\":\"#FFF2F4\",\"ump-border\":\"#FCCED3\",\"ump-start-bg\":\"#FF6034\",\"ump-end-bg\":\"#EE0A24\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":true,\"shopInfo\":{\"address\":\"杭州市临平区杭州迪安家具有限公司(西北门)\",\"area\":\"临平区\",\"business\":\"41\",\"businessName\":\"综合食品\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"183******99\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2021-12-10 11:10:19\",\"intro\":\"精耕杭城，专注烘焙水吧原材料批发30年，欢迎使用！\",\"kdtId\":98675540,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2021/12/24/FlKuRT9zyINsPE1ZRX5kQBhg3ul-.jpg\",\"province\":\"浙江省\",\"shopId\":68492016,\"shopName\":\"杭州琛宝网络科技有限公司-总部\",\"shopType\":7},\"shopConfig\":{\"sold_out_goods_flag\":\"https://img01.yzcdn.cn/upload_files/2021/12/28/FlpOM8X9HUrWy2QumzIip8-FGn79.png\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "search\n", "tag_list_left\n", "[{'loading': True, 'title': '西餐', 'alias': '35vsclyzvi2ry', 'number': 51, 'goodsNumber': 100}, {'loading': True, 'title': '成品饮料类', 'alias': '3ngyidd2uvsum', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '常温|冷藏牛奶', 'alias': '3f35nhpn0c3ku', 'number': 10, 'goodsNumber': 100}, {'loading': True, 'title': '奶酪芝士马苏', 'alias': '2x5nvlc642m9q', 'number': 24, 'goodsNumber': 100}, {'loading': True, 'title': '稀奶油淡奶油', 'alias': '2xgtec0bd5yy6', 'number': 18, 'goodsNumber': 100}, {'loading': True, 'title': '黄油', 'alias': '3f4crzocvq1mm', 'number': 24, 'goodsNumber': 100}, {'loading': True, 'title': '肉制品', 'alias': '3ezhb5qzqwi3i', 'number': 26, 'goodsNumber': 100}, {'loading': True, 'title': '法甜长条造型蛋糕', 'alias': '35zhqb5fd1hvi', 'number': 24, 'goodsNumber': 100}]\n", "Using proxy: *************************************************\n", "{\"code\":0,\"msg\":\"ok\",\"data\":{\"alias\":\"TBvPHOkmw0\",\"attributes\":\"\",\"channel\":1,\"components\":[{\"color\":\"#f9f9f9\",\"description\":\"\",\"remark_name\":\"\",\"type\":\"config\",\"title\":\"冷冻蛋糕\",\"category\":[],\"uuid\":\"ca0ecdf5-87b5-4c34-a7b6-d4f6146c2bd0\",\"is_global_setting\":\"1\",\"risk_type\":0,\"risk_alias\":\"TBvPHOkmw0\"},{\"search_switch\":\"0\",\"title_content_type\":\"text\",\"shortcut_list\":[{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FlAXlOJ3nAelb3Bbyf9OrmL_zV9D.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fs26ERXnzNzvpPt5Fyr1ItJoTs48.png\"},\"show\":1,\"title\":\"搜索\",\"key\":\"search\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Fnf1a96N5ioffE716rwUZH0KsLJG.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FrAW5LpbT1So0NB7n6TRd6MujqJk.png\"},\"show\":1,\"title\":\"活动会场\",\"key\":\"marketingPage\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/Frd172B9rSAKTBXqq6z2hgU4dL5q.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FnFYXLxaXNdTYMt6krGWc1xkq9eG.png\"},\"show\":0,\"title\":\"全部商品\",\"key\":\"allGoods\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FtC1A6xZEuyypFxwCYOc36dM1AAw.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FhVqjJmjIrO-R-gfNiPI5u4nbQC8.png\"},\"show\":0,\"title\":\"购物车\",\"key\":\"shopcar\"},{\"images\":{\"standard\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"after_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgTEMnGKHAQR8LJlEbLYwEgndPrO.png\",\"before_slide\":\"https://img01.yzcdn.cn/upload_files/2021/09/06/FgQX1WGpDJrqZbM_ndqtN7fLMFP2.png\"},\"show\":0,\"title\":\"个人中心\",\"key\":\"usercenter\"}],\"navigationbar_type\":\"standard\",\"style_color_custom_background_color\":\"#ffffff\",\"style_color_type\":\"custom\",\"type\":\"navigationbar_config\",\"shortcut_position\":\"left\",\"uuid\":\"************************************\",\"title_switch\":\"1\",\"search_position\":\"center\",\"style_color_custom_type\":\"purecolor\",\"navigationbar_config_type\":\"custom\",\"shortcut_switch\":\"0\",\"style_color_custom_font_color\":\"black\",\"title_position\":\"center\",\"title_image_url\":\"\",\"remark_name\":\"\",\"risk_type\":0,\"risk_alias\":\"TBvPHOkmw0\"},{\"component\":\"dc-search\",\"type\":\"search\",\"placeholder\":\"搜索商品\",\"hotWords\":[],\"position\":0,\"showMode\":0,\"showScan\":false,\"borderRadius\":8,\"textAlign\":\"left\",\"height\":40,\"color\":\"#999\",\"bgColor\":\"#F9F9F9\",\"borderColor\":\"#FFF\",\"zIndex\":110,\"uuid\":\"98edfc8e-0984-4df2-a50c-1882918591b5\",\"showSearchComponent\":1,\"minHeightOpt\":null,\"risk_type\":0,\"risk_alias\":\"TBvPHOkmw0\"},{\"type\":\"tag_list_left\",\"tags\":[{\"loading\":true,\"title\":\"法甜|长条|造型\",\"alias\":\"35zhqb5fd1hvi\",\"number\":24,\"goodsNumber\":100},{\"loading\":true,\"title\":\"三角切块蛋糕\",\"alias\":\"35y8v5rpwxtse\",\"number\":61,\"goodsNumber\":100},{\"loading\":true,\"title\":\"芝士蛋糕\",\"alias\":\"2frxixqz6224u\",\"number\":22,\"goodsNumber\":100},{\"loading\":true,\"title\":\"千层蛋糕\",\"alias\":\"2g31hcyocmmu6\",\"number\":11,\"goodsNumber\":100},{\"loading\":true,\"title\":\"茶歇|马卡龙\",\"alias\":\"271te3ksjy6n2\",\"number\":19,\"goodsNumber\":100},{\"loading\":true,\"title\":\"独立包装小蛋糕\",\"alias\":\"2okj7g5qg3lym\",\"number\":48,\"goodsNumber\":100}],\"uuid\":\"5f1628ae-edc6-4b93-b7e2-5e6da329f434\",\"tagGroupOpt\":{\"goodsMargin\":20,\"pageMargin\":15,\"itemCardOpt\":{\"type\":\"card\",\"layout\":\"horizontal\",\"imgHeight\":100,\"corner\":\"rect\",\"imgOpt\":{\"fill\":\"cover\",\"corner\":\"rect\",\"radius\":2,\"maskIconSize\":0.5},\"titleOpt\":{\"titleFontWeight\":400,\"titleFontSize\":13,\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleLines\":1},\"priceOpt\":{\"fontWeight\":400,\"fontSize\":18,\"tagGap\":2},\"oPriceOpt\":null,\"subTitleOpt\":{\"titleFontSize\":12,\"titleLines\":1,\"titleColor\":\"#969799\",\"vMargin\":0,\"hMargin\":0,\"bgColor\":\"transparent\",\"textAlign\":\"left\",\"titleExtraStyle\":{\"height\":18}},\"btnOpt\":{\"kdtId\":\"98595742\",\"type\":\"icon\",\"name\":\"cart-circle-o\"}}},\"risk_type\":0,\"risk_alias\":\"TBvPHOkmw0\"}],\"createdTime\":1660118599000,\"goodsNum\":0,\"id\":104349822,\"isDelete\":0,\"isDisplay\":1,\"isLock\":0,\"isTiming\":0,\"kdtId\":98595742,\"num\":0,\"platform\":3,\"publishTime\":-28800000,\"remark\":\"\",\"source\":2,\"templateId\":1,\"title\":\"冷冻蛋糕\",\"type\":0,\"updateTime\":1686191331000,\"useNewTagListInterface\":true,\"needPointSwitch\":false,\"requestId\":\"\",\"shopMetaInfo\":{\"chainOnlineShopMode\":2,\"joinType\":1,\"kdtId\":98595742,\"lockStatus\":0,\"offlineShopOpen\":false,\"onlineShopOpen\":true,\"parentKdtId\":98675540,\"rootKdtId\":98675540,\"saasSolution\":2,\"shopName\":\"料料活子\",\"shopRole\":2,\"shopTopic\":0,\"shopType\":7,\"subSolution\":2},\"themeAndColors\":{\"type\":13,\"colors\":{\"general\":\"#EE0A24\",\"main-bg\":\"#EE0A24\",\"main-bg-gradient\":\"linear-gradient(to right, #FF6034, #EE0A24)\",\"main-text\":\"#ffffff\",\"vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"vice-text\":\"#ffffff\",\"icon\":\"#EE0A24\",\"price\":\"#EE0A24\",\"tag-text\":\"#EE0A24\",\"tag-bg\":\"#FDE6E9\",\"start-bg\":\"#FF6034\",\"end-bg\":\"#EE0A24\",\"ump-main-bg\":\"#EE0A24\",\"ump-main-text\":\"#ffffff\",\"ump-vice-bg\":\"linear-gradient(to right, #FFD01E, #FF8917)\",\"ump-vice-text\":\"#ffffff\",\"ump-icon\":\"#EE0A24\",\"ump-price\":\"#EE0A24\",\"ump-tag-text\":\"#EE0A24\",\"ump-tag-bg\":\"#FDE6E9\",\"ump-coupon-bg\":\"#FFF2F4\",\"ump-border\":\"#FCCED3\",\"ump-start-bg\":\"#FF6034\",\"ump-end-bg\":\"#EE0A24\",\"brand-wechat\":\"#1AAD19\",\"brand-alipay\":\"#027AFF\",\"brand-youzandanbao\":\"#07C160\",\"brand-xiaohongshu\":\"#FF2442\",\"brand-baidu\":\"#2A32E1\",\"brand-youzandanbao-bg\":\"#E5F7EE\",\"notice\":\"#ED6A0C\",\"notice-bg\":\"#FFFBE8\",\"link\":\"#576B95\",\"score\":\"#FF5200\",\"error\":\"#EE0A24\",\"error-bg\":\"#FDE6E9\",\"success\":\"#07C160\",\"success-bg\":\"#E6F8EF\",\"warn\":\"#EE0A24\",\"highlight\":\"#EE0A24\",\"neutral-white\":\"#ffffff\",\"neutral-black\":\"#000000\",\"neutral-text-main\":\"#323233\",\"neutral-text-prompt\":\"#969799\",\"neutral-text-disable\":\"#c8c9cc\",\"neutral-line-main\":\"#dcdee0\",\"neutral-line-vice\":\"#ebedf0\",\"neutral-bg-main\":\"#f2f3f5\",\"neutral-bg-vice\":\"#f7f8fa\"}},\"needEnterShop\":true,\"shopInfo\":{\"address\":\"杭州市临平区杭州迪安家具有限公司(西北门)\",\"area\":\"临平区\",\"business\":\"41\",\"businessName\":\"综合食品\",\"city\":\"杭州市\",\"contactCountryCode\":\"+86\",\"contactMobile\":\"***********\",\"contactName\":\"183******99\",\"contactQQ\":\"\",\"countyId\":330113,\"createdTime\":\"2021-12-10 11:10:19\",\"intro\":\"精耕杭城，专注烘焙水吧原材料批发30年，欢迎使用！\",\"kdtId\":98675540,\"lockStatus\":0,\"logo\":\"https://img.yzcdn.cn/upload_files/2021/12/24/FlKuRT9zyINsPE1ZRX5kQBhg3ul-.jpg\",\"province\":\"浙江省\",\"shopId\":68492016,\"shopName\":\"杭州琛宝网络科技有限公司-总部\",\"shopType\":7},\"shopConfig\":{\"sold_out_goods_flag\":\"https://img01.yzcdn.cn/upload_files/2021/12/28/FlpOM8X9HUrWy2QumzIip8-FGn79.png\",\"homepage_gray\":\"{\\\"isOpen\\\":false,\\\"timeRange\\\":[0,0]}\"},\"skeleton\":false}}\n", "config\n", "navigationbar_config\n", "search\n", "tag_list_left\n", "[{'loading': True, 'title': '法甜|长条|造型', 'alias': '35zhqb5fd1hvi', 'number': 24, 'goodsNumber': 100}, {'loading': True, 'title': '三角切块蛋糕', 'alias': '35y8v5rpwxtse', 'number': 61, 'goodsNumber': 100}, {'loading': True, 'title': '芝士蛋糕', 'alias': '2frxixqz6224u', 'number': 22, 'goodsNumber': 100}, {'loading': True, 'title': '千层蛋糕', 'alias': '2g31hcyocmmu6', 'number': 11, 'goodsNumber': 100}, {'loading': True, 'title': '茶歇|马卡龙', 'alias': '271te3ksjy6n2', 'number': 19, 'goodsNumber': 100}, {'loading': True, 'title': '独立包装小蛋糕', 'alias': '2okj7g5qg3lym', 'number': 48, 'goodsNumber': 100}]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>root_cate</th>\n", "      <th>sub_cate_list</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'image_url': '', 'link_title': '水果', 'image_width': 0, 'title': '水果', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346040, 'link_type': 'feature', 'image_height': 0, 'alias': 'Js2HnsYHeV', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=Js2HnsYHeV', 'template_id': 1, 'image_id': '0'}</td>\n", "      <td>[{'loading': True, 'title': '芒果', 'alias': '2okjw8u5sldxq', 'number': 5, 'goodsNumber': 10}, {'loading': True, 'title': '莓果', 'alias': '361y2o62pqgxa', 'number': 8, 'goodsNumber': 10}, {'loading': True, 'title': '火龙果', 'alias': '2olqi8cgw6a7i', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '柠檬/金桔', 'alias': '276soicrxzz32', 'number': 4, 'goodsNumber': 99}, {'loading': True, 'title': '葡提/番茄', 'alias': '36btvdij3o432', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '凤梨/橙橘', 'alias': '2732lwy1fqq0e', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '瓜类', 'alias': '2od558j9wvt4e', 'number': 1, 'goodsNumber': 99}, {'loading': True, 'title': '果汁|原浆|果酱', 'alias': '35y91ke7563u6', 'number': 39, 'goodsNumber': 99}, {'loading': True, 'title': '鲜鸡蛋|蛋类', 'alias': '2fo8hpl4xd7um', 'number': 4, 'goodsNumber': 100}]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                                                                                                                                                                                                                                                                          root_cate  \\\n", "0  {'image_url': '', 'link_title': '水果', 'image_width': 0, 'title': '水果', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346040, 'link_type': 'feature', 'image_height': 0, 'alias': 'Js2HnsYHeV', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=Js2HnsYHeV', 'template_id': 1, 'image_id': '0'}   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        sub_cate_list  \n", "0  [{'loading': True, 'title': '芒果', 'alias': '2okjw8u5sldxq', 'number': 5, 'goodsNumber': 10}, {'loading': True, 'title': '莓果', 'alias': '361y2o62pqgxa', 'number': 8, 'goodsNumber': 10}, {'loading': True, 'title': '火龙果', 'alias': '2olqi8cgw6a7i', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '柠檬/金桔', 'alias': '276soicrxzz32', 'number': 4, 'goodsNumber': 99}, {'loading': True, 'title': '葡提/番茄', 'alias': '36btvdij3o432', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '凤梨/橙橘', 'alias': '2732lwy1fqq0e', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '瓜类', 'alias': '2od558j9wvt4e', 'number': 1, 'goodsNumber': 99}, {'loading': True, 'title': '果汁|原浆|果酱', 'alias': '35y91ke7563u6', 'number': 39, 'goodsNumber': 99}, {'loading': True, 'title': '鲜鸡蛋|蛋类', 'alias': '2fo8hpl4xd7um', 'number': 4, 'goodsNumber': 100}]  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_sub_cate_list_by_root_cate(root_cate):\n", "    root_cate_alias='pDXPeDPwe0'\n", "    root_cate_alias=root_cate['alias']\n", "    get_second_cate_url=f'https://h5.youzan.com/wscdeco/feature-detail.json?{app_and_kdt_id}&&alias={root_cate_alias}&check_chainstore=true&stage=16&check_multi_store=1&close_chainstore_webview_limit=true&check_old_home=1'\n", "    second_cate_list=get_remote_data_with_proxy(get_second_cate_url)\n", "    print(second_cate_list)\n", "\n", "    tag_list_left=None\n", "    for component in json.loads(second_cate_list)['data']['components']:\n", "        print(component['type'])\n", "        if component['type']=='tag_list_left':\n", "            tag_list_left=component['tags']\n", "            break\n", "\n", "    print(tag_list_left)\n", "    return tag_list_left\n", "\n", "\n", "all_sub_cate_list=[]\n", "for root_cate in root_cate_list:\n", "    sub_cate_list=get_sub_cate_list_by_root_cate(root_cate)\n", "    all_sub_cate_list.append({\"root_cate\": root_cate, \"sub_cate_list\": sub_cate_list})\n", "\n", "all_sub_cate_list_df=pd.DataFrame(all_sub_cate_list)\n", "all_sub_cate_list_df.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据类目ID获取类目的商品"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'root_cate': {'image_url': '', 'link_title': '水果', 'image_width': 0, 'title': '水果', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346040, 'link_type': 'feature', 'image_height': 0, 'alias': 'Js2HnsYHeV', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=Js2HnsYHeV', 'template_id': 1, 'image_id': '0'}, 'sub_cate_list': [{'loading': True, 'title': '芒果', 'alias': '2okjw8u5sldxq', 'number': 5, 'goodsNumber': 10}, {'loading': True, 'title': '莓果', 'alias': '361y2o62pqgxa', 'number': 8, 'goodsNumber': 10}, {'loading': True, 'title': '火龙果', 'alias': '2olqi8cgw6a7i', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '柠檬/金桔', 'alias': '276soicrxzz32', 'number': 4, 'goodsNumber': 99}, {'loading': True, 'title': '葡提/番茄', 'alias': '36btvdij3o432', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '凤梨/橙橘', 'alias': '2732lwy1fqq0e', 'number': 2, 'goodsNumber': 99}, {'loading': True, 'title': '瓜类', 'alias': '2od558j9wvt4e', 'number': 1, 'goodsNumber': 99}, {'loading': True, 'title': '果汁|原浆|果酱', 'alias': '35y91ke7563u6', 'number': 39, 'goodsNumber': 99}, {'loading': True, 'title': '鲜鸡蛋|蛋类', 'alias': '2fo8hpl4xd7um', 'number': 4, 'goodsNumber': 100}]}\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "{'root_cate': {'image_url': '', 'link_title': '乳制品', 'image_width': 0, 'title': '乳制品', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346064, 'link_type': 'feature', 'image_height': 0, 'alias': 'ZlZDIQVOT8', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=ZlZDIQVOT8', 'template_id': 1, 'image_id': '0'}, 'sub_cate_list': [{'loading': True, 'title': '稀奶油淡奶油', 'alias': '2xgtec0bd5yy6', 'number': 18, 'goodsNumber': 100}, {'loading': True, 'title': '乳脂|植物|牛奶奶油', 'alias': '274as0u9gphou', 'number': 9, 'goodsNumber': 100}, {'loading': True, 'title': '奶酪芝士马苏', 'alias': '2x5nvlc642m9q', 'number': 24, 'goodsNumber': 100}, {'loading': True, 'title': '黄油', 'alias': '3f4crzocvq1mm', 'number': 24, 'goodsNumber': 100}, {'loading': True, 'title': '常温|冷藏牛奶', 'alias': '3f35nhpn0c3ku', 'number': 10, 'goodsNumber': 100}, {'loading': True, 'title': '奶粉', 'alias': '2xj7u75l5eu6m', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '其他油脂', 'alias': '3negs7uu00mf2', 'number': 10, 'goodsNumber': 10}, {'loading': True, 'title': '包材', 'alias': '2fy4gh9yzl5fi', 'number': 16, 'goodsNumber': 100}]}\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "{'root_cate': {'image_url': '', 'link_title': '面粉', 'image_width': 0, 'title': '面粉', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346018, 'link_type': 'feature', 'image_height': 0, 'alias': 'GpnyHaen0h', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=GpnyHaen0h', 'template_id': 1, 'image_id': '0'}, 'sub_cate_list': [{'loading': True, 'title': '日清面粉', 'alias': '2780hz2tgqw4e', 'number': 5, 'goodsNumber': 100}, {'loading': True, 'title': '美玫面粉', 'alias': '2ovmzr9jf7jlq', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '金像面粉', 'alias': '2xkh808r83z9q', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '王后面粉', 'alias': '2fy443sne4qha', 'number': 5, 'goodsNumber': 100}, {'loading': True, 'title': '王后柔风', 'alias': '2ft7xzogeccpa', 'number': 5, 'goodsNumber': 100}, {'loading': True, 'title': '伯爵面粉', 'alias': '2xj8cxhrck41a', 'number': 6, 'goodsNumber': 100}, {'loading': True, 'title': '鸟越面粉', 'alias': '366wblbolj2oe', 'number': 3, 'goodsNumber': 100}, {'loading': True, 'title': '日本昭和', 'alias': '2xmylyef2hmby', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '日本制粉 NIPPN', 'alias': '1y4dmmcqjmn1a', 'number': 3, 'goodsNumber': 100}, {'loading': True, 'title': '预拌粉', 'alias': '3f36v3gik0ifi', 'number': 1, 'goodsNumber': 100}, {'loading': True, 'title': '法国都梅', 'alias': '2xmxk7zzd97vy', 'number': 1, 'goodsNumber': 10}, {'loading': True, 'title': '其他粉类', 'alias': '3f1wm3uxd3k3i', 'number': 4, 'goodsNumber': 100}]}\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "{'root_cate': {'image_url': '', 'link_title': '半成品', 'image_width': 0, 'title': '成/半成品', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346095, 'link_type': 'feature', 'image_height': 0, 'alias': 'MQjbS8pvJZ', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=MQjbS8pvJZ', 'template_id': 1, 'image_id': '0'}, 'sub_cate_list': [{'loading': True, 'title': '烘焙半成品', 'alias': '26ugjjg3odq7y', 'number': 29, 'goodsNumber': 100}, {'loading': True, 'title': '肉制品', 'alias': '3ezhb5qzqwi3i', 'number': 26, 'goodsNumber': 100}, {'loading': True, 'title': '糖果', 'alias': '2xbw0zc9cbqbi', 'number': 1, 'goodsNumber': 100}, {'loading': True, 'title': '西餐', 'alias': '3f34s8nyl9q3y', 'number': 1, 'goodsNumber': 100}]}\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "{'root_cate': {'image_url': '', 'link_title': '烘焙辅料', 'image_width': 0, 'title': '烘焙辅料', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346096, 'link_type': 'feature', 'image_height': 0, 'alias': '4DahSDpGH7', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=4DahSDpGH7', 'template_id': 1, 'image_id': '0'}, 'sub_cate_list': [{'loading': True, 'title': '糖类', 'alias': '3nugk8hlajgry', 'number': 13, 'goodsNumber': 100}, {'loading': True, 'title': '果泥果酱果肉', 'alias': '26y6m0erzq1ny', 'number': 22, 'goodsNumber': 100}, {'loading': True, 'title': '沙拉酱|奶制夹心馅', 'alias': '2opg8pw5wgj9q', 'number': 12, 'goodsNumber': 100}, {'loading': True, 'title': '食用馅料|豆沙|泥制馅料', 'alias': '3f34m3n9msfta', 'number': 15, 'goodsNumber': 100}, {'loading': True, 'title': '可可豆及其制品', 'alias': '3etaw4q9es7xq', 'number': 8, 'goodsNumber': 100}, {'loading': True, 'title': '预拌粉', 'alias': '3f36v3gik0ifi', 'number': 1, 'goodsNumber': 100}, {'loading': True, 'title': '添加剂|酵母|泡打粉', 'alias': '36833rjlxycla', 'number': 16, 'goodsNumber': 100}, {'loading': True, 'title': '坚果及其制品', 'alias': '2xj86td4hjg5a', 'number': 17, 'goodsNumber': 100}, {'loading': True, 'title': '装饰|撒料|淋面', 'alias': '2omzq62y1v2um', 'number': 12, 'goodsNumber': 100}, {'loading': True, 'title': '肉松', 'alias': '3f1xzwabdgice', 'number': 3, 'goodsNumber': 100}, {'loading': True, 'title': '调制酒', 'alias': '3f36cagwcaym6', 'number': 6, 'goodsNumber': 100}, {'loading': True, 'title': '调味粉|香精|浓缩汁', 'alias': '1yah9swwsr45q', 'number': 7, 'goodsNumber': 100}, {'loading': True, 'title': '鲜鸡蛋|蛋类', 'alias': '2fo8hpl4xd7um', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '其他辅料', 'alias': '1yajclybyrpta', 'number': 12, 'goodsNumber': 100}, {'loading': True, 'title': '包材', 'alias': '2fy4gh9yzl5fi', 'number': 16, 'goodsNumber': 100}]}\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "{'root_cate': {'image_url': '', 'link_title': '水吧咖啡', 'image_width': 0, 'title': '水吧咖啡', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346087, 'link_type': 'feature', 'image_height': 0, 'alias': 'hDXz72qOC8', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=hDXz72qOC8', 'template_id': 1, 'image_id': '0'}, 'sub_cate_list': [{'loading': True, 'title': '鲜奶|常温牛奶|调配奶', 'alias': '2xbut4vjay2zi', 'number': 12, 'goodsNumber': 100}, {'loading': True, 'title': '咖啡豆', 'alias': '2xj8cs7lobu8u', 'number': 2, 'goodsNumber': 100}, {'loading': True, 'title': '成品饮料类', 'alias': '3ngyidd2uvsum', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '果汁|原浆|果酱', 'alias': '35y91ke7563u6', 'number': 39, 'goodsNumber': 100}, {'loading': True, 'title': '茶叶|茶汤', 'alias': '3es213pewjo6m', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '饮品配料|小料', 'alias': '2fuflhke0jhv2', 'number': 15, 'goodsNumber': 100}, {'loading': True, 'title': '食用冰', 'alias': '277za906phs4e', 'number': 1, 'goodsNumber': 100}, {'loading': True, 'title': '植脂末|炼乳|粉类', 'alias': '3equjwxfjbzzy', 'number': 5, 'goodsNumber': 100}, {'loading': True, 'title': '烘焙半成品', 'alias': '26ugjjg3odq7y', 'number': 29, 'goodsNumber': 100}]}\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "{'root_cate': {'image_url': '', 'link_title': '油脂类', 'image_width': 0, 'title': '油脂类', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 103346019, 'link_type': 'feature', 'image_height': 0, 'alias': 'x3OdzbTYI2', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=x3OdzbTYI2', 'template_id': 1, 'image_id': '0'}, 'sub_cate_list': [{'loading': True, 'title': '西餐', 'alias': '35vsclyzvi2ry', 'number': 51, 'goodsNumber': 100}, {'loading': True, 'title': '成品饮料类', 'alias': '3ngyidd2uvsum', 'number': 4, 'goodsNumber': 100}, {'loading': True, 'title': '常温|冷藏牛奶', 'alias': '3f35nhpn0c3ku', 'number': 10, 'goodsNumber': 100}, {'loading': True, 'title': '奶酪芝士马苏', 'alias': '2x5nvlc642m9q', 'number': 24, 'goodsNumber': 100}, {'loading': True, 'title': '稀奶油淡奶油', 'alias': '2xgtec0bd5yy6', 'number': 18, 'goodsNumber': 100}, {'loading': True, 'title': '黄油', 'alias': '3f4crzocvq1mm', 'number': 24, 'goodsNumber': 100}, {'loading': True, 'title': '肉制品', 'alias': '3ezhb5qzqwi3i', 'number': 26, 'goodsNumber': 100}, {'loading': True, 'title': '法甜长条造型蛋糕', 'alias': '35zhqb5fd1hvi', 'number': 24, 'goodsNumber': 100}]}\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "{'root_cate': {'image_url': '', 'link_title': '冷冻蛋糕', 'image_width': 0, 'title': '冷冻蛋糕', 'type': 'image_ad_selection', 'image_thumb_url': '', 'link_id': 104349822, 'link_type': 'feature', 'image_height': 0, 'alias': 'TBvPHOkmw0', 'link_url': 'https://h5.youzan.com/v2/showcase/feature?alias=TBvPHOkmw0', 'template_id': 1, 'image_id': '0'}, 'sub_cate_list': [{'loading': True, 'title': '法甜|长条|造型', 'alias': '35zhqb5fd1hvi', 'number': 24, 'goodsNumber': 100}, {'loading': True, 'title': '三角切块蛋糕', 'alias': '35y8v5rpwxtse', 'number': 61, 'goodsNumber': 100}, {'loading': True, 'title': '芝士蛋糕', 'alias': '2frxixqz6224u', 'number': 22, 'goodsNumber': 100}, {'loading': True, 'title': '千层蛋糕', 'alias': '2g31hcyocmmu6', 'number': 11, 'goodsNumber': 100}, {'loading': True, 'title': '茶歇|马卡龙', 'alias': '271te3ksjy6n2', 'number': 19, 'goodsNumber': 100}, {'loading': True, 'title': '独立包装小蛋糕', 'alias': '2okj7g5qg3lym', 'number': 48, 'goodsNumber': 100}]}\n", "Using proxy: ************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "Using proxy: *************************************************\n", "Using proxy: ************************************************\n", "Using proxy: *************************************************\n", "{'itemCardOpt': {'tagsOpt': {'_assign': True, 'list': []}, 'oPriceOpt': {}, 'btnGoodsNumberOpt': {'num': 0, '_assign': True}, 'priceOpt': {'price': '142.00'}, 'imgOpt': {'src': 'https://img01.yzcdn.cn/upload_files/2022/07/28/FhbnZ6c_MrJABFa8zs0sRneRijLJ.jpg', 'mask': ''}, 'titleOpt': {'title': '大青芒 毛重17-18斤大果芒果单果750-1000g'}, 'subTitleOpt': {'title': ' '}, 'extraInfo': {'slg': 'tagGoodList-default,OpBottom,uuid,abTraceId'}, 'goodsPreloadOpt': {'image': {'height': 800, 'id': 5019301164, 'url': 'https://img01.yzcdn.cn/upload_files/2022/07/28/FhbnZ6c_MrJABFa8zs0sRneRijLJ.jpg', 'width': 800}, 'alias': '2orx9tvkoer7ymv', 'price': '1.42', 'title': '大青芒 毛重17-18斤大果芒果单果750-1000g', '_assign': True}, 'actionOpt': {'_assign': True, 'alias': '2orx9tvkoer7ymv', 'id': 2379083990, 'postage': 0, 'isVirtual': 0, 'picture': 'https://img01.yzcdn.cn/upload_files/2022/07/28/FhbnZ6c_MrJABFa8zs0sRneRijLJ.jpg', 'link': {'type': 'goods', 'alias': '2orx9tvkoer7ymv'}}, 'extOpt': {'_assign': True, 'list': [{'title': ' ', 'vMargin': 4, 'hMargin': 0, 'titleColor': 'var(--general, #999999)', 'titleFontSize': 12, 'titleLines': 1, 'titleFontWeight': 'normal', 'titleExtraStyle': {'line-height': 1}}], 'align': 'left'}}, 'url': 'https://shop98787910.youzan.com/v2/goods/2orx9tvkoer7ymv', 'alias': '2orx9tvkoer7ymv', 'id': 2379083990, '类目名': '水果', '二级类目名': '芒果', '类目link': 'https://h5.youzan.com/v2/showcase/feature?alias=Js2HnsYHeV'}\n"]}], "source": ["all_products=[]\n", "for cate in all_sub_cate_list:\n", "    print(cate)\n", "    cate_info=cate['root_cate']\n", "    sub_cate_list=cate['sub_cate_list']\n", "    for tag in sub_cate_list:\n", "        alias=tag['alias']\n", "        url=f'https://h5.youzan.com/wscdeco/tee/goodsByTagAlias.json?{app_and_kdt_id}&page=1&alias={alias}&json=1&offlineId=0&pageSize=60&activityPriceIndependent=1&needOPriceAndTagsOpt=1&isShowPeriod=1'\n", "        cate_product_list=get_remote_data_with_proxy(url)\n", "        cate_product_list=json.loads(cate_product_list)\n", "        for product in cate_product_list['data']['list']:\n", "            product['类目名']=cate_info['title']\n", "            product['二级类目名']=tag['title']\n", "            product['类目link']=cate_info['link_url']\n", "            all_products.append(product)\n", "\n", "print(all_products[0])\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>一级类目</th>\n", "      <th>二级类目</th>\n", "      <th>alias</th>\n", "      <th>link</th>\n", "      <th>数据获取时间</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>水果</td>\n", "      <td></td>\n", "      <td>Js2HnsYHeV</td>\n", "      <td>https://h5.youzan.com/v2/showcase/feature?alias=Js2HnsYHeV</td>\n", "      <td>2024-02-01 10:33:15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>水果</td>\n", "      <td>芒果</td>\n", "      <td>2okjw8u5sldxq</td>\n", "      <td></td>\n", "      <td>2024-02-01 10:33:15</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  一级类目 二级类目          alias  \\\n", "0   水果          Js2HnsYHeV   \n", "1   水果   芒果  2okjw8u5sldxq   \n", "\n", "                                                         link  \\\n", "0  https://h5.youzan.com/v2/showcase/feature?alias=Js2HnsYHeV   \n", "1                                                               \n", "\n", "                数据获取时间  \n", "0  2024-02-01 10:33:15  \n", "1  2024-02-01 10:33:15  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["cate_list_to_save=[]\n", "for root_cate in all_sub_cate_list:\n", "    # print(root_cate)\n", "    cate_list_to_save.append({\"一级类目\":root_cate['root_cate']['title'],\n", "                              \"二级类目\":\"\", \n", "                              \"alias\":root_cate['root_cate']['alias'],\n", "                              \"link\":root_cate['root_cate']['link_url'],\n", "                              \"数据获取时间\":time_of_now})\n", "    for sub_cate in root_cate['sub_cate_list']:\n", "        cate_list_to_save.append({\"一级类目\":root_cate['root_cate']['title'],\n", "                                  \"二级类目\":sub_cate['title'], \n", "                                  \"alias\":sub_cate['alias'], \n", "                                  \"link\":'',\n", "                                  \"数据获取时间\":time_of_now})\n", "\n", "cate_list_to_save_df=pd.DataFrame(cate_list_to_save)\n", "cate_list_to_save_df.to_csv(f\"./data/{brand_name}/{brand_name}-全部类目树.csv\", index=False)\n", "\n", "cate_list_to_save_df.head(2)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取商品的SKU信息并解析"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: *************************************************\n", "[{'skuId': '2orx9tvkoer7ymv', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '26vno27t06vm653', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '36d1ow2slj2q66q', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2okjdk9zpnwguqc', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fltvs2m28c5qih', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 8977215495, 'skuStock': 46, 'skuSpec': '125g/盒*2盒', 'skuPrice': 2800}, {'skuId': 17932287744, 'skuStock': 7, 'skuSpec': '125g*12盒/箱', 'skuPrice': 15800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 12983236935, 'skuStock': 4, 'skuSpec': '2盒', 'skuPrice': 2750}, {'skuId': 12983236936, 'skuStock': 1, 'skuSpec': '5盒', 'skuPrice': 6750}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nn2o7mubg0j2xf', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '35vtkmjloibzyg1', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fy43vvws2cwe1q', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2g1sslbr5x572ue', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fuejz5ab7xsurk', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2ot427y543666vp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xbvidckx75lqcb', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fvnlczv675jicx', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 8977625371, 'skuStock': 15, 'skuSpec': '3斤/袋(净重)', 'skuPrice': 1300}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oqpg7u0rrh0ulb', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fphp4wj35ocuwx', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nn3jeyzh73dqrg', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nwytc6ry40ge27', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '361y2ubfcu17yoy', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2g32j2f78pnziac', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 17911480712, 'skuStock': 8, 'skuSpec': '2个', 'skuPrice': 4800}, {'skuId': 17911480713, 'skuStock': 2, 'skuSpec': '7个/箱(毛重22-24斤)', 'skuPrice': 15500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2x9cwo9c08qpa2b', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3ex15885gntlqf5', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37950156, 'skuStock': 70, 'skuSpec': '3kg/袋', 'skuPrice': 28800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5069706525, 'skuStock': 19, 'skuSpec': '阿方索芒果', 'skuPrice': 7600}, {'skuId': 5038541239, 'skuStock': 36, 'skuSpec': '草莓', 'skuPrice': 7550}, {'skuId': 10153199559, 'skuStock': 57, 'skuSpec': '树莓/覆盆子', 'skuPrice': 8350}, {'skuId': 13660410533, 'skuStock': 29, 'skuSpec': '芒果', 'skuPrice': 6200}, {'skuId': 14159834417, 'skuStock': 4, 'skuSpec': '野生蓝莓', 'skuPrice': 11900}, {'skuId': 24538087311, 'skuStock': 5, 'skuSpec': '西番莲', 'skuPrice': 9200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9664199082, 'skuStock': 7, 'skuSpec': '芒果', 'skuPrice': 12000}, {'skuId': 9701178017, 'skuStock': 12, 'skuSpec': '草莓', 'skuPrice': 10300}, {'skuId': 9731590146, 'skuStock': 9, 'skuSpec': '覆盆子/红莓/桑子/木莓', 'skuPrice': 15800}, {'skuId': 9701178024, 'skuStock': 4, 'skuSpec': '白桃', 'skuPrice': 12200}, {'skuId': 9701178020, 'skuStock': 9, 'skuSpec': '百香果', 'skuPrice': 11500}, {'skuId': 23584882198, 'skuStock': 10, 'skuSpec': '凤梨', 'skuPrice': 10300}, {'skuId': 9701178018, 'skuStock': 4, 'skuSpec': '椰子', 'skuPrice': 12300}, {'skuId': 9701178016, 'skuStock': 2, 'skuSpec': '蓝莓', 'skuPrice': 15800}, {'skuId': 9701178022, 'skuStock': 3, 'skuSpec': '无糖黄柠檬', 'skuPrice': 10500}, {'skuId': 11240184841, 'skuStock': 4, 'skuSpec': '杏子', 'skuPrice': 12000}, {'skuId': 24225290737, 'skuStock': 0, 'skuSpec': '黑加仑', 'skuPrice': 11500}, {'skuId': 14186139822, 'skuStock': 0, 'skuSpec': '荔枝', 'skuPrice': 13000}, {'skuId': 14484241966, 'skuStock': 0, 'skuSpec': '日本柚子', 'skuPrice': 75000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7175732460, 'skuStock': 0, 'skuSpec': '香草风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732463, 'skuStock': 0, 'skuSpec': '榛果风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732464, 'skuStock': 0, 'skuSpec': '焦糖风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732458, 'skuStock': 0, 'skuSpec': '绿薄荷风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732459, 'skuStock': 4, 'skuSpec': '桂花风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732461, 'skuStock': 6, 'skuSpec': '莫西多薄荷风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 24239677493, 'skuStock': 6, 'skuSpec': '山茶花风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732462, 'skuStock': 7, 'skuSpec': '纯蔗糖风味糖浆 1000ml*1瓶', 'skuPrice': 6900}, {'skuId': 16968894232, 'skuStock': 6, 'skuSpec': '焦糖风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 9011230833, 'skuStock': 3, 'skuSpec': '香草风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 9011230834, 'skuStock': 1, 'skuSpec': '绿薄荷风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 22643588819, 'skuStock': 3, 'skuSpec': '榛果风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 25427996162, 'skuStock': 6, 'skuSpec': '太妃果风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 14648631254, 'skuStock': 5, 'skuSpec': '海盐焦糖风味糖浆 1L装', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14243726338, 'skuStock': 20, 'skuSpec': '3kg*1包', 'skuPrice': 22500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 7163904048, 'skuStock': 646, 'skuSpec': '1L/盒', 'skuPrice': 1480}, {'skuId': 7163904049, 'skuStock': 53, 'skuSpec': '1L*12盒/箱', 'skuPrice': 14500}, {'skuId': 13736519341, 'skuStock': 10, 'skuSpec': '5箱', 'skuPrice': 70000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24302687773, 'skuStock': 314, 'skuSpec': '200ml*1瓶', 'skuPrice': 680}, {'skuId': 24302687774, 'skuStock': 26, 'skuSpec': '200ml*12瓶/箱', 'skuPrice': 6000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1y6s8kqy6qxemwz', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xd2gpe21trkucd', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24525086972, 'skuStock': 104, 'skuSpec': '1kg*1包', 'skuPrice': 1850}, {'skuId': 24525086971, 'skuStock': 5, 'skuSpec': '1kg*20包/箱', 'skuPrice': 30500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2731wryj2on5arc', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24871381261, 'skuStock': 63, 'skuSpec': '1罐', 'skuPrice': 1230}, {'skuId': 24871381262, 'skuStock': 12, 'skuSpec': '5罐', 'skuPrice': 5950}, {'skuId': 24871381263, 'skuStock': 2, 'skuSpec': '24罐/箱', 'skuPrice': 26800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5169711120, 'skuStock': 11, 'skuSpec': '330ml*12瓶/箱', 'skuPrice': 5900}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2frwzxrjo3ptani', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3npllznpz2qemch', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24510792539, 'skuStock': 13, 'skuSpec': '500g*1袋', 'skuPrice': 1500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13487514003, 'skuStock': 94, 'skuSpec': '1L*1盒', 'skuPrice': 3190}, {'skuId': 13487514002, 'skuStock': 7, 'skuSpec': '1L*12盒/箱', 'skuPrice': 34800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7164515271, 'skuStock': 20, 'skuSpec': '1.26kg*1盒', 'skuPrice': 1680}, {'skuId': 7164515270, 'skuStock': 1, 'skuSpec': '1.26kg*12盒/箱', 'skuPrice': 19200}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1ylkpgvn4yaf2qq', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3et9oqw4csvcu4p', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 19230881750, 'skuStock': 4, 'skuSpec': '820g*24罐/箱', 'skuPrice': 21600}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1y6seotcy4flqjs', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2x5qb1h6poptaqy', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1y8252g0ep68ehp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3636xxx0bdllqs1', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13487517388, 'skuStock': 507, 'skuSpec': '1L*1盒', 'skuPrice': 2900}, {'skuId': 13487517389, 'skuStock': 42, 'skuSpec': '1L*12盒/箱', 'skuPrice': 28000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24525086104, 'skuStock': 40, 'skuSpec': '980g/瓶', 'skuPrice': 1980}, {'skuId': 24525086103, 'skuStock': 3, 'skuSpec': '980g*12瓶/箱', 'skuPrice': 21500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1y80r884ldgdayo', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 10544944758, 'skuStock': 160, 'skuSpec': '400ml*1瓶', 'skuPrice': 990}, {'skuId': 10726221693, 'skuStock': 32, 'skuSpec': '400ml*5瓶', 'skuPrice': 4750}, {'skuId': 10544944759, 'skuStock': 6, 'skuSpec': '400ml*24瓶/箱', 'skuPrice': 22300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 19858077593, 'skuStock': 19, 'skuSpec': '1kg/包', 'skuPrice': 1580}, {'skuId': 19858077594, 'skuStock': 0, 'skuSpec': '1kg*20包/箱', 'skuPrice': 29800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13979834774, 'skuStock': 7, 'skuSpec': '1L*1瓶', 'skuPrice': 4500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2x6y4uijstldqts', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3f4cs6owxvtpqqq', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13802545792, 'skuStock': 0, 'skuSpec': '720ml*1瓶', 'skuPrice': 24800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '27cwu3tuw41oevx', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5169636673, 'skuStock': 0, 'skuSpec': '1L*1瓶', 'skuPrice': 1980}]\n", "Using proxy: ************************************************\n", "[{'skuId': '36856rm6yvlmml2', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nvqgyy86xqamm7', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nt934a841chq22', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37841743, 'skuStock': 22, 'skuSpec': '10-13克/粒*100粒', 'skuPrice': 12500}, {'skuId': 37841744, 'skuStock': 0, 'skuSpec': '13-15克/粒*100粒', 'skuPrice': 14500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1ycxs2767egbimi', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25435681048, 'skuStock': 0, 'skuSpec': '蛋黄液970g*12瓶/箱', 'skuPrice': 36000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37851504, 'skuStock': 465, 'skuSpec': '1L*12盒', 'skuPrice': 58500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37853446, 'skuStock': 6, 'skuSpec': '1L*12盒/箱', 'skuPrice': 69500}, {'skuId': 23540189668, 'skuStock': 2, 'skuSpec': '3箱', 'skuPrice': 205500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 12755646813, 'skuStock': 117, 'skuSpec': '1L*12盒/箱', 'skuPrice': 52000}, {'skuId': 23123497955, 'skuStock': 0, 'skuSpec': '3箱送1包裱花袋', 'skuPrice': 156000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14021536580, 'skuStock': 157, 'skuSpec': '1L*1瓶', 'skuPrice': 3900}, {'skuId': 14021536581, 'skuStock': 26, 'skuSpec': '1L*6瓶/箱', 'skuPrice': 22500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 12753013257, 'skuStock': 258, 'skuSpec': '1KG*1瓶', 'skuPrice': 3500}, {'skuId': 12753013258, 'skuStock': 21, 'skuSpec': '1KG*12盒/箱', 'skuPrice': 36500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nt9rw4xjehzizd', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 6588827544, 'skuStock': 341, 'skuSpec': '1L*1盒', 'skuPrice': 4000}, {'skuId': 6588827543, 'skuStock': 28, 'skuSpec': '1箱', 'skuPrice': 44500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 11652678671, 'skuStock': 1570, 'skuSpec': '1L*1瓶', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 3271640975, 'skuStock': 10, 'skuSpec': '1L*12瓶/箱', 'skuPrice': 43500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3ey9bad8mcxby0f', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37949105, 'skuStock': 65, 'skuSpec': '1L*12盒/箱', 'skuPrice': 45500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23615790227, 'skuStock': 34, 'skuSpec': '700ml*1罐', 'skuPrice': 5900}, {'skuId': 13332543761, 'skuStock': 5, 'skuSpec': '700ml*6罐/箱', 'skuPrice': 35000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14188311729, 'skuStock': 107, 'skuSpec': '500g*1瓶', 'skuPrice': 3700}, {'skuId': 37959896, 'skuStock': 8, 'skuSpec': '500g*12瓶/箱', 'skuPrice': 42000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24981081954, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 33500}, {'skuId': 24981081955, 'skuStock': 0, 'skuSpec': '1L*1盒', 'skuPrice': 3000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9399218880, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 39500}, {'skuId': 23616694638, 'skuStock': 0, 'skuSpec': '1L*1盒', 'skuPrice': 3625}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13736800037, 'skuStock': 0, 'skuSpec': '950ml*12盒/箱', 'skuPrice': 55800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 9630343259, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 49000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24289991876, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 50500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37982175, 'skuStock': 31, 'skuSpec': '1L*12盒/箱', 'skuPrice': 37500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24542293101, 'skuStock': 2411, 'skuSpec': '907g*1瓶', 'skuPrice': 1850}, {'skuId': 22521583859, 'skuStock': 200, 'skuSpec': '907g*12瓶/箱', 'skuPrice': 19900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37954755, 'skuStock': 86, 'skuSpec': '1L*12盒/箱', 'skuPrice': 29800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 17920595830, 'skuStock': 375, 'skuSpec': '907g*1盒', 'skuPrice': 2190}, {'skuId': 17920595831, 'skuStock': 31, 'skuSpec': '907g*12盒/箱', 'skuPrice': 23900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 17920284112, 'skuStock': 264, 'skuSpec': '1kg*1盒', 'skuPrice': 1270}, {'skuId': 17920284113, 'skuStock': 22, 'skuSpec': '1kg*12盒/箱', 'skuPrice': 13900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 8982539164, 'skuStock': 248, 'skuSpec': '907g*1盒', 'skuPrice': 2740}, {'skuId': 8982539165, 'skuStock': 20, 'skuSpec': '907g*12盒/箱', 'skuPrice': 29900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 17919592928, 'skuStock': 72, 'skuSpec': '907g*1盒', 'skuPrice': 1730}, {'skuId': 17919592929, 'skuStock': 6, 'skuSpec': '907g*12盒/箱', 'skuPrice': 18900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 8983128966, 'skuStock': 89, 'skuSpec': '1kg*盒', 'skuPrice': 1730}, {'skuId': 8983128967, 'skuStock': 7, 'skuSpec': '1kg*12盒/箱', 'skuPrice': 18900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24542595736, 'skuStock': 0, 'skuSpec': '400g*4罐', 'skuPrice': 3800}, {'skuId': 19548382980, 'skuStock': 0, 'skuSpec': '400g*6罐', 'skuPrice': 5600}, {'skuId': 19945079286, 'skuStock': 0, 'skuSpec': '400g*48罐/箱', 'skuPrice': 42500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37879126, 'skuStock': 54, 'skuSpec': '白片', 'skuPrice': 6100}, {'skuId': 37879127, 'skuStock': 5, 'skuSpec': '白片', 'skuPrice': 61500}, {'skuId': 708304548, 'skuStock': 145, 'skuSpec': '橙片', 'skuPrice': 7200}, {'skuId': 708304547, 'skuStock': 14, 'skuSpec': '橙片', 'skuPrice': 72000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37848799, 'skuStock': 329, 'skuSpec': '1KG', 'skuPrice': 5200}, {'skuId': 37854200, 'skuStock': 27, 'skuSpec': '1kg*12盒/箱', 'skuPrice': 60500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37837787, 'skuStock': 41, 'skuSpec': '5KG', 'skuPrice': 24000}, {'skuId': 37972788, 'skuStock': 10, 'skuSpec': '5KG*4包/箱', 'skuPrice': 95000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37845733, 'skuStock': 22, 'skuSpec': '2KG*1袋', 'skuPrice': 9900}, {'skuId': 37845734, 'skuStock': 3, 'skuSpec': '2KG*6袋/箱', 'skuPrice': 56300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 6344597651, 'skuStock': 1290, 'skuSpec': '500g/盒', 'skuPrice': 5200}, {'skuId': 23665985943, 'skuStock': 107, 'skuSpec': '2箱', 'skuPrice': 57000}, {'skuId': 6344597652, 'skuStock': 215, 'skuSpec': '500g/盒*6/箱', 'skuPrice': 29000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 3906133072, 'skuStock': 403, 'skuSpec': '1KG*1盒', 'skuPrice': 9300}, {'skuId': 3906133073, 'skuStock': 134, 'skuSpec': '1KG*3盒', 'skuPrice': 27600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 7381015512, 'skuStock': 180, 'skuSpec': '960g*1包', 'skuPrice': 5730}, {'skuId': 7381015511, 'skuStock': 15, 'skuSpec': '960g*12包/箱', 'skuPrice': 66500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 10144494861, 'skuStock': 58, 'skuSpec': '3瓶', 'skuPrice': 5800}, {'skuId': 10144494862, 'skuStock': 7, 'skuSpec': '24瓶/箱', 'skuPrice': 45200}, {'skuId': 10560287308, 'skuStock': 176, 'skuSpec': '1瓶', 'skuPrice': 1980}]\n", "Using proxy: *************************************************\n", "[{'skuId': 7003212052, 'skuStock': 75, 'skuSpec': '3kg/包', 'skuPrice': 13600}, {'skuId': 7003212053, 'skuStock': 18, 'skuSpec': '3kg*4包/箱', 'skuPrice': 52800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14647338740, 'skuStock': 5, 'skuSpec': '黄片984g/袋', 'skuPrice': 5800}, {'skuId': 14647338741, 'skuStock': 0, 'skuSpec': '黄片984g*8袋/箱', 'skuPrice': 43100}, {'skuId': 25444476868, 'skuStock': 8, 'skuSpec': '白片984g/袋', 'skuPrice': 5600}, {'skuId': 25444476867, 'skuStock': 1, 'skuSpec': '白片984g*8袋/箱', 'skuPrice': 42200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37866448, 'skuStock': 9, 'skuSpec': '2KG*5盒', 'skuPrice': 34000}, {'skuId': 10568689696, 'skuStock': 47, 'skuSpec': '2KG*1盒', 'skuPrice': 7000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14465704483, 'skuStock': 44, 'skuSpec': '小奶酪球150g(15个球)', 'skuPrice': 2280}, {'skuId': 14465720932, 'skuStock': 0, 'skuSpec': '小奶酪球150g(15个球)', 'skuPrice': 99800}, {'skuId': 14465704484, 'skuStock': 21, 'skuSpec': '中奶酪球100g(2个球)', 'skuPrice': 1500}, {'skuId': 14465720933, 'skuStock': 1, 'skuSpec': '中奶酪球100g(2个球)', 'skuPrice': 29800}, {'skuId': 14465704485, 'skuStock': 10, 'skuSpec': '大奶酪球250g(1个大球)', 'skuPrice': 2880}, {'skuId': 14465720934, 'skuStock': 0, 'skuSpec': '大奶酪球250g(1个大球)', 'skuPrice': 104000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37842883, 'skuStock': 25, 'skuSpec': '20KG', 'skuPrice': 82000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9803042120, 'skuStock': 85, 'skuSpec': '1.8kg*1盒', 'skuPrice': 9000}, {'skuId': 9803042121, 'skuStock': 28, 'skuSpec': '1.8kg*3盒/箱', 'skuPrice': 25800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9803428994, 'skuStock': 174, 'skuSpec': '2KG*1袋', 'skuPrice': 10300}, {'skuId': 9803428995, 'skuStock': 29, 'skuSpec': '2KG*6袋/箱', 'skuPrice': 60000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14465243941, 'skuStock': 28, 'skuSpec': '1盒', 'skuPrice': 2480}, {'skuId': 14465243942, 'skuStock': 2, 'skuSpec': '12盒/箱', 'skuPrice': 28500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24783989424, 'skuStock': 28, 'skuSpec': '3瓶', 'skuPrice': 2920}, {'skuId': 24783989425, 'skuStock': 3, 'skuSpec': '24瓶/箱', 'skuPrice': 21500}, {'skuId': 24783989426, 'skuStock': 84, 'skuSpec': '1瓶', 'skuPrice': 990}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14457117615, 'skuStock': 9, 'skuSpec': '1KG/包', 'skuPrice': 10700}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37865267, 'skuStock': 1, 'skuSpec': '10KG*2块/箱', 'skuPrice': 87000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14456941058, 'skuStock': 7, 'skuSpec': '1盒', 'skuPrice': 1920}, {'skuId': 14456941059, 'skuStock': 1, 'skuSpec': '5盒', 'skuPrice': 9100}, {'skuId': 14456941060, 'skuStock': 0, 'skuSpec': '12盒/箱', 'skuPrice': 19800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25086286014, 'skuStock': 1, 'skuSpec': '1块', 'skuPrice': 45500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 11761885869, 'skuStock': 12, 'skuSpec': '10kg*1箱', 'skuPrice': 84500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14466146574, 'skuStock': 24, 'skuSpec': '1块', 'skuPrice': 2580}, {'skuId': 14466146575, 'skuStock': 2, 'skuSpec': '10块', 'skuPrice': 24800}, {'skuId': 14466146576, 'skuStock': 0, 'skuSpec': '200g*25块/箱', 'skuPrice': 59500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25443689401, 'skuStock': 0, 'skuSpec': '3kg/袋', 'skuPrice': 12850}, {'skuId': 25443689402, 'skuStock': 0, 'skuSpec': '3KG*4袋/箱', 'skuPrice': 51200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37845790, 'skuStock': 19, 'skuSpec': '25KG', 'skuPrice': 154000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37870121, 'skuStock': 107, 'skuSpec': '5KG*1包', 'skuPrice': 33000}, {'skuId': 37870122, 'skuStock': 26, 'skuSpec': '5KG*4包/箱', 'skuPrice': 129500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 8715823322, 'skuStock': 351, 'skuSpec': '1kg*1片', 'skuPrice': 12000}, {'skuId': 37877296, 'skuStock': 35, 'skuSpec': '1KG*10片/箱', 'skuPrice': 110000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24812885814, 'skuStock': 141, 'skuSpec': '5KG*1包', 'skuPrice': 18900}, {'skuId': 24812885815, 'skuStock': 35, 'skuSpec': '5KG*4包/箱', 'skuPrice': 72600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14191746620, 'skuStock': 48, 'skuSpec': '1KG*1片', 'skuPrice': 9600}, {'skuId': 37840693, 'skuStock': 4, 'skuSpec': '1KG*10片/箱', 'skuPrice': 78000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 2443117615, 'skuStock': 12, 'skuSpec': '227g*40块/箱', 'skuPrice': 68800}, {'skuId': 4887876221, 'skuStock': 48, 'skuSpec': '227g*10块/组', 'skuPrice': 18500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 19206181870, 'skuStock': 22, 'skuSpec': '2kg*1片', 'skuPrice': 28500}, {'skuId': 19206181871, 'skuStock': 4, 'skuSpec': '2kg*5片/箱', 'skuPrice': 138000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 8350081834, 'skuStock': 14, 'skuSpec': '1KG*20片/箱', 'skuPrice': 142800}, {'skuId': 8350081835, 'skuStock': 293, 'skuSpec': '1KG/片', 'skuPrice': 7800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14595646432, 'skuStock': 5, 'skuSpec': '10kg/箱', 'skuPrice': 73500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13801216361, 'skuStock': 159, 'skuSpec': '454g*1块', 'skuPrice': 2750}, {'skuId': 13801216362, 'skuStock': 7, 'skuSpec': '454g*20块/箱', 'skuPrice': 44500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5035349508, 'skuStock': 10, 'skuSpec': '10kg/箱', 'skuPrice': 105000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nt9lq9rzmsm6q4', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14184441881, 'skuStock': 42, 'skuSpec': '2kg/片', 'skuPrice': 19800}, {'skuId': 14184441882, 'skuStock': 8, 'skuSpec': '2kg*5片/箱', 'skuPrice': 78000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37858617, 'skuStock': 1, 'skuSpec': '7g*288粒', 'skuPrice': 18800}, {'skuId': 8416012550, 'skuStock': 14, 'skuSpec': '7g*36粒', 'skuPrice': 2880}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37825427, 'skuStock': 0, 'skuSpec': '25KG', 'skuPrice': 165000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 8715919309, 'skuStock': 0, 'skuSpec': '1kg*1片', 'skuPrice': 13500}, {'skuId': 37866507, 'skuStock': 0, 'skuSpec': '1KG*10片/箱', 'skuPrice': 125000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23813680619, 'skuStock': 0, 'skuSpec': '250g*1块', 'skuPrice': 3300}, {'skuId': 13546532492, 'skuStock': 0, 'skuSpec': '250g*3块', 'skuPrice': 9600}, {'skuId': 13546532491, 'skuStock': 0, 'skuSpec': '250g*20块/箱', 'skuPrice': 59500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 10012094397, 'skuStock': 0, 'skuSpec': '250g*20条/箱', 'skuPrice': 59500}, {'skuId': 10012094398, 'skuStock': 0, 'skuSpec': '250g*3条', 'skuPrice': 9600}, {'skuId': 10012094399, 'skuStock': 0, 'skuSpec': '250g*1条', 'skuPrice': 3300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13802911388, 'skuStock': 0, 'skuSpec': '250g*1卷', 'skuPrice': 3800}, {'skuId': 13802911389, 'skuStock': 0, 'skuSpec': '250g*3卷', 'skuPrice': 9800}, {'skuId': 13802911390, 'skuStock': 0, 'skuSpec': '250g*20卷/箱', 'skuPrice': 59500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14657745123, 'skuStock': 0, 'skuSpec': '25KG', 'skuPrice': 87500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 11603287650, 'skuStock': 0, 'skuSpec': '24卷/箱', 'skuPrice': 75500}, {'skuId': 11603287651, 'skuStock': 0, 'skuSpec': '250g*3卷', 'skuPrice': 9600}, {'skuId': 12410893881, 'skuStock': 0, 'skuSpec': '250g*1卷', 'skuPrice': 3300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5576205627, 'skuStock': 0, 'skuSpec': '2kg*1片', 'skuPrice': 21500}, {'skuId': 5576205628, 'skuStock': 0, 'skuSpec': '2kg*5片/箱', 'skuPrice': 98500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13857543004, 'skuStock': 0, 'skuSpec': '2kg*5片/箱', 'skuPrice': 85500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '27ah6wmg7cb1apf', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37997496, 'skuStock': 1000, 'skuSpec': '950ml*12盒/箱', 'skuPrice': 16800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5619723627, 'skuStock': 8, 'skuSpec': '950ml*12瓶/箱', 'skuPrice': 19200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13709045654, 'skuStock': 5, 'skuSpec': '950ml*12盒/箱', 'skuPrice': 16800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25273092138, 'skuStock': 43, 'skuSpec': '125ml*9盒/箱', 'skuPrice': 2850}, {'skuId': 25273092139, 'skuStock': 4, 'skuSpec': '10箱', 'skuPrice': 27200}, {'skuId': 25439478129, 'skuStock': 21, 'skuSpec': '2箱', 'skuPrice': 5900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24868290179, 'skuStock': 680, 'skuSpec': '1L*12盒/箱', 'skuPrice': 7400}, {'skuId': 24868290180, 'skuStock': 136, 'skuSpec': '5箱', 'skuPrice': 36000}, {'skuId': 24868290181, 'skuStock': 68, 'skuSpec': '10箱', 'skuPrice': 68000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13487146659, 'skuStock': 305, 'skuSpec': '1L*12盒/箱', 'skuPrice': 8600}, {'skuId': 13782529537, 'skuStock': 61, 'skuSpec': '5箱', 'skuPrice': 42000}, {'skuId': 13782529538, 'skuStock': 30, 'skuSpec': '10箱', 'skuPrice': 82000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24515381610, 'skuStock': 31, 'skuSpec': '950ml*2盒', 'skuPrice': 3600}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oi2iji1l97y6dw', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14560906565, 'skuStock': 0, 'skuSpec': '200ml*12盒/箱', 'skuPrice': 5350}, {'skuId': 14560906566, 'skuStock': 0, 'skuSpec': '10箱', 'skuPrice': 51800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '26wxkmkt9devime', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37871443, 'skuStock': 52, 'skuSpec': '25KG', 'skuPrice': 75000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14343506384, 'skuStock': 115, 'skuSpec': '500g*1包', 'skuPrice': 3500}, {'skuId': 14343506385, 'skuStock': 4, 'skuSpec': '500g*24包/箱', 'skuPrice': 78200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14476716162, 'skuStock': 22, 'skuSpec': '10kg/箱', 'skuPrice': 21000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 11422475897, 'skuStock': 128, 'skuSpec': '520g/瓶', 'skuPrice': 4800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5076525583, 'skuStock': 12, 'skuSpec': '10L/桶', 'skuPrice': 13800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5076537814, 'skuStock': 7, 'skuSpec': '10L/桶', 'skuPrice': 16500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37967758, 'skuStock': 7, 'skuSpec': '10kg/箱', 'skuPrice': 22900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37960099, 'skuStock': 2, 'skuSpec': '10kg/箱', 'skuPrice': 22950}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37954570, 'skuStock': 25, 'skuSpec': '10kg/箱', 'skuPrice': 26300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13985525510, 'skuStock': 3, 'skuSpec': '20kg/桶', 'skuPrice': 40500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '361z4bwg29sbysc', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 38006505, 'skuStock': 3, 'skuSpec': '16kg/桶', 'skuPrice': 32600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37938859, 'skuStock': 2, 'skuSpec': '1kg*10片/箱', 'skuPrice': 23500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14650814405, 'skuStock': 28, 'skuSpec': '白牛皮不防粘型 500张/包', 'skuPrice': 13800}, {'skuId': 14650814406, 'skuStock': 14, 'skuSpec': '白牛皮不防粘型 500张*2包/箱', 'skuPrice': 27000}, {'skuId': 14650814407, 'skuStock': 20, 'skuSpec': '防油纸防粘型 500张/包', 'skuPrice': 14800}, {'skuId': 14650814408, 'skuStock': 10, 'skuSpec': '防油纸防粘型 500张*2包/箱', 'skuPrice': 29000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 8344049992, 'skuStock': 227, 'skuSpec': '大号(23*35cm)', 'skuPrice': 500}, {'skuId': 8344049991, 'skuStock': 0, 'skuSpec': '中号(20*31cm)', 'skuPrice': 420}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14608628683, 'skuStock': 57, 'skuSpec': '方形卷口杯白色87*87*35mm 50个', 'skuPrice': 880}, {'skuId': 14608628684, 'skuStock': 59, 'skuSpec': '方形卷口杯本色87*87*35mm 50个', 'skuPrice': 880}, {'skuId': 14608628685, 'skuStock': 60, 'skuSpec': '方形卷口杯绿色87*87*35mm 50个', 'skuPrice': 880}, {'skuId': 14608628686, 'skuStock': 58, 'skuSpec': '长方形卷口杯白色82*40*42mm 50个', 'skuPrice': 780}, {'skuId': 14608628687, 'skuStock': 57, 'skuSpec': '长方形卷口杯本色82*40*42mm 50个', 'skuPrice': 780}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13258710515, 'skuStock': 15, 'skuSpec': '4寸双层17*17*20  10个', 'skuPrice': 3380}, {'skuId': 13258710513, 'skuStock': 7, 'skuSpec': '4寸三层17*17*28  10个', 'skuPrice': 4390}, {'skuId': 13258710514, 'skuStock': 17, 'skuSpec': '6寸双层21.5*21.5*24  10个', 'skuPrice': 4450}, {'skuId': 13258710516, 'skuStock': 4, 'skuSpec': '6寸三层21.5*21.5*30  10个', 'skuPrice': 5350}, {'skuId': 13258710517, 'skuStock': 15, 'skuSpec': '8寸双层26*26*25  10个', 'skuPrice': 5350}, {'skuId': 13258710518, 'skuStock': 1, 'skuSpec': '8寸三层26*26*31  10个', 'skuPrice': 6360}, {'skuId': 14653616962, 'skuStock': 10, 'skuSpec': '8寸单层26*26*18 10个', 'skuPrice': 4200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14600300860, 'skuStock': 19, 'skuSpec': '750ml(115*90*65mm) 50个', 'skuPrice': 1560}, {'skuId': 14600300861, 'skuStock': 40, 'skuSpec': '1080ml（155*120*47mm）50个', 'skuPrice': 1830}, {'skuId': 14600300862, 'skuStock': 5, 'skuSpec': '1400ml（155*120*65mm）50个', 'skuPrice': 2090}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25348589435, 'skuStock': 10, 'skuSpec': 'BURGER黄色 500张/包', 'skuPrice': 3180}, {'skuId': 25348589436, 'skuStock': 9, 'skuSpec': 'BURGER红色 500张/包', 'skuPrice': 3180}, {'skuId': 25348589437, 'skuStock': 10, 'skuSpec': 'BURGER绿色 500张/包', 'skuPrice': 3180}, {'skuId': 25348589438, 'skuStock': 9, 'skuSpec': 'I LOVE IT 500张/包', 'skuPrice': 3180}, {'skuId': 25348589439, 'skuStock': 10, 'skuSpec': '美味驾到 500张/包', 'skuPrice': 3180}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25365883700, 'skuStock': 76, 'skuSpec': '50*30mm 500个', 'skuPrice': 790}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25426981797, 'skuStock': 57, 'skuSpec': '白底彩心中号63*55mm 50个', 'skuPrice': 380}, {'skuId': 25426981798, 'skuStock': 96, 'skuSpec': '小号粉底白兔50*45mm 50个', 'skuPrice': 380}, {'skuId': 25426981799, 'skuStock': 96, 'skuSpec': '小号蓝底白点50*45mm 50个', 'skuPrice': 380}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14605749839, 'skuStock': 11, 'skuSpec': '150牛皮纸碗+盖 50个', 'skuPrice': 3350}, {'skuId': 14605749840, 'skuStock': 20, 'skuSpec': '115牛皮纸碗+盖 50个', 'skuPrice': 2450}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25366684611, 'skuStock': 2, 'skuSpec': '750ml白卡方碗+低盖 50个', 'skuPrice': 3450}, {'skuId': 25471283487, 'skuStock': 5, 'skuSpec': '750ml白卡方碗+高盖 50个', 'skuPrice': 3450}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14607716403, 'skuStock': 16, 'skuSpec': '4寸纸碗+盖 50个', 'skuPrice': 2580}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14606107247, 'skuStock': 11, 'skuSpec': '120*55mm 100个', 'skuPrice': 2550}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14607526246, 'skuStock': 10, 'skuSpec': '90*80*40mm 100个', 'skuPrice': 1480}, {'skuId': 14607526247, 'skuStock': 14, 'skuSpec': '80*32.5mm 100个', 'skuPrice': 1120}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25363196866, 'skuStock': 48, 'skuSpec': '24*20mm 1000个', 'skuPrice': 840}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25362894824, 'skuStock': 17, 'skuSpec': '47*37mm 600个', 'skuPrice': 2640}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14608112574, 'skuStock': 31, 'skuSpec': '140*50*30mm 240个', 'skuPrice': 1150}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37987028, 'skuStock': 108, 'skuSpec': '25KG', 'skuPrice': 31500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37966642, 'skuStock': 16, 'skuSpec': '20KG/袋', 'skuPrice': 52900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37947721, 'skuStock': 6, 'skuSpec': '25KG', 'skuPrice': 32800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37951701, 'skuStock': 54, 'skuSpec': '25KG', 'skuPrice': 33900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37943734, 'skuStock': 0, 'skuSpec': '20KG/包', 'skuPrice': 49800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2g0k3x9gaxj7imu', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fo90jepu3wji2b', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37887674, 'skuStock': 23, 'skuSpec': '25kg/包（纸袋）', 'skuPrice': 16500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37880401, 'skuStock': 12, 'skuSpec': '25kg/包（纸袋）', 'skuPrice': 28000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37842869, 'skuStock': 18, 'skuSpec': '25kg/包（编织袋）', 'skuPrice': 13300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37843119, 'skuStock': 17, 'skuSpec': '25kg/包（编织袋）', 'skuPrice': 14300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 3392440528, 'skuStock': 66, 'skuSpec': '25kg/袋', 'skuPrice': 15000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 769439394, 'skuStock': 19, 'skuSpec': '25KG*1包', 'skuPrice': 17200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 769636446, 'skuStock': 23, 'skuSpec': '25KG*1包', 'skuPrice': 16200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 6796883040, 'skuStock': 16, 'skuSpec': '25kg/袋', 'skuPrice': 17800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '36btvdj0pbbsevd', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2ot59veb22cimsw', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '270nh3usn03lqa3', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1yllr6dw8dj0uj3', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1yhx286q4vbtar3', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24843394508, 'skuStock': 0, 'skuSpec': '25kg/袋', 'skuPrice': 28000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2flra62fzz9hqkt', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2omyibmyr4v2mzf', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2g0k3oh9feipanp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '36833ox0t973ij0', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xloctk9yvcxq4t', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nocx82w31m6mjg', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fvnf9s1fws32qc', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1y359wsz6fei6mn', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2g0kg3ov5tjnysk', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '360pwra9uanweah', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1ykd27qevqm72r1', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fo9p48ztztni9v', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '35zgp1fawgv6mgp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2omy5z0guyqpaef', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2okjjy0j8y9nypp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23558985778, 'skuStock': 0, 'skuSpec': '25KG', 'skuPrice': 86000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37839537, 'skuStock': 0, 'skuSpec': '5KG*1袋', 'skuPrice': 17400}, {'skuId': 5705046076, 'skuStock': 0, 'skuSpec': '1KG/包', 'skuPrice': 3300}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nn36rtafoi9qc7', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2ft5va52d5gsepb', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 7688598064, 'skuStock': 53, 'skuSpec': '1袋', 'skuPrice': 850}, {'skuId': 7688598065, 'skuStock': 10, 'skuSpec': '5袋', 'skuPrice': 3890}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37966776, 'skuStock': 5, 'skuSpec': '25kg/袋', 'skuPrice': 11800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37942720, 'skuStock': 5, 'skuSpec': '2.5kg/袋', 'skuPrice': 2800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24887780213, 'skuStock': 71, 'skuSpec': '北海道恋人(坚果塔)', 'skuPrice': 1000}, {'skuId': 24887780214, 'skuStock': 7, 'skuSpec': '北海道恋人(坚果塔)', 'skuPrice': 9500}, {'skuId': 24887780215, 'skuStock': 93, 'skuSpec': '印第安之舟', 'skuPrice': 1100}, {'skuId': 24887780216, 'skuStock': 9, 'skuSpec': '印第安之舟', 'skuPrice': 10500}, {'skuId': 24887780219, 'skuStock': 49, 'skuSpec': '无蔗糖匠心手作酥饼', 'skuPrice': 900}, {'skuId': 24887780220, 'skuStock': 4, 'skuSpec': '无蔗糖匠心手作酥饼', 'skuPrice': 8500}, {'skuId': 24887780221, 'skuStock': 0, 'skuSpec': '莓你不行', 'skuPrice': 900}, {'skuId': 24887780222, 'skuStock': 0, 'skuSpec': '莓你不行', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 721790103, 'skuStock': 12, 'skuSpec': '40个*10袋/箱', 'skuPrice': 15800}, {'skuId': 11071297522, 'skuStock': 125, 'skuSpec': '40个*1袋', 'skuPrice': 1980}]\n", "Using proxy: ************************************************\n", "[{'skuId': 11471595286, 'skuStock': 316, 'skuSpec': '10片/包', 'skuPrice': 1580}, {'skuId': 11471595287, 'skuStock': 22, 'skuSpec': '10片*14包/箱', 'skuPrice': 18800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14602775947, 'skuStock': 62, 'skuSpec': '25g*40个*1包', 'skuPrice': 2850}, {'skuId': 14602775948, 'skuStock': 7, 'skuSpec': '25g*8包*1箱', 'skuPrice': 18000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nn42677m5ecunh', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9696144800, 'skuStock': 180, 'skuSpec': '原味50g速冻甜甜圈', 'skuPrice': 3200}, {'skuId': 9696144802, 'skuStock': 30, 'skuSpec': '原味50g速冻甜甜圈', 'skuPrice': 12960}, {'skuId': 23678290236, 'skuStock': 0, 'skuSpec': '原味50g速冻甜甜圈', 'skuPrice': 12960}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23762495602, 'skuStock': 18, 'skuSpec': '原味', 'skuPrice': 17500}, {'skuId': 23762495603, 'skuStock': 5, 'skuSpec': '草莓味', 'skuPrice': 18500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37836223, 'skuStock': 9, 'skuSpec': '11袋/箱（660个）（3*6cm）', 'skuPrice': 15950}, {'skuId': 11422690956, 'skuStock': 99, 'skuSpec': '60个/袋（3*6cm）', 'skuPrice': 1780}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25436182316, 'skuStock': 2, 'skuSpec': '30g*294个/箱', 'skuPrice': 27500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 1539390183, 'skuStock': 14, 'skuSpec': '紫薯味 6袋/箱（120个）', 'skuPrice': 13850}, {'skuId': 9878088664, 'skuStock': 87, 'skuSpec': '20个/袋', 'skuPrice': 2450}]\n", "Using proxy: ************************************************\n", "[{'skuId': 549523496, 'skuStock': 2, 'skuSpec': '5个*48盒/箱', 'skuPrice': 42600}, {'skuId': 549523497, 'skuStock': 101, 'skuSpec': '5个/盒', 'skuPrice': 900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 431847654, 'skuStock': 5, 'skuSpec': '30个*10袋/箱', 'skuPrice': 20680}, {'skuId': 11071594576, 'skuStock': 56, 'skuSpec': '30个/袋', 'skuPrice': 2200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25436281838, 'skuStock': 3, 'skuSpec': '9kg/箱', 'skuPrice': 22800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 845496418, 'skuStock': 5, 'skuSpec': '咸香芝士味', 'skuPrice': 21680}, {'skuId': 845496419, 'skuStock': 0, 'skuSpec': '蛋奶味', 'skuPrice': 19900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7314105002, 'skuStock': 120, 'skuSpec': '6片*1包', 'skuPrice': 1150}, {'skuId': 7314105003, 'skuStock': 12, 'skuSpec': '6片*10包*1箱', 'skuPrice': 8700}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37851319, 'skuStock': 8, 'skuSpec': '10袋/箱（50个）（10.5*7cm）', 'skuPrice': 22800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25345891674, 'skuStock': 2, 'skuSpec': '240个/箱', 'skuPrice': 23900}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xgs6xc31bx4uc4', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9695936184, 'skuStock': 2, 'skuSpec': '草莓味50g速冻甜甜圈', 'skuPrice': 13800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1ykcdceaio4ym9w', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 6788188742, 'skuStock': 8, 'skuSpec': '150只*45g/箱', 'skuPrice': 18800}, {'skuId': 6795791246, 'skuStock': 89, 'skuSpec': '15只*45g/包', 'skuPrice': 2500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25345882592, 'skuStock': 2, 'skuSpec': '228个/箱', 'skuPrice': 28800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 722176359, 'skuStock': 6, 'skuSpec': '速冻老婆饼（20个*13层/箱）', 'skuPrice': 17500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3ngyc5psrob6m7w', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3evsmkplzawemxw', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37877501, 'skuStock': 4, 'skuSpec': '速冻榴莲酥', 'skuPrice': 27600}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xeaafu6wrb5q8b', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '26zdqulzll12mg6', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '276so7t2vr89qib', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37844783, 'skuStock': 13, 'skuSpec': '10袋/箱（10kg）', 'skuPrice': 24000}, {'skuId': 4802334580, 'skuStock': 136, 'skuSpec': '1kg/包', 'skuPrice': 2880}]\n", "Using proxy: *************************************************\n", "[{'skuId': 9748975997, 'skuStock': 172, 'skuSpec': '1kg/包', 'skuPrice': 3720}, {'skuId': 9748975998, 'skuStock': 14, 'skuSpec': '1kg*12包/箱', 'skuPrice': 43500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37845370, 'skuStock': 11, 'skuSpec': '10袋/箱（10kg）', 'skuPrice': 35800}, {'skuId': 10011398529, 'skuStock': 113, 'skuSpec': '1kg/包', 'skuPrice': 3880}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37987769, 'skuStock': 8, 'skuSpec': '10袋/箱（10kg）', 'skuPrice': 20800}, {'skuId': 4802645962, 'skuStock': 84, 'skuSpec': '1kg/包', 'skuPrice': 2500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fzbxh2ren67y3n', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37871157, 'skuStock': 5, 'skuSpec': '12袋/箱（10kg）', 'skuPrice': 43800}, {'skuId': 9619793162, 'skuStock': 60, 'skuSpec': '1kg/包', 'skuPrice': 3900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 11368583763, 'skuStock': 79, 'skuSpec': '1.2kg*1包', 'skuPrice': 4950}, {'skuId': 11368583764, 'skuStock': 7, 'skuSpec': '1.2kg*10包/箱', 'skuPrice': 43900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37845890, 'skuStock': 5, 'skuSpec': '8袋/箱（10.4kg）', 'skuPrice': 28000}, {'skuId': 4802918872, 'skuStock': 45, 'skuSpec': '1.3kg/袋', 'skuPrice': 3800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 9618180381, 'skuStock': 47, 'skuSpec': '1kg/袋', 'skuPrice': 3880}, {'skuId': 9618180382, 'skuStock': 3, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 38800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9618694683, 'skuStock': 33, 'skuSpec': '1kg/包', 'skuPrice': 3060}, {'skuId': 9618694684, 'skuStock': 3, 'skuSpec': '1kg*10包/箱', 'skuPrice': 28800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9617796706, 'skuStock': 102, 'skuSpec': '1kg/袋', 'skuPrice': 3980}, {'skuId': 9617796707, 'skuStock': 8, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 44600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37859313, 'skuStock': 9, 'skuSpec': '12袋/箱（12kg）', 'skuPrice': 43800}, {'skuId': 4802420793, 'skuStock': 116, 'skuSpec': '1kg/包', 'skuPrice': 3880}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37834481, 'skuStock': 5, 'skuSpec': '4袋/箱（8.8kg）', 'skuPrice': 18800}, {'skuId': 5035404669, 'skuStock': 22, 'skuSpec': '2.2kg/袋', 'skuPrice': 5100}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5076516841, 'skuStock': 104, 'skuSpec': '1.3kg/袋', 'skuPrice': 4800}, {'skuId': 5076516842, 'skuStock': 13, 'skuSpec': '1.3kg*8袋/箱', 'skuPrice': 36800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37882309, 'skuStock': 6, 'skuSpec': '30g/根', 'skuPrice': 35800}, {'skuId': 37882308, 'skuStock': 66, 'skuSpec': '30g/根', 'skuPrice': 3880}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9617177297, 'skuStock': 8, 'skuSpec': '2.4kg（50片）/包', 'skuPrice': 4860}, {'skuId': 9617177298, 'skuStock': 2, 'skuSpec': '2.4kg（50片）/包*4/箱', 'skuPrice': 18500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14308975039, 'skuStock': 24, 'skuSpec': '1.25kg/包', 'skuPrice': 2450}, {'skuId': 14308975040, 'skuStock': 3, 'skuSpec': '1.25kg*8包/箱', 'skuPrice': 16850}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5807645929, 'skuStock': 10, 'skuSpec': '1kg/包', 'skuPrice': 12300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37844306, 'skuStock': 1, 'skuSpec': '8袋/箱（8kg）', 'skuPrice': 13500}, {'skuId': 11713493018, 'skuStock': 12, 'skuSpec': '1kg*1袋', 'skuPrice': 1950}]\n", "Using proxy: ************************************************\n", "[{'skuId': 15419281593, 'skuStock': 36, 'skuSpec': '1kg*1包', 'skuPrice': 3950}, {'skuId': 15419281594, 'skuStock': 3, 'skuSpec': '1kg*12包/箱', 'skuPrice': 43750}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13488512843, 'skuStock': 16, 'skuSpec': '2KG*1包', 'skuPrice': 6800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24523877311, 'skuStock': 15, 'skuSpec': '1kg*1袋', 'skuPrice': 2950}, {'skuId': 24523877312, 'skuStock': 1, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 27500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13661349097, 'skuStock': 13, 'skuSpec': '1KG*1包', 'skuPrice': 6600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462430152, 'skuStock': 23, 'skuSpec': '1KG*1包', 'skuPrice': 4500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24523794699, 'skuStock': 10, 'skuSpec': '1kg*1袋', 'skuPrice': 2400}, {'skuId': 24523875500, 'skuStock': 1, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 22000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13773112942, 'skuStock': 14, 'skuSpec': '1KG*1袋', 'skuPrice': 2200}, {'skuId': 13773112943, 'skuStock': 1, 'skuSpec': '1KG*10袋/箱', 'skuPrice': 21000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14174121911, 'skuStock': 0, 'skuSpec': '草莓味(你的眼睛像星星) 1盒', 'skuPrice': 1500}, {'skuId': 14174121912, 'skuStock': 0, 'skuSpec': '草莓味(我曾遇到一束光) 1盒', 'skuPrice': 1500}, {'skuId': 14174121913, 'skuStock': 0, 'skuSpec': '蜜桃味 1盒', 'skuPrice': 1500}, {'skuId': 14174121914, 'skuStock': 0, 'skuSpec': '哈密瓜味 1盒', 'skuPrice': 1500}, {'skuId': 14174121915, 'skuStock': 8, 'skuSpec': '葡萄味 1盒', 'skuPrice': 1500}, {'skuId': 14174121916, 'skuStock': 13, 'skuSpec': '香橙味 1盒', 'skuPrice': 1500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13709632998, 'skuStock': 1000, 'skuSpec': '500g/袋', 'skuPrice': 4900}, {'skuId': 14160904546, 'skuStock': 1000, 'skuSpec': '500g*2袋', 'skuPrice': 9000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 38003975, 'skuStock': 167, 'skuSpec': '30kg/袋', 'skuPrice': 27500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 7175732460, 'skuStock': 0, 'skuSpec': '香草风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732463, 'skuStock': 0, 'skuSpec': '榛果风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732464, 'skuStock': 0, 'skuSpec': '焦糖风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732458, 'skuStock': 0, 'skuSpec': '绿薄荷风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732459, 'skuStock': 4, 'skuSpec': '桂花风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732461, 'skuStock': 6, 'skuSpec': '莫西多薄荷风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 24239677493, 'skuStock': 6, 'skuSpec': '山茶花风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732462, 'skuStock': 7, 'skuSpec': '纯蔗糖风味糖浆 1000ml*1瓶', 'skuPrice': 6900}, {'skuId': 16968894232, 'skuStock': 6, 'skuSpec': '焦糖风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 9011230833, 'skuStock': 3, 'skuSpec': '香草风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 9011230834, 'skuStock': 1, 'skuSpec': '绿薄荷风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 22643588819, 'skuStock': 3, 'skuSpec': '榛果风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 25427996162, 'skuStock': 6, 'skuSpec': '太妃果风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 14648631254, 'skuStock': 5, 'skuSpec': '海盐焦糖风味糖浆 1L装', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23942887789, 'skuStock': 16, 'skuSpec': '700g*1瓶', 'skuPrice': 1450}, {'skuId': 23942887790, 'skuStock': 24, 'skuSpec': '1.2kg*1瓶', 'skuPrice': 2380}]\n", "Using proxy: *************************************************\n", "[{'skuId': 11363981625, 'skuStock': 0, 'skuSpec': '500g*1瓶', 'skuPrice': 1980}, {'skuId': 11363981626, 'skuStock': 37, 'skuSpec': '1000g*1瓶', 'skuPrice': 3490}, {'skuId': 23511191651, 'skuStock': 21, 'skuSpec': '2kg*1瓶', 'skuPrice': 5800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37960468, 'skuStock': 23, 'skuSpec': '13.62kg/桶', 'skuPrice': 22800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37866900, 'skuStock': 22, 'skuSpec': '1KG', 'skuPrice': 6800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3eta0y9t7wka6nb', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3ezfeqhprywryfr', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '36e9imggrtktqej', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7164515271, 'skuStock': 20, 'skuSpec': '1.26kg*1盒', 'skuPrice': 1680}, {'skuId': 7164515270, 'skuStock': 1, 'skuSpec': '1.26kg*12盒/箱', 'skuPrice': 19200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14145135838, 'skuStock': 3, 'skuSpec': '150ml*1瓶', 'skuPrice': 3800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1yfgwfvp19i72ho', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2798ufvhy0a7iv3', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3ex15885gntlqf5', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37950156, 'skuStock': 70, 'skuSpec': '3kg/袋', 'skuPrice': 28800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5069706525, 'skuStock': 19, 'skuSpec': '阿方索芒果', 'skuPrice': 7600}, {'skuId': 5038541239, 'skuStock': 36, 'skuSpec': '草莓', 'skuPrice': 7550}, {'skuId': 10153199559, 'skuStock': 57, 'skuSpec': '树莓/覆盆子', 'skuPrice': 8350}, {'skuId': 13660410533, 'skuStock': 29, 'skuSpec': '芒果', 'skuPrice': 6200}, {'skuId': 14159834417, 'skuStock': 4, 'skuSpec': '野生蓝莓', 'skuPrice': 11900}, {'skuId': 24538087311, 'skuStock': 5, 'skuSpec': '西番莲', 'skuPrice': 9200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9664199082, 'skuStock': 7, 'skuSpec': '芒果', 'skuPrice': 12000}, {'skuId': 9701178017, 'skuStock': 12, 'skuSpec': '草莓', 'skuPrice': 10300}, {'skuId': 9731590146, 'skuStock': 9, 'skuSpec': '覆盆子/红莓/桑子/木莓', 'skuPrice': 15800}, {'skuId': 9701178024, 'skuStock': 4, 'skuSpec': '白桃', 'skuPrice': 12200}, {'skuId': 9701178020, 'skuStock': 9, 'skuSpec': '百香果', 'skuPrice': 11500}, {'skuId': 23584882198, 'skuStock': 10, 'skuSpec': '凤梨', 'skuPrice': 10300}, {'skuId': 9701178018, 'skuStock': 4, 'skuSpec': '椰子', 'skuPrice': 12300}, {'skuId': 9701178016, 'skuStock': 2, 'skuSpec': '蓝莓', 'skuPrice': 15800}, {'skuId': 9701178022, 'skuStock': 3, 'skuSpec': '无糖黄柠檬', 'skuPrice': 10500}, {'skuId': 11240184841, 'skuStock': 4, 'skuSpec': '杏子', 'skuPrice': 12000}, {'skuId': 24225290737, 'skuStock': 0, 'skuSpec': '黑加仑', 'skuPrice': 11500}, {'skuId': 14186139822, 'skuStock': 0, 'skuSpec': '荔枝', 'skuPrice': 13000}, {'skuId': 14484241966, 'skuStock': 0, 'skuSpec': '日本柚子', 'skuPrice': 75000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37854071, 'skuStock': 8, 'skuSpec': '草莓', 'skuPrice': 6800}, {'skuId': 37854073, 'skuStock': 0, 'skuSpec': '樱桃', 'skuPrice': 7800}, {'skuId': 37854075, 'skuStock': 12, 'skuSpec': '蓝莓', 'skuPrice': 9500}, {'skuId': 37854077, 'skuStock': 15, 'skuSpec': '芒果', 'skuPrice': 7200}, {'skuId': 37854079, 'skuStock': 0, 'skuSpec': '美国大蔓越莓', 'skuPrice': 8800}, {'skuId': 37854081, 'skuStock': 0, 'skuSpec': '大颗粒芒果（特级）', 'skuPrice': 999900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14243726338, 'skuStock': 20, 'skuSpec': '3kg*1包', 'skuPrice': 22500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1y6s8kqy6qxemwz', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xd2gpe21trkucd', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2731wryj2on5arc', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24871381261, 'skuStock': 63, 'skuSpec': '1罐', 'skuPrice': 1230}, {'skuId': 24871381262, 'skuStock': 12, 'skuSpec': '5罐', 'skuPrice': 5950}, {'skuId': 24871381263, 'skuStock': 2, 'skuSpec': '24罐/箱', 'skuPrice': 26800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '276qy1wm82f72jl', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2frwzxrjo3ptani', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24510792539, 'skuStock': 13, 'skuSpec': '500g*1袋', 'skuPrice': 1500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37846179, 'skuStock': 4, 'skuSpec': '3kg/桶', 'skuPrice': 17800}, {'skuId': 37846180, 'skuStock': 1, 'skuSpec': '4桶/箱（12kg）', 'skuPrice': 999900}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1ylkpgvn4yaf2qq', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3et9oqw4csvcu4p', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 19230881750, 'skuStock': 4, 'skuSpec': '820g*24罐/箱', 'skuPrice': 21600}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1y6seotcy4flqjs', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2x5qb1h6poptaqy', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1y8252g0ep68ehp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1y80r884ldgdayo', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2x6y4uijstldqts', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5064840886, 'skuStock': 50, 'skuSpec': '蛋奶味', 'skuPrice': 2200}, {'skuId': 5064840887, 'skuStock': 8, 'skuSpec': '蛋奶味', 'skuPrice': 11800}, {'skuId': 11887400048, 'skuStock': 0, 'skuSpec': '酸奶味', 'skuPrice': 2200}, {'skuId': 11887400049, 'skuStock': 0, 'skuSpec': '酸奶味', 'skuPrice': 12200}, {'skuId': 13460043472, 'skuStock': 78, 'skuSpec': '奶酪味', 'skuPrice': 2000}, {'skuId': 13460043471, 'skuStock': 13, 'skuSpec': '奶酪味', 'skuPrice': 10800}, {'skuId': 24545089353, 'skuStock': 6, 'skuSpec': '巧克力味', 'skuPrice': 2300}, {'skuId': 24545089354, 'skuStock': 1, 'skuSpec': '巧克力味', 'skuPrice': 12200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37972001, 'skuStock': 9, 'skuSpec': '香甜', 'skuPrice': 23500}, {'skuId': 37972002, 'skuStock': 95, 'skuSpec': '香甜', 'skuPrice': 2800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37956781, 'skuStock': 43, 'skuSpec': '肉松小贝专用', 'skuPrice': 1850}, {'skuId': 37956782, 'skuStock': 3, 'skuSpec': '肉松小贝专用', 'skuPrice': 19800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37838149, 'skuStock': 175, 'skuSpec': '1kg*1袋', 'skuPrice': 1550}, {'skuId': 37838150, 'skuStock': 14, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 16800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37963986, 'skuStock': 2, 'skuSpec': '原味', 'skuPrice': 26500}, {'skuId': 37963987, 'skuStock': 29, 'skuSpec': '原味', 'skuPrice': 2780}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37857980, 'skuStock': 58, 'skuSpec': '1kg*1袋', 'skuPrice': 1680}, {'skuId': 37857981, 'skuStock': 4, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 16800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37851531, 'skuStock': 37, 'skuSpec': '1kg*1袋', 'skuPrice': 990}, {'skuId': 37851532, 'skuStock': 3, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 11000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 6159919022, 'skuStock': 8, 'skuSpec': '1kg*1包', 'skuPrice': 2550}, {'skuId': 6159919023, 'skuStock': 0, 'skuSpec': '1kg*10包/箱', 'skuPrice': 23500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 11240399540, 'skuStock': 9, 'skuSpec': '1kg*1包', 'skuPrice': 1980}, {'skuId': 11240399541, 'skuStock': 0, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 19000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37960099, 'skuStock': 2, 'skuSpec': '10kg/箱', 'skuPrice': 22950}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37954570, 'skuStock': 25, 'skuSpec': '10kg/箱', 'skuPrice': 26300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37870239, 'skuStock': 7, 'skuSpec': '1kg*1包', 'skuPrice': 2280}, {'skuId': 37870240, 'skuStock': 0, 'skuSpec': '1kg*12包/箱', 'skuPrice': 23500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 6270790225, 'skuStock': 164, 'skuSpec': '400g/包', 'skuPrice': 1800}, {'skuId': 6270790226, 'skuStock': 6, 'skuSpec': '400g*24包/箱', 'skuPrice': 38500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 10493828317, 'skuStock': 248, 'skuSpec': '1KG*1包', 'skuPrice': 2200}, {'skuId': 10493828318, 'skuStock': 49, 'skuSpec': '1kg*5包', 'skuPrice': 10800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2ot59wb8k4okuyo', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24871381261, 'skuStock': 63, 'skuSpec': '1罐', 'skuPrice': 1230}, {'skuId': 24871381262, 'skuStock': 12, 'skuSpec': '5罐', 'skuPrice': 5950}, {'skuId': 24871381263, 'skuStock': 2, 'skuSpec': '24罐/箱', 'skuPrice': 26800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37844306, 'skuStock': 1, 'skuSpec': '金牌芋泥馅', 'skuPrice': 9800}, {'skuId': 37844307, 'skuStock': 0, 'skuSpec': '金牌芋泥馅', 'skuPrice': 38800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37842610, 'skuStock': 20, 'skuSpec': '2.5kg*1包', 'skuPrice': 3880}, {'skuId': 37842611, 'skuStock': 3, 'skuSpec': '2.5kg*6包/箱', 'skuPrice': 21890}]\n", "Using proxy: *************************************************\n", "[{'skuId': '276qy1wm82f72jl', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37863220, 'skuStock': 26, 'skuSpec': '3kg*1包', 'skuPrice': 3950}, {'skuId': 37863221, 'skuStock': 4, 'skuSpec': '3kg*6包/箱', 'skuPrice': 22900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 8716806159, 'skuStock': 250, 'skuSpec': '420g*1罐', 'skuPrice': 690}, {'skuId': 8716806160, 'skuStock': 83, 'skuSpec': '420g*3罐', 'skuPrice': 1960}, {'skuId': 8716806161, 'skuStock': 10, 'skuSpec': '420g*24罐/箱', 'skuPrice': 14200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23909086565, 'skuStock': 10, 'skuSpec': '5KG*1包', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37838131, 'skuStock': 5, 'skuSpec': '奶油红豆粒馅', 'skuPrice': 10200}, {'skuId': 37838132, 'skuStock': 1, 'skuSpec': '奶油红豆粒馅', 'skuPrice': 36000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37829625, 'skuStock': 1, 'skuSpec': '5kg*4包/箱', 'skuPrice': 23800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37843903, 'skuStock': 4, 'skuSpec': '5kg*1包', 'skuPrice': 5150}, {'skuId': 37843904, 'skuStock': 1, 'skuSpec': '5kg*4包/箱', 'skuPrice': 20600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37843269, 'skuStock': 9, 'skuSpec': '特制绿豆沙馅（低甜）', 'skuPrice': 11500}, {'skuId': 37843270, 'skuStock': 2, 'skuSpec': '特制绿豆沙馅（低甜）', 'skuPrice': 41000}, {'skuId': 37843279, 'skuStock': 12, 'skuSpec': '金装绿豆沙馅', 'skuPrice': 11500}, {'skuId': 37843280, 'skuStock': 3, 'skuSpec': '金装绿豆沙馅', 'skuPrice': 39500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2ophg768myiq655', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37862736, 'skuStock': 22, 'skuSpec': '黑巧克力粒-70.5%', 'skuPrice': 24900}, {'skuId': 37841592, 'skuStock': 25, 'skuSpec': '2815黑巧克力粒-57.9%', 'skuPrice': 23800}, {'skuId': 37841593, 'skuStock': 31, 'skuSpec': '811黑巧克力粒-54.5%', 'skuPrice': 23900}, {'skuId': 37862713, 'skuStock': 27, 'skuSpec': 'W3低糖白巧克力粒-32%', 'skuPrice': 24500}, {'skuId': 37862712, 'skuStock': 19, 'skuSpec': 'W2白巧克力粒-28%', 'skuPrice': 24700}, {'skuId': 37862711, 'skuStock': 22, 'skuSpec': '823牛奶巧克力粒-33.6%', 'skuPrice': 23800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37977894, 'skuStock': 11, 'skuSpec': '纯脂黑巧克力币53.9%', 'skuPrice': 7800}, {'skuId': 37977896, 'skuStock': 10, 'skuSpec': '耐烤巧克力粒水滴型37.8%', 'skuPrice': 6950}, {'skuId': 24873376390, 'skuStock': 10, 'skuSpec': '牛奶巧克力币34.8%', 'skuPrice': 8500}, {'skuId': 24912394272, 'skuStock': 9, 'skuSpec': '黑巧克力币 65%', 'skuPrice': 7800}, {'skuId': 24912394273, 'skuStock': 12, 'skuSpec': '白巧克力币30.5%', 'skuPrice': 8600}, {'skuId': 24912394274, 'skuStock': 17, 'skuSpec': '黑巧克力币57%', 'skuPrice': 7800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '27329f2v569zizf', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37959275, 'skuStock': 13, 'skuSpec': '1kg*10块/箱', 'skuPrice': 33500}, {'skuId': 37959276, 'skuStock': 13, 'skuSpec': '1kg*10块/箱', 'skuPrice': 33000}, {'skuId': 37959277, 'skuStock': 137, 'skuSpec': '1kg*1块', 'skuPrice': 3880}, {'skuId': 37959278, 'skuStock': 132, 'skuSpec': '1kg*1块', 'skuPrice': 3880}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37968537, 'skuStock': 9, 'skuSpec': '深黑 1kg*1包', 'skuPrice': 6600}, {'skuId': 37968539, 'skuStock': 30, 'skuSpec': '防潮 1kg*1包', 'skuPrice': 8300}, {'skuId': 23742692214, 'skuStock': 50, 'skuSpec': '高脂 1kg*1包', 'skuPrice': 5500}, {'skuId': 23742692215, 'skuStock': 0, 'skuSpec': '高脂 5kg*1包', 'skuPrice': 26200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 8587736711, 'skuStock': 10, 'skuSpec': '1kg*3包/箱', 'skuPrice': 59000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37853918, 'skuStock': 51, 'skuSpec': '1.6KG', 'skuPrice': 11600}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3f1ycjf0rf6has8', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37839537, 'skuStock': 0, 'skuSpec': '5KG*1袋', 'skuPrice': 17400}, {'skuId': 5705046076, 'skuStock': 0, 'skuSpec': '1KG/包', 'skuPrice': 3300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37856610, 'skuStock': 19, 'skuSpec': '300g*1包', 'skuPrice': 1380}, {'skuId': 37856611, 'skuStock': 0, 'skuSpec': '300g*24包/箱', 'skuPrice': 27800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37857977, 'skuStock': 68, 'skuSpec': '耐高糖', 'skuPrice': 2250}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37968678, 'skuStock': 0, 'skuSpec': '1kg*10包/箱', 'skuPrice': 36900}, {'skuId': 3459405816, 'skuStock': 1, 'skuSpec': '1kg/包', 'skuPrice': 3800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 10553776763, 'skuStock': 272, 'skuSpec': '1块', 'skuPrice': 820}, {'skuId': 5407112364, 'skuStock': 54, 'skuSpec': '5块', 'skuPrice': 3850}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14647635729, 'skuStock': 19, 'skuSpec': '500g/盒', 'skuPrice': 2200}, {'skuId': 14647635730, 'skuStock': 0, 'skuSpec': '500g*20盒/箱', 'skuPrice': 39800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37975624, 'skuStock': 23, 'skuSpec': '1kg/盒（200片）', 'skuPrice': 10500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14351414994, 'skuStock': 238, 'skuSpec': '400g*1包', 'skuPrice': 190}, {'skuId': 14351414995, 'skuStock': 47, 'skuSpec': '400g*5包', 'skuPrice': 880}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37865527, 'skuStock': 9, 'skuSpec': '1350g', 'skuPrice': 6800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24078789336, 'skuStock': 37, 'skuSpec': '454g*1包', 'skuPrice': 1280}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nn3jcce8qnimvp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37864121, 'skuStock': 8, 'skuSpec': '2700g', 'skuPrice': 7500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37868359, 'skuStock': 4, 'skuSpec': '5kg', 'skuPrice': 17250}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24961576442, 'skuStock': 53, 'skuSpec': '400g*1包', 'skuPrice': 300}, {'skuId': 24961576443, 'skuStock': 10, 'skuSpec': '400g*5包', 'skuPrice': 1400}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37959026, 'skuStock': 21, 'skuSpec': '1kg/盒（400片）', 'skuPrice': 11500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2xanc3a94refyla', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 11419879331, 'skuStock': 6, 'skuSpec': '1KG/包', 'skuPrice': 11900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25445480901, 'skuStock': 2, 'skuSpec': '907g*16袋/箱', 'skuPrice': 59900}, {'skuId': 25445480902, 'skuStock': 47, 'skuSpec': '907g/袋', 'skuPrice': 4200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24887588369, 'skuStock': 20, 'skuSpec': '1kg/包', 'skuPrice': 6900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13784631295, 'skuStock': 41, 'skuSpec': '1KG*1包', 'skuPrice': 6700}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5170840817, 'skuStock': 25, 'skuSpec': '3kg*1包', 'skuPrice': 9600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14191321154, 'skuStock': 4, 'skuSpec': '11.34kg/箱', 'skuPrice': 49500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14306132045, 'skuStock': 0, 'skuSpec': '1/4鲜红', 'skuPrice': 5200}, {'skuId': 14306132046, 'skuStock': 3, 'skuSpec': '1/4暗红', 'skuPrice': 4800}, {'skuId': 14306132047, 'skuStock': 0, 'skuSpec': '1/8暗红', 'skuPrice': 4800}, {'skuId': 14306231452, 'skuStock': 0, 'skuSpec': '1/4暗红 样品1kg/包', 'skuPrice': 4500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37981065, 'skuStock': 5, 'skuSpec': '11.34kg/箱', 'skuPrice': 66000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25445285692, 'skuStock': 5, 'skuSpec': '11.34kg/箱', 'skuPrice': 46000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37856458, 'skuStock': 30, 'skuSpec': '2.5KG', 'skuPrice': 2680}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37955216, 'skuStock': 4, 'skuSpec': '11.34kg/箱', 'skuPrice': 58500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13784125641, 'skuStock': 52, 'skuSpec': '500g*1包', 'skuPrice': 2280}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24149191974, 'skuStock': 17, 'skuSpec': '11.34kg/箱', 'skuPrice': 63500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37867889, 'skuStock': 3, 'skuSpec': '10kg', 'skuPrice': 15800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13661640279, 'skuStock': 8, 'skuSpec': '11.34kg/箱', 'skuPrice': 45500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37942720, 'skuStock': 5, 'skuSpec': '2.5kg/袋', 'skuPrice': 2800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3njdtcbh2zkniup', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14365603738, 'skuStock': 0, 'skuSpec': '1KG*1包', 'skuPrice': 6200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 6270790225, 'skuStock': 164, 'skuSpec': '400g/包', 'skuPrice': 1800}, {'skuId': 6270790226, 'skuStock': 6, 'skuSpec': '400g*24包/箱', 'skuPrice': 38500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 10144494861, 'skuStock': 58, 'skuSpec': '3瓶', 'skuPrice': 5800}, {'skuId': 10144494862, 'skuStock': 7, 'skuSpec': '24瓶/箱', 'skuPrice': 45200}, {'skuId': 10560287308, 'skuStock': 176, 'skuSpec': '1瓶', 'skuPrice': 1980}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24028097677, 'skuStock': 12, 'skuSpec': '2.5kg/盒', 'skuPrice': 24500}, {'skuId': 24028097678, 'skuStock': 3, 'skuSpec': '2.5kg*4盒/箱', 'skuPrice': 94500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24783989424, 'skuStock': 28, 'skuSpec': '3瓶', 'skuPrice': 2920}, {'skuId': 24783989425, 'skuStock': 3, 'skuSpec': '24瓶/箱', 'skuPrice': 21500}, {'skuId': 24783989426, 'skuStock': 84, 'skuSpec': '1瓶', 'skuPrice': 990}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24871381261, 'skuStock': 63, 'skuSpec': '1罐', 'skuPrice': 1230}, {'skuId': 24871381262, 'skuStock': 12, 'skuSpec': '5罐', 'skuPrice': 5950}, {'skuId': 24871381263, 'skuStock': 2, 'skuSpec': '24罐/箱', 'skuPrice': 26800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23731583090, 'skuStock': 0, 'skuSpec': '原味翻糖膏 908g*1包', 'skuPrice': 4800}, {'skuId': 23738090455, 'skuStock': 23, 'skuSpec': '防潮蝴蝶结干佩斯 454g/包', 'skuPrice': 2800}, {'skuId': 23738090454, 'skuStock': 51, 'skuSpec': '通用型防潮干佩斯 454g/包', 'skuPrice': 2800}, {'skuId': 23738090456, 'skuStock': 35, 'skuSpec': '防潮糖牌干佩斯 454g/包', 'skuPrice': 4600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37856458, 'skuStock': 30, 'skuSpec': '2.5KG', 'skuPrice': 2680}]\n", "Using proxy: ************************************************\n", "[{'skuId': 8716806159, 'skuStock': 250, 'skuSpec': '420g*1罐', 'skuPrice': 690}, {'skuId': 8716806160, 'skuStock': 83, 'skuSpec': '420g*3罐', 'skuPrice': 1960}, {'skuId': 8716806161, 'skuStock': 10, 'skuSpec': '420g*24罐/箱', 'skuPrice': 14200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37841209, 'skuStock': 10, 'skuSpec': '100g*1瓶', 'skuPrice': 3300}, {'skuId': 37841210, 'skuStock': 0, 'skuSpec': '100g*12瓶/箱', 'skuPrice': 35800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '35zhqj1fhs5qmze', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24333279450, 'skuStock': 5, 'skuSpec': '5kg*1桶', 'skuPrice': 24200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5387234844, 'skuStock': 0, 'skuSpec': '1包', 'skuPrice': 1299}, {'skuId': 5387234845, 'skuStock': 0, 'skuSpec': '10包', 'skuPrice': 11800}, {'skuId': 13472744000, 'skuStock': 0, 'skuSpec': '20包/箱', 'skuPrice': 99900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37857655, 'skuStock': 115, 'skuSpec': '海苔酥脆松（3A海苔）', 'skuPrice': 13900}, {'skuId': 37857657, 'skuStock': 0, 'skuSpec': '原味酥脆松（3A香酥）', 'skuPrice': 14500}, {'skuId': 37857659, 'skuStock': 19, 'skuSpec': '蟹黄味酥脆松', 'skuPrice': 14500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37847290, 'skuStock': 7, 'skuSpec': '原味（A级）', 'skuPrice': 15800}, {'skuId': 37847292, 'skuStock': 0, 'skuSpec': '辣味（A级）', 'skuPrice': 99900}, {'skuId': 37847294, 'skuStock': 22, 'skuSpec': '原味（B级）', 'skuPrice': 13750}, {'skuId': 37847296, 'skuStock': 0, 'skuSpec': '原味（B级）-白袋', 'skuPrice': 99900}, {'skuId': 37847298, 'skuStock': 0, 'skuSpec': '辣味（B级）', 'skuPrice': 99900}, {'skuId': 37847300, 'skuStock': 0, 'skuSpec': '原味（C级）', 'skuPrice': 99900}, {'skuId': 37847302, 'skuStock': 0, 'skuSpec': '原味（D级）', 'skuPrice': 99900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37854774, 'skuStock': 16, 'skuSpec': '1kg/袋', 'skuPrice': 6200}, {'skuId': 37854775, 'skuStock': 1, 'skuSpec': '15袋/箱（15kg）', 'skuPrice': 91000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2xlq34q19v58ejn', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14654425559, 'skuStock': 12, 'skuSpec': '750ml/瓶', 'skuPrice': 1950}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1yczc8cuoklj2l6', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nknjko3g61n2og', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 11425089027, 'skuStock': 3, 'skuSpec': '1L/瓶', 'skuPrice': 9300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 11426089509, 'skuStock': 5, 'skuSpec': '1L/瓶', 'skuPrice': 17900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24302687773, 'skuStock': 314, 'skuSpec': '200ml*1瓶', 'skuPrice': 680}, {'skuId': 24302687774, 'skuStock': 26, 'skuSpec': '200ml*12瓶/箱', 'skuPrice': 6000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25087286221, 'skuStock': 29, 'skuSpec': '玉桂粉/肉桂粉 380g', 'skuPrice': 4800}, {'skuId': 25087286222, 'skuStock': 8, 'skuSpec': '细黑胡椒粉 425g', 'skuPrice': 8500}, {'skuId': 25087286223, 'skuStock': 5, 'skuSpec': '精选红甜椒粉 453g', 'skuPrice': 5600}, {'skuId': 25087286220, 'skuStock': 6, 'skuSpec': '黑胡椒整粒 450g', 'skuPrice': 7900}, {'skuId': 25087286224, 'skuStock': 6, 'skuSpec': '香菜籽粉397g', 'skuPrice': 3300}, {'skuId': 14641319603, 'skuStock': 12, 'skuSpec': '大蒜粉640g', 'skuPrice': 6200}, {'skuId': 14641319602, 'skuStock': 12, 'skuSpec': '洋葱粉500g', 'skuPrice': 3800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14351414994, 'skuStock': 238, 'skuSpec': '400g*1包', 'skuPrice': 190}, {'skuId': 14351414995, 'skuStock': 47, 'skuSpec': '400g*5包', 'skuPrice': 880}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24961576442, 'skuStock': 53, 'skuSpec': '400g*1包', 'skuPrice': 300}, {'skuId': 24961576443, 'skuStock': 10, 'skuSpec': '400g*5包', 'skuPrice': 1400}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2oo8f0bpw1fr2pr', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '27bqkqm1mfczyls', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13802545792, 'skuStock': 0, 'skuSpec': '720ml*1瓶', 'skuPrice': 24800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nt934a841chq22', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37841743, 'skuStock': 22, 'skuSpec': '10-13克/粒*100粒', 'skuPrice': 12500}, {'skuId': 37841744, 'skuStock': 0, 'skuSpec': '13-15克/粒*100粒', 'skuPrice': 14500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1ycxs2767egbimi', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25435681048, 'skuStock': 0, 'skuSpec': '蛋黄液970g*12瓶/箱', 'skuPrice': 36000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14654408467, 'skuStock': 80, 'skuSpec': '500g/包', 'skuPrice': 720}, {'skuId': 14654408468, 'skuStock': 5, 'skuSpec': '500g*16包/箱', 'skuPrice': 11200}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2ft5va52d5gsepb', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5065529811, 'skuStock': 874, 'skuSpec': '1罐', 'skuPrice': 1180}, {'skuId': 5065529812, 'skuStock': 145, 'skuSpec': '6罐', 'skuPrice': 6900}, {'skuId': 5065529813, 'skuStock': 18, 'skuSpec': '48罐/箱', 'skuPrice': 46500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37841743, 'skuStock': 22, 'skuSpec': '10-13克/粒*100粒', 'skuPrice': 12500}, {'skuId': 37841744, 'skuStock': 0, 'skuSpec': '13-15克/粒*100粒', 'skuPrice': 14500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5096044863, 'skuStock': 29, 'skuSpec': '5kg/桶', 'skuPrice': 9250}, {'skuId': 5096044864, 'skuStock': 7, 'skuSpec': '5kg*4桶/箱', 'skuPrice': 36000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7688598064, 'skuStock': 53, 'skuSpec': '1袋', 'skuPrice': 850}, {'skuId': 7688598065, 'skuStock': 10, 'skuSpec': '5袋', 'skuPrice': 3890}]\n", "Using proxy: *************************************************\n", "[{'skuId': 17919592928, 'skuStock': 72, 'skuSpec': '907g*1盒', 'skuPrice': 1730}, {'skuId': 17919592929, 'skuStock': 6, 'skuSpec': '907g*12盒/箱', 'skuPrice': 18900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37828402, 'skuStock': 13, 'skuSpec': '1kg*1包', 'skuPrice': 1700}, {'skuId': 37828403, 'skuStock': 1, 'skuSpec': '1kg*10包/箱', 'skuPrice': 16600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37966776, 'skuStock': 5, 'skuSpec': '25kg/袋', 'skuPrice': 11800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nn3jcce8qnimvp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oo8f0bpw1fr2pr', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25435681048, 'skuStock': 0, 'skuSpec': '蛋黄液970g*12瓶/箱', 'skuPrice': 36000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14650814405, 'skuStock': 28, 'skuSpec': '白牛皮不防粘型 500张/包', 'skuPrice': 13800}, {'skuId': 14650814406, 'skuStock': 14, 'skuSpec': '白牛皮不防粘型 500张*2包/箱', 'skuPrice': 27000}, {'skuId': 14650814407, 'skuStock': 20, 'skuSpec': '防油纸防粘型 500张/包', 'skuPrice': 14800}, {'skuId': 14650814408, 'skuStock': 10, 'skuSpec': '防油纸防粘型 500张*2包/箱', 'skuPrice': 29000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 8344049992, 'skuStock': 227, 'skuSpec': '大号(23*35cm)', 'skuPrice': 500}, {'skuId': 8344049991, 'skuStock': 0, 'skuSpec': '中号(20*31cm)', 'skuPrice': 420}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14608628683, 'skuStock': 57, 'skuSpec': '方形卷口杯白色87*87*35mm 50个', 'skuPrice': 880}, {'skuId': 14608628684, 'skuStock': 59, 'skuSpec': '方形卷口杯本色87*87*35mm 50个', 'skuPrice': 880}, {'skuId': 14608628685, 'skuStock': 60, 'skuSpec': '方形卷口杯绿色87*87*35mm 50个', 'skuPrice': 880}, {'skuId': 14608628686, 'skuStock': 58, 'skuSpec': '长方形卷口杯白色82*40*42mm 50个', 'skuPrice': 780}, {'skuId': 14608628687, 'skuStock': 57, 'skuSpec': '长方形卷口杯本色82*40*42mm 50个', 'skuPrice': 780}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13258710515, 'skuStock': 15, 'skuSpec': '4寸双层17*17*20  10个', 'skuPrice': 3380}, {'skuId': 13258710513, 'skuStock': 7, 'skuSpec': '4寸三层17*17*28  10个', 'skuPrice': 4390}, {'skuId': 13258710514, 'skuStock': 17, 'skuSpec': '6寸双层21.5*21.5*24  10个', 'skuPrice': 4450}, {'skuId': 13258710516, 'skuStock': 4, 'skuSpec': '6寸三层21.5*21.5*30  10个', 'skuPrice': 5350}, {'skuId': 13258710517, 'skuStock': 15, 'skuSpec': '8寸双层26*26*25  10个', 'skuPrice': 5350}, {'skuId': 13258710518, 'skuStock': 1, 'skuSpec': '8寸三层26*26*31  10个', 'skuPrice': 6360}, {'skuId': 14653616962, 'skuStock': 10, 'skuSpec': '8寸单层26*26*18 10个', 'skuPrice': 4200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14600300860, 'skuStock': 19, 'skuSpec': '750ml(115*90*65mm) 50个', 'skuPrice': 1560}, {'skuId': 14600300861, 'skuStock': 40, 'skuSpec': '1080ml（155*120*47mm）50个', 'skuPrice': 1830}, {'skuId': 14600300862, 'skuStock': 5, 'skuSpec': '1400ml（155*120*65mm）50个', 'skuPrice': 2090}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25348589435, 'skuStock': 10, 'skuSpec': 'BURGER黄色 500张/包', 'skuPrice': 3180}, {'skuId': 25348589436, 'skuStock': 9, 'skuSpec': 'BURGER红色 500张/包', 'skuPrice': 3180}, {'skuId': 25348589437, 'skuStock': 10, 'skuSpec': 'BURGER绿色 500张/包', 'skuPrice': 3180}, {'skuId': 25348589438, 'skuStock': 9, 'skuSpec': 'I LOVE IT 500张/包', 'skuPrice': 3180}, {'skuId': 25348589439, 'skuStock': 10, 'skuSpec': '美味驾到 500张/包', 'skuPrice': 3180}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25365883700, 'skuStock': 76, 'skuSpec': '50*30mm 500个', 'skuPrice': 790}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25426981797, 'skuStock': 57, 'skuSpec': '白底彩心中号63*55mm 50个', 'skuPrice': 380}, {'skuId': 25426981798, 'skuStock': 96, 'skuSpec': '小号粉底白兔50*45mm 50个', 'skuPrice': 380}, {'skuId': 25426981799, 'skuStock': 96, 'skuSpec': '小号蓝底白点50*45mm 50个', 'skuPrice': 380}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14605749839, 'skuStock': 11, 'skuSpec': '150牛皮纸碗+盖 50个', 'skuPrice': 3350}, {'skuId': 14605749840, 'skuStock': 20, 'skuSpec': '115牛皮纸碗+盖 50个', 'skuPrice': 2450}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25366684611, 'skuStock': 2, 'skuSpec': '750ml白卡方碗+低盖 50个', 'skuPrice': 3450}, {'skuId': 25471283487, 'skuStock': 5, 'skuSpec': '750ml白卡方碗+高盖 50个', 'skuPrice': 3450}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14607716403, 'skuStock': 16, 'skuSpec': '4寸纸碗+盖 50个', 'skuPrice': 2580}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14606107247, 'skuStock': 11, 'skuSpec': '120*55mm 100个', 'skuPrice': 2550}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14607526246, 'skuStock': 10, 'skuSpec': '90*80*40mm 100个', 'skuPrice': 1480}, {'skuId': 14607526247, 'skuStock': 14, 'skuSpec': '80*32.5mm 100个', 'skuPrice': 1120}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25363196866, 'skuStock': 48, 'skuSpec': '24*20mm 1000个', 'skuPrice': 840}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25362894824, 'skuStock': 17, 'skuSpec': '47*37mm 600个', 'skuPrice': 2640}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14608112574, 'skuStock': 31, 'skuSpec': '140*50*30mm 240个', 'skuPrice': 1150}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37997496, 'skuStock': 1000, 'skuSpec': '950ml*12盒/箱', 'skuPrice': 16800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5619723627, 'skuStock': 8, 'skuSpec': '950ml*12瓶/箱', 'skuPrice': 19200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37982175, 'skuStock': 31, 'skuSpec': '1L*12盒/箱', 'skuPrice': 37500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13709045654, 'skuStock': 5, 'skuSpec': '950ml*12盒/箱', 'skuPrice': 16800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25273092138, 'skuStock': 43, 'skuSpec': '125ml*9盒/箱', 'skuPrice': 2850}, {'skuId': 25273092139, 'skuStock': 4, 'skuSpec': '10箱', 'skuPrice': 27200}, {'skuId': 25439478129, 'skuStock': 21, 'skuSpec': '2箱', 'skuPrice': 5900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24868290179, 'skuStock': 680, 'skuSpec': '1L*12盒/箱', 'skuPrice': 7400}, {'skuId': 24868290180, 'skuStock': 136, 'skuSpec': '5箱', 'skuPrice': 36000}, {'skuId': 24868290181, 'skuStock': 68, 'skuSpec': '10箱', 'skuPrice': 68000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13487146659, 'skuStock': 305, 'skuSpec': '1L*12盒/箱', 'skuPrice': 8600}, {'skuId': 13782529537, 'skuStock': 61, 'skuSpec': '5箱', 'skuPrice': 42000}, {'skuId': 13782529538, 'skuStock': 30, 'skuSpec': '10箱', 'skuPrice': 82000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24515381610, 'skuStock': 31, 'skuSpec': '950ml*2盒', 'skuPrice': 3600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13736618910, 'skuStock': 119, 'skuSpec': '1箱', 'skuPrice': 11000}, {'skuId': 13736618909, 'skuStock': 23, 'skuSpec': '5箱', 'skuPrice': 54000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oi2iji1l97y6dw', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14560906565, 'skuStock': 0, 'skuSpec': '200ml*12盒/箱', 'skuPrice': 5350}, {'skuId': 14560906566, 'skuStock': 0, 'skuSpec': '10箱', 'skuPrice': 51800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24542595736, 'skuStock': 0, 'skuSpec': '400g*4罐', 'skuPrice': 3800}, {'skuId': 19548382980, 'skuStock': 0, 'skuSpec': '400g*6罐', 'skuPrice': 5600}, {'skuId': 19945079286, 'skuStock': 0, 'skuSpec': '400g*48罐/箱', 'skuPrice': 42500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3ex0mg534bacu82', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xhzhvmby769a66', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5169701687, 'skuStock': 0, 'skuSpec': '原味苏打水', 'skuPrice': 1780}, {'skuId': 5169701688, 'skuStock': 0, 'skuSpec': '原味苏打水', 'skuPrice': 6190}, {'skuId': 5169701689, 'skuStock': 0, 'skuSpec': '柠檬味苏打水', 'skuPrice': 2050}, {'skuId': 5169701690, 'skuStock': 3, 'skuSpec': '柠檬味苏打水', 'skuPrice': 7180}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5169711120, 'skuStock': 11, 'skuSpec': '330ml*12瓶/箱', 'skuPrice': 5900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24291486947, 'skuStock': 7, 'skuSpec': '330ml*24瓶/箱', 'skuPrice': 12500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14145102258, 'skuStock': 0, 'skuSpec': '4L*6瓶/箱', 'skuPrice': 4600}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3ex15885gntlqf5', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37950156, 'skuStock': 70, 'skuSpec': '3kg/袋', 'skuPrice': 28800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5069706525, 'skuStock': 19, 'skuSpec': '阿方索芒果', 'skuPrice': 7600}, {'skuId': 5038541239, 'skuStock': 36, 'skuSpec': '草莓', 'skuPrice': 7550}, {'skuId': 10153199559, 'skuStock': 57, 'skuSpec': '树莓/覆盆子', 'skuPrice': 8350}, {'skuId': 13660410533, 'skuStock': 29, 'skuSpec': '芒果', 'skuPrice': 6200}, {'skuId': 14159834417, 'skuStock': 4, 'skuSpec': '野生蓝莓', 'skuPrice': 11900}, {'skuId': 24538087311, 'skuStock': 5, 'skuSpec': '西番莲', 'skuPrice': 9200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9664199082, 'skuStock': 7, 'skuSpec': '芒果', 'skuPrice': 12000}, {'skuId': 9701178017, 'skuStock': 12, 'skuSpec': '草莓', 'skuPrice': 10300}, {'skuId': 9731590146, 'skuStock': 9, 'skuSpec': '覆盆子/红莓/桑子/木莓', 'skuPrice': 15800}, {'skuId': 9701178024, 'skuStock': 4, 'skuSpec': '白桃', 'skuPrice': 12200}, {'skuId': 9701178020, 'skuStock': 9, 'skuSpec': '百香果', 'skuPrice': 11500}, {'skuId': 23584882198, 'skuStock': 10, 'skuSpec': '凤梨', 'skuPrice': 10300}, {'skuId': 9701178018, 'skuStock': 4, 'skuSpec': '椰子', 'skuPrice': 12300}, {'skuId': 9701178016, 'skuStock': 2, 'skuSpec': '蓝莓', 'skuPrice': 15800}, {'skuId': 9701178022, 'skuStock': 3, 'skuSpec': '无糖黄柠檬', 'skuPrice': 10500}, {'skuId': 11240184841, 'skuStock': 4, 'skuSpec': '杏子', 'skuPrice': 12000}, {'skuId': 24225290737, 'skuStock': 0, 'skuSpec': '黑加仑', 'skuPrice': 11500}, {'skuId': 14186139822, 'skuStock': 0, 'skuSpec': '荔枝', 'skuPrice': 13000}, {'skuId': 14484241966, 'skuStock': 0, 'skuSpec': '日本柚子', 'skuPrice': 75000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 7175732460, 'skuStock': 0, 'skuSpec': '香草风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732463, 'skuStock': 0, 'skuSpec': '榛果风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732464, 'skuStock': 0, 'skuSpec': '焦糖风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732458, 'skuStock': 0, 'skuSpec': '绿薄荷风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732459, 'skuStock': 4, 'skuSpec': '桂花风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732461, 'skuStock': 6, 'skuSpec': '莫西多薄荷风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 24239677493, 'skuStock': 6, 'skuSpec': '山茶花风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 7175732462, 'skuStock': 7, 'skuSpec': '纯蔗糖风味糖浆 1000ml*1瓶', 'skuPrice': 6900}, {'skuId': 16968894232, 'skuStock': 6, 'skuSpec': '焦糖风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 9011230833, 'skuStock': 3, 'skuSpec': '香草风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 9011230834, 'skuStock': 1, 'skuSpec': '绿薄荷风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 22643588819, 'skuStock': 3, 'skuSpec': '榛果风味糖浆1000ml*1瓶', 'skuPrice': 8900}, {'skuId': 25427996162, 'skuStock': 6, 'skuSpec': '太妃果风味糖浆 700ml*1瓶', 'skuPrice': 7500}, {'skuId': 14648631254, 'skuStock': 5, 'skuSpec': '海盐焦糖风味糖浆 1L装', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14243726338, 'skuStock': 20, 'skuSpec': '3kg*1包', 'skuPrice': 22500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7163904048, 'skuStock': 646, 'skuSpec': '1L/盒', 'skuPrice': 1480}, {'skuId': 7163904049, 'skuStock': 53, 'skuSpec': '1L*12盒/箱', 'skuPrice': 14500}, {'skuId': 13736519341, 'skuStock': 10, 'skuSpec': '5箱', 'skuPrice': 70000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24302687773, 'skuStock': 314, 'skuSpec': '200ml*1瓶', 'skuPrice': 680}, {'skuId': 24302687774, 'skuStock': 26, 'skuSpec': '200ml*12瓶/箱', 'skuPrice': 6000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1y6s8kqy6qxemwz', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2xd2gpe21trkucd', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24525086972, 'skuStock': 104, 'skuSpec': '1kg*1包', 'skuPrice': 1850}, {'skuId': 24525086971, 'skuStock': 5, 'skuSpec': '1kg*20包/箱', 'skuPrice': 30500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2731wryj2on5arc', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24871381261, 'skuStock': 63, 'skuSpec': '1罐', 'skuPrice': 1230}, {'skuId': 24871381262, 'skuStock': 12, 'skuSpec': '5罐', 'skuPrice': 5950}, {'skuId': 24871381263, 'skuStock': 2, 'skuSpec': '24罐/箱', 'skuPrice': 26800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5169711120, 'skuStock': 11, 'skuSpec': '330ml*12瓶/箱', 'skuPrice': 5900}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2frwzxrjo3ptani', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3npllznpz2qemch', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24510792539, 'skuStock': 13, 'skuSpec': '500g*1袋', 'skuPrice': 1500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13487514003, 'skuStock': 94, 'skuSpec': '1L*1盒', 'skuPrice': 3190}, {'skuId': 13487514002, 'skuStock': 7, 'skuSpec': '1L*12盒/箱', 'skuPrice': 34800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7164515271, 'skuStock': 20, 'skuSpec': '1.26kg*1盒', 'skuPrice': 1680}, {'skuId': 7164515270, 'skuStock': 1, 'skuSpec': '1.26kg*12盒/箱', 'skuPrice': 19200}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1ylkpgvn4yaf2qq', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3et9oqw4csvcu4p', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 19230881750, 'skuStock': 4, 'skuSpec': '820g*24罐/箱', 'skuPrice': 21600}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1y6seotcy4flqjs', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2x5qb1h6poptaqy', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1y8252g0ep68ehp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3636xxx0bdllqs1', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13487517388, 'skuStock': 507, 'skuSpec': '1L*1盒', 'skuPrice': 2900}, {'skuId': 13487517389, 'skuStock': 42, 'skuSpec': '1L*12盒/箱', 'skuPrice': 28000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24525086104, 'skuStock': 40, 'skuSpec': '980g/瓶', 'skuPrice': 1980}, {'skuId': 24525086103, 'skuStock': 3, 'skuSpec': '980g*12瓶/箱', 'skuPrice': 21500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1y80r884ldgdayo', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 10544944758, 'skuStock': 160, 'skuSpec': '400ml*1瓶', 'skuPrice': 990}, {'skuId': 10726221693, 'skuStock': 32, 'skuSpec': '400ml*5瓶', 'skuPrice': 4750}, {'skuId': 10544944759, 'skuStock': 6, 'skuSpec': '400ml*24瓶/箱', 'skuPrice': 22300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 19858077593, 'skuStock': 19, 'skuSpec': '1kg/包', 'skuPrice': 1580}, {'skuId': 19858077594, 'skuStock': 0, 'skuSpec': '1kg*20包/箱', 'skuPrice': 29800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13979834774, 'skuStock': 7, 'skuSpec': '1L*1瓶', 'skuPrice': 4500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2x6y4uijstldqts', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3f4cs6owxvtpqqq', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13802545792, 'skuStock': 0, 'skuSpec': '720ml*1瓶', 'skuPrice': 24800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '27cwu3tuw41oevx', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5169636673, 'skuStock': 0, 'skuSpec': '1L*1瓶', 'skuPrice': 1980}]\n", "Using proxy: ************************************************\n", "[{'skuId': '36856rm6yvlmml2', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nvqgyy86xqamm7', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '36856g7x6r7kuk5', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '27334jt5wvggunr', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7373932468, 'skuStock': 36, 'skuSpec': '1L*1盒', 'skuPrice': 1780}, {'skuId': 7373932469, 'skuStock': 3, 'skuSpec': '1L*12盒/箱', 'skuPrice': 15600}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1y9ah7sfnrijyh4', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 6270790225, 'skuStock': 164, 'skuSpec': '400g/包', 'skuPrice': 1800}, {'skuId': 6270790226, 'skuStock': 6, 'skuSpec': '400g*24包/箱', 'skuPrice': 38500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '276qy1wm82f72jl', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '365nswxkrzdcevb', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3ezgfuxka3bym6c', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3npllznpz2qemch', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oki64p33ai723u', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2ophg768myiq655', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13979804770, 'skuStock': 11, 'skuSpec': '850g/罐', 'skuPrice': 2450}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fn0o1t2881e6o9', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3eqvf2ifj90f2gb', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1yahfzmrz2oxabd', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '276t0nxz6bjkeuw', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24493584472, 'skuStock': 19, 'skuSpec': '500g*1包', 'skuPrice': 840}, {'skuId': 24493584473, 'skuStock': 3, 'skuSpec': '5包', 'skuPrice': 4000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '271twpj3y884eti', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nehtl2zqebsuii', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14144848291, 'skuStock': 0, 'skuSpec': '5KG*1袋', 'skuPrice': 1600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5065529811, 'skuStock': 874, 'skuSpec': '1罐', 'skuPrice': 1180}, {'skuId': 5065529812, 'skuStock': 145, 'skuSpec': '6罐', 'skuPrice': 6900}, {'skuId': 5065529813, 'skuStock': 18, 'skuSpec': '48罐/箱', 'skuPrice': 46500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5096044863, 'skuStock': 29, 'skuSpec': '5kg/桶', 'skuPrice': 9250}, {'skuId': 5096044864, 'skuStock': 7, 'skuSpec': '5kg*4桶/箱', 'skuPrice': 36000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oeecnx9fvn4uyz', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '27cwbf8ydqmkemd', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24542595736, 'skuStock': 0, 'skuSpec': '400g*4罐', 'skuPrice': 3800}, {'skuId': 19548382980, 'skuStock': 0, 'skuSpec': '400g*6罐', 'skuPrice': 5600}, {'skuId': 19945079286, 'skuStock': 0, 'skuSpec': '400g*48罐/箱', 'skuPrice': 42500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24887780213, 'skuStock': 71, 'skuSpec': '北海道恋人(坚果塔)', 'skuPrice': 1000}, {'skuId': 24887780214, 'skuStock': 7, 'skuSpec': '北海道恋人(坚果塔)', 'skuPrice': 9500}, {'skuId': 24887780215, 'skuStock': 93, 'skuSpec': '印第安之舟', 'skuPrice': 1100}, {'skuId': 24887780216, 'skuStock': 9, 'skuSpec': '印第安之舟', 'skuPrice': 10500}, {'skuId': 24887780219, 'skuStock': 49, 'skuSpec': '无蔗糖匠心手作酥饼', 'skuPrice': 900}, {'skuId': 24887780220, 'skuStock': 4, 'skuSpec': '无蔗糖匠心手作酥饼', 'skuPrice': 8500}, {'skuId': 24887780221, 'skuStock': 0, 'skuSpec': '莓你不行', 'skuPrice': 900}, {'skuId': 24887780222, 'skuStock': 0, 'skuSpec': '莓你不行', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 721790103, 'skuStock': 12, 'skuSpec': '40个*10袋/箱', 'skuPrice': 15800}, {'skuId': 11071297522, 'skuStock': 125, 'skuSpec': '40个*1袋', 'skuPrice': 1980}]\n", "Using proxy: *************************************************\n", "[{'skuId': 11471595286, 'skuStock': 316, 'skuSpec': '10片/包', 'skuPrice': 1580}, {'skuId': 11471595287, 'skuStock': 22, 'skuSpec': '10片*14包/箱', 'skuPrice': 18800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14602775947, 'skuStock': 62, 'skuSpec': '25g*40个*1包', 'skuPrice': 2850}, {'skuId': 14602775948, 'skuStock': 7, 'skuSpec': '25g*8包*1箱', 'skuPrice': 18000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nn42677m5ecunh', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 9696144800, 'skuStock': 180, 'skuSpec': '原味50g速冻甜甜圈', 'skuPrice': 3200}, {'skuId': 9696144802, 'skuStock': 30, 'skuSpec': '原味50g速冻甜甜圈', 'skuPrice': 12960}, {'skuId': 23678290236, 'skuStock': 0, 'skuSpec': '原味50g速冻甜甜圈', 'skuPrice': 12960}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23762495602, 'skuStock': 18, 'skuSpec': '原味', 'skuPrice': 17500}, {'skuId': 23762495603, 'skuStock': 5, 'skuSpec': '草莓味', 'skuPrice': 18500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37836223, 'skuStock': 9, 'skuSpec': '11袋/箱（660个）（3*6cm）', 'skuPrice': 15950}, {'skuId': 11422690956, 'skuStock': 99, 'skuSpec': '60个/袋（3*6cm）', 'skuPrice': 1780}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25436182316, 'skuStock': 2, 'skuSpec': '30g*294个/箱', 'skuPrice': 27500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 1539390183, 'skuStock': 14, 'skuSpec': '紫薯味 6袋/箱（120个）', 'skuPrice': 13850}, {'skuId': 9878088664, 'skuStock': 87, 'skuSpec': '20个/袋', 'skuPrice': 2450}]\n", "Using proxy: *************************************************\n", "[{'skuId': 549523496, 'skuStock': 2, 'skuSpec': '5个*48盒/箱', 'skuPrice': 42600}, {'skuId': 549523497, 'skuStock': 101, 'skuSpec': '5个/盒', 'skuPrice': 900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 431847654, 'skuStock': 5, 'skuSpec': '30个*10袋/箱', 'skuPrice': 20680}, {'skuId': 11071594576, 'skuStock': 56, 'skuSpec': '30个/袋', 'skuPrice': 2200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25436281838, 'skuStock': 3, 'skuSpec': '9kg/箱', 'skuPrice': 22800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 845496418, 'skuStock': 5, 'skuSpec': '咸香芝士味', 'skuPrice': 21680}, {'skuId': 845496419, 'skuStock': 0, 'skuSpec': '蛋奶味', 'skuPrice': 19900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7314105002, 'skuStock': 120, 'skuSpec': '6片*1包', 'skuPrice': 1150}, {'skuId': 7314105003, 'skuStock': 12, 'skuSpec': '6片*10包*1箱', 'skuPrice': 8700}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37851319, 'skuStock': 8, 'skuSpec': '10袋/箱（50个）（10.5*7cm）', 'skuPrice': 22800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25345891674, 'skuStock': 2, 'skuSpec': '240个/箱', 'skuPrice': 23900}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2xgs6xc31bx4uc4', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9695936184, 'skuStock': 2, 'skuSpec': '草莓味50g速冻甜甜圈', 'skuPrice': 13800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1ykcdceaio4ym9w', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 6788188742, 'skuStock': 8, 'skuSpec': '150只*45g/箱', 'skuPrice': 18800}, {'skuId': 6795791246, 'skuStock': 89, 'skuSpec': '15只*45g/包', 'skuPrice': 2500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25345882592, 'skuStock': 2, 'skuSpec': '228个/箱', 'skuPrice': 28800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 722176359, 'skuStock': 6, 'skuSpec': '速冻老婆饼（20个*13层/箱）', 'skuPrice': 17500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3ngyc5psrob6m7w', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3evsmkplzawemxw', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37877501, 'skuStock': 4, 'skuSpec': '速冻榴莲酥', 'skuPrice': 27600}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xeaafu6wrb5q8b', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '26zdqulzll12mg6', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '276so7t2vr89qib', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25158099099, 'skuStock': 35, 'skuSpec': '680g/罐', 'skuPrice': 980}, {'skuId': 25158196900, 'skuStock': 2, 'skuSpec': '680g*12罐/箱', 'skuPrice': 11000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25087286221, 'skuStock': 29, 'skuSpec': '玉桂粉/肉桂粉 380g', 'skuPrice': 4800}, {'skuId': 25087286222, 'skuStock': 8, 'skuSpec': '细黑胡椒粉 425g', 'skuPrice': 8500}, {'skuId': 25087286223, 'skuStock': 5, 'skuSpec': '精选红甜椒粉 453g', 'skuPrice': 5600}, {'skuId': 25087286220, 'skuStock': 6, 'skuSpec': '黑胡椒整粒 450g', 'skuPrice': 7900}, {'skuId': 25087286224, 'skuStock': 6, 'skuSpec': '香菜籽粉397g', 'skuPrice': 3300}, {'skuId': 14641319603, 'skuStock': 12, 'skuSpec': '大蒜粉640g', 'skuPrice': 6200}, {'skuId': 14641319602, 'skuStock': 12, 'skuSpec': '洋葱粉500g', 'skuPrice': 3800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14503032590, 'skuStock': 42, 'skuSpec': '500g/包', 'skuPrice': 680}, {'skuId': 14503032591, 'skuStock': 1, 'skuSpec': '500g*24包/箱', 'skuPrice': 16800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14456941058, 'skuStock': 7, 'skuSpec': '1盒', 'skuPrice': 1920}, {'skuId': 14456941059, 'skuStock': 1, 'skuSpec': '5盒', 'skuPrice': 9100}, {'skuId': 14456941060, 'skuStock': 0, 'skuSpec': '12盒/箱', 'skuPrice': 19800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14502416775, 'skuStock': 15, 'skuSpec': '400g/罐', 'skuPrice': 680}, {'skuId': 14502416776, 'skuStock': 0, 'skuSpec': '400g*24罐/箱', 'skuPrice': 15800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25166379131, 'skuStock': 11, 'skuSpec': '820g/罐', 'skuPrice': 3800}, {'skuId': 25166379132, 'skuStock': 1, 'skuSpec': '820g*6罐/箱', 'skuPrice': 21800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25101875196, 'skuStock': 34, 'skuSpec': '1瓶', 'skuPrice': 1350}, {'skuId': 25101875197, 'skuStock': 2, 'skuSpec': '335g*12瓶/箱', 'skuPrice': 15500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14465704483, 'skuStock': 44, 'skuSpec': '小奶酪球150g(15个球)', 'skuPrice': 2280}, {'skuId': 14465720932, 'skuStock': 0, 'skuSpec': '小奶酪球150g(15个球)', 'skuPrice': 99800}, {'skuId': 14465704484, 'skuStock': 21, 'skuSpec': '中奶酪球100g(2个球)', 'skuPrice': 1500}, {'skuId': 14465720933, 'skuStock': 1, 'skuSpec': '中奶酪球100g(2个球)', 'skuPrice': 29800}, {'skuId': 14465704485, 'skuStock': 10, 'skuSpec': '大奶酪球250g(1个大球)', 'skuPrice': 2880}, {'skuId': 14465720934, 'skuStock': 0, 'skuSpec': '大奶酪球250g(1个大球)', 'skuPrice': 104000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14502621222, 'skuStock': 44, 'skuSpec': '400g/罐', 'skuPrice': 680}, {'skuId': 14502621223, 'skuStock': 1, 'skuSpec': '400g*24罐/箱', 'skuPrice': 15500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14465243941, 'skuStock': 28, 'skuSpec': '1盒', 'skuPrice': 2480}, {'skuId': 14465243942, 'skuStock': 2, 'skuSpec': '12盒/箱', 'skuPrice': 28500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25166476420, 'skuStock': 92, 'skuSpec': '500g/包', 'skuPrice': 750}, {'skuId': 25166476421, 'skuStock': 3, 'skuSpec': '500g*24包/箱', 'skuPrice': 14500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14466146574, 'skuStock': 24, 'skuSpec': '1块', 'skuPrice': 2580}, {'skuId': 14466146575, 'skuStock': 2, 'skuSpec': '10块', 'skuPrice': 24800}, {'skuId': 14466146576, 'skuStock': 0, 'skuSpec': '200g*25块/箱', 'skuPrice': 59500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25446195872, 'skuStock': 2, 'skuSpec': '250g/包', 'skuPrice': 6200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14496932180, 'skuStock': 19, 'skuSpec': '500g/包', 'skuPrice': 990}, {'skuId': 14496932181, 'skuStock': 0, 'skuSpec': '500g*24包/箱', 'skuPrice': 20800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25146896350, 'skuStock': 21, 'skuSpec': '500g/包', 'skuPrice': 990}, {'skuId': 25146896351, 'skuStock': 0, 'skuSpec': '500g*24包/箱', 'skuPrice': 20800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14502349438, 'skuStock': 24, 'skuSpec': '400g/罐', 'skuPrice': 680}, {'skuId': 14502349439, 'skuStock': 1, 'skuSpec': '400g*24罐/箱', 'skuPrice': 15800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25156684210, 'skuStock': 45, 'skuSpec': '400g/罐', 'skuPrice': 860}, {'skuId': 25156684211, 'skuStock': 1, 'skuSpec': '400g*24罐/箱', 'skuPrice': 18900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25146475662, 'skuStock': 23, 'skuSpec': '500g/包', 'skuPrice': 990}, {'skuId': 25146475663, 'skuStock': 0, 'skuSpec': '500g*24包/箱', 'skuPrice': 20800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14474808406, 'skuStock': 6, 'skuSpec': '1瓶', 'skuPrice': 3720}, {'skuId': 14474808407, 'skuStock': 1, 'skuSpec': '850g*6瓶/箱', 'skuPrice': 22000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25147077841, 'skuStock': 23, 'skuSpec': '500g/包', 'skuPrice': 1180}, {'skuId': 25147077842, 'skuStock': 0, 'skuSpec': '500g*24包/箱', 'skuPrice': 26800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14503015927, 'skuStock': 8, 'skuSpec': '3kg/包', 'skuPrice': 3950}, {'skuId': 14503015928, 'skuStock': 2, 'skuSpec': '3kg*4包/箱', 'skuPrice': 15300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14496445650, 'skuStock': 23, 'skuSpec': '500g/包', 'skuPrice': 1180}, {'skuId': 14496445651, 'skuStock': 0, 'skuSpec': '500g*24包/箱', 'skuPrice': 26500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14502700882, 'skuStock': 46, 'skuSpec': '400g/罐', 'skuPrice': 860}, {'skuId': 14502700883, 'skuStock': 1, 'skuSpec': '400g*24罐/箱', 'skuPrice': 18900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25319982420, 'skuStock': 1, 'skuSpec': '1KG/包', 'skuPrice': 8940}, {'skuId': 25319982421, 'skuStock': 0, 'skuSpec': '1kg*10包/箱', 'skuPrice': 85650}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14585436000, 'skuStock': 4, 'skuSpec': '1kg/包', 'skuPrice': 6880}, {'skuId': 14585436001, 'skuStock': 0, 'skuSpec': '1kg*10包/箱', 'skuPrice': 65890}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25157981946, 'skuStock': 11, 'skuSpec': '850g/罐', 'skuPrice': 3300}, {'skuId': 25157981947, 'skuStock': 0, 'skuSpec': '850g*6罐/箱', 'skuPrice': 19000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14474802532, 'skuStock': 23, 'skuSpec': '1瓶', 'skuPrice': 1250}, {'skuId': 14474802533, 'skuStock': 1, 'skuSpec': '335g*12瓶/箱', 'skuPrice': 14500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25156592495, 'skuStock': 28, 'skuSpec': '2.55KG/罐', 'skuPrice': 3350}, {'skuId': 25156592496, 'skuStock': 4, 'skuSpec': '2.55KG*6罐/箱', 'skuPrice': 20500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14586328344, 'skuStock': 5, 'skuSpec': '1kg/包', 'skuPrice': 6880}, {'skuId': 14586328345, 'skuStock': 0, 'skuSpec': '1kg*10包/箱', 'skuPrice': 65890}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14503732880, 'skuStock': 11, 'skuSpec': '335g/罐', 'skuPrice': 1420}, {'skuId': 14503732881, 'skuStock': 0, 'skuSpec': '335g*12罐/箱', 'skuPrice': 13800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14474748336, 'skuStock': 11, 'skuSpec': '1瓶', 'skuPrice': 1150}, {'skuId': 14474748337, 'skuStock': 0, 'skuSpec': '225g*12瓶/箱', 'skuPrice': 12000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25331488938, 'skuStock': 1, 'skuSpec': '2.26KG*6包/箱', 'skuPrice': 26800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25323897882, 'skuStock': 1, 'skuSpec': '1kg/包', 'skuPrice': 8130}, {'skuId': 25323897883, 'skuStock': 0, 'skuSpec': '1kg*10包/箱', 'skuPrice': 77860}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25166288148, 'skuStock': 83, 'skuSpec': '500ml/瓶', 'skuPrice': 8300}, {'skuId': 25166288149, 'skuStock': 13, 'skuSpec': '500ml*6瓶/箱', 'skuPrice': 36000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14503422948, 'skuStock': 10, 'skuSpec': '500g/罐', 'skuPrice': 13800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25158681789, 'skuStock': 22, 'skuSpec': '1L/瓶', 'skuPrice': 5300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25323187881, 'skuStock': 1, 'skuSpec': '2.26KG*6包/箱', 'skuPrice': 26800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14589312746, 'skuStock': 3, 'skuSpec': '800g±100g/条', 'skuPrice': 12200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25322777849, 'skuStock': 1, 'skuSpec': '2.04KG*6包/箱', 'skuPrice': 18900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14585623161, 'skuStock': 1, 'skuSpec': '2.26KG*6包/箱', 'skuPrice': 21800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25321793679, 'skuStock': 10, 'skuSpec': '1kg/包', 'skuPrice': 8250}, {'skuId': 25321793680, 'skuStock': 1, 'skuSpec': '1kg*10包/箱', 'skuPrice': 79060}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25321884659, 'skuStock': 1, 'skuSpec': '1kg/包', 'skuPrice': 9630}, {'skuId': 25321884660, 'skuStock': 0, 'skuSpec': '1kg*10包/箱', 'skuPrice': 92240}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25329890150, 'skuStock': 2, 'skuSpec': '800g±100g/条', 'skuPrice': 12000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25329887950, 'skuStock': 3, 'skuSpec': '800g±100g/根', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25322976537, 'skuStock': 1, 'skuSpec': '1.36KG*5包/箱', 'skuPrice': 24500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25086286014, 'skuStock': 1, 'skuSpec': '1块', 'skuPrice': 45500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25156389002, 'skuStock': 0, 'skuSpec': '500g/包', 'skuPrice': 820}, {'skuId': 25156389003, 'skuStock': 0, 'skuSpec': '500g*24包/箱', 'skuPrice': 18800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14650224721, 'skuStock': 0, 'skuSpec': '2kg/包', 'skuPrice': 3410}, {'skuId': 14650224722, 'skuStock': 0, 'skuSpec': '2KG*6包/箱', 'skuPrice': 17800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14474749259, 'skuStock': 0, 'skuSpec': '1瓶', 'skuPrice': 4220}, {'skuId': 14474749260, 'skuStock': 0, 'skuSpec': '820g*6瓶/箱', 'skuPrice': 25000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14503847739, 'skuStock': 0, 'skuSpec': '225g/罐', 'skuPrice': 990}, {'skuId': 14503847740, 'skuStock': 0, 'skuSpec': '225g*12罐/箱', 'skuPrice': 10800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14503828751, 'skuStock': 0, 'skuSpec': '335g/罐', 'skuPrice': 1320}, {'skuId': 14503828752, 'skuStock': 0, 'skuSpec': '335g*12罐/箱', 'skuPrice': 12800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5169701687, 'skuStock': 0, 'skuSpec': '原味苏打水', 'skuPrice': 1780}, {'skuId': 5169701688, 'skuStock': 0, 'skuSpec': '原味苏打水', 'skuPrice': 6190}, {'skuId': 5169701689, 'skuStock': 0, 'skuSpec': '柠檬味苏打水', 'skuPrice': 2050}, {'skuId': 5169701690, 'skuStock': 3, 'skuSpec': '柠檬味苏打水', 'skuPrice': 7180}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5169711120, 'skuStock': 11, 'skuSpec': '330ml*12瓶/箱', 'skuPrice': 5900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24291486947, 'skuStock': 7, 'skuSpec': '330ml*24瓶/箱', 'skuPrice': 12500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14145102258, 'skuStock': 0, 'skuSpec': '4L*6瓶/箱', 'skuPrice': 4600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37997496, 'skuStock': 1000, 'skuSpec': '950ml*12盒/箱', 'skuPrice': 16800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5619723627, 'skuStock': 8, 'skuSpec': '950ml*12瓶/箱', 'skuPrice': 19200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13709045654, 'skuStock': 5, 'skuSpec': '950ml*12盒/箱', 'skuPrice': 16800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25273092138, 'skuStock': 43, 'skuSpec': '125ml*9盒/箱', 'skuPrice': 2850}, {'skuId': 25273092139, 'skuStock': 4, 'skuSpec': '10箱', 'skuPrice': 27200}, {'skuId': 25439478129, 'skuStock': 21, 'skuSpec': '2箱', 'skuPrice': 5900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24868290179, 'skuStock': 680, 'skuSpec': '1L*12盒/箱', 'skuPrice': 7400}, {'skuId': 24868290180, 'skuStock': 136, 'skuSpec': '5箱', 'skuPrice': 36000}, {'skuId': 24868290181, 'skuStock': 68, 'skuSpec': '10箱', 'skuPrice': 68000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13487146659, 'skuStock': 305, 'skuSpec': '1L*12盒/箱', 'skuPrice': 8600}, {'skuId': 13782529537, 'skuStock': 61, 'skuSpec': '5箱', 'skuPrice': 42000}, {'skuId': 13782529538, 'skuStock': 30, 'skuSpec': '10箱', 'skuPrice': 82000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24515381610, 'skuStock': 31, 'skuSpec': '950ml*2盒', 'skuPrice': 3600}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oi2iji1l97y6dw', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14560906565, 'skuStock': 0, 'skuSpec': '200ml*12盒/箱', 'skuPrice': 5350}, {'skuId': 14560906566, 'skuStock': 0, 'skuSpec': '10箱', 'skuPrice': 51800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '26wxkmkt9devime', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37879126, 'skuStock': 54, 'skuSpec': '白片', 'skuPrice': 6100}, {'skuId': 37879127, 'skuStock': 5, 'skuSpec': '白片', 'skuPrice': 61500}, {'skuId': 708304548, 'skuStock': 145, 'skuSpec': '橙片', 'skuPrice': 7200}, {'skuId': 708304547, 'skuStock': 14, 'skuSpec': '橙片', 'skuPrice': 72000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37848799, 'skuStock': 329, 'skuSpec': '1KG', 'skuPrice': 5200}, {'skuId': 37854200, 'skuStock': 27, 'skuSpec': '1kg*12盒/箱', 'skuPrice': 60500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37837787, 'skuStock': 41, 'skuSpec': '5KG', 'skuPrice': 24000}, {'skuId': 37972788, 'skuStock': 10, 'skuSpec': '5KG*4包/箱', 'skuPrice': 95000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37845733, 'skuStock': 22, 'skuSpec': '2KG*1袋', 'skuPrice': 9900}, {'skuId': 37845734, 'skuStock': 3, 'skuSpec': '2KG*6袋/箱', 'skuPrice': 56300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 6344597651, 'skuStock': 1290, 'skuSpec': '500g/盒', 'skuPrice': 5200}, {'skuId': 23665985943, 'skuStock': 107, 'skuSpec': '2箱', 'skuPrice': 57000}, {'skuId': 6344597652, 'skuStock': 215, 'skuSpec': '500g/盒*6/箱', 'skuPrice': 29000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 3906133072, 'skuStock': 403, 'skuSpec': '1KG*1盒', 'skuPrice': 9300}, {'skuId': 3906133073, 'skuStock': 134, 'skuSpec': '1KG*3盒', 'skuPrice': 27600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7381015512, 'skuStock': 180, 'skuSpec': '960g*1包', 'skuPrice': 5730}, {'skuId': 7381015511, 'skuStock': 15, 'skuSpec': '960g*12包/箱', 'skuPrice': 66500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 10144494861, 'skuStock': 58, 'skuSpec': '3瓶', 'skuPrice': 5800}, {'skuId': 10144494862, 'skuStock': 7, 'skuSpec': '24瓶/箱', 'skuPrice': 45200}, {'skuId': 10560287308, 'skuStock': 176, 'skuSpec': '1瓶', 'skuPrice': 1980}]\n", "Using proxy: *************************************************\n", "[{'skuId': 7003212052, 'skuStock': 75, 'skuSpec': '3kg/包', 'skuPrice': 13600}, {'skuId': 7003212053, 'skuStock': 18, 'skuSpec': '3kg*4包/箱', 'skuPrice': 52800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14647338740, 'skuStock': 5, 'skuSpec': '黄片984g/袋', 'skuPrice': 5800}, {'skuId': 14647338741, 'skuStock': 0, 'skuSpec': '黄片984g*8袋/箱', 'skuPrice': 43100}, {'skuId': 25444476868, 'skuStock': 8, 'skuSpec': '白片984g/袋', 'skuPrice': 5600}, {'skuId': 25444476867, 'skuStock': 1, 'skuSpec': '白片984g*8袋/箱', 'skuPrice': 42200}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37866448, 'skuStock': 9, 'skuSpec': '2KG*5盒', 'skuPrice': 34000}, {'skuId': 10568689696, 'skuStock': 47, 'skuSpec': '2KG*1盒', 'skuPrice': 7000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14465704483, 'skuStock': 44, 'skuSpec': '小奶酪球150g(15个球)', 'skuPrice': 2280}, {'skuId': 14465720932, 'skuStock': 0, 'skuSpec': '小奶酪球150g(15个球)', 'skuPrice': 99800}, {'skuId': 14465704484, 'skuStock': 21, 'skuSpec': '中奶酪球100g(2个球)', 'skuPrice': 1500}, {'skuId': 14465720933, 'skuStock': 1, 'skuSpec': '中奶酪球100g(2个球)', 'skuPrice': 29800}, {'skuId': 14465704485, 'skuStock': 10, 'skuSpec': '大奶酪球250g(1个大球)', 'skuPrice': 2880}, {'skuId': 14465720934, 'skuStock': 0, 'skuSpec': '大奶酪球250g(1个大球)', 'skuPrice': 104000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37842883, 'skuStock': 25, 'skuSpec': '20KG', 'skuPrice': 82000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9803042120, 'skuStock': 85, 'skuSpec': '1.8kg*1盒', 'skuPrice': 9000}, {'skuId': 9803042121, 'skuStock': 28, 'skuSpec': '1.8kg*3盒/箱', 'skuPrice': 25800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 9803428994, 'skuStock': 174, 'skuSpec': '2KG*1袋', 'skuPrice': 10300}, {'skuId': 9803428995, 'skuStock': 29, 'skuSpec': '2KG*6袋/箱', 'skuPrice': 60000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14465243941, 'skuStock': 28, 'skuSpec': '1盒', 'skuPrice': 2480}, {'skuId': 14465243942, 'skuStock': 2, 'skuSpec': '12盒/箱', 'skuPrice': 28500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24783989424, 'skuStock': 28, 'skuSpec': '3瓶', 'skuPrice': 2920}, {'skuId': 24783989425, 'skuStock': 3, 'skuSpec': '24瓶/箱', 'skuPrice': 21500}, {'skuId': 24783989426, 'skuStock': 84, 'skuSpec': '1瓶', 'skuPrice': 990}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14457117615, 'skuStock': 9, 'skuSpec': '1KG/包', 'skuPrice': 10700}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37865267, 'skuStock': 1, 'skuSpec': '10KG*2块/箱', 'skuPrice': 87000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14456941058, 'skuStock': 7, 'skuSpec': '1盒', 'skuPrice': 1920}, {'skuId': 14456941059, 'skuStock': 1, 'skuSpec': '5盒', 'skuPrice': 9100}, {'skuId': 14456941060, 'skuStock': 0, 'skuSpec': '12盒/箱', 'skuPrice': 19800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25086286014, 'skuStock': 1, 'skuSpec': '1块', 'skuPrice': 45500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 11761885869, 'skuStock': 12, 'skuSpec': '10kg*1箱', 'skuPrice': 84500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14466146574, 'skuStock': 24, 'skuSpec': '1块', 'skuPrice': 2580}, {'skuId': 14466146575, 'skuStock': 2, 'skuSpec': '10块', 'skuPrice': 24800}, {'skuId': 14466146576, 'skuStock': 0, 'skuSpec': '200g*25块/箱', 'skuPrice': 59500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 25443689401, 'skuStock': 0, 'skuSpec': '3kg/袋', 'skuPrice': 12850}, {'skuId': 25443689402, 'skuStock': 0, 'skuSpec': '3KG*4袋/箱', 'skuPrice': 51200}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37851504, 'skuStock': 465, 'skuSpec': '1L*12盒', 'skuPrice': 58500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37853446, 'skuStock': 6, 'skuSpec': '1L*12盒/箱', 'skuPrice': 69500}, {'skuId': 23540189668, 'skuStock': 2, 'skuSpec': '3箱', 'skuPrice': 205500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 12755646813, 'skuStock': 118, 'skuSpec': '1L*12盒/箱', 'skuPrice': 52000}, {'skuId': 23123497955, 'skuStock': 0, 'skuSpec': '3箱送1包裱花袋', 'skuPrice': 156000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14021536580, 'skuStock': 157, 'skuSpec': '1L*1瓶', 'skuPrice': 3900}, {'skuId': 14021536581, 'skuStock': 26, 'skuSpec': '1L*6瓶/箱', 'skuPrice': 22500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 12753013257, 'skuStock': 258, 'skuSpec': '1KG*1瓶', 'skuPrice': 3500}, {'skuId': 12753013258, 'skuStock': 21, 'skuSpec': '1KG*12盒/箱', 'skuPrice': 36500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nt9rw4xjehzizd', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 6588827544, 'skuStock': 341, 'skuSpec': '1L*1盒', 'skuPrice': 4000}, {'skuId': 6588827543, 'skuStock': 28, 'skuSpec': '1箱', 'skuPrice': 44500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 11652678671, 'skuStock': 1570, 'skuSpec': '1L*1瓶', 'skuPrice': 4800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 3271640975, 'skuStock': 10, 'skuSpec': '1L*12瓶/箱', 'skuPrice': 43500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3ey9bad8mcxby0f', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37949105, 'skuStock': 65, 'skuSpec': '1L*12盒/箱', 'skuPrice': 45500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23615790227, 'skuStock': 34, 'skuSpec': '700ml*1罐', 'skuPrice': 5900}, {'skuId': 13332543761, 'skuStock': 5, 'skuSpec': '700ml*6罐/箱', 'skuPrice': 35000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14188311729, 'skuStock': 107, 'skuSpec': '500g*1瓶', 'skuPrice': 3700}, {'skuId': 37959896, 'skuStock': 8, 'skuSpec': '500g*12瓶/箱', 'skuPrice': 42000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24981081954, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 33500}, {'skuId': 24981081955, 'skuStock': 0, 'skuSpec': '1L*1盒', 'skuPrice': 3000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9399218880, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 39500}, {'skuId': 23616694638, 'skuStock': 0, 'skuSpec': '1L*1盒', 'skuPrice': 3625}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13736800037, 'skuStock': 0, 'skuSpec': '950ml*12盒/箱', 'skuPrice': 55800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 9630343259, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 49000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24289991876, 'skuStock': 0, 'skuSpec': '1L*12盒/箱', 'skuPrice': 50500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37845790, 'skuStock': 19, 'skuSpec': '25KG', 'skuPrice': 154000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37870121, 'skuStock': 107, 'skuSpec': '5KG*1包', 'skuPrice': 33000}, {'skuId': 37870122, 'skuStock': 26, 'skuSpec': '5KG*4包/箱', 'skuPrice': 129500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 8715823322, 'skuStock': 351, 'skuSpec': '1kg*1片', 'skuPrice': 12000}, {'skuId': 37877296, 'skuStock': 35, 'skuSpec': '1KG*10片/箱', 'skuPrice': 110000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24812885814, 'skuStock': 141, 'skuSpec': '5KG*1包', 'skuPrice': 18900}, {'skuId': 24812885815, 'skuStock': 35, 'skuSpec': '5KG*4包/箱', 'skuPrice': 72600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14191746620, 'skuStock': 48, 'skuSpec': '1KG*1片', 'skuPrice': 9600}, {'skuId': 37840693, 'skuStock': 4, 'skuSpec': '1KG*10片/箱', 'skuPrice': 78000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 2443117615, 'skuStock': 12, 'skuSpec': '227g*40块/箱', 'skuPrice': 68800}, {'skuId': 4887876221, 'skuStock': 48, 'skuSpec': '227g*10块/组', 'skuPrice': 18500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 19206181870, 'skuStock': 22, 'skuSpec': '2kg*1片', 'skuPrice': 28500}, {'skuId': 19206181871, 'skuStock': 4, 'skuSpec': '2kg*5片/箱', 'skuPrice': 138000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 8350081834, 'skuStock': 14, 'skuSpec': '1KG*20片/箱', 'skuPrice': 142800}, {'skuId': 8350081835, 'skuStock': 293, 'skuSpec': '1KG/片', 'skuPrice': 7800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14595646432, 'skuStock': 5, 'skuSpec': '10kg/箱', 'skuPrice': 73500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13801216361, 'skuStock': 159, 'skuSpec': '454g*1块', 'skuPrice': 2750}, {'skuId': 13801216362, 'skuStock': 7, 'skuSpec': '454g*20块/箱', 'skuPrice': 44500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5035349508, 'skuStock': 10, 'skuSpec': '10kg/箱', 'skuPrice': 105000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nt9lq9rzmsm6q4', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14184441881, 'skuStock': 42, 'skuSpec': '2kg/片', 'skuPrice': 19800}, {'skuId': 14184441882, 'skuStock': 8, 'skuSpec': '2kg*5片/箱', 'skuPrice': 78000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37858617, 'skuStock': 1, 'skuSpec': '7g*288粒', 'skuPrice': 18800}, {'skuId': 8416012550, 'skuStock': 14, 'skuSpec': '7g*36粒', 'skuPrice': 2880}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37825427, 'skuStock': 0, 'skuSpec': '25KG', 'skuPrice': 165000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 8715919309, 'skuStock': 0, 'skuSpec': '1kg*1片', 'skuPrice': 13500}, {'skuId': 37866507, 'skuStock': 0, 'skuSpec': '1KG*10片/箱', 'skuPrice': 125000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 23813680619, 'skuStock': 0, 'skuSpec': '250g*1块', 'skuPrice': 3300}, {'skuId': 13546532492, 'skuStock': 0, 'skuSpec': '250g*3块', 'skuPrice': 9600}, {'skuId': 13546532491, 'skuStock': 0, 'skuSpec': '250g*20块/箱', 'skuPrice': 59500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 10012094397, 'skuStock': 0, 'skuSpec': '250g*20条/箱', 'skuPrice': 59500}, {'skuId': 10012094398, 'skuStock': 0, 'skuSpec': '250g*3条', 'skuPrice': 9600}, {'skuId': 10012094399, 'skuStock': 0, 'skuSpec': '250g*1条', 'skuPrice': 3300}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13802911388, 'skuStock': 0, 'skuSpec': '250g*1卷', 'skuPrice': 3800}, {'skuId': 13802911389, 'skuStock': 0, 'skuSpec': '250g*3卷', 'skuPrice': 9800}, {'skuId': 13802911390, 'skuStock': 0, 'skuSpec': '250g*20卷/箱', 'skuPrice': 59500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14657745123, 'skuStock': 0, 'skuSpec': '25KG', 'skuPrice': 87500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 11603287650, 'skuStock': 0, 'skuSpec': '24卷/箱', 'skuPrice': 75500}, {'skuId': 11603287651, 'skuStock': 0, 'skuSpec': '250g*3卷', 'skuPrice': 9600}, {'skuId': 12410893881, 'skuStock': 0, 'skuSpec': '250g*1卷', 'skuPrice': 3300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 5576205627, 'skuStock': 0, 'skuSpec': '2kg*1片', 'skuPrice': 21500}, {'skuId': 5576205628, 'skuStock': 0, 'skuSpec': '2kg*5片/箱', 'skuPrice': 98500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13857543004, 'skuStock': 0, 'skuSpec': '2kg*5片/箱', 'skuPrice': 85500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '27ah6wmg7cb1apf', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37844783, 'skuStock': 13, 'skuSpec': '10袋/箱（10kg）', 'skuPrice': 24000}, {'skuId': 4802334580, 'skuStock': 136, 'skuSpec': '1kg/包', 'skuPrice': 2880}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9748975997, 'skuStock': 172, 'skuSpec': '1kg/包', 'skuPrice': 3720}, {'skuId': 9748975998, 'skuStock': 14, 'skuSpec': '1kg*12包/箱', 'skuPrice': 43500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37845370, 'skuStock': 11, 'skuSpec': '10袋/箱（10kg）', 'skuPrice': 35800}, {'skuId': 10011398529, 'skuStock': 113, 'skuSpec': '1kg/包', 'skuPrice': 3880}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37987769, 'skuStock': 8, 'skuSpec': '10袋/箱（10kg）', 'skuPrice': 20800}, {'skuId': 4802645962, 'skuStock': 84, 'skuSpec': '1kg/包', 'skuPrice': 2500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fzbxh2ren67y3n', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37871157, 'skuStock': 5, 'skuSpec': '12袋/箱（10kg）', 'skuPrice': 43800}, {'skuId': 9619793162, 'skuStock': 60, 'skuSpec': '1kg/包', 'skuPrice': 3900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 11368583763, 'skuStock': 79, 'skuSpec': '1.2kg*1包', 'skuPrice': 4950}, {'skuId': 11368583764, 'skuStock': 7, 'skuSpec': '1.2kg*10包/箱', 'skuPrice': 43900}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37845890, 'skuStock': 5, 'skuSpec': '8袋/箱（10.4kg）', 'skuPrice': 28000}, {'skuId': 4802918872, 'skuStock': 45, 'skuSpec': '1.3kg/袋', 'skuPrice': 3800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9618180381, 'skuStock': 47, 'skuSpec': '1kg/袋', 'skuPrice': 3880}, {'skuId': 9618180382, 'skuStock': 3, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 38800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9618694683, 'skuStock': 33, 'skuSpec': '1kg/包', 'skuPrice': 3060}, {'skuId': 9618694684, 'skuStock': 3, 'skuSpec': '1kg*10包/箱', 'skuPrice': 28800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9617796706, 'skuStock': 102, 'skuSpec': '1kg/袋', 'skuPrice': 3980}, {'skuId': 9617796707, 'skuStock': 8, 'skuSpec': '1kg*12袋/箱', 'skuPrice': 44600}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37859313, 'skuStock': 9, 'skuSpec': '12袋/箱（12kg）', 'skuPrice': 43800}, {'skuId': 4802420793, 'skuStock': 116, 'skuSpec': '1kg/包', 'skuPrice': 3880}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37834481, 'skuStock': 5, 'skuSpec': '4袋/箱（8.8kg）', 'skuPrice': 18800}, {'skuId': 5035404669, 'skuStock': 22, 'skuSpec': '2.2kg/袋', 'skuPrice': 5100}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5076516841, 'skuStock': 104, 'skuSpec': '1.3kg/袋', 'skuPrice': 4800}, {'skuId': 5076516842, 'skuStock': 13, 'skuSpec': '1.3kg*8袋/箱', 'skuPrice': 36800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 37882309, 'skuStock': 6, 'skuSpec': '30g/根', 'skuPrice': 35800}, {'skuId': 37882308, 'skuStock': 66, 'skuSpec': '30g/根', 'skuPrice': 3880}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9617177297, 'skuStock': 8, 'skuSpec': '2.4kg（50片）/包', 'skuPrice': 4860}, {'skuId': 9617177298, 'skuStock': 2, 'skuSpec': '2.4kg（50片）/包*4/箱', 'skuPrice': 18500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14308975039, 'skuStock': 24, 'skuSpec': '1.25kg/包', 'skuPrice': 2450}, {'skuId': 14308975040, 'skuStock': 3, 'skuSpec': '1.25kg*8包/箱', 'skuPrice': 16850}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5807645929, 'skuStock': 10, 'skuSpec': '1kg/包', 'skuPrice': 12300}]\n", "Using proxy: *************************************************\n", "[{'skuId': 37844306, 'skuStock': 1, 'skuSpec': '8袋/箱（8kg）', 'skuPrice': 13500}, {'skuId': 11713493018, 'skuStock': 12, 'skuSpec': '1kg*1袋', 'skuPrice': 1950}]\n", "Using proxy: *************************************************\n", "[{'skuId': 15419281593, 'skuStock': 36, 'skuSpec': '1kg*1包', 'skuPrice': 3950}, {'skuId': 15419281594, 'skuStock': 3, 'skuSpec': '1kg*12包/箱', 'skuPrice': 43750}]\n", "Using proxy: *************************************************\n", "[{'skuId': 13488512843, 'skuStock': 16, 'skuSpec': '2KG*1包', 'skuPrice': 6800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24523877311, 'skuStock': 15, 'skuSpec': '1kg*1袋', 'skuPrice': 2950}, {'skuId': 24523877312, 'skuStock': 1, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 27500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13661349097, 'skuStock': 13, 'skuSpec': '1KG*1包', 'skuPrice': 6600}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13462430152, 'skuStock': 23, 'skuSpec': '1KG*1包', 'skuPrice': 4500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24523794699, 'skuStock': 10, 'skuSpec': '1kg*1袋', 'skuPrice': 2400}, {'skuId': 24523875500, 'skuStock': 1, 'skuSpec': '1kg*10袋/箱', 'skuPrice': 22000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 13773112942, 'skuStock': 14, 'skuSpec': '1KG*1袋', 'skuPrice': 2200}, {'skuId': 13773112943, 'skuStock': 1, 'skuSpec': '1KG*10袋/箱', 'skuPrice': 21000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3etb2cbor1u0ui4', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oi1h2rusku1aba', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3njdzi6r52i3yvh', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2flr9zwldieamn5', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oo82ajxibp4ebl', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2xd35p5i8uyb2xf', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2okj1c2gi8ajyx1', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1y6srbyopfpsu9n', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2x85fiap6uzmm0l', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2fzca0ntzz4m65g', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2g4aclu1p6kda5x', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2798br9cqujnyec', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oi0ymxwlbyoutc', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3f1xheq35m326uf', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fn21ymnr24ameo', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xhzhsz08fdby0v', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2ofk3dde9b70err', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3637t71n5r30uz1', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3ns0e3wl8hmoejk', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '275ko9sdffnu6af', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2fwwagvizox7ylh', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '26zcvo6ekjttqx9', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14503148460, 'skuStock': 0, 'skuSpec': '平安之果 100g*9个/盒', 'skuPrice': 11000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '35y8vixi6d1z2bb', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3etb2cbor1u0ui4', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2oi1h2rusku1aba', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3njdzi6r52i3yvh', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2flr9zwldieamn5', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oo82ajxibp4ebl', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2xd35p5i8uyb2xf', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2okj1c2gi8ajyx1', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1y6srbyopfpsu9n', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2x85fiap6uzmm0l', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fzca0ntzz4m65g', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2g4aclu1p6kda5x', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2798br9cqujnyec', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oi0ymxwlbyoutc', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3f1xheq35m326uf', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fn21ymnr24ameo', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2xhzhsz08fdby0v', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2ofk3dde9b70err', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3637t71n5r30uz1', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3ns0e3wl8hmoejk', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '275ko9sdffnu6af', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2fwwagvizox7ylh', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '26zcvo6ekjttqx9', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14503148460, 'skuStock': 0, 'skuSpec': '平安之果 100g*9个/盒', 'skuPrice': 11000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '35y8vixi6d1z2bb', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nkmo9tjx90wuk3', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2olquvgbheq9ac9', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nknpmz8tdi32us', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xmxkjempl7em1f', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3njfpxqcoe5322f', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2g0lu4wk52nmmxn', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 10202397757, 'skuStock': 7, 'skuSpec': '8寸10切', 'skuPrice': 7900}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oj9at7qzdicuil', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2olrdnjmcvzfi5v', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2orwekrk6420e4i', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2fvnrmcooagbylk', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 5023200325, 'skuStock': 23, 'skuSpec': '8寸10切', 'skuPrice': 7500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fphpb2vk1db2xe', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2olsex8vy7iwe09', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2foa7zvosoe7izu', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2781d2xo08e66tg', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2od54y1x2ue8ebv', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '35x11h3peko5ac9', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oecytpmit22may', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1yhwpzyoxferi91', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1yaj043epgae6ii', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3ezf8a38i1fymrk', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fqqdxe5qum66of', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fltvs3lnwk66q3', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '271tebgsihoq6a6', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nwzi6o2mibpqeu', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1ycytlj306qvyjp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '360q8xr1g2xb20q', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1ybr61tww87umu1', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2g32ist1wwijiu7', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3etblbfb1u6kecp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3npl9gygo1l4ubo', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2xja9csin72z2r5', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2omyuyr6foonypy', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nob0eq1bseu6y5', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2ot4846165frynl', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 3538448592, 'skuStock': 1, 'skuSpec': '8寸10切', 'skuPrice': 7500}]\n", "Using proxy: ************************************************\n", "[{'skuId': '27334bxenr9umnx', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '36am1ipru1be6zv', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '366w54zjvoxdq6b', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nwzi5sdj9uf2ak', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1yfgw2r0ulqwug1', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '364fa3af7zuse7p', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '36e9679hh8d7i4i', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xi1r2fe6wa4ub3', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3eodcbn3vpswemn', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '365nyxjfrj8lar8', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3noao5mk9u12m5i', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3ni5zpeq5mzgeh2', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '35vrnt9tt0hjixp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3eqstocs5ocem1k', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nkmbx6khwfumhc', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '26zcpk25dinpacd', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 7088789055, 'skuStock': 12, 'skuSpec': '8寸10切', 'skuPrice': 8000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2x6x9coox78ry29', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3636rjbn0xfoe7d', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nqskjmo3clb2jo', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nfo9en6h777ix0', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3nkn0yowp5mbiiw', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oucr0gvn0toui8', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24484078453, 'skuStock': 10, 'skuSpec': '原味', 'skuPrice': 950}, {'skuId': 14156849518, 'skuStock': 0, 'skuSpec': '原味', 'skuPrice': 25200}, {'skuId': 24484078454, 'skuStock': 190, 'skuSpec': '奥利奥', 'skuPrice': 1050}, {'skuId': 14156849516, 'skuStock': 6, 'skuSpec': '奥利奥', 'skuPrice': 27800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 9077807809, 'skuStock': 10, 'skuSpec': '榴莲味', 'skuPrice': 8500}, {'skuId': 9077807810, 'skuStock': 45, 'skuSpec': '抹茶味', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14358704481, 'skuStock': 34, 'skuSpec': '1盒', 'skuPrice': 1000}, {'skuId': 14358704482, 'skuStock': 1, 'skuSpec': '20盒', 'skuPrice': 19000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24981796564, 'skuStock': 8, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 24981796565, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14413439638, 'skuStock': 9, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413439639, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '35zixxpnp8d8ebp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14413423294, 'skuStock': 13, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413423295, 'skuStock': 2, 'skuSpec': '60盒/箱', 'skuPrice': 60000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fphpb2vk1db2xe', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14413449278, 'skuStock': 10, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413449279, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2olsex8vy7iwe09', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24981778555, 'skuStock': 20, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 24981778556, 'skuStock': 3, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24981887959, 'skuStock': 3, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 24981887960, 'skuStock': 0, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14413512758, 'skuStock': 5, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413512759, 'skuStock': 0, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24981793579, 'skuStock': 6, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 24981793580, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14413529720, 'skuStock': 8, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413529721, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 3538448592, 'skuStock': 1, 'skuSpec': '8寸10切', 'skuPrice': 7500}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2xgr5gm56s9mml0', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '364fa3af7zuse7p', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14413343396, 'skuStock': 9, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413343397, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '35vu3b6q5h9u6qb', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1ykdejg3wjh6my6', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fy2wa8ltjczi9b', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1yaj043epgae6ii', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3ezf8a38i1fymrk', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2g0kmd31agqtabp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2ot4846165frynl', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oi1hda2htunihn', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3f4eolz1zjbqm04', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '27ahvx8upomkull', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '35vrnt9tt0hjixp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oqpmbz0tuskep2', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '275jgpuq8j9z2v9', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oucr0gvn0toui8', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25435886758, 'skuStock': 1, 'skuSpec': '一叶芒香芝士迷你挞 48粒/盒', 'skuPrice': 12500}, {'skuId': 25436177502, 'skuStock': 1, 'skuSpec': '粉墨蝴蝶结芝士迷你挞 48粒/盒', 'skuPrice': 12500}, {'skuId': 25436097597, 'skuStock': 1, 'skuSpec': '小花抹茶芝士迷你挞 48粒/盒', 'skuPrice': 12500}, {'skuId': 25436097598, 'skuStock': 0, 'skuSpec': '黑玉巧克力纸杯蛋糕 40粒/盒', 'skuPrice': 12500}, {'skuId': 25436097595, 'skuStock': 1, 'skuSpec': '粉莓檬趣纸杯蛋糕 40粒/盒', 'skuPrice': 12500}, {'skuId': 25436097599, 'skuStock': 0, 'skuSpec': '赤龙红丝绒纸杯蛋糕 40粒/盒', 'skuPrice': 12500}, {'skuId': 25436097596, 'skuStock': 1, 'skuSpec': '仙人掌可可乐乐杯蛋糕 30杯/盒', 'skuPrice': 12500}, {'skuId': 25436177500, 'skuStock': 1, 'skuSpec': '芋见四叶草乐乐杯蛋糕 30杯/盒', 'skuPrice': 12500}, {'skuId': 25436097594, 'skuStock': 1, 'skuSpec': '甜沁草莓乐乐杯蛋糕 30杯/盒', 'skuPrice': 12500}, {'skuId': 25436177501, 'skuStock': 5, 'skuSpec': '无花果茉莉慕斯蛋糕 70粒/盒', 'skuPrice': 7500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24887780213, 'skuStock': 71, 'skuSpec': '北海道恋人(坚果塔)', 'skuPrice': 1000}, {'skuId': 24887780214, 'skuStock': 7, 'skuSpec': '北海道恋人(坚果塔)', 'skuPrice': 9500}, {'skuId': 24887780215, 'skuStock': 93, 'skuSpec': '印第安之舟', 'skuPrice': 1100}, {'skuId': 24887780216, 'skuStock': 9, 'skuSpec': '印第安之舟', 'skuPrice': 10500}, {'skuId': 24887780219, 'skuStock': 49, 'skuSpec': '无蔗糖匠心手作酥饼', 'skuPrice': 900}, {'skuId': 24887780220, 'skuStock': 4, 'skuSpec': '无蔗糖匠心手作酥饼', 'skuPrice': 8500}, {'skuId': 24887780221, 'skuStock': 0, 'skuSpec': '莓你不行', 'skuPrice': 900}, {'skuId': 24887780222, 'skuStock': 0, 'skuSpec': '莓你不行', 'skuPrice': 8500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24484078453, 'skuStock': 10, 'skuSpec': '原味', 'skuPrice': 950}, {'skuId': 14156849518, 'skuStock': 0, 'skuSpec': '原味', 'skuPrice': 25200}, {'skuId': 24484078454, 'skuStock': 190, 'skuSpec': '奥利奥', 'skuPrice': 1050}, {'skuId': 14156849516, 'skuStock': 6, 'skuSpec': '奥利奥', 'skuPrice': 27800}]\n", "Using proxy: *************************************************\n", "[{'skuId': '1yhvulkw97jz2jw', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9078223287, 'skuStock': 18, 'skuSpec': '提拉米苏', 'skuPrice': 6800}, {'skuId': 9078223288, 'skuStock': 16, 'skuSpec': '抹茶芝士', 'skuPrice': 6800}, {'skuId': 9078223289, 'skuStock': 12, 'skuSpec': '草莓芝士', 'skuPrice': 6800}, {'skuId': 9078223290, 'skuStock': 11, 'skuSpec': '红丝绒', 'skuPrice': 6800}, {'skuId': 9078223291, 'skuStock': 7, 'skuSpec': '芒果芝士', 'skuPrice': 6800}, {'skuId': 9078223293, 'skuStock': 7, 'skuSpec': '黄桃芝士', 'skuPrice': 6800}, {'skuId': 9078223294, 'skuStock': 9, 'skuSpec': '蓝莓芝士', 'skuPrice': 6800}, {'skuId': 9078223295, 'skuStock': 6, 'skuSpec': '雪域牛乳芝士', 'skuPrice': 6800}, {'skuId': 9078223296, 'skuStock': 3, 'skuSpec': '奥利奥芝士', 'skuPrice': 6800}, {'skuId': 9078223297, 'skuStock': 6, 'skuSpec': '黑巧克力芝士', 'skuPrice': 6800}, {'skuId': 14663200721, 'skuStock': 2, 'skuSpec': '卡布基诺', 'skuPrice': 6800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14189305989, 'skuStock': 36, 'skuSpec': '生巧 1盒', 'skuPrice': 980}, {'skuId': 14189813049, 'skuStock': 3, 'skuSpec': '生巧 10盒', 'skuPrice': 9000}, {'skuId': 14189305990, 'skuStock': 159, 'skuSpec': '香草冰淇淋 1盒', 'skuPrice': 980}, {'skuId': 14189813048, 'skuStock': 15, 'skuSpec': '香草冰淇淋 10盒', 'skuPrice': 9000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14358714308, 'skuStock': 137, 'skuSpec': '1盒', 'skuPrice': 1090}, {'skuId': 14358714309, 'skuStock': 6, 'skuSpec': '20盒', 'skuPrice': 21100}]\n", "Using proxy: *************************************************\n", "[{'skuId': 11955377841, 'skuStock': 18, 'skuSpec': '72块多C金桔慕斯', 'skuPrice': 6900}, {'skuId': 11955377842, 'skuStock': 18, 'skuSpec': '72块芝芝抹茶', 'skuPrice': 6900}, {'skuId': 11955377843, 'skuStock': 18, 'skuSpec': '72块波波黑糖慕斯', 'skuPrice': 6900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 23762495602, 'skuStock': 18, 'skuSpec': '原味', 'skuPrice': 17500}, {'skuId': 23762495603, 'skuStock': 5, 'skuSpec': '草莓味', 'skuPrice': 18500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9077807809, 'skuStock': 10, 'skuSpec': '榴莲味', 'skuPrice': 8500}, {'skuId': 9077807810, 'skuStock': 45, 'skuSpec': '抹茶味', 'skuPrice': 8500}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14189135176, 'skuStock': 63, 'skuSpec': '4盒', 'skuPrice': 5000}, {'skuId': 14189135177, 'skuStock': 12, 'skuSpec': '20盒', 'skuPrice': 24000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3ezfr0gmv9bj2c5', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 11955292364, 'skuStock': 18, 'skuSpec': '88切提拉米苏', 'skuPrice': 6800}, {'skuId': 11955292365, 'skuStock': 19, 'skuSpec': '88切雪域牛乳', 'skuPrice': 6800}, {'skuId': 6001007116, 'skuStock': 18, 'skuSpec': '88切抹茶', 'skuPrice': 6800}, {'skuId': 18103598178, 'skuStock': 5, 'skuSpec': '88切草莓', 'skuPrice': 6800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3f1xb68u59wi6tg', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2orweob2as1pqiq', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24874375705, 'skuStock': 27, 'skuSpec': '1盒', 'skuPrice': 800}, {'skuId': 24874375706, 'skuStock': 1, 'skuSpec': '20盒', 'skuPrice': 15500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 9695936184, 'skuStock': 2, 'skuSpec': '草莓味50g速冻甜甜圈', 'skuPrice': 13800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 25476577031, 'skuStock': 2, 'skuSpec': '落樱莓莓芝士蛋糕 220G/盒（10粒）', 'skuPrice': 6850}, {'skuId': 25476577032, 'skuStock': 1, 'skuSpec': '琉璃蓝柑芝士蛋糕 220G/盒（10粒）', 'skuPrice': 99900}, {'skuId': 25476577033, 'skuStock': 1, 'skuSpec': '抹茶松风芝士蛋糕 240G/盒（10粒）', 'skuPrice': 99900}, {'skuId': 25476577034, 'skuStock': 1, 'skuSpec': '桃天菠萝芝士蛋糕 340G/盒（10粒）', 'skuPrice': 99900}, {'skuId': 25476577035, 'skuStock': 1, 'skuSpec': '乳白芒芒芝士蛋糕 330G/盒（10粒）', 'skuPrice': 99900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 11955292622, 'skuStock': 0, 'skuSpec': '66块元气西柚慕斯', 'skuPrice': 6900}, {'skuId': 6000946237, 'skuStock': 0, 'skuSpec': '66块浓情提拉米苏', 'skuPrice': 6900}, {'skuId': 11955292624, 'skuStock': 0, 'skuSpec': '66块梦幻香芋慕斯', 'skuPrice': 6900}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24484078453, 'skuStock': 10, 'skuSpec': '原味', 'skuPrice': 950}, {'skuId': 14156849518, 'skuStock': 0, 'skuSpec': '原味', 'skuPrice': 25200}, {'skuId': 24484078454, 'skuStock': 190, 'skuSpec': '奥利奥', 'skuPrice': 1050}, {'skuId': 14156849516, 'skuStock': 6, 'skuSpec': '奥利奥', 'skuPrice': 27800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14189305989, 'skuStock': 36, 'skuSpec': '生巧 1盒', 'skuPrice': 980}, {'skuId': 14189813049, 'skuStock': 3, 'skuSpec': '生巧 10盒', 'skuPrice': 9000}, {'skuId': 14189305990, 'skuStock': 159, 'skuSpec': '香草冰淇淋 1盒', 'skuPrice': 980}, {'skuId': 14189813048, 'skuStock': 15, 'skuSpec': '香草冰淇淋 10盒', 'skuPrice': 9000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14358714308, 'skuStock': 137, 'skuSpec': '1盒', 'skuPrice': 1090}, {'skuId': 14358714309, 'skuStock': 6, 'skuSpec': '20盒', 'skuPrice': 21100}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14189135176, 'skuStock': 63, 'skuSpec': '4盒', 'skuPrice': 5000}, {'skuId': 14189135177, 'skuStock': 12, 'skuSpec': '20盒', 'skuPrice': 24000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14155706098, 'skuStock': 33, 'skuSpec': '1盒', 'skuPrice': 2050}, {'skuId': 14162801389, 'skuStock': 3, 'skuSpec': '10盒', 'skuPrice': 20000}, {'skuId': 14162801388, 'skuStock': 1, 'skuSpec': '24盒/箱', 'skuPrice': 47000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14358704481, 'skuStock': 34, 'skuSpec': '1盒', 'skuPrice': 1000}, {'skuId': 14358704482, 'skuStock': 1, 'skuSpec': '20盒', 'skuPrice': 19000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oi1h2rusku1aba', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24981796564, 'skuStock': 8, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 24981796565, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xd35p5i8uyb2xf', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2olrdnjmcvzfi5v', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24874375705, 'skuStock': 27, 'skuSpec': '1盒', 'skuPrice': 800}, {'skuId': 24874375706, 'skuStock': 1, 'skuSpec': '20盒', 'skuPrice': 15500}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14413439638, 'skuStock': 9, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413439639, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2oqor6e0q8c3ie7', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '35zixxpnp8d8ebp', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14155732350, 'skuStock': 46, 'skuSpec': '1盒', 'skuPrice': 1050}, {'skuId': 24493898515, 'skuStock': 2, 'skuSpec': '20盒', 'skuPrice': 20000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14413423294, 'skuStock': 13, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413423295, 'skuStock': 2, 'skuSpec': '60盒/箱', 'skuPrice': 60000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14413449278, 'skuStock': 10, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413449279, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24981778555, 'skuStock': 20, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 24981778556, 'skuStock': 3, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14155718074, 'skuStock': 31, 'skuSpec': '1盒', 'skuPrice': 950}, {'skuId': 14162903027, 'skuStock': 1, 'skuSpec': '20盒', 'skuPrice': 18000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2fy3xw6hexnxakx', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14155620680, 'skuStock': 26, 'skuSpec': '1盒', 'skuPrice': 1250}, {'skuId': 24493787403, 'skuStock': 1, 'skuSpec': '20盒', 'skuPrice': 24000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 24981887959, 'skuStock': 3, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 24981887960, 'skuStock': 0, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14155719537, 'skuStock': 160, 'skuSpec': '1盒', 'skuPrice': 1250}, {'skuId': 14162821755, 'skuStock': 10, 'skuSpec': '15盒', 'skuPrice': 18000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14413512758, 'skuStock': 5, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413512759, 'skuStock': 0, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 24981793579, 'skuStock': 6, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 24981793580, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14189323314, 'skuStock': 32, 'skuSpec': '2粒*1盒', 'skuPrice': 1050}, {'skuId': 14189323315, 'skuStock': 1, 'skuSpec': '20盒', 'skuPrice': 20000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14413529720, 'skuStock': 8, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413529721, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1y9b00rfs5ppazw', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2ot59q609q2rit5', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14155601544, 'skuStock': 8, 'skuSpec': '1盒', 'skuPrice': 1650}, {'skuId': 24493978499, 'skuStock': 0, 'skuSpec': '20盒', 'skuPrice': 32000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xja9dmqbld72pj', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14189306088, 'skuStock': 30, 'skuSpec': '2粒*1盒', 'skuPrice': 1050}, {'skuId': 14189306089, 'skuStock': 1, 'skuSpec': '20盒', 'skuPrice': 20000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2xj80c51rklnilx', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14155635603, 'skuStock': 47, 'skuSpec': '1盒', 'skuPrice': 1250}, {'skuId': 14162904815, 'skuStock': 2, 'skuSpec': '20盒', 'skuPrice': 24000}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14413343396, 'skuStock': 9, 'skuSpec': '10盒/组', 'skuPrice': 10500}, {'skuId': 14413343397, 'skuStock': 1, 'skuSpec': '60盒/箱', 'skuPrice': 59800}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14663442800, 'skuStock': 1, 'skuSpec': '东海龙王榴芒慕斯 95g*6个/盒', 'skuPrice': 8800}, {'skuId': 14663442801, 'skuStock': 1, 'skuSpec': '飞龙在天熔岩慕斯 95g*6个/盒', 'skuPrice': 8800}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1yhwdbyapjz1anu', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2omyca5abp1z28q', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3nfqohnnbc1imw2', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2ojaov9a928qm19', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '3637za8hfb6oe5i', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': '1y4bjqppqt7lquq', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': '3f4f7mtbb53u6gy', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14155730807, 'skuStock': 0, 'skuSpec': '1盒', 'skuPrice': 1550}, {'skuId': 14162937336, 'skuStock': 0, 'skuSpec': '20盒', 'skuPrice': 30000}]\n", "Using proxy: *************************************************\n", "[{'skuId': '2x6xm0nda6k4uct', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n", "Using proxy: ************************************************\n", "[{'skuId': 14145703407, 'skuStock': 0, 'skuSpec': '2粒*1盒', 'skuPrice': 950}, {'skuId': 24493887069, 'skuStock': 0, 'skuSpec': '20盒', 'skuPrice': 18000}]\n", "Using proxy: *************************************************\n", "[{'skuId': 14503148460, 'skuStock': 0, 'skuSpec': '平安之果 100g*9个/盒', 'skuPrice': 11000}]\n", "Using proxy: ************************************************\n", "[{'skuId': '2fo8ua2ipcor2jc', 'skuStock': 10000, 'skuSpec': '无子SKU, 见标题', 'skuPrice': 0}]\n"]}], "source": ["import json\n", "\n", "# Parse the JSON data\n", "def parsed_sku_data(product):\n", "    data = product['product_details']['goodsData']['skuInfo']\n", "\n", "    # Extracting SKU information\n", "    skus = data.get('skus', [])\n", "    sku_prices = data.get('skuPrices', [])\n", "    sku_stocks = data.get('skuStocks', [])\n", "    sku_specs = {str(item['id']): item['name'] for prop in data.get('props', []) for item in prop.get('v', [])}\n", "\n", "    # Creating a list to store extracted SKU information\n", "    extracted_skus = []\n", "\n", "    # Iterating through each SKU entry\n", "    for sku in skus:\n", "        sku_id = sku.get('skuId')\n", "        sku_stock = next((stock['stockNum'] for stock in sku_stocks if stock['skuId'] == sku_id), None)\n", "        sku_spec_id = sku.get('s1')\n", "        sku_spec = sku_specs.get(sku_spec_id)\n", "        sku_price = next((price['price'] for price in sku_prices if price['skuId'] == sku_id), None)\n", "\n", "        # Creating a dictionary for each SKU\n", "        extracted_sku = {\n", "            'skuId': sku_id,\n", "            'skuStock': sku_stock,\n", "            'skuSpec': sku_spec,\n", "            'skuPrice': sku_price\n", "        }\n", "        extracted_skus.append(extracted_sku)\n", "\n", "    # Printing the extracted SKU information\n", "    if len(extracted_skus) <=0:\n", "        extracted_skus.append({\n", "            'skuId': product['alias'],\n", "            'skuStock': 10000,\n", "            'skuSpec': '无子SKU, 见标题',\n", "            'skuPrice': 0\n", "        })\n", "    print(extracted_skus)\n", "    product['sku_obj_list']=extracted_skus\n", "\n", "def get_product_detail(alias='2frx68v0712dq06'):\n", "    detail_url=f'https://h5.m.youzan.com/wscgoods/tee-app/detail.json?{app_and_kdt_id}&bizEnv=retail&mpVersion=3.113.15&alias={alias}&slg=tagGoodList-default%2COpBottom%2C11927391377%2CabTraceId&banner_id=f.103346086~tag_list_left.1~0~Y1qPtmOd&oid=0&scene=1089&ump_alias=&ump_type=&activityId=&activityType=&subKdtId=0&fullPresaleSupportCart=true&platform=weixin&client=weapp&isGoodsWeappNative=1&withoutSkuDirectOrder=1'\n", "    product_detail=get_remote_data_with_proxy(detail_url)\n", "    return json.loads(product_detail)\n", "\n", "\n", "for product in all_products:\n", "    if 'product_details' in product:\n", "        print(\"已经获取过了\")\n", "        continue\n", "    product_details=get_product_detail(product['alias'])\n", "    if \"data\" in product_details:\n", "        product['product_details']=product_details[\"data\"]\n", "        parsed_sku_data(product)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 清洗商品数据"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["all_products_clean=[]\n", "for product in all_products:\n", "    for item_tag in ['tagsOpt', 'oPriceOpt', 'priceOpt', 'imgOpt', 'titleOpt', 'subTitleOpt', 'extraInfo', 'goodsPreloadOpt', 'actionOpt', 'extOpt']:\n", "        product[item_tag] = product['itemCardOpt'][item_tag]\n", "    clean_product={}\n", "    clean_product['title']=product['titleOpt']['title']\n", "    clean_product['类目名']=product['类目名']\n", "    clean_product['二级类目名']=product['二级类目名']\n", "    clean_product['price']=product['priceOpt']['price']\n", "        # print(product['oPriceOpt'],product['url'],product)\n", "    if 'price' in product['oPriceOpt']:\n", "        clean_product['origin_price']=product['oPriceOpt']['price']\n", "    else:\n", "        clean_product['origin_price']=\"NotFound\"\n", "    clean_product['img']=product['imgOpt']['src']\n", "    clean_product['url']=product['url']\n", "    clean_product['alias']=product['alias']\n", "    clean_product['id']=product['id']\n", "    clean_product['类目link']=product['类目link']\n", "    clean_product['skuInfo']=product['sku_obj_list']\n", "    sku_info=clean_product['skuInfo']\n", "    if len(sku_info) <=0:\n", "        sku_info=[{\n", "            \"skuId\": clean_product['alias'],\n", "            \"skuStock\": 10000,\n", "            \"skuSpec\": '无子SKU,见标题',\n", "            \"skuPrice\": clean_product['price']\n", "        }]\n", "    clean_product['skuInfo'] = sku_info\n", "    clean_product['数据获取时间']=time_of_now\n", "    all_products_clean.append(clean_product)\n", "\n", "df_cate_list=pd.DataFrame(all_products)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品列表-原始数据-{date_of_now}.csv', index=False)\n", "df_cate_list=pd.DataFrame(all_products_clean)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品列表-清洗后数据-{date_of_now}.csv', index=False)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["df_exploded = df_cate_list.explode('skuInfo')\n", "\n", "df_exploded['skuId'] = df_exploded['skuInfo'].apply(lambda x: x['skuId'])\n", "df_exploded['skuStock'] = df_exploded['skuInfo'].apply(lambda x: x['skuStock'])\n", "df_exploded['skuSpec'] = df_exploded['skuInfo'].apply(lambda x: x['skuSpec'])\n", "df_exploded['skuPrice'] = df_exploded['skuInfo'].apply(lambda x: x['skuPrice']/100.00)\n", "\n", "# Dropping the original 'skuInfo' column\n", "df_exploded.drop(columns=['skuInfo'], inplace=True)\n", "df_exploded.drop_duplicates(subset=['alias','skuId'], inplace=True)\n", "df_exploded[df_exploded['alias']=='2xecd73d11s5agv']\n", "df_exploded.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-{date_of_now}.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_exploded['title_embedding']=df_exploded['title'].apply(getEmbeddingsFromAzure)\n", "df_exploded.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False)\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xianmu_path_to_csv='./data/鲜沐/xianmu_sku_with_name_and_price_with_embedding.csv'\n", "xianmu_df=pd.read_csv(xianmu_path_to_csv)\n", "area_name='杭州'\n", "xianmu_df=xianmu_df[xianmu_df['area_name']==area_name]\n", "xianmu_dairy_sku_list=['N001S01R005','N001S01R002','L001S01R001','N001H01Y003','Q001L01S001',\n", "                       '60553221161','2400337515','15103217314','15300532428','605874603137',\n", "                       '15178473420','1233884862','464633265','607188000000','D009H20T012']\n", "xianmu_order_category_sku_list=['15487486557','T001S01H001','3816076886','6151406635','78083116121','831273000000',\n", "                                '3803673132','15487540817','L001A01A001','300231','3834605103','5116666618','83065074266',\n", "                                '791251702788','802382247651','800878814020','827584118207','15418784088','1884053024','16331003164','100803']\n", "xianmu_fruit_sku_list=['6472','6416','16738467013','5432522553','5432522553','14042','555812873340','16788463466','5432522553','16788463466',\n", "                       '611184462','5432522553','145515','14042','555812873340','16788463466','17120703381','5432522406','17320871038','28482']\n", "combined_list = xianmu_dairy_sku_list + xianmu_order_category_sku_list + xianmu_fruit_sku_list\n", "xianmu_analytic_df=xianmu_df[xianmu_df['sku'].isin(combined_list)]\n", "\n", "xianmu_analytic_df[['sku','area_name','administrative_area','area_sku_price','sku_full_name_with_weight']].head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 基于Embedding的搜索匹配算法"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_cate_list=df_exploded.drop_duplicates(subset=['alias','skuId','数据获取时间'])\n", "print(df_cate_list.columns)\n", "print(xianmu_analytic_df.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "\n", "def dotProduct(a = [], b = []):\n", "    return sum(val * b[idx] for idx, val in enumerate(a))\n", "\n", "def magnitude(vector):\n", "    return math.sqrt(sum(val * val for val in vector))\n", "\n", "def cosine_similarity(a=[], b=[]):\n", "    if a is None or b is None:\n", "        return 0.0;\n", "    dotProductValue = dotProduct(a, b)\n", "    magnitudesA = magnitude(a)\n", "    magnitudesB = magnitude(b)\n", "\n", "    return dotProductValue / (magnitudesA * magnitudesB)\n", "\n", "\n", "def match_xianmu_to_this(embedding='[]', topK = 10, df_to_search = df_cate_list):\n", "    embedding=json.loads(embedding)\n", "    searching_index=0\n", "    embedding_scores=[]\n", "    for (index,row) in df_to_search.iterrows():\n", "        title_embedding = row['title_embedding']\n", "        if title_embedding is None:\n", "            print(f\"{row['title']} has no embedding.\")\n", "            continue\n", "        # print(f\"embedding:{len(embedding)}, topK:{topK}, title_embedding:{len(title_embedding)}\")\n", "        # print(embedding)\n", "        searching_index = searching_index+1\n", "        price = row['skuPrice']\n", "        if price<=0:\n", "            price=row['price']\n", "        embedding_scores.append({'sku':row['skuId'], \n", "                                'title':row['title']+\", \"+row['skuSpec'],\n", "                                '单位成本':row['单位成本'],\n", "                                '类目':f\"{row['类目名']}->{row['二级类目名']}\",\n", "                                'price':price,\n", "                                'url':row['url'],\n", "                                '数据获取时间':row['数据获取时间'],\n", "                                'img':row['img'],\n", "                                'score':cosine_similarity(embedding,title_embedding)})\n", "    embedding_scores.sort(key=lambda x: x['score'], reverse=True)\n", "    return embedding_scores[:topK]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取水果的单位价格"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT\n", "import os\n", "import socket\n", "\n", "def get_local_ip():\n", "    try:\n", "        # Create a socket object\n", "        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)\n", "        sock.connect((\"*******\", 80))  # Connecting to a known external server (Google's DNS server)\n", "\n", "        # Get the local IP address connected to the external server\n", "        local_ip = sock.getsockname()[0]\n", "        return local_ip\n", "    except socket.error as e:\n", "        return f\"Error: {e}\"\n", "\n", "# Get and print your local IP\n", "local_ip = get_local_ip()\n", "print(f\"Your local IP address is: {local_ip}\")\n", "\n", "\n", "ANTHROPIC_API_KEY=os.environ['ANTHROPIC_API_KEY']\n", "\n", "proxies=f'http://{local_ip}:8001'\n", "print(proxies)\n", "\n", "anthropic = Anthropic(\n", "    api_key=ANTHROPIC_API_KEY,\n", "    proxies=proxies\n", ")\n", "\n", "\n", "def call_claude_api_to_get_price_by_unit(product_name_with_price):\n", "    prompt=f\"\"\"你是一个聪明的采购商，在采购水果时，总是能够计算出每个商品报价中的单位价格，比如3.2斤橙子总价¥18，则单位成本为¥18/3.2=¥5.625(斤)。\n", "用户会给你一个商品的报价单，请你用JSON的格式输出该商品的单位价格。\n", "{HUMAN_PROMPT}请计算该商品的单位价格：16788463466AC, AC广东粗皮香水柠檬 净重5-5.1斤/一级/（大果）, ¥56.88\n", "{AI_PROMPT}{{\"商品ID\":\"16788463466AC\",\"商品名称\":\"AC广东粗皮香水柠檬 净重5-5.1斤/一级/（大果）\",\"商品规格\":\"净重5-5.1斤/一级\",\"单位成本\":\"11.376\",\"总价\":\"56.88\"}}\n", "{HUMAN_PROMPT}很好，很准确。请继续计算商品的价格：17120703381AB, 丹东红颜草莓ABC 净重3-3.3斤/一级/10g以上, ¥79.5\n", "{AI_PROMPT}{{\"商品ID\":\"17120703381AB\",\"商品名称\":\"丹东红颜草莓ABC 净重3-3.3斤/一级/10g以上\",\"商品规格\":\"净重3-3.3斤/一级/10g以上\",\"单位成本\":\"26.5\",\"总价\":\"79.5\"}}\n", "{HUMAN_PROMPT}很好，非常准确，你使用了最低净重，这很重要！请继续计算商品的价格：{product_name_with_price}\n", "{AI_PROMPT}\"\"\"\n", "    # print(prompt)\n", "    completion = anthropic.completions.create(\n", "        model=\"claude-2.1\",\n", "        max_tokens_to_sample=300,\n", "        prompt=prompt,\n", "        temperature=0.1,\n", "    )\n", "    # print(completion.completion)\n", "    return completion.completion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "def get_brand_unit_price(row):\n", "    if row['类目名'] !='水果' or row['二级类目名'] == '果汁|原浆|果酱':\n", "        # 果汁|原浆|果酱 不是水果，其实是包装材料\n", "        return {'单位成本':row['price']}\n", "    prompt=f'{row[\"alias\"]}, {row[\"title\"]}, ¥{row[\"price\"]}'\n", "    unit_price=json.loads(call_claude_api_to_get_price_by_unit(prompt))\n", "    print(unit_price)\n", "    return unit_price\n", "\n", "def get_xianmu_unit_price(row):\n", "    if row['root_category'] !='新鲜水果':\n", "        return {'单位成本':row['area_sku_price']}\n", "    prompt=f'{row[\"sku\"]}, {row[\"sku_full_name_with_weight\"]}, ¥{row[\"area_sku_price\"]}'\n", "    unit_price=json.loads(call_claude_api_to_get_price_by_unit(prompt))\n", "    print(unit_price)\n", "    return unit_price\n", "# xianmu_analytic_df.head(1)\n", "xianmu_analytic_df['单位成本']=xianmu_analytic_df.apply(get_xianmu_unit_price, axis=1)\n", "# xianmu_analytic_df[xianmu_analytic_df['root_category']=='新鲜水果'][['单位成本','sku_full_name_with_weight','sku','area_sku_price']]\n", "\n", "# {'商品ID': '2frx68v0712dq06', '商品名称': '99红颜草莓丹东/江苏产地随机发 24颗/盒', '商品规格': '24颗/盒', '单位成本': '1.02', '总价': '24.50'}\n", "\n", "print(df_cate_list.columns)\n", "df_cate_list.groupby('类目名')['title'].count()\n", "df_cate_list['单位成本']=df_cate_list.apply(get_brand_unit_price, axis=1)\n", "df_cate_list[df_cate_list['类目名']=='水果'][['单位成本','title','alias','price']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.core.display import HTML\n", "\n", "print(cosine_similarity([1,2,3],[4,5,6]))\n", "xianmu_analytic_df.loc[:, 'matched_top5'] = xianmu_analytic_df['sku_full_name_with_weight_embedding'].apply(match_xianmu_to_this, topK=5)\n", "\n", "# Assuming 'df' is your DataFrame\n", "css = \"\"\"\n", "<link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css\" integrity=\"sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\" crossorigin=\"anonymous\">\n", "<style type=\\\"text/css\\\">\n", "table {\n", "color: #333;\n", "font-family: unset;\n", "font-size: 12px;\n", "line-height: 1.5;\n", "width: 1024px;\n", "border-collapse:\n", "collapse; \n", "border-spacing: 0;\n", "}\n", "\n", "tr{\n", "border-bottom: 1px solid #C1C3D1;\n", "}\n", "\n", "tr:nth-child(even) {\n", "background-color: #F8F8F8;\n", "}\n", "\n", "td, th {\n", "border: 1px solid transparent; /* No more visible border */\n", "height: 30px;\n", "}\n", "\n", "th {\n", "background-color: #DFDFDF; /* Darken header a bit */\n", "font-weight: bolder;\n", "min-width: 100px;\n", "text-align: center;\n", "}\n", "\n", "td {\n", "background-color: #FAFAFA;\n", "text-align: center;\n", "}\n", "\n", "ol li{\n", "text-align: left;\n", "}\n", ".brand-container{\n", "display:flex;\n", "}\n", ".xianmu-container, .brand-sku{\n", "padding:0 10px;\n", "margin-right:5px\n", "border:solid 1px #efefef;\n", "}\n", ".xianmu-container{\n", "width:200px;\n", "}\n", ".brand-sku{\n", "width:180px;\n", "background-color:#fff;\n", "}\n", ".match-score{\n", "font-size:smaller;\n", "}\n", ".matched-very-well{\n", "color:lightgreen;\n", "}\n", ".higher-price{\n", "color:purple;\n", "}\n", ".lower-price{\n", "color:lightblue;\n", "font-weight:bolder;\n", "}\n", ".unit-price{color:red;}\n", "</style>\n", "\"\"\"\n", "def display_xianmu_html(row):\n", "    unit_price=row['单位成本']\n", "    if '商品规格' in unit_price:\n", "        unit_price='单价:¥'+unit_price['单位成本']+'<br>规格:'+unit_price['商品规格']\n", "        unit_price=f\"\"\"<span class='unit-price'>{unit_price}</span><br>\"\"\"\n", "    else:\n", "        unit_price=\"\"\n", "    img = row[\"img\"]\n", "    content= f\"\"\"<div class=\"xianmu-container\"><img width=\"80\" src=\"{img}\"><br>售价:¥{row[\"area_sku_price\"]}, 平均成本价:¥{row['avg_cost']}<br>{unit_price}<span>{row['sku']}, {row[\"sku_full_name_with_weight\"]}</span></div>\"\"\"\n", "    return content\n", "\n", "# Assuming 'df' is your DataFrame and 'url' and 'title' are your columns\n", "def display_brand_html(row_of_df):\n", "    # {'商品ID': '2frx68v0712dq06', '商品名称': '99红颜草莓丹东/江苏产地随机发 24颗/盒', '商品规格': '24颗/盒', '单位成本': '1.02', '总价': '24.50'}\n", "    matched_top=row_of_df['matched_top5']\n", "    content_list=[]\n", "    is_top_one=True\n", "    for row in matched_top:\n", "        unit_price=row['单位成本']\n", "        if '商品规格' in unit_price:\n", "            unit_price='单价:¥'+unit_price['单位成本']+'<br>规格:'+unit_price['商品规格']\n", "            unit_price=f\"\"\"<span class='unit-price'>{unit_price}</span><br>\"\"\"\n", "        else:\n", "            unit_price=\"\"\n", "        price_diff=row_of_df['area_sku_price']-float(row[\"price\"])\n", "        is_higher_price_class=\"higher-price\"\n", "        if price_diff<=0:\n", "            is_higher_price_class=\"lower-price\"\n", "        is_top_one=False\n", "        score_class=\"match-score\"\n", "        if row['score']>0.9:\n", "            score_class=\"match-score matched-very-well\"\n", "        img = row[\"img\"]\n", "        content= f\"\"\"<div class=\"brand-sku\"><img width=\"80\" src=\"{img}\"><br><span class=\"{is_higher_price_class}\">¥{row[\"price\"]}, 差价:¥{price_diff}</span><br>{unit_price}<span class=\"{score_class}\">匹配分数:{round(row['score'], 4)}</span><br/><a target=\"_blank\" href=\"{row[\"url\"]}\">{row[\"title\"]}</a><br>ID:{row[\"sku\"]}</div>\"\"\"\n", "        content_list.append(content)\n", "    content_list=\"\".join(content_list)\n", "    return f'<div class=\"brand-container\">{content_list}</div>'\n", "\n", "xianmu_sku_column_name=f'鲜沐SKU:{area_name}'\n", "xianmu_analytic_df[xianmu_sku_column_name]=xianmu_analytic_df.apply(display_xianmu_html, axis=1)\n", "brand_column_name=f'{brand_name}SKU:{time_of_now}'\n", "xianmu_analytic_df[brand_column_name]=xianmu_analytic_df.apply(display_brand_html, axis=1)\n", "xianmu_analytic_df=xianmu_analytic_df.sort_values(['root_category','sku_full_name'])\n", "\n", "html_content=css+xianmu_analytic_df[[xianmu_sku_column_name,brand_column_name]].to_html(escape=False, index=False, classes='table dataframe')\n", "html_content=f'<html><head><meta charset=\"UTF-8\"><meta name=\"title\" content=\"{brand_name}和鲜沐的比较-{date_of_now}\"></head><body>{html_content}</body></html>'\n", "display(HTML(html_content))\n", "\n", "# 保存HTML到本地文件：\n", "with open(f'./data/{brand_name}/{brand_name}和鲜沐的比较-{date_of_now}.html', 'w') as f:\n", "    f.write(html_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取过去7天的DF，进行价格变动监控"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['2024-01-10', '2024-01-09', '2024-01-08', '2024-01-07', '2024-01-06', '2024-01-05', '2024-01-04', '2024-01-03']\n"]}], "source": ["from datetime import datetime, timedelta\n", "\n", "# Calculate the date 7 days ago\n", "seven_days_ago = datetime.now() - <PERSON><PERSON><PERSON>(days=7)\n", "\n", "# Create a set to store date strings\n", "date_set = []\n", "\n", "# Generate date strings starting from 7 days ago until today\n", "current_date = datetime.now()\n", "while seven_days_ago <= current_date:\n", "    date_string = current_date.strftime(\"%Y-%m-%d\")  # Format date as string\n", "    date_set.append(date_string)\n", "    current_date -= <PERSON><PERSON><PERSON>(days=1)  # Move to the previous day\n", "\n", "print(date_set)\n", "\n", "all_brand_df=[]\n", "for date in date_set:\n", "    file = f'./data/{brand_name}/{brand_name}-商品列表-清洗后数据-{date}.csv'\n", "    if os.path.isfile(file):\n", "        all_brand_df.append(pd.read_csv(file))\n", "if len(all_brand_df) > 0:\n", "    all_brand_df=pd.concat(all_brand_df)\n", "\n", "aliases_with_multiple_prices = pandasql.sqldf(\"select alias,count(distinct price) as cnt from all_brand_df group by alias having cnt>1\")\n", "\n", "has_price_changed_df=all_brand_df[all_brand_df['alias'].isin(aliases_with_multiple_prices['alias'].unique())][['alias','title','二级类目名','price','img','url','数据获取时间']]\n", "has_price_changed_df.sort_values(['alias','数据获取时间'], inplace=True)\n", "deduplicated_df = has_price_changed_df.drop_duplicates(subset=['alias','数据获取时间'])\n", "deduplicated_df.to_csv(f'./data/{brand_name}/{brand_name}-价格有变动的商品列表-{date_of_now}.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
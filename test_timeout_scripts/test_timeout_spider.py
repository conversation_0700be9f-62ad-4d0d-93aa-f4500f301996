#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试超时机制的爬虫脚本
模拟不同的结束情况
"""

import time
import sys
import os
import random
from datetime import datetime

def test_scenario():
    """测试不同的结束场景"""
    script_name = os.path.basename(__file__)
    scenario = script_name.replace('.py', '').split('_')[-1]
    
    print(f"[{datetime.now()}] 开始执行测试场景: {scenario}")
    
    if 'timeout' in script_name:
        # 模拟超时场景 - 运行很长时间
        print("模拟长时间运行的任务...")
        for i in range(3600):  # 运行1小时
            time.sleep(1)
            if i % 60 == 0:
                print(f"已运行 {i//60} 分钟...")
        return 0
    
    elif 'error' in script_name:
        # 模拟错误场景
        print("模拟错误场景...")
        time.sleep(5)
        print("ERROR: 网络连接失败")
        print("Exception: Connection timeout")
        return 1
    
    elif 'success' in script_name:
        # 模拟成功场景
        print("模拟成功场景...")
        time.sleep(10)
        print("数据处理完成")
        print("===new_record===测试品牌, 商品数:50")
        return 0
    
    elif 'partial' in script_name:
        # 模拟部分成功场景（有日志但没有成功标记）
        print("模拟部分成功场景...")
        time.sleep(8)
        print("成功写入odps数据库")
        print("数据处理完成，但未输出统计信息")
        return 0
    
    else:
        # 默认场景
        print("默认测试场景...")
        time.sleep(random.randint(5, 15))
        if random.random() > 0.5:
            print("===new_record===默认测试, 商品数:25")
            return 0
        else:
            print("处理失败")
            return 1

if __name__ == "__main__":
    exit_code = test_scenario()
    sys.exit(exit_code)

## 爬虫程序

### 本地全量运行（发飞书通知）：
```
docker rmi xianmu-spider ; docker build -f Dockerfile_local -t xianmu-spider . && docker run --rm -e ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5tQzmpz2nQEWdiqvQGsc -e ALIBABA_CLOUD_ACCESS_KEY_SECRET=****************************** -e FEISHU_NOTIFY_TOKEN=4c945cb4-1ab8-450d-bb78-89db0f578cba xianmu-spider | tee output.log
```

### 本地仅运行单个文件（发飞书通知）：
* 比如说仅运行: scripts/cangzhen_spider.py 
```
docker rmi xianmu-spider ; docker build -f Dockerfile_local -t xianmu-spider . && docker run --rm -e FILE_TO_EXECUTE=cangzhen_spider.py -e ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5tQzmpz2nQEWdiqvQGsc -e ALIBABA_CLOUD_ACCESS_KEY_SECRET=****************************** -e FEISHU_NOTIFY_TOKEN=4c945cb4-1ab8-450d-bb78-89db0f578cba xianmu-spider | tee output.log
```

### 本地运行单个文件（不发飞书通知）：
```
docker rmi xianmu-spider ; docker build -f Dockerfile_local -t xianmu-spider . && docker run --rm -e FILE_TO_EXECUTE=cangzhen_spider.py -e ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5tQzmpz2nQEWdiqvQGsc -e ALIBABA_CLOUD_ACCESS_KEY_SECRET=****************************** xianmu-spider | tee output.log
```
import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,
    get_odps_sql_result_as_df,
    THREAD_CNT,
)
import os

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6",
    "Connection": "keep-alive",
    "Content-Type": "application/json;charset=UTF-8",
    "Origin": "https://www.ymt.com",
    "Referer": "https://www.ymt.com/supplylist/search?keyword=%E5%A4%A7%E4%BA%94%E6%A3%B1%E5%B1%B1%E6%A5%82",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
    "X-App-Version": "V1.0.1",
    "X-User-Agent": "4001",
    "sec-ch-ua": '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"macOS"',
}
brand_name = "一亩田"
competitor_name_en = "yimutian"

print(f"{time_of_now}, headers:{headers}")

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
days_15 = (datetime.now() - timedelta(15)).strftime("%Y%m%d")

print(f"time_of_now:{time_of_now},headers:{headers}")
# 获取鲜沐所有有GMV的鲜果SPU Name
xianmu_fruits_df = get_odps_sql_result_as_df(
    f"""
SELECT  spu_name
        ,SUM(sku_cnt) AS total_sale_volume
        ,case when c.is_domestic = 1 then '国产' else '进口' end as is_domestic
        ,COLLECT_SET(warehouse_name) AS warehouse_names
        ,CONCAT(MIN(a.ds),'~',MAX(a.ds)) AS data_range
        ,COUNT(DISTINCT a.ds) AS days_has_sell
FROM    summerfarm_tech.dwd_dlv_delivery_cost_di a
INNER JOIN  summerfarm_tech.ods_merchant_df b on a.cust_id = b.m_id and b.ds=MAX_PT('summerfarm_tech.ods_merchant_df') and b.size !='大客户'
INNER JOIN  summerfarm_tech.ods_inventory_df c on a.sku_id = c.sku and c.ds=MAX_PT('summerfarm_tech.ods_inventory_df')
WHERE   a.ds >= '{days_15}'
AND     a.category1 = '鲜果'
and     a.spu_name not like '%【礼盒】%'
GROUP BY a.spu_name,c.is_domestic
HAVING total_sale_volume>15
ORDER BY total_sale_volume DESC;
"""
)
print(xianmu_fruits_df)

# 爬取一亩田数据
text_response_list = []
all_products = []


def get_yumutian_result_for_spu_name(spu_name, start=0):
    global all_products
    url = "https://www.ymt.com/gfw/supply_search/g/v15/supply/search"
    page_size = 200
    data = {
        "category_id": -1,
        "product_id": -1,
        "breed_id": -1,
        "location_id": -1,
        "keyword": f"{spu_name}",
        "start": start,
        "page_size": page_size,
        "orderby": "",
        "search_guide": 1,
        "filter_location_dist": -1,
        "filter_location_id": -1,
        "filter_price_max": -1,
        "filter_price_min": -1,
        "filter_update": -1,
        "filter_operations": [],
    }

    response_text = get_remote_data_with_proxy(url, headers=headers, json=data)
    text_response_list.append(
        {"spu_name": spu_name, "yimutian_search_result": response_text}
    )
    response_json = json.loads(response_text)
    if "page" not in response_json:
        print(f"=====data:{data}, response_text:{response_text}")
        return []
    page = response_json["page"]
    supply_list = response_json["result"]
    new_len = len(supply_list)
    if new_len <= 0:
        print(f"data:{data}, response_text:{response_text}")
        return []
    has_more = page["real_total"] >= (start + page_size)
    for product in supply_list:
        product["xianmu_spu_name"] = spu_name
        if "effect" not in product:
            product["effect"] = "无人购买"
    print(
        f"spu_name:{spu_name}, has_more:{has_more}, page:{page}, requested data:{data}"
    )
    if has_more:
        sub_list = get_yumutian_result_for_spu_name(spu_name, start + page_size)
        if sub_list is not None and len(sub_list) > 0:
            supply_list.extend(sub_list)
        else:
            print(f"请求分页结果失败:{new_len+start}")
    print(f"spu_name:{spu_name} 总条数:{len(supply_list)}")
    all_products.extend(supply_list)
    return supply_list


with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    # Submit tasks to the executor
    futures = [
        executor.submit(get_yumutian_result_for_spu_name, row["spu_name"])
        for index, row in xianmu_fruits_df.iterrows()
    ]

    # Wait for all tasks to complete
    concurrent.futures.wait(futures)

all_products_df = None
if len(all_products) > 1:
    all_products_df = pd.DataFrame(all_products)
    print(all_products_df.head(2))
# 写入odps
all_products_df["competitor"] = brand_name
all_products_df = all_products_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_df"

result = write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name 
                             order by ds desc limit 50"""
)


# 爬取一亩田价格趋势：
print("开始爬取价格趋势和商品品种..")
import math

product_ids = all_products_df["product_id"].unique()
product_price_list = []
product_breed_all = []


def get_product_price_trend_and_breed(id):
    global product_price_list
    global product_breed_all
    print(f"product_id:{id}")
    id = float(id)
    if math.isnan(id):
        return 0
    id = int(id)
    hot_sale_data = {
        "index": 0,
        "length": 200,
        "product_id": id,
        "location_id": -1,
        "breed_id": -1,
        "isTouTiao": False,
    }
    sale_trends_result = get_remote_data_with_proxy(
        "https://www.ymt.com/gfw/price/v13/market/origin_market?app_key=4001",
        json=hot_sale_data,
        headers=headers,
    )
    sale_trends_result = json.loads(sale_trends_result)
    if "result" not in sale_trends_result:
        print(f"error:{sale_trends_result}")
        return 0
    sale_trends_result = sale_trends_result["result"]
    product_obj = {
        "product_name": sale_trends_result["product_name"],
        "product_id": sale_trends_result["product_id"],
        "last_update": sale_trends_result["last_update"],
        "price_unit": sale_trends_result["unit"],
    }

    if "breed" in sale_trends_result:
        breeds = sale_trends_result["breed"]
        for b in breeds:
            b.update(product_obj)
        product_breed_all.extend(breeds)
    if "list" in sale_trends_result:
        sales = sale_trends_result["list"]
        for s in sales:
            s.update(product_obj)
        product_price_list.extend(sales)
    print(product_price_list[-1], product_breed_all[-1])


with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    # Submit tasks to the executor
    futures = [
        executor.submit(get_product_price_trend_and_breed, id) for id in product_ids
    ]
    # Wait for all tasks to complete
    concurrent.futures.wait(futures)

product_price_list_df = pd.DataFrame(product_price_list)
product_breed_all_df = pd.DataFrame(product_breed_all)

import re


def extract_numbers(html):
    numbers = re.findall(r">(-?\d*\.?\d*)</font>", html)
    if numbers is not None and len(numbers) > 0:
        return numbers[0]
    else:
        return html


def get_trend_desc(trend_real):
    trend_real = float(trend_real)
    if trend_real > 0:
        return "涨价了"
    elif trend_real < 0:
        return "降价了"
    else:
        return "持平"


product_price_list_df["price_real"] = product_price_list_df["price"].apply(
    extract_numbers
)
product_price_list_df["trend_real"] = product_price_list_df["trend"].apply(
    extract_numbers
)
product_price_list_df["trend_desc"] = product_price_list_df["trend_real"].apply(
    get_trend_desc
)
print(f"价格趋势数据量:{len(product_price_list_df)}")
print(f"产品品种数据量:{len(product_breed_all_df)}")

# 写入odps
product_breed_all_df["competitor"] = brand_name
product_breed_all_df = product_breed_all_df.astype(str)
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_breed_result_df"
result = result and write_pandas_df_into_odps(
    product_breed_all_df, table_name, partition_spec
)

product_price_list_df["competitor"] = brand_name
product_price_list_df = product_price_list_df.astype(str)
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_price_trend_result_df"
result = result and write_pandas_df_into_odps(
    product_price_list_df, table_name, partition_spec
)

if result:
    print(f"成功了！{datetime.now()},\n{df}")
    print(f"===new_record==={brand_name}, 商品数:{len(all_products)}")
else:
    print(f"{brand_name}, 写入ODPS失败")

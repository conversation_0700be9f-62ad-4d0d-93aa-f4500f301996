import requests
import json
import hashlib
import time
import pandas as pd
import os
from datetime import datetime,timedelta
from odps import ODPS,DataFrame
from odps.accounts import StsAccount
import traceback
from proxy_setup import get_remote_data_with_proxy,write_pandas_df_into_odps,get_odps_sql_result_as_df

from datetime import datetime 
time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
date_of_now=datetime.now().strftime('%Y-%m-%d')

def get_proxy_list_from_server():
    all_proxies=requests.get("http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=****************&sign=11c5546b75cde3e3122d05e9e6c056fe").text
    print(all_proxies)
    proxy_list=all_proxies.split("\r\n")
    return proxy_list

import requests
import urllib.parse
city_name='苏州市'
county_name='姑苏区'

records_fetched=0

def get_all_products_of_second_category(all_second_category):
    all_products=[]
    for second_cate in all_second_category:
        data = {
        "userId": 31999,
        "cityName": city_name,
        "countyName": county_name,
        "littleClass": second_cate['id'],
        "unitPriceStart": "",
        "unitPriceEnd": 10000,
        "pageNum": 1,
        "pageSize": 10000,
        "isGetCount": 0,
        "v": 0.8328895114326689
        }
        
        
        # if second_cate['一级类目名'] != '鲜果':
        #     print(f"跳过:{second_cate['一级类目名']}")
        #     continue

        print(f"{second_cate['一级类目名']}->{second_cate['name']},{data}")
        url=f'https://miniapi.topingr.com/topingr/category/getItemByBrandParameters'
        cate_product_list = get_remote_data_with_proxy(url, json=data)
        cate_product_list=json.loads(cate_product_list)
        print(cate_product_list)

        print('商品个数：',second_cate['name'],len(cate_product_list['data']['list']))

        for product in cate_product_list['data']['list']:
            product['二级类目名']=second_cate['name']
            product['类目图片']=second_cate['pic']
            all_products.append(product)

    all_product_raw_df=pd.DataFrame(all_products)
    return all_product_raw_df

import hashlib

def scrapy_and_save_into_odps(brand_name, competitor_name_en="-"):
    url=f'https://miniapi.topingr.com/topingr/category/getBigClassList'
    cate_list=json.loads(get_remote_data_with_proxy(url))['data']
    print(f"类目列表:{cate_list}")

    city_name='苏州市'
    county_name='姑苏区'
    city_name_encode=urllib.parse.quote(city_name)
    county_name_encode=urllib.parse.quote(county_name)
    # 根据一级类目获取所有二级类目
    all_second_category = []
    all_second_brand = []
    for first_cate in cate_list:
        bigClassId=first_cate['id']
        first_cate_name = first_cate['name']
        print(bigClassId)
        url=f'https://miniapi.topingr.com/topingr/category/getLittleClassAndBrandByBigClassId?bigClassId={bigClassId}&cityName={city_name_encode}&countyName={county_name_encode}'
        data=json.loads(get_remote_data_with_proxy(url))['data']
        second_category_list = data['littleClassList']
        
        for second_cate in second_category_list:
            second_cate['一级类目ID']=bigClassId
            second_cate['一级类目名']=first_cate_name
            all_second_category.append(second_cate)

        second_brand_list = data['brandList']
        for second_brand in second_brand_list:
            second_brand['一级类目ID']=bigClassId
            second_brand['一级类目名']=first_cate_name
            all_second_brand.append(second_brand)
        
    print(f'all_second_category:{all_second_category}','all_second_brand:{all_second_brand}')

    all_products_raw_df=get_all_products_of_second_category(all_second_category=all_second_category)
    records_fetched=len(all_products_raw_df)
    all_product_clean_df=all_products_raw_df[['skuId','skuName','keyword','skuPics','dataTag','salesVolume','supplierName','unitOriginalPrice','promotionPrice','unitPrice','brandName','二级类目名']].copy()
    all_product_clean_df['数据获取时间']=time_of_now
    all_product_clean_df['competitor']=brand_name


    all_products_raw_df['hash']=all_products_raw_df.apply(lambda row: hashlib.md5(json.dumps(row.to_dict()).encode()).hexdigest(), axis=1)
    all_products_raw_df['json']=all_products_raw_df.apply(lambda row: json.dumps(row.to_dict()), axis=1)
    all_products_raw_df=all_products_raw_df[['hash','json']]


    table_name = 'summerfarm_ds.spider_taoyuan_product_result_df'
    raw_table_name = 'summerfarm_ds.spider_taoyuan_product_raw_result_df'
    today = datetime.now().strftime('%Y%m%d')
    partition_spec = f'ds={today},competitor_name={competitor_name_en}'
    write_pandas_df_into_odps(all_product_clean_df, table_name, partition_spec)
    write_pandas_df_into_odps(all_products_raw_df, raw_table_name, partition_spec)

scrapy_and_save_into_odps(brand_name="淘源商城", competitor_name_en='taoyuanshangcheng')

two_weeks_ago=(datetime.now()-timedelta(14)).strftime("%Y%m%d")
df=get_odps_sql_result_as_df(f"select count(1) cnt,ds from summerfarm_ds.spider_taoyuan_product_result_df where ds >='{two_weeks_ago}' group by ds order by ds desc")
print(df)
print(f"完成了!{datetime.now()}")
print(f"===new_record==={records_fetched}")
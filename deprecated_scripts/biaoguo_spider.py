import requests
import json
import pandas as pd
from datetime import datetime,timedelta
import concurrent.futures
from proxy_setup import get_remote_data_with_proxy_json,get_remote_data_with_proxy,write_pandas_df_into_odps,get_odps_sql_result_as_df,THREAD_CNT
import os

os.environ['PYTHONIOENCODING'] = 'UTF-8'

time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')

headers={'token':'d962d0ba06514ffa8b80a335d851563f',
'sid':'7731297',
'time':'1702521175012',}
brand_name='标果'
competitor_name_en='biaoguo'

print(f"{time_of_now}, headers:{headers}")

days_30=(datetime.now()-timedelta(30)).strftime('%Y%m%d')
print(f"time_of_now:{time_of_now},headers:{headers}")
# 获取所有商品类目：
category_list=get_remote_data_with_proxy_json("https://demeter-api.biaoguoworks.com/leechee/api/h5/store/front-categorys", headers=headers, json={})

# 根据三级类目获取商品列表：
def get_products_for_category(categoryId=5745, current=1):
    url='https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods'
    data={
        "size": 20,
        "current": current,
        "categoryId": categoryId,
        "goodsSourceType": 0,
        "searchSortType": "COMPREHENSIVE",
        "goodsSaleTagId": "",
        "propertyValueIds": []
    }
    text=get_remote_data_with_proxy(url, headers=headers, json=data)
    text_json=json.loads(text)
    if 'content' in text_json and 'records' in text_json['content']:
        return text_json['content']['records']
    else:
        print(f"异常:{text}, 请求的data:{data}")
        return []

print(get_products_for_category())
product_list_all=[]

def process_category(category={}):
    global product_list_all

    categoryLevel=category['categoryLevel']
    goodsCount=category["goodsCount"]
    categoryId=category['id']
    categoryName=category["categoryName"]
    print(f'{categoryName}, categoryId:{categoryId}, goodsCount:{goodsCount}')
    if categoryLevel != 3:
        print(f"非叶子类目:{categoryLevel}, categoryName:{categoryName}")
        return
    size=0
    current=1
    while size<goodsCount:
        print(f"current:{current}, size:{size}, category:{categoryName}-{categoryId}-level:{categoryLevel}")
        sub_list=get_products_for_category(categoryId=categoryId, current=current)
        current=current+1
        if sub_list is None or len(sub_list)<=0:
            break
        size=size+len(sub_list)
        print(f"{categoryName}:{sub_list[0]}")
        product_list_all.extend(sub_list)
# 多线程
with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    # Submit tasks to the executor
    futures = [executor.submit(process_category, category) for category in category_list['content']]
    # Wait for all tasks to complete
    concurrent.futures.wait(futures)

# for category in category_list['content']:
#     process_category(category=category)

product_list_all_df=pd.DataFrame(product_list_all)
# 写入odps
product_list_all_df['competitor']=brand_name
all_products_df=product_list_all_df.astype(str)

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

result=write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50""")

if result:
    print(f"成功了！{datetime.now()},\n{df}")
    print(f"===new_record==={brand_name}, 商品数:{len(all_products_df)}")
else:
    print(f"{brand_name}, 写入ODPS失败")
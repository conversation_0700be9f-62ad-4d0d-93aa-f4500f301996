{"cells": [{"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Your local IP address is: ************\n", "http://************:8001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/cl/v_4j9fbj5nn_jj6q9d3r5ggc0000gn/T/ipykernel_8673/913560618.py:27: DeprecationWarning: The `proxies` argument is deprecated. The `http_client` argument should be passed instead\n", "  anthropic = Anthropic(\n"]}], "source": ["from anthropic import Anthropic, HUMAN_PROMPT, AI_PROMPT\n", "import os\n", "import socket\n", "\n", "def get_local_ip():\n", "    try:\n", "        # Create a socket object\n", "        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)\n", "        sock.connect((\"*******\", 80))  # Connecting to a known external server (Google's DNS server)\n", "\n", "        # Get the local IP address connected to the external server\n", "        local_ip = sock.getsockname()[0]\n", "        return local_ip\n", "    except socket.error as e:\n", "        return f\"Error: {e}\"\n", "\n", "# Get and print your local IP\n", "local_ip = get_local_ip()\n", "print(f\"Your local IP address is: {local_ip}\")\n", "\n", "\n", "ANTHROPIC_API_KEY=os.environ['ANTHROPIC_API_KEY']\n", "\n", "proxies=f'http://{local_ip}:8001'\n", "print(proxies)\n", "\n", "anthropic = Anthropic(\n", "    api_key=ANTHROPIC_API_KEY,\n", "    proxies=proxies\n", ")\n", "\n", "\n", "def call_claude_api_to_get_price_by_unit(product_name_with_price):\n", "    prompt=f\"\"\"你是一个聪明的采购商，在采购水果时，总是能够计算出每个商品报价中的单位价格，比如3.2斤橙子总价¥18，则单位成本为¥18/3.2=¥5.625(斤)。\n", "用户会给你一个商品的报价单，请你用JSON的格式输出该商品的单位价格。\n", "{HUMAN_PROMPT}请计算该商品的单位价格：16788463466AC, AC广东粗皮香水柠檬 净重5-5.1斤/一级/（大果）, ¥56.88\n", "{AI_PROMPT}{{\"商品ID\":\"16788463466AC\",\"商品名称\":\"AC广东粗皮香水柠檬 净重5-5.1斤/一级/（大果）\",\"商品规格\":\"净重5-5.1斤/一级\",\"单位成本\":\"11.376\",\"总价\":\"56.88\"}}\n", "{HUMAN_PROMPT}很好，很准确。请继续计算商品的价格：17120703381AB, 丹东红颜草莓ABC 净重3-3.3斤/一级/10g以上, ¥79.5\n", "{AI_PROMPT}{{\"商品ID\":\"17120703381AB\",\"商品名称\":\"丹东红颜草莓ABC 净重3-3.3斤/一级/10g以上\",\"商品规格\":\"净重3-3.3斤/一级/10g以上\",\"单位成本\":\"26.5\",\"总价\":\"79.5\"}}\n", "{HUMAN_PROMPT}很好，非常准确，你使用了最低净重，这很重要！请继续计算商品的价格：{product_name_with_price}\n", "{AI_PROMPT}\"\"\"\n", "    # print(prompt)\n", "    completion = anthropic.completions.create(\n", "        model=\"claude-2.1\",\n", "        max_tokens_to_sample=300,\n", "        prompt=prompt,\n", "        temperature=0.1,\n", "    )\n", "    # print(completion.completion)\n", "    return completion.completion"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'商品ID': '145515', '商品名称': '越南白心火龙果 净重4-4.3斤*1包/普通/大果', '商品规格': '净重4-4.3斤*1包/普通/大果', '单位成本': '6.744', '总价': '29.00'}\n"]}], "source": ["import json\n", "unit_price=call_claude_api_to_get_price_by_unit('145515, 越南白心火龙果 净重4-4.3斤*1包/普通/大果, ¥29.00')\n", "print(json.loads(unit_price))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" {\"商品ID\":\"145515\",\"商品名称\":\"越南白心火龙果 净重4-4.3斤*1包/普通/大果\",\"商品规格\":\"净重4-4.3斤*1包/普通/大果\",\"单位成本\":\"6.744\",\"总价\":\"29.00\"}\n", "{'商品ID': '145515', '商品名称': '越南白心火龙果 净重4-4.3斤*1包/普通/大果', '商品规格': '净重4-4.3斤*1包/普通/大果', '单位成本': '6.744', '总价': '29.00'}\n"]}], "source": []}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" {\"商品ID\":\"145515\",\"商品名称\":\"越南白心火龙果 净重4-4.3斤*1包/普通/大果\",\"商品规格\":\"净重4-4.3斤*1包/普通/大果\",\"单位成本\":\"6.744\",\"总价\":\"29.00\"}\n"]}], "source": ["print(call_claude_api_to_get_price_by_unit('145515, 越南白心火龙果 净重4-4.3斤*1包/普通/大果, ¥29.00'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
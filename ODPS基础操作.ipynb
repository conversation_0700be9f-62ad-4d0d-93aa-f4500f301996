{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from pandasql import sqldf\n", "import numpy as np\n", "import traceback\n", "import time\n", "from datetime import datetime, timedelta\n", "from scripts.proxy_setup import get_odps_sql_result_as_df\n", "\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "\n", "path_to_csv='./data/鲜沐/xianmu_sku_with_name_and_price.csv'\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "def add_new_column_to_table(table_name, column_name):\n", "    if 'summerfarm_ds.' not in table_name:\n", "        table_name=f'summerfarm_ds.{table_name}'\n", "    sql = f\"ALTER TABLE {table_name} ADD COLUMNS ({column_name} STRING);\"\n", "    instance = odps.execute_sql(sql)\n", "    instance.wait_for_success()\n", "    print(f\"添加新字段成功:{table_name}, {column_name}\")\n", "\n", "def is_all_df_columns_in_odps_table(df, table_name):\n", "    if 'summerfarm_ds.' not in table_name:\n", "        table_name=f'summerfarm_ds.{table_name}'\n", "    table=odps.get_table(table_name)\n", "    column_names = set([column.name for column in table.table_schema])\n", "    print(column_names)\n", "    df_columns=df.columns.tolist()\n", "    for df_col in df_columns:\n", "        if df_col not in column_names:\n", "            print(f'新字段:{df_col}, ODPS全部的字段:{column_names}')\n", "            add_new_column_to_table(table_name, df_col)\n", "\n", "\n", "def write_pandas_df_into_odps(df, table_name, partition_spec):\n", "    exception=None\n", "    error_message=''\n", "    for attemp in range(5):\n", "        try:\n", "            odps_df = DataFrame(df)\n", "            odps_df.persist(table_name, partition=partition_spec, drop_partition=True, create_partition=True, overwrite=True, lifecycle=365)\n", "            print(f\"成功写入odps:{table_name}, partition_spec:{partition_spec}, attemp:{attemp}\")\n", "            # 返回true\n", "            return True\n", "        except Exception as e:\n", "            exception=e\n", "            error_message=str(e)\n", "            print(f\"写入ODPS不成功:{table_name}\", e)\n", "            traceback.print_exc()\n", "            time.sleep(10)\n", "    # 默认返回false\n", "    \n", "    if exception is not None:\n", "        raise exception\n", "    return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list_of_dummy=[{'inventory_control_name':'123'},{'inventory_control_name':'456'}]\n", "df=pd.DataFrame(list_of_dummy)\n", "\n", "is_all_df_columns_in_odps_table(df, 'summerfarm_ds.spider_cangzhen_product_result_df')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["qiyu_df = get_odps_sql_result_as_df(\n", "    \"\"\"SELECT  ds\n", "        ,staffname\n", "        ,direction\n", "        ,feishu_file_recognize_result AS \"飞书识别结果\"\n", "        ,communication_time_in_seconds AS \"通话时长(秒)\"\n", "        ,`对话还原`\n", "        ,`对话总结`\n", "        ,`销售达成情况`\n", "        ,`客情判断`\n", "FROM    summerfarm_ds.crm_qiyu_call_ai_analytics_di\n", "WHERE   ds = MAX_PT('summerfarm_ds.crm_qiyu_call_ai_analytics_di')\n", ";\"\"\"\n", ")\n", "\n", "qiyu_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to add HTML line breaks\n", "def add_line_breaks(text):\n", "    return text.replace(\"\\n\", \"<br>\")\n", "\n", "\n", "# Apply the function to specified columns\n", "columns_to_modify = [\"对话还原\", \"对话总结\", \"销售达成情况\", \"客情判断\"]\n", "for column in columns_to_modify:\n", "    qiyu_df[column] = qiyu_df[column].apply(add_line_breaks)\n", "\n", "# Display the modified DataFrame\n", "qiyu_df.to_html(\n", "    f\"./data/qiyu_df_{datetime.now().strftime('%Y%m%d')}.html\",\n", "    escape=False,\n", "    index=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tables=odps.list_tables(prefix=\"spider_\", project=\"summerfarm_ds\")\n", "for table in tables:\n", "    column_names = set([column.name for column in table.table_schema])\n", "    print(f\"column_names:{column_names},\\n table.name:{table.name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import httpx\n", "from openai import AzureOpenAI, OpenAI, Completion\n", "import traceback\n", "\n", "client_gpt4o = AzureOpenAI(\n", "    api_version=\"2024-02-15-preview\",\n", "    azure_endpoint=\"https://xm-ai-us2.openai.azure.com\",\n", "    api_key=os.getenv(\"AZURE_GPT4O_API_KEY\", \"\"),\n", "    http_client=httpx.Client(proxies=None),\n", ")\n", "\n", "\n", "def extract_sales_stock_and_names(columns: str, retry: int = 1):\n", "    if retry < 0:\n", "        return \"failed\"\n", "\n", "    print(f\"请求azure获取结果中...{columns}\")\n", "    try:\n", "        completion = client_gpt4o.chat.completions.create(\n", "            model=\"gpt-4o\",\n", "            temperature=0.1,\n", "            max_tokens=4095,\n", "            messages=[\n", "                {\n", "                    \"role\": \"system\",\n", "                    \"content\": f\"你是一个智能的数据分析师。\",\n", "                },\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": f\"\"\"这是一张表的字段合集:{columns}\n", "                    请你分别列出可能是\"库存\"、\"销售量(历史总量或者近期销售量)\"、\"名字(sku或者spu)\"、\"id\"  的字段。\n", "                    return pure JSON string like this: {{\"stock_columns\":[], \"sales_volumn_columns\":[],\"ids\":[], \"names\":[]}}\"\"\",\n", "                },\n", "            ],\n", "        )\n", "        response = completion.choices[0].message.content\n", "        print(response)\n", "        return response\n", "    except Exception as e:\n", "        traceback.print_exc()\n", "        return extract_sales_stock_and_names(columns=columns, retry=retry - 1)\n", "\n", "\n", "tables = odps.list_tables(prefix=\"spider_\", project=\"summerfarm_ds\")\n", "result = []\n", "for table in tables:\n", "    column_names = set([column.name for column in table.table_schema])\n", "    columns = \", \".join(column_names)\n", "    result.append(\n", "        {\n", "            \"table_name\": table.name,\n", "            \"columns\": columns,\n", "            \"result\": extract_sales_stock_and_names(columns=columns),\n", "        }\n", "    )\n", "\n", "result_df = pd.DataFrame(result)\n", "result_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assuming recordurl_df is your DataFrame\n", "import re\n", "import json\n", "\n", "pd.set_option('display.max_colwidth', None)\n", "def parse_json_from_markdown(markdown_json):\n", "    # Use regex to remove the ```json and ``` tags\n", "    cleaned_json = re.sub(r'^```json|```$', '', markdown_json, flags=re.MULTILINE).strip()\n", "    \n", "    # Load the cleaned JSON string into a dictionary\n", "    try:\n", "        json_data = json.loads(cleaned_json)\n", "    except json.JSONDecodeError:\n", "        json_data = {}\n", "    \n", "    return json_data\n", "\n", "# Apply the function to create new columns\n", "result_df['parsed_json'] = result_df['result'].apply(parse_json_from_markdown)\n", "result_df['stock_columns'] = result_df['parsed_json'].apply(lambda x: x.get('stock_columns', []))\n", "result_df['ids'] = result_df['parsed_json'].apply(lambda x: x.get('ids', []))\n", "result_df['names'] = result_df['parsed_json'].apply(lambda x: x.get('names', []))\n", "result_df['sales_volumn_columns'] = result_df['parsed_json'].apply(lambda x: x.get('sales_volumn_columns', []))\n", "\n", "result_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql_list = []\n", "\n", "\n", "def build_odps_sql_for_row(row):\n", "    table_name = row[\"table_name\"]\n", "    if \"result\" in row[\"result\"]:\n", "        print(f\"失败了：{table_name}\")\n", "        return \"failed\"\n", "    columns_to_select = [\"ds\", f\"'{table_name}' as table_name\", \"competitor\"]\n", "    columns_to_select.extend(row[\"ids\"])\n", "    for col in row[\"names\"]:\n", "        if \"competitor_name\" in col:\n", "            continue\n", "        else:\n", "            columns_to_select.append(col)\n", "    if len(row[\"sales_volumn_columns\"]) <= 0 and len(row[\"stock_columns\"]) <= 0:\n", "        print(f\"{table_name} 似乎也出错了，没有找到任何库存或者销量字段\")\n", "        return \"failed\"\n", "    columns_to_select.extend(row[\"sales_volumn_columns\"])\n", "    columns_to_select.extend(row[\"stock_columns\"])\n", "    return f\"\"\"select {','.join(columns_to_select)} \n", "                    from summerfarm_ds.{table_name} where ds=max_pt('summerfarm_ds.{table_name}') limit 200;\"\"\"\n", "\n", "\n", "result_df[\"odps_sql\"] = result_df.apply(build_odps_sql_for_row, axis=1)\n", "\n", "result_df[[\"odps_sql\", \"table_name\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import get_odps_sql_result_as_df\n", "\n", "for index, row in result_df.iterrows():\n", "    sql = row[\"odps_sql\"]\n", "    if \"fail\" in sql:\n", "        print(f\"失败了:{sql}\")\n", "        continue\n", "    df = get_odps_sql_result_as_df(sql)\n", "    df.to_csv(f\"./data/{row['table_name']}_销量与库存字段.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<re.Match object; span=(0, 2), match='零食'>\n"]}], "source": ["import re\n", "\n", "pattern = re.compile(r\"零食|耗材|买过多次|礼盒\")\n", "print(pattern.match(\"零食\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}
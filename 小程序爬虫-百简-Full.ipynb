{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 10:47:01, date_of_now:2024-03-06, brand_name:百简\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/百简/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY='********************************'\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "brand_name=\"百简\"\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 10:30:47, date_of_now:2024-03-06, brand_name:百简\n", "{'domainhost': 'cdbj.sdongpo.com'}\n", "2024-03-06 10:30:47\n"]}], "source": ["import requests\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "headers={\"domainhost\":\"cdbj.sdongpo.com\"}\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "brand_name=\"百简\"\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}\")\n", "root_path=f'./data/{brand_name}'\n", "create_directory_if_not_exists(root_path)\n", "print(headers)\n", "print(time_of_now)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["************:38451\n", "*************:36789\n", "**************:35513\n", "***************:41120\n", "*************:31417\n", "**************:33734\n", "**************:41732\n", "*************:39510\n", "***************:47205\n", "**************:47189\n", "['************:38451', '*************:36789', '**************:35513', '***************:41120', '*************:31417', '**************:33734', '**************:41732', '*************:39510', '***************:47205', '**************:47189']\n"]}], "source": ["def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "import requests\n", "import random\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url, max_retries=3):\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, proxies=proxies,headers=headers, timeout=30)\n", "            if response.status_code == 200:\n", "                return response.text\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None\n", "\n", "def get_remote_data_with_header_proxy(url, max_retries=3):\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response.text\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 10:30:53, date_of_now:2024-03-06, brand_name:百简\n", "2024-03-06 10:30:53\n"]}], "source": ["import requests\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "headers={\"domainhost\":\"cdbj.sdongpo.com\"}\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "brand_name=\"百简\"\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}\")\n", "root_path=f'./data/{brand_name}'\n", "create_directory_if_not_exists(root_path)\n", "\n", "print(time_of_now)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: *************************************************\n", "{\"status\":1,\"data\":{\"user_id\":\"1019\",\"site_id\":\"6\",\"token\":\"3b73c3d9b816243dec11038f688e7295\",\"is_sales\":\"N\",\"update_code\":\"cdbj.sdongpo.com\",\"is_admin_group\":false,\"curr_edition\":\"0\"},\"message\":null}\n", "3b73c3d9b816243dec11038f688e7295\n"]}], "source": ["# 登录\n", "url='https://scm.shudongpoo.com/commonApi/wapData/checkLogin?username=13883824673&password=bj123456&version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=0&group_user_id=0&site_id=6&token='\n", "token_info=get_remote_data_with_proxy(url,3)\n", "print(token_info)\n", "token_info=json.loads(token_info)\n", "token=token_info['data']['token']\n", "\n", "print(token)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 爬取类目树"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: *************************************************\n", "{\"status\":1,\"data\":{\"categoryList\":[{\"id\":\"298\",\"level\":\"1\",\"name\":\"\\u6c34\\u679c\",\"pic_category\":\"\",\"pic_path_big\":\"\",\"pid\":\"0\",\"sequence\":\"999\",\"alias_name\":\"\"},{\"id\":\"299\",\"level\":\"1\",\"name\":\"\\u852c\\u83dc\",\"pic_category\":\"\",\"pic_path_big\":\"\",\"pid\":\"0\",\"sequence\":\"998\",\"alias_name\":\"\"},{\"id\":\"303\",\"level\":\"1\",\"name\":\"\\u9999\\u6599\",\"pic_category\":\"\",\"pic_path_big\":\"\",\"pid\":\"0\",\"sequence\":\"997\",\"alias_name\":\"\"},{\"id\":\"300\",\"level\":\"1\",\"name\":\"\\u8089\\u79bd\\u86cb\",\"pic_category\":\"\",\"pic_path_big\":\"\",\"pid\":\"0\",\"sequence\":\"996\",\"alias_name\":\"\"},{\"id\":\"397\",\"level\":\"1\",\"name\":\"\\u4e66\\u4ea6\\u516c\\u53f8\\u4e13\\u7528\",\"pic_category\":\"\",\"pic_path_big\":\"\",\"pid\":\"0\",\"sequence\":\"1\",\"alias_name\":\"\"},{\"id\":\"401\",\"level\":\"1\",\"name\":\"\\u5ddd\\u4e4b\\u5473\\u4e13\\u7528\",\"pic_category\":\"\",\"pic_path_big\":\"\",\"pid\":\"0\",\"sequence\":\"0\",\"alias_name\":\"\"}]},\"message\":null}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>level</th>\n", "      <th>name</th>\n", "      <th>pic_category</th>\n", "      <th>pic_path_big</th>\n", "      <th>pid</th>\n", "      <th>sequence</th>\n", "      <th>alias_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>298</td>\n", "      <td>1</td>\n", "      <td>水果</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>999</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>299</td>\n", "      <td>1</td>\n", "      <td>蔬菜</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>998</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>303</td>\n", "      <td>1</td>\n", "      <td>香料</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>997</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>300</td>\n", "      <td>1</td>\n", "      <td>肉禽蛋</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>996</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>397</td>\n", "      <td>1</td>\n", "      <td>书亦公司专用</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>401</td>\n", "      <td>1</td>\n", "      <td>川之味专用</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id level    name pic_category pic_path_big pid sequence alias_name\n", "0  298     1      水果                             0      999           \n", "1  299     1      蔬菜                             0      998           \n", "2  303     1      香料                             0      997           \n", "3  300     1     肉禽蛋                             0      996           \n", "4  397     1  书亦公司专用                             0        1           \n", "5  401     1   川之味专用                             0        0           "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取一级类目列表\n", "\n", "url='https://scm.shudongpoo.com/commonApi/wapData/categoryList?version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=1019&group_user_id=0&site_id=6&token={token}'\n", "\n", "categoryList=get_remote_data_with_header_proxy(url)\n", "print(categoryList)\n", "categoryList=json.loads(categoryList)['data']['categoryList']\n", "root_cate_df=None\n", "root_cate_df = pd.DataFrame(categoryList)\n", "root_cate_df"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_colwidth', None)\n", "root_cate_df.to_csv(f'./data/{brand_name}/{brand_name}一级类目(顶部).csv',index=False, encoding='utf_8_sig')"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["298\n", "Using proxy: ***********************************************\n", "299\n", "Using proxy: ************************************************\n", "303\n", "Using proxy: *************************************************\n", "300\n", "Using proxy: ************************************************\n", "397\n", "Using proxy: ***********************************************\n", "401\n", "Using proxy: **********************************************\n", "[{'id': '305', 'level': '2', 'name': '柑橘橙柚柠', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '998', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '306', 'level': '2', 'name': '桃李枣杏梅', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '997', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '307', 'level': '2', 'name': '荔枝菠萝热带水果', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '996', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '308', 'level': '2', 'name': '板栗马蹄瓜果', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '995', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '309', 'level': '2', 'name': '草莓圣女果浆果', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '994', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '329', 'level': '2', 'name': '葡提奇异果', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '993', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '328', 'level': '2', 'name': '运输费用', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '992', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '310', 'level': '2', 'name': '叶菜花菜', 'pic_category': '', 'pic_path_big': '', 'pid': '299', 'sequence': '999', 'alias_name': '', '一级类目ID': '299', '一级类目名': '蔬菜'}, {'id': '311', 'level': '2', 'name': '土豆根茎', 'pic_category': '', 'pic_path_big': '', 'pid': '299', 'sequence': '998', 'alias_name': '', '一级类目ID': '299', '一级类目名': '蔬菜'}, {'id': '312', 'level': '2', 'name': '茄果豆瓜', 'pic_category': '', 'pic_path_big': '', 'pid': '299', 'sequence': '997', 'alias_name': '', '一级类目ID': '299', '一级类目名': '蔬菜'}, {'id': '313', 'level': '2', 'name': '鲜菌菇', 'pic_category': '', 'pic_path_big': '', 'pid': '299', 'sequence': '996', 'alias_name': '', '一级类目ID': '299', '一级类目名': '蔬菜'}, {'id': '314', 'level': '2', 'name': '葱姜蒜辣椒调料', 'pic_category': '', 'pic_path_big': '', 'pid': '299', 'sequence': '995', 'alias_name': '', '一级类目ID': '299', '一级类目名': '蔬菜'}, {'id': '315', 'level': '2', 'name': '芽苗藕水生', 'pic_category': '', 'pic_path_big': '', 'pid': '299', 'sequence': '994', 'alias_name': '', '一级类目ID': '299', '一级类目名': '蔬菜'}, {'id': '316', 'level': '2', 'name': '特菜野菜其他', 'pic_category': '', 'pic_path_big': '', 'pid': '299', 'sequence': '993', 'alias_name': '', '一级类目ID': '299', '一级类目名': '蔬菜'}, {'id': '323', 'level': '2', 'name': '香草', 'pic_category': '', 'pic_path_big': '', 'pid': '303', 'sequence': '999', 'alias_name': '', '一级类目ID': '303', '一级类目名': '香料'}, {'id': '318', 'level': '2', 'name': '蛋', 'pic_category': '', 'pic_path_big': '', 'pid': '300', 'sequence': '999', 'alias_name': '', '一级类目ID': '300', '一级类目名': '肉禽蛋'}, {'id': '319', 'level': '2', 'name': '驴其他', 'pic_category': '', 'pic_path_big': '', 'pid': '300', 'sequence': '998', 'alias_name': '', '一级类目ID': '300', '一级类目名': '肉禽蛋'}, {'id': '398', 'level': '2', 'name': '鲜果系列', 'pic_category': '', 'pic_path_big': '', 'pid': '397', 'sequence': '0', 'alias_name': '', '一级类目ID': '397', '一级类目名': '书亦公司专用'}, {'id': '402', 'level': '2', 'name': '专用', 'pic_category': '', 'pic_path_big': '', 'pid': '401', 'sequence': '0', 'alias_name': '', '一级类目ID': '401', '一级类目名': '川之味专用'}]\n"]}], "source": ["# 根据一级类目获取所有二级类目\n", "all_second_category = []\n", "for first_cate in categoryList:\n", "    pid=first_cate['id']\n", "    first_cate_name = first_cate['name']\n", "    print(pid)\n", "    url=f'https://scm.shudongpoo.com/commonApi/wapData/categoryList?pid={pid}&version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=1019&group_user_id=0&site_id=6&token={token}'\n", "    second_category_list=get_remote_data_with_header_proxy(url,3)\n", "    second_category_list=json.loads(second_category_list)['data']['categoryList']\n", "    for second_cate in second_category_list:\n", "        second_cate['一级类目ID']=pid\n", "        second_cate['一级类目名']=first_cate_name\n", "        all_second_category.append(second_cate)\n", "    \n", "print(all_second_category)\n", "pd.DataFrame(all_second_category).to_csv(f'{root_path}/二级类目列表.csv',index=False, encoding='utf_8_sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据类目ID获取类目的商品"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["298 305 柑橘橙柚柠\n", "Using proxy: *************************************************\n", "商品个数： 柑橘橙柚柠 2\n", "298 306 桃李枣杏梅\n", "Using proxy: ************************************************\n", "商品个数： 桃李枣杏梅 1\n", "298 307 荔枝菠萝热带水果\n", "Using proxy: ***********************************************\n", "商品个数： 荔枝菠萝热带水果 3\n", "298 308 板栗马蹄瓜果\n", "Using proxy: ***********************************************\n", "商品个数： 板栗马蹄瓜果 2\n", "298 309 草莓圣女果浆果\n", "Using proxy: *************************************************\n", "商品个数： 草莓圣女果浆果 3\n", "298 329 葡提奇异果\n", "Using proxy: ************************************************\n", "商品个数： 葡提奇异果 2\n", "298 328 运输费用\n", "Using proxy: **********************************************\n", "商品个数： 运输费用 1\n", "299 310 叶菜花菜\n", "Using proxy: *************************************************\n", "商品个数： 叶菜花菜 0\n", "299 311 土豆根茎\n", "Using proxy: ***********************************************\n", "商品个数： 土豆根茎 0\n", "299 312 茄果豆瓜\n", "Using proxy: ***********************************************\n", "商品个数： 茄果豆瓜 0\n", "299 313 鲜菌菇\n", "Using proxy: ***********************************************\n", "商品个数： 鲜菌菇 0\n", "299 314 葱姜蒜辣椒调料\n", "Using proxy: ***********************************************\n", "商品个数： 葱姜蒜辣椒调料 0\n", "299 315 芽苗藕水生\n", "Using proxy: ************************************************\n", "商品个数： 芽苗藕水生 0\n", "299 316 特菜野菜其他\n", "Using proxy: ***********************************************\n", "商品个数： 特菜野菜其他 0\n", "303 323 香草\n", "Using proxy: **********************************************\n", "商品个数： 香草 1\n", "300 318 蛋\n", "Using proxy: *************************************************\n", "商品个数： 蛋 0\n", "300 319 驴其他\n", "Using proxy: ***********************************************\n", "商品个数： 驴其他 0\n", "397 398 鲜果系列\n", "Using proxy: ***********************************************\n", "商品个数： 鲜果系列 0\n", "401 402 专用\n", "Using proxy: ************************************************\n", "商品个数： 专用 0\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>is_sell</th>\n", "      <th>is_sell_stock</th>\n", "      <th>sell_stock</th>\n", "      <th>commodity_mark</th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>notice</th>\n", "      <th>price</th>\n", "      <th>unit</th>\n", "      <th>summary</th>\n", "      <th>...</th>\n", "      <th>min_price</th>\n", "      <th>section_price</th>\n", "      <th>section_price_desc</th>\n", "      <th>detail_pic</th>\n", "      <th>video</th>\n", "      <th>within_operation_time</th>\n", "      <th>operating_time</th>\n", "      <th>provider_name</th>\n", "      <th>quaImg</th>\n", "      <th>quaImg_arr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>3</td>\n", "      <td>154</td>\n", "      <td>广东香水柠檬</td>\n", "      <td>广东香水柠檬露天果子会有个别果子有花皮，介意者请谨慎下单哦</td>\n", "      <td>9.00</td>\n", "      <td>斤</td>\n", "      <td>季节原因，果子会偏黄一级轻微碰伤处于正常情况</td>\n", "      <td>...</td>\n", "      <td>9.00</td>\n", "      <td>9.00</td>\n", "      <td>9.00</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>True</td>\n", "      <td>{'default_delivery_date': '2024-03-07', 'is_support_today_delivery': 'N', 'delivery_date_period': ['2024-03-07'], 'delivery_time': [{'id': '2', 'start_time': '04:00', 'end_time': '14:00', 'site_id': '6', 'is_default': '1', 'name': ''}], 'link_type': '1', 'order_deliver_time': 1, 'order_deliver_date': 1}</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>[]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 75 columns</p>\n", "</div>"], "text/plain": ["  is_sell is_sell_stock sell_stock commodity_mark   id    name  \\\n", "0       1             0       0.00              3  154  广东香水柠檬   \n", "\n", "                          notice price unit                 summary  ...  \\\n", "0  广东香水柠檬露天果子会有个别果子有花皮，介意者请谨慎下单哦  9.00    斤  季节原因，果子会偏黄一级轻微碰伤处于正常情况  ...   \n", "\n", "  min_price section_price section_price_desc detail_pic video  \\\n", "0      9.00          9.00               9.00         []    []   \n", "\n", "  within_operation_time  \\\n", "0                  True   \n", "\n", "                                                                                                                                                                                                                                                                                                     operating_time  \\\n", "0  {'default_delivery_date': '2024-03-07', 'is_support_today_delivery': 'N', 'delivery_date_period': ['2024-03-07'], 'delivery_time': [{'id': '2', 'start_time': '04:00', 'end_time': '14:00', 'site_id': '6', 'is_default': '1', 'name': ''}], 'link_type': '1', 'order_deliver_time': 1, 'order_deliver_date': 1}   \n", "\n", "  provider_name quaImg quaImg_arr  \n", "0                              []  \n", "\n", "[1 rows x 75 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["# 根据一级和二级类目ID爬取商品信息, 返回array\n", "def get_products_for_category(cateLevel1Id=298, cateLevel2Id=305):\n", "    url=f'https://scm.shudongpoo.com/commonApi/wapData/goodsList?type_id={cateLevel1Id}&type_id2={cateLevel2Id}&page=1&pageSize=50&page_size=50&version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=1019&group_user_id=0&site_id=6&token={token}'\n", "    products=json.loads(get_remote_data_with_header_proxy(url,3))['data']['list']\n", "    return products\n", "\n", "all_product_raw=[]\n", "for cate in all_second_category:\n", "    print(cate['一级类目ID'], cate['id'],cate['name'])\n", "    sub_list = get_products_for_category(cate['一级类目ID'], cate['id'])\n", "    print('商品个数：',cate['name'],len(sub_list))\n", "    all_product_raw.extend(sub_list)\n", "\n", "all_product_raw_df=pd.DataFrame(all_product_raw)\n", "all_product_raw_df.to_csv(f'{root_path}/所有商品列表raw.csv', index=False, encoding='utf_8_sig')\n", "\n", "all_product_raw_df.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["清洗商品数据"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>alias</th>\n", "      <th>brand</th>\n", "      <th>create_time</th>\n", "      <th>logo</th>\n", "      <th>price</th>\n", "      <th>min_price</th>\n", "      <th>max_price</th>\n", "      <th>order_quantity</th>\n", "      <th>summary</th>\n", "      <th>unit</th>\n", "      <th>unit_num</th>\n", "      <th>数据获取时间</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>154</td>\n", "      <td>广东香水柠檬</td>\n", "      <td>香水柠檬</td>\n", "      <td></td>\n", "      <td>2022-05-24 12:51:03</td>\n", "      <td>https://base-image.shudongpoo.com/base_1320/upload_pic/头图_com_thumb_202307111149281162e7b564acd1481d77f.png</td>\n", "      <td>9.00</td>\n", "      <td>9.00</td>\n", "      <td>9.00</td>\n", "      <td>1.00</td>\n", "      <td>季节原因，果子会偏黄一级轻微碰伤处于正常情况</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>172</td>\n", "      <td>耙耙柑</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2022-05-24 12:51:07</td>\n", "      <td>https://base-image.shudongpoo.com/base_1190/upload_pic/com_thumb_202205241316252c0a9d12628c6a29a0b69.png</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>3.00</td>\n", "      <td></td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1637</td>\n", "      <td>车厘子-XL</td>\n", "      <td>进口大樱桃</td>\n", "      <td></td>\n", "      <td>2024-01-26 16:32:43</td>\n", "      <td>https://base-image.shudongpoo.com/base_1380/upload_pic/300_com_thumb_20240205130544d452035365c06ca89cd72.jfif</td>\n", "      <td>30.00</td>\n", "      <td>30.00</td>\n", "      <td>30.00</td>\n", "      <td>1.00</td>\n", "      <td>XLD</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>89</td>\n", "      <td>小台芒</td>\n", "      <td>芒果</td>\n", "      <td></td>\n", "      <td>2022-04-28 15:33:01</td>\n", "      <td>https://base-image.shudongpoo.com/base_1180/upload_pic/com_thumb_202204281549407feb1d7a626a47142fd6e.jpg</td>\n", "      <td>6.00</td>\n", "      <td>6.00</td>\n", "      <td>6.00</td>\n", "      <td>1.00</td>\n", "      <td></td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>114</td>\n", "      <td>大台芒</td>\n", "      <td>芒果</td>\n", "      <td></td>\n", "      <td>2022-05-08 18:04:45</td>\n", "      <td>https://base-image.shudongpoo.com/sdpcloud/upload_pic/com_thumb_20180613180106a38366e65b20eb6291ce0.jpg</td>\n", "      <td>6.50</td>\n", "      <td>6.50</td>\n", "      <td>6.50</td>\n", "      <td>1.00</td>\n", "      <td>单果≥150g</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>152</td>\n", "      <td>无冠都乐金菠萝</td>\n", "      <td>菠萝</td>\n", "      <td>都乐</td>\n", "      <td>2022-05-24 12:51:03</td>\n", "      <td>https://base-image.shudongpoo.com/base_1230/upload_pic/com_thumb_2022052413202629b166e5628c6b1a2832e_com_thumb_2022100317340309cc38c4633aac8bd9505.png</td>\n", "      <td>7.50</td>\n", "      <td>7.00</td>\n", "      <td>7.50</td>\n", "      <td>3.50</td>\n", "      <td>每个约2.7-3.6斤</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>30</td>\n", "      <td>无籽西瓜</td>\n", "      <td>西瓜</td>\n", "      <td></td>\n", "      <td>2022-03-01 09:39:32</td>\n", "      <td>https://base-image.shudongpoo.com/base_1180/upload_pic/com_thumb_2022043016473550c247f4626cf7a71730a.jpg</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>7.00</td>\n", "      <td>毛重带箱，一个西瓜约7-15斤。</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1194</td>\n", "      <td>牛油果【斤】</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2023-04-08 18:00:41</td>\n", "      <td>https://base-image.shudongpoo.com/sdpcloud/upload_pic/com_thumb_20180613180104a2d5d9a25b20eb60e5a40.jpg</td>\n", "      <td>25.00</td>\n", "      <td>25.00</td>\n", "      <td>25.00</td>\n", "      <td>1.00</td>\n", "      <td>坏果率超过5%售后   5%以内免赔</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>315</td>\n", "      <td>德昌草莓（黔莓）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2022-12-03 00:16:19</td>\n", "      <td>https://base-image.shudongpoo.com/base_1250/upload_pic/src=http___img95_com_thumb_20221203001605f8b746f2638a24c58dc0f.699pic</td>\n", "      <td>75.00</td>\n", "      <td>73.33</td>\n", "      <td>73.33</td>\n", "      <td>1.00</td>\n", "      <td></td>\n", "      <td>件</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>317</td>\n", "      <td>红颜草莓4X7</td>\n", "      <td>草莓</td>\n", "      <td></td>\n", "      <td>2022-12-03 02:51:45</td>\n", "      <td>https://base-image.shudongpoo.com/base_1250/upload_pic/u=1802527260,1849689251&amp;fm=193_com_thumb_202212030252078721a680638a49576cbfd.jfif</td>\n", "      <td>9.00</td>\n", "      <td>9.00</td>\n", "      <td>9.00</td>\n", "      <td>1.00</td>\n", "      <td></td>\n", "      <td>盒</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>355</td>\n", "      <td>红颜草莓4X5</td>\n", "      <td>红颜草莓</td>\n", "      <td></td>\n", "      <td>2022-12-31 19:03:18</td>\n", "      <td>https://base-image.shudongpoo.com/base_1360/upload_pic/ffbc852f60c7f41a7b73368dcd7815b_com_thumb_20231206101757a1fa56a1656fd9d5cd483.png</td>\n", "      <td>11.00</td>\n", "      <td>11.00</td>\n", "      <td>11.00</td>\n", "      <td>1.00</td>\n", "      <td>红颜草莓盒装4X5（纸盒）</td>\n", "      <td>盒</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>162</td>\n", "      <td>夏黑葡萄</td>\n", "      <td>葡萄</td>\n", "      <td></td>\n", "      <td>2022-05-24 12:51:05</td>\n", "      <td>https://base-image.shudongpoo.com/base_1190/upload_pic/com_thumb_202205241321384afba1b9628c6b622c5e3.jpg</td>\n", "      <td>10.50</td>\n", "      <td>10.50</td>\n", "      <td>10.50</td>\n", "      <td>3.00</td>\n", "      <td></td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>1411</td>\n", "      <td>阳光玫瑰（一级）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2023-07-12 19:01:29</td>\n", "      <td>https://base-image.shudongpoo.com/base_1320/upload_pic/头图_com_thumb_20230712190246426df11664ae885633f66.jpg</td>\n", "      <td>19.00</td>\n", "      <td>19.00</td>\n", "      <td>19.00</td>\n", "      <td>3.00</td>\n", "      <td>一级果</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>134</td>\n", "      <td>配送费</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2022-05-24 12:35:31</td>\n", "      <td>https://base-image.shudongpoo.com/base_1190/upload_pic/com_thumb_202205241440270f900e8a628c7ddb64092.jpg</td>\n", "      <td>15.00</td>\n", "      <td>15.00</td>\n", "      <td>15.00</td>\n", "      <td>1.00</td>\n", "      <td>订单未满配送要求则收取15元配送费。</td>\n", "      <td>次</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>157</td>\n", "      <td>香茅</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2022-05-24 12:51:04</td>\n", "      <td>https://base-image.shudongpoo.com/base_1190/upload_pic/com_thumb_202205241322172c6463d6628c6b89bbcf5.jpeg</td>\n", "      <td>4.50</td>\n", "      <td>4.50</td>\n", "      <td>4.50</td>\n", "      <td>1.00</td>\n", "      <td></td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-03-06 10:30:53</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id      name  alias brand          create_time  \\\n", "0    154    广东香水柠檬   香水柠檬        2022-05-24 12:51:03   \n", "1    172       耙耙柑               2022-05-24 12:51:07   \n", "2   1637    车厘子-<PERSON><PERSON>  进口大樱桃        2024-01-26 16:32:43   \n", "3     89       小台芒     芒果        2022-04-28 15:33:01   \n", "4    114       大台芒     芒果        2022-05-08 18:04:45   \n", "5    152   无冠都乐金菠萝     菠萝    都乐  2022-05-24 12:51:03   \n", "6     30      无籽西瓜     西瓜        2022-03-01 09:39:32   \n", "7   1194    牛油果【斤】               2023-04-08 18:00:41   \n", "8    315  德昌草莓（黔莓）               2022-12-03 00:16:19   \n", "9    317   红颜草莓4X7     草莓        2022-12-03 02:51:45   \n", "10   355   红颜草莓4X5   红颜草莓        2022-12-31 19:03:18   \n", "11   162      夏黑葡萄     葡萄        2022-05-24 12:51:05   \n", "12  1411  阳光玫瑰（一级）               2023-07-12 19:01:29   \n", "13   134       配送费               2022-05-24 12:35:31   \n", "14   157        香茅               2022-05-24 12:51:04   \n", "\n", "                                                                                                                                                      logo  \\\n", "0                                              https://base-image.shudongpoo.com/base_1320/upload_pic/头图_com_thumb_202307111149281162e7b564acd1481d77f.png   \n", "1                                                 https://base-image.shudongpoo.com/base_1190/upload_pic/com_thumb_202205241316252c0a9d12628c6a29a0b69.png   \n", "2                                            https://base-image.shudongpoo.com/base_1380/upload_pic/300_com_thumb_20240205130544d452035365c06ca89cd72.jfif   \n", "3                                                 https://base-image.shudongpoo.com/base_1180/upload_pic/com_thumb_202204281549407feb1d7a626a47142fd6e.jpg   \n", "4                                                  https://base-image.shudongpoo.com/sdpcloud/upload_pic/com_thumb_20180613180106a38366e65b20eb6291ce0.jpg   \n", "5   https://base-image.shudongpoo.com/base_1230/upload_pic/com_thumb_2022052413202629b166e5628c6b1a2832e_com_thumb_2022100317340309cc38c4633aac8bd9505.png   \n", "6                                                 https://base-image.shudongpoo.com/base_1180/upload_pic/com_thumb_2022043016473550c247f4626cf7a71730a.jpg   \n", "7                                                  https://base-image.shudongpoo.com/sdpcloud/upload_pic/com_thumb_20180613180104a2d5d9a25b20eb60e5a40.jpg   \n", "8                             https://base-image.shudongpoo.com/base_1250/upload_pic/src=http___img95_com_thumb_20221203001605f8b746f2638a24c58dc0f.699pic   \n", "9                 https://base-image.shudongpoo.com/base_1250/upload_pic/u=1802527260,1849689251&fm=193_com_thumb_202212030252078721a680638a49576cbfd.jfif   \n", "10                https://base-image.shudongpoo.com/base_1360/upload_pic/ffbc852f60c7f41a7b73368dcd7815b_com_thumb_20231206101757a1fa56a1656fd9d5cd483.png   \n", "11                                                https://base-image.shudongpoo.com/base_1190/upload_pic/com_thumb_202205241321384afba1b9628c6b622c5e3.jpg   \n", "12                                             https://base-image.shudongpoo.com/base_1320/upload_pic/头图_com_thumb_20230712190246426df11664ae885633f66.jpg   \n", "13                                                https://base-image.shudongpoo.com/base_1190/upload_pic/com_thumb_202205241440270f900e8a628c7ddb64092.jpg   \n", "14                                               https://base-image.shudongpoo.com/base_1190/upload_pic/com_thumb_202205241322172c6463d6628c6b89bbcf5.jpeg   \n", "\n", "    price min_price max_price order_quantity                 summary unit  \\\n", "0    9.00      9.00      9.00           1.00  季节原因，果子会偏黄一级轻微碰伤处于正常情况    斤   \n", "1    4.00      4.00      4.00           3.00                            斤   \n", "2   30.00     30.00     30.00           1.00                     XLD    斤   \n", "3    6.00      6.00      6.00           1.00                            斤   \n", "4    6.50      6.50      6.50           1.00                 单果≥150g    斤   \n", "5    7.50      7.00      7.50           3.50             每个约2.7-3.6斤    斤   \n", "6    4.00      4.00      4.00           7.00        毛重带箱，一个西瓜约7-15斤。    斤   \n", "7   25.00     25.00     25.00           1.00      坏果率超过5%售后   5%以内免赔    斤   \n", "8   75.00     73.33     73.33           1.00                            件   \n", "9    9.00      9.00      9.00           1.00                            盒   \n", "10  11.00     11.00     11.00           1.00           红颜草莓盒装4X5（纸盒）    盒   \n", "11  10.50     10.50     10.50           3.00                            斤   \n", "12  19.00     19.00     19.00           3.00                     一级果    斤   \n", "13  15.00     15.00     15.00           1.00      订单未满配送要求则收取15元配送费。    次   \n", "14   4.50      4.50      4.50           1.00                            斤   \n", "\n", "   unit_num               数据获取时间  \n", "0      1.00  2024-03-06 10:30:53  \n", "1      1.00  2024-03-06 10:30:53  \n", "2      1.00  2024-03-06 10:30:53  \n", "3      1.00  2024-03-06 10:30:53  \n", "4      1.00  2024-03-06 10:30:53  \n", "5      1.00  2024-03-06 10:30:53  \n", "6      1.00  2024-03-06 10:30:53  \n", "7      1.00  2024-03-06 10:30:53  \n", "8      1.00  2024-03-06 10:30:53  \n", "9      1.00  2024-03-06 10:30:53  \n", "10     1.00  2024-03-06 10:30:53  \n", "11     1.00  2024-03-06 10:30:53  \n", "12     1.00  2024-03-06 10:30:53  \n", "13     1.00  2024-03-06 10:30:53  \n", "14     1.00  2024-03-06 10:30:53  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# print(all_product_raw_df.columns)\n", "all_product_clean_df=all_product_raw_df[['id','name','alias','brand','create_time','logo','price','min_price','max_price','order_quantity','summary','unit','unit_num']].copy()\n", "all_product_clean_df['数据获取时间']=time_of_now\n", "all_product_clean_df.to_csv(f\"{root_path}/所有商品列表-清洗后-{time_of_now.split(' ')[0]}.csv\")\n", "all_product_clean_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:广东香水柠檬, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:2844.3496227264404ms\n", "inputText:耙耙柑, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:668.8954830169678ms\n", "inputText:车厘子-XL, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:523.003339767456ms\n", "inputText:小台芒, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:550.1787662506104ms\n", "inputText:大台芒, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:696.7625617980957ms\n", "inputText:无冠都乐金菠萝, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:560.4932308197021ms\n", "inputText:无籽西瓜, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:499.42541122436523ms\n", "inputText:牛油果【斤】, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:508.20398330688477ms\n", "inputText:德昌草莓（黔莓）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:529.0281772613525ms\n", "inputText:红颜草莓4X7, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:637.9671096801758ms\n", "inputText:红颜草莓4X5, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:538.332462310791ms\n", "inputText:夏黑葡萄, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:629.6498775482178ms\n", "inputText:阳光玫瑰（一级）, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:629.7800540924072ms\n", "inputText:配送费, usage:{\"prompt_tokens\": 3, \"total_tokens\": 3}, time cost:510.4525089263916ms\n", "inputText:香茅, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:511.84630393981934ms\n"]}], "source": ["all_product_clean_df['title_embedding']=all_product_clean_df['name'].apply(getEmbeddingsFromAzure)\n", "all_product_clean_df.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False)\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
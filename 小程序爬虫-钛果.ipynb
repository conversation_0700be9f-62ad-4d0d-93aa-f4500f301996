{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "def randomString(length):\n", "    length = length or 32\n", "    chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'\n", "    maxPos = len(chars)\n", "    pwd = ''\n", "    for i in range(length):\n", "        pwd += chars[int(random.random() * maxPos)]\n", "    return pwd\n", "\n", "print(randomString(3))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["username = \"***********\"\n", "pwd = \"0b5e0b72c9864a6d348ea9fc72cad5129b102d3dbd399307765ffc417ad21b40\"\n", "login_object = {\n", "    \"u\": f\"{username}{randomString(5)}\",\n", "    \"su\": None,\n", "    \"p\": f\"{pwd}{randomString(6)}\",\n", "    \"c\": None,\n", "    \"sc\": None,\n", "    \"d\": None,\n", "    \"al\": True,\n", "    \"e\": f\"{randomString(3)}0{randomString(6)}\",\n", "}\n", "\n", "\n", "login_object\n", "\n", "url = \"https://www.tigo1000.com/login.php?0.32836446903677086\"\n", "\n", "auth_cookie_value = \"5f2632d2ea504a338a1ed40029a41f2f\"\n", "\n", "payload = \"u=***********zfySS&su=&p=0b5e0b72c9864a6d348ea9fc72cad5129b102d3dbd399307765ffc417ad21b40j3Drzw&al=false&e=bwe02Ht7pw\"\n", "\n", "import requests\n", "\n", "response=requests.post(\n", "    url=url,\n", "    params=login_object,\n", "    headers={\n", "        \"accept\": \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\",\n", "        \"accept-language\": \"en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6\",\n", "        \"sec-ch-ua\": '\"Google Chrome\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"',\n", "        \"sec-ch-ua-mobile\": \"?0\",\n", "        \"sec-ch-ua-platform\": '\"macOS\"',\n", "        \"sec-fetch-dest\": \"document\",\n", "        \"sec-fetch-mode\": \"navigate\",\n", "        \"sec-fetch-site\": \"same-site\",\n", "        \"upgrade-insecure-requests\": \"1\",\n", "        \"user-agent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n", "    },\n", ")\n", "\n", "print(response.status_code, response.text)\n", "new_cookies=response.cookies.get_dict()\n", "response_headers=response.headers\n", "\n", "new_cookies\n", "response_headers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = requests.get(\n", "    \"https://app.tigo1000.com\",\n", "    cookies=new_cookies,\n", "    allow_redirects=False,\n", "    headers={\n", "        \"accept\": \"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\",\n", "        \"accept-language\": \"en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6\",\n", "        \"sec-ch-ua\": '\"Google Chrome\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"',\n", "        \"sec-ch-ua-mobile\": \"?0\",\n", "        \"sec-ch-ua-platform\": '\"macOS\"',\n", "        \"sec-fetch-dest\": \"document\",\n", "        \"sec-fetch-mode\": \"navigate\",\n", "        \"sec-fetch-site\": \"same-site\",\n", "        \"upgrade-insecure-requests\": \"1\",\n", "        \"user-agent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n", "    },\n", ")\n", "\n", "print(f\"response.status_code:{response.status_code},response.text{response.text}\\n\\n\\n\")\n", "print(f\"cookies:{response.cookies.get_dict()}, headers:{response.headers}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["headers = {\n", "    \"accept\": \"*/*\",\n", "    \"accept-language\": \"en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6\",\n", "    \"cookie\": f\"\"\"a9a68f4fefd3b693f10be4a89799dc48={new_cookies[\"a9a68f4fefd3b693f10be4a89799dc48\"]}; qmuid=-1675764941; cxuid=1508239; gauserid=qm59596fon; sxuid=1081117876;\"\"\",\n", "    \"origin\": \"https://app.tigo1000.com\",\n", "    \"referer\": \"https://app.tigo1000.com/\",\n", "    \"sec-ch-ua\": '\"Google Chrome\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"',\n", "    \"sec-ch-ua-mobile\": \"?0\",\n", "    \"sec-ch-ua-platform\": '\"macOS\"',\n", "    \"sec-fetch-dest\": \"empty\",\n", "    \"sec-fetch-mode\": \"cors\",\n", "    \"sec-fetch-site\": \"same-site\",\n", "    \"supid\": \"A7169348\",\n", "    \"user-agent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n", "}\n", "\n", "list_url = \"https://d2p-api.tigo1000.com/goods/v2/list\"\n", "\n", "params = {\n", "    \"q\": \"\",\n", "    \"barCode\": \"\",\n", "    \"bn\": \"\",\n", "    \"cats\": \"\",\n", "    \"supId\": \"A7169348\",\n", "    \"basic\": \"\",\n", "    \"label\": \"\",\n", "    \"pageNo\": 0,\n", "    \"pageSize\": 100,\n", "}\n", "\n", "\n", "headers = {\n", "    \"supid\": \"A7169348\",\n", "    \"user-agent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\n", "}\n", "\n", "product_list = requests.get(\n", "    url=list_url,\n", "    params=params,\n", "    cookies=new_cookies,\n", "    headers=headers,\n", ").json()\n", "product_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 写入odps\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json\n", "import requests\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN\",\n", "}\n", "brand_name = \"钛果-苏皖\"\n", "competitor_name_en = \"taiguo\"\n", "#***********\n", "#984474"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "goods_url = \"https://d2p-api.tigo1000.com/goods/v2/list\"\n", "\n", "params = {\n", "    \"q\": \"\",\n", "    \"barCode\": \"\",\n", "    \"bn\": \"\",\n", "    \"cats\": \"\",\n", "    \"supId\": \"A7169348\",\n", "    \"basic\": \"\",\n", "    \"label\": \"\",\n", "    \"pageNo\": 0,\n", "    \"pageSize\": 100,\n", "}\n", "\n", "\n", "def get_all_products(page_no=0, all_product_list=[]):\n", "    params[\"pageNo\"] = page_no\n", "    print(f\"params:{params}\")\n", "    data = requests.get(\n", "        url=goods_url, params=params, headers=headers, cookies=new_cookies\n", "    ).json()[\"data\"]\n", "    totalCount = data[\"totalCount\"]\n", "    if len(data[\"dataList\"]) > 0:\n", "        dataList = data[\"dataList\"]\n", "        skuIds = [sku[\"skuId\"] for sku in dataList]\n", "        goodsPrices = get_good_prices_by_id(skuIds)\n", "        print(f\"goodsPrices:{goodsPrices}\")\n", "        for sku in dataList:\n", "            sku[\"goodsPrices\"] = goodsPrices[sku[\"skuId\"]]\n", "        all_product_list.extend(dataList)\n", "    if len(all_product_list) < totalCount:\n", "        get_all_products(page_no=page_no + 1, all_product_list=all_product_list)\n", "\n", "\n", "def get_good_prices_by_id(goods_id=[]):\n", "    print(f\"goods_id:{goods_id}\")\n", "    prices_url = (\n", "        f\"https://d2p-api.tigo1000.com/goods/v2/prices?goodsIds={','.join(goods_id)}\"\n", "    )\n", "    return requests.get(\n", "        url=prices_url, params=params, headers=headers, cookies=new_cookies\n", "    ).json()[\"data\"]\n", "\n", "\n", "# Print the response content\n", "all_product_list = []\n", "get_all_products(page_no=0, all_product_list=all_product_list)\n", "all_product_list_df = pd.DataFrame(all_product_list)\n", "all_product_list_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps, get_odps_sql_result_as_df\n", "\n", "# 写入odps\n", "all_sku_list_df = all_product_list_df\n", "all_sku_list_df[\"competitor\"] = brand_name\n", "all_products_df = all_sku_list_df.astype(str)\n", "\n", "today = datetime.now().strftime(\"%Y%m%d\")\n", "partition_spec = f\"ds={today},competitor_name={competitor_name_en}\"\n", "table_name = f\"summerfarm_ds.spider_{competitor_name_en}_product_result_df\"\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30 = (datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime(\"%Y%m%d\")\n", "df = get_odps_sql_result_as_df(\n", "    f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\"\n", ")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
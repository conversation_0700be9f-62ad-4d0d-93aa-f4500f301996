{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 14:57:20, date_of_now:2024-03-06, brand_name:答音云仓, headers:{'token': '5319aeee-58ed-4f97-a28f-91da3c4f7575', 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/答音云仓/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "brand_name=\"答音云仓\"\n", "headers={'token':'5319aeee-58ed-4f97-a28f-91da3c4f7575',\n", "         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "         'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}\n", "brand_name='答音云仓'\n", "competitor_name_en='dayinyuncang'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, headers:{headers}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**************:40940\n", "*************:33351\n", "*************:43838\n", "*************:32819\n", "***************:32505\n", "************:34707\n", "*************:44245\n", "**************:48694\n", "***************:40956\n", "************:45257\n", "['**************:40940', '*************:33351', '*************:43838', '*************:32819', '***************:32505', '************:34707', '*************:44245', '**************:48694', '***************:40956', '************:45257']\n"]}], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url, data, headers, cookies):\n", "    max_retries=3;\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, data=data, headers=headers, proxies=proxies, cookies=cookies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None\n", "def post_remote_data_with_proxy(url, data, headers):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, data=data, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["登录获取token并保存"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: *************************************************\n", "sessionid: ab9e8i65dds5fc1v2yoewqfncsshztu8\n", "response cookie:{'sessionid': 'ab9e8i65dds5fc1v2yoewqfncsshztu8'}\n", "{'code': 0, 'msg': '登录成功', 'data': {'user_id': 3003009}} 登录成功\n"]}], "source": ["# 登录\n", "from urllib.parse import unquote\n", "\n", "url='https://bshop.guanmai.cn/login'\n", "login_response=post_remote_data_with_proxy(url, data={'username':'17729941198', 'password':'aa123456'}, headers=headers)\n", "\n", "\n", "after_login_cookie={}\n", "# Print all the cookies set by the server\n", "for cookie in login_response.cookies:\n", "    print(f'{cookie.name}: {cookie.value}')\n", "    after_login_cookie[cookie.name]=cookie.value\n", "\n", "\n", "print(f\"response cookie:{after_login_cookie}\")\n", "print(login_response.json(),unquote(login_response.json()['msg']))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ***********************************************\n", "<!doctype html><html><head><meta charset=\"UTF-8\"/><meta name=\"format-detection\" content=\"telephone=n\n", "new sessionid:ab9e8i65dds5fc1v2yoewqfncsshztu8\n"]}], "source": ["after_login_cookie.update({'cms_key':'cygyl','group_id':'3252'})\n", "after_login_cookie\n", "\n", "sessionid=after_login_cookie['sessionid']\n", "\n", "url = 'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp=1706286340876'\n", "\n", "headers = {\n", "    'authority': 'bshop.guanmai.cn',\n", "    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "    'accept-language': 'en-US,en;q=0.9',\n", "    'cookie': f'cms_key=cygyl; group_id=3252; sessionid={sessionid}; gr_user_id=62c026d8-a829-40c7-823f-d7e38bf255d6; 9beedda875b5420f_gr_session_id=2a97577a-00ae-45a7-8392-4cf0d0fde7cb; 9beedda875b5420f_gr_session_id_sent_vst=2a97577a-00ae-45a7-8392-4cf0d0fde7cb',\n", "    'referer': 'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp=1706286034360',\n", "    'sec-fetch-dest': 'document',\n", "    'sec-fetch-mode': 'navigate',\n", "    'sec-fetch-site': 'same-origin',\n", "    'sec-fetch-user': '?1',\n", "    'upgrade-insecure-requests': '1',\n", "    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'\n", "}\n", "\n", "response = get_remote_data_with_proxy(url, headers=headers,data=None,cookies=None)\n", "\n", "print(response.text[0:100])\n", "\n", "print(f\"new sessionid:{sessionid}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取一级类目列表"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'limit': 99, 'page': 1, 'type': 1}\n", "Using proxy: ************************************************\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>image</th>\n", "      <th>weigh</th>\n", "      <th>type_text</th>\n", "      <th>flag_text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230407/3...</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230407/2...</td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20231209/2...</td>\n", "      <td>4</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230601/e...</td>\n", "      <td>74</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230601/5...</td>\n", "      <td>118</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>141</td>\n", "      <td>其他</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230601/b...</td>\n", "      <td>128</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230407/e...</td>\n", "      <td>141</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  name                                              image  weigh  \\\n", "0   92  鲜果鸡蛋  https://bd.dayinyuncang.com/uploads/20230407/3...      1   \n", "1   18   乳制品  https://bd.dayinyuncang.com/uploads/20230407/2...      2   \n", "2   17  烘焙辅料  https://bd.dayinyuncang.com/uploads/20231209/2...      4   \n", "3  119  甜品奶茶  https://bd.dayinyuncang.com/uploads/20230601/e...     74   \n", "4  111   巧克力  https://bd.dayinyuncang.com/uploads/20230601/5...    118   \n", "5  141    其他  https://bd.dayinyuncang.com/uploads/20230601/b...    128   \n", "6   74  包装用品  https://bd.dayinyuncang.com/uploads/20230407/e...    141   \n", "\n", "  type_text flag_text  \n", "0      None      None  \n", "1      None      None  \n", "2      None      None  \n", "3      None      None  \n", "4      None      None  \n", "5      None      None  \n", "6      None      None  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取一级类目列表\n", "\n", "import urllib.parse\n", "import json\n", "\n", "# Convert the dictionary into a JSON object\n", "json_obj = {'limit':99, 'page':1, 'type':1}\n", "\n", "print(json_obj)\n", "\n", "url='https://bd.dayinyuncang.com/api/product/category'\n", "\n", "categoryList=json.loads(post_remote_data_with_proxy(url, data=json_obj, headers=None).text)['data']['data']\n", "category_list_df=pd.DataFrame(categoryList)\n", "category_list_df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ***********************************************\n", "secondCategoryList:[{'id': 93, 'name': '芒果', 'image': '', 'weigh': 93, 'type_text': None, 'flag_text': None}, {'id': 94, 'name': '火龙果', 'image': '', 'weigh': 94, 'type_text': None, 'flag_text': None}, {'id': 95, 'name': '草莓｜蓝莓', 'image': '', 'weigh': 95, 'type_text': None, 'flag_text': None}, {'id': 96, 'name': '瓜类｜香蕉', 'image': '', 'weigh': 96, 'type_text': None, 'flag_text': None}, {'id': 97, 'name': '番茄｜桃子', 'image': '', 'weigh': 97, 'type_text': None, 'flag_text': None}, {'id': 101, 'name': '鸡蛋｜石榴', 'image': '', 'weigh': 98, 'type_text': None, 'flag_text': None}, {'id': 164, 'name': '葡提｜椰子', 'image': '', 'weigh': 99, 'type_text': None, 'flag_text': None}, {'id': 168, 'name': '应季水果', 'image': '', 'weigh': 100, 'type_text': None, 'flag_text': None}, {'id': 100, 'name': '猕猴桃', 'image': '', 'weigh': 101, 'type_text': None, 'flag_text': None}, {'id': 98, 'name': '橘子｜柠檬', 'image': '', 'weigh': 120, 'type_text': None, 'flag_text': None}, {'id': 120, 'name': '梨｜苹果', 'image': '', 'weigh': 164, 'type_text': None, 'flag_text': None}, {'id': 99, 'name': '凤梨｜菠萝', 'image': '', 'weigh': 166, 'type_text': None, 'flag_text': None}, {'id': 159, 'name': '牛油果', 'image': '', 'weigh': 168, 'type_text': None, 'flag_text': None}, {'id': 166, 'name': '水果制品', 'image': '', 'weigh': 170, 'type_text': None, 'flag_text': None}]\n", "Using proxy: *************************************************\n", "secondCategoryList:[{'id': 102, 'name': '淡奶油', 'image': '', 'weigh': 102, 'type_text': None, 'flag_text': None}, {'id': 167, 'name': '散装奶油', 'image': '', 'weigh': 103, 'type_text': None, 'flag_text': None}, {'id': 103, 'name': '纯牛奶', 'image': '', 'weigh': 104, 'type_text': None, 'flag_text': None}, {'id': 104, 'name': '奶酪I芝士', 'image': '', 'weigh': 105, 'type_text': None, 'flag_text': None}, {'id': 105, 'name': '黄油', 'image': '', 'weigh': 107, 'type_text': None, 'flag_text': None}, {'id': 107, 'name': '奶粉', 'image': '', 'weigh': 167, 'type_text': None, 'flag_text': None}]\n", "Using proxy: ***********************************************\n", "secondCategoryList:[{'id': 108, 'name': '夹心馅料', 'image': '', 'weigh': 1, 'type_text': None, 'flag_text': None}, {'id': 158, 'name': '罐头', 'image': '', 'weigh': 44, 'type_text': None, 'flag_text': None}, {'id': 169, 'name': '饼干', 'image': '', 'weigh': 108, 'type_text': None, 'flag_text': None}, {'id': 173, 'name': '色素', 'image': '', 'weigh': 109, 'type_text': None, 'flag_text': None}, {'id': 109, 'name': '砂糖', 'image': '', 'weigh': 122, 'type_text': None, 'flag_text': None}, {'id': 44, 'name': '面粉丨淀粉', 'image': '', 'weigh': 123, 'type_text': None, 'flag_text': None}, {'id': 122, 'name': '肉松', 'image': '', 'weigh': 127, 'type_text': None, 'flag_text': None}, {'id': 123, 'name': '调味酒', 'image': '', 'weigh': 132, 'type_text': None, 'flag_text': None}, {'id': 127, 'name': '添加剂', 'image': '', 'weigh': 165, 'type_text': None, 'flag_text': None}, {'id': 132, 'name': '油类', 'image': '', 'weigh': 165, 'type_text': None, 'flag_text': None}, {'id': 136, 'name': '冻品原料', 'image': '', 'weigh': 165, 'type_text': None, 'flag_text': None}, {'id': 165, 'name': '坚果干货', 'image': '', 'weigh': 165, 'type_text': None, 'flag_text': None}]\n", "Using proxy: **********************************************\n", "secondCategoryList:[{'id': 124, 'name': '沙拉酱', 'image': '', 'weigh': 124, 'type_text': None, 'flag_text': None}, {'id': 125, 'name': '果酱', 'image': '', 'weigh': 125, 'type_text': None, 'flag_text': None}, {'id': 131, 'name': '果汁原料', 'image': '', 'weigh': 131, 'type_text': None, 'flag_text': None}, {'id': 133, 'name': '罐头', 'image': '', 'weigh': 133, 'type_text': None, 'flag_text': None}, {'id': 137, 'name': '雪媚娘', 'image': '', 'weigh': 137, 'type_text': None, 'flag_text': None}, {'id': 138, 'name': '椰蓉', 'image': '', 'weigh': 138, 'type_text': None, 'flag_text': None}]\n", "Using proxy: ************************************************\n", "secondCategoryList:[{'id': 139, 'name': '常规品', 'image': '', 'weigh': 112, 'type_text': None, 'flag_text': None}, {'id': 113, 'name': '装饰', 'image': '', 'weigh': 113, 'type_text': None, 'flag_text': None}, {'id': 112, 'name': '可可豆', 'image': '', 'weigh': 114, 'type_text': None, 'flag_text': None}, {'id': 114, 'name': '饼干', 'image': '', 'weigh': 116, 'type_text': None, 'flag_text': None}, {'id': 116, 'name': '寿桃', 'image': '', 'weigh': 135, 'type_text': None, 'flag_text': None}, {'id': 135, 'name': '装饰用碎', 'image': '', 'weigh': 139, 'type_text': None, 'flag_text': None}, {'id': 140, 'name': '立体类', 'image': '', 'weigh': 140, 'type_text': None, 'flag_text': None}]\n", "Using proxy: ***********************************************\n", "secondCategoryList:[{'id': 142, 'name': '餐具', 'image': '', 'weigh': 142, 'type_text': None, 'flag_text': None}, {'id': 143, 'name': '工具', 'image': '', 'weigh': 143, 'type_text': None, 'flag_text': None}]\n", "Using proxy: ************************************************\n", "secondCategoryList:[{'id': 110, 'name': '刀叉盘子', 'image': '', 'weigh': 1, 'type_text': None, 'flag_text': None}, {'id': 77, 'name': '蛋糕盒', 'image': '', 'weigh': 2, 'type_text': None, 'flag_text': None}, {'id': 78, 'name': '丝带', 'image': '', 'weigh': 74, 'type_text': None, 'flag_text': None}, {'id': 80, 'name': '生日帽', 'image': '', 'weigh': 80, 'type_text': None, 'flag_text': None}]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>image</th>\n", "      <th>weigh</th>\n", "      <th>type_text</th>\n", "      <th>flag_text</th>\n", "      <th>first_category_id</th>\n", "      <th>first_category_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>93</td>\n", "      <td>芒果</td>\n", "      <td></td>\n", "      <td>93</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>94</td>\n", "      <td>火龙果</td>\n", "      <td></td>\n", "      <td>94</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>95</td>\n", "      <td>草莓｜蓝莓</td>\n", "      <td></td>\n", "      <td>95</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>96</td>\n", "      <td>瓜类｜香蕉</td>\n", "      <td></td>\n", "      <td>96</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>97</td>\n", "      <td>番茄｜桃子</td>\n", "      <td></td>\n", "      <td>97</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>101</td>\n", "      <td>鸡蛋｜石榴</td>\n", "      <td></td>\n", "      <td>98</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>164</td>\n", "      <td>葡提｜椰子</td>\n", "      <td></td>\n", "      <td>99</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>168</td>\n", "      <td>应季水果</td>\n", "      <td></td>\n", "      <td>100</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>100</td>\n", "      <td>猕猴桃</td>\n", "      <td></td>\n", "      <td>101</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>98</td>\n", "      <td>橘子｜柠檬</td>\n", "      <td></td>\n", "      <td>120</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>120</td>\n", "      <td>梨｜苹果</td>\n", "      <td></td>\n", "      <td>164</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>99</td>\n", "      <td>凤梨｜菠萝</td>\n", "      <td></td>\n", "      <td>166</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>159</td>\n", "      <td>牛油果</td>\n", "      <td></td>\n", "      <td>168</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>166</td>\n", "      <td>水果制品</td>\n", "      <td></td>\n", "      <td>170</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>102</td>\n", "      <td>淡奶油</td>\n", "      <td></td>\n", "      <td>102</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>167</td>\n", "      <td>散装奶油</td>\n", "      <td></td>\n", "      <td>103</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>103</td>\n", "      <td>纯牛奶</td>\n", "      <td></td>\n", "      <td>104</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>104</td>\n", "      <td>奶酪I芝士</td>\n", "      <td></td>\n", "      <td>105</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>105</td>\n", "      <td>黄油</td>\n", "      <td></td>\n", "      <td>107</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>107</td>\n", "      <td>奶粉</td>\n", "      <td></td>\n", "      <td>167</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>18</td>\n", "      <td>乳制品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>108</td>\n", "      <td>夹心馅料</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>158</td>\n", "      <td>罐头</td>\n", "      <td></td>\n", "      <td>44</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>169</td>\n", "      <td>饼干</td>\n", "      <td></td>\n", "      <td>108</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>173</td>\n", "      <td>色素</td>\n", "      <td></td>\n", "      <td>109</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>109</td>\n", "      <td>砂糖</td>\n", "      <td></td>\n", "      <td>122</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>44</td>\n", "      <td>面粉丨淀粉</td>\n", "      <td></td>\n", "      <td>123</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>122</td>\n", "      <td>肉松</td>\n", "      <td></td>\n", "      <td>127</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>123</td>\n", "      <td>调味酒</td>\n", "      <td></td>\n", "      <td>132</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>127</td>\n", "      <td>添加剂</td>\n", "      <td></td>\n", "      <td>165</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>132</td>\n", "      <td>油类</td>\n", "      <td></td>\n", "      <td>165</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>136</td>\n", "      <td>冻品原料</td>\n", "      <td></td>\n", "      <td>165</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>165</td>\n", "      <td>坚果干货</td>\n", "      <td></td>\n", "      <td>165</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>17</td>\n", "      <td>烘焙辅料</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>124</td>\n", "      <td>沙拉酱</td>\n", "      <td></td>\n", "      <td>124</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>125</td>\n", "      <td>果酱</td>\n", "      <td></td>\n", "      <td>125</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>131</td>\n", "      <td>果汁原料</td>\n", "      <td></td>\n", "      <td>131</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>133</td>\n", "      <td>罐头</td>\n", "      <td></td>\n", "      <td>133</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>137</td>\n", "      <td>雪媚娘</td>\n", "      <td></td>\n", "      <td>137</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>138</td>\n", "      <td>椰蓉</td>\n", "      <td></td>\n", "      <td>138</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>119</td>\n", "      <td>甜品奶茶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>139</td>\n", "      <td>常规品</td>\n", "      <td></td>\n", "      <td>112</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>113</td>\n", "      <td>装饰</td>\n", "      <td></td>\n", "      <td>113</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>112</td>\n", "      <td>可可豆</td>\n", "      <td></td>\n", "      <td>114</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>114</td>\n", "      <td>饼干</td>\n", "      <td></td>\n", "      <td>116</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>116</td>\n", "      <td>寿桃</td>\n", "      <td></td>\n", "      <td>135</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>135</td>\n", "      <td>装饰用碎</td>\n", "      <td></td>\n", "      <td>139</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>140</td>\n", "      <td>立体类</td>\n", "      <td></td>\n", "      <td>140</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>111</td>\n", "      <td>巧克力</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>142</td>\n", "      <td>餐具</td>\n", "      <td></td>\n", "      <td>142</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>141</td>\n", "      <td>其他</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>143</td>\n", "      <td>工具</td>\n", "      <td></td>\n", "      <td>143</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>141</td>\n", "      <td>其他</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>110</td>\n", "      <td>刀叉盘子</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>77</td>\n", "      <td>蛋糕盒</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>78</td>\n", "      <td>丝带</td>\n", "      <td></td>\n", "      <td>74</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>80</td>\n", "      <td>生日帽</td>\n", "      <td></td>\n", "      <td>80</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>74</td>\n", "      <td>包装用品</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     id   name image  weigh type_text flag_text  first_category_id  \\\n", "0    93     芒果           93      None      None                 92   \n", "1    94    火龙果           94      None      None                 92   \n", "2    95  草莓｜蓝莓           95      None      None                 92   \n", "3    96  瓜类｜香蕉           96      None      None                 92   \n", "4    97  番茄｜桃子           97      None      None                 92   \n", "5   101  鸡蛋｜石榴           98      None      None                 92   \n", "6   164  葡提｜椰子           99      None      None                 92   \n", "7   168   应季水果          100      None      None                 92   \n", "8   100    猕猴桃          101      None      None                 92   \n", "9    98  橘子｜柠檬          120      None      None                 92   \n", "10  120   梨｜苹果          164      None      None                 92   \n", "11   99  凤梨｜菠萝          166      None      None                 92   \n", "12  159    牛油果          168      None      None                 92   \n", "13  166   水果制品          170      None      None                 92   \n", "14  102    淡奶油          102      None      None                 18   \n", "15  167   散装奶油          103      None      None                 18   \n", "16  103    纯牛奶          104      None      None                 18   \n", "17  104  奶酪I芝士          105      None      None                 18   \n", "18  105     黄油          107      None      None                 18   \n", "19  107     奶粉          167      None      None                 18   \n", "20  108   夹心馅料            1      None      None                 17   \n", "21  158     罐头           44      None      None                 17   \n", "22  169     饼干          108      None      None                 17   \n", "23  173     色素          109      None      None                 17   \n", "24  109     砂糖          122      None      None                 17   \n", "25   44  面粉丨淀粉          123      None      None                 17   \n", "26  122     肉松          127      None      None                 17   \n", "27  123    调味酒          132      None      None                 17   \n", "28  127    添加剂          165      None      None                 17   \n", "29  132     油类          165      None      None                 17   \n", "30  136   冻品原料          165      None      None                 17   \n", "31  165   坚果干货          165      None      None                 17   \n", "32  124    沙拉酱          124      None      None                119   \n", "33  125     果酱          125      None      None                119   \n", "34  131   果汁原料          131      None      None                119   \n", "35  133     罐头          133      None      None                119   \n", "36  137    雪媚娘          137      None      None                119   \n", "37  138     椰蓉          138      None      None                119   \n", "38  139    常规品          112      None      None                111   \n", "39  113     装饰          113      None      None                111   \n", "40  112    可可豆          114      None      None                111   \n", "41  114     饼干          116      None      None                111   \n", "42  116     寿桃          135      None      None                111   \n", "43  135   装饰用碎          139      None      None                111   \n", "44  140    立体类          140      None      None                111   \n", "45  142     餐具          142      None      None                141   \n", "46  143     工具          143      None      None                141   \n", "47  110   刀叉盘子            1      None      None                 74   \n", "48   77    蛋糕盒            2      None      None                 74   \n", "49   78     丝带           74      None      None                 74   \n", "50   80    生日帽           80      None      None                 74   \n", "\n", "   first_category_name  \n", "0                 鲜果鸡蛋  \n", "1                 鲜果鸡蛋  \n", "2                 鲜果鸡蛋  \n", "3                 鲜果鸡蛋  \n", "4                 鲜果鸡蛋  \n", "5                 鲜果鸡蛋  \n", "6                 鲜果鸡蛋  \n", "7                 鲜果鸡蛋  \n", "8                 鲜果鸡蛋  \n", "9                 鲜果鸡蛋  \n", "10                鲜果鸡蛋  \n", "11                鲜果鸡蛋  \n", "12                鲜果鸡蛋  \n", "13                鲜果鸡蛋  \n", "14                 乳制品  \n", "15                 乳制品  \n", "16                 乳制品  \n", "17                 乳制品  \n", "18                 乳制品  \n", "19                 乳制品  \n", "20                烘焙辅料  \n", "21                烘焙辅料  \n", "22                烘焙辅料  \n", "23                烘焙辅料  \n", "24                烘焙辅料  \n", "25                烘焙辅料  \n", "26                烘焙辅料  \n", "27                烘焙辅料  \n", "28                烘焙辅料  \n", "29                烘焙辅料  \n", "30                烘焙辅料  \n", "31                烘焙辅料  \n", "32                甜品奶茶  \n", "33                甜品奶茶  \n", "34                甜品奶茶  \n", "35                甜品奶茶  \n", "36                甜品奶茶  \n", "37                甜品奶茶  \n", "38                 巧克力  \n", "39                 巧克力  \n", "40                 巧克力  \n", "41                 巧克力  \n", "42                 巧克力  \n", "43                 巧克力  \n", "44                 巧克力  \n", "45                  其他  \n", "46                  其他  \n", "47                包装用品  \n", "48                包装用品  \n", "49                包装用品  \n", "50                包装用品  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["all_second_cate_list=[]\n", "def get_second_category(firstCate={\"id\":18, \"name\":\"乳制品\"}):\n", "    reanmed_first_cate={\"first_category_id\":firstCate['id'], \"first_category_name\":firstCate['name']}\n", "    json_obj = {'limit':99, 'page':1, 'type':1, \"pid\": firstCate['id']}\n", "    url='https://bd.dayinyuncang.com/api/product/category'\n", "\n", "    secondCategoryList=json.loads(post_remote_data_with_proxy(url, data=json_obj, headers=None).text)['data']['data']\n", "    print(f'secondCategoryList:{secondCategoryList}')\n", "    for cate in secondCategoryList:\n", "        cate.update(reanmed_first_cate)\n", "    all_second_cate_list.extend(secondCategoryList)\n", "    return secondCategoryList\n", "\n", "for first in categoryList:\n", "    get_second_category(firstCate=first)\n", "\n", "all_second_cate_list_df=pd.DataFrame(all_second_cate_list)\n", "all_second_cate_list_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级、二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ***********************************************\n", "芒果 商品数量：9\n", "Using proxy: **********************************************\n", "火龙果 商品数量：8\n", "Using proxy: ************************************************\n", "草莓｜蓝莓 商品数量：15\n", "Using proxy: ***********************************************\n", "瓜类｜香蕉 商品数量：13\n", "Using proxy: ***********************************************\n", "番茄｜桃子 商品数量：7\n", "Using proxy: **********************************************\n", "鸡蛋｜石榴 商品数量：4\n", "Using proxy: ***********************************************\n", "葡提｜椰子 商品数量：13\n", "Using proxy: ***********************************************\n", "应季水果 商品数量：7\n", "Using proxy: ***********************************************\n", "猕猴桃 商品数量：6\n", "Using proxy: **********************************************\n", "橘子｜柠檬 商品数量：13\n", "Using proxy: **********************************************\n", "梨｜苹果 商品数量：6\n", "Using proxy: ***********************************************\n", "凤梨｜菠萝 商品数量：6\n", "Using proxy: *************************************************\n", "牛油果 商品数量：2\n", "Using proxy: ***********************************************\n", "水果制品 商品数量：24\n", "Using proxy: *************************************************\n", "淡奶油 商品数量：12\n", "Using proxy: ***********************************************\n", "散装奶油 商品数量：11\n", "Using proxy: ************************************************\n", "纯牛奶 商品数量：13\n", "Using proxy: ***********************************************\n", "奶酪I芝士 商品数量：5\n", "Using proxy: ************************************************\n", "黄油 商品数量：5\n", "Using proxy: **********************************************\n", "奶粉 商品数量：1\n", "Using proxy: ***********************************************\n", "夹心馅料 商品数量：16\n", "Using proxy: ***********************************************\n", "罐头 商品数量：6\n", "Using proxy: *************************************************\n", "饼干 商品数量：5\n", "Using proxy: ***********************************************\n", "色素 商品数量：12\n", "Using proxy: *************************************************\n", "砂糖 商品数量：6\n", "Using proxy: **********************************************\n", "面粉丨淀粉 商品数量：13\n", "Using proxy: ***********************************************\n", "肉松 商品数量：9\n", "Using proxy: *************************************************\n", "调味酒 商品数量：9\n", "Using proxy: ***********************************************\n", "添加剂 商品数量：8\n", "Using proxy: **********************************************\n", "油类 商品数量：5\n", "Using proxy: ***********************************************\n", "冻品原料 商品数量：4\n", "Using proxy: **********************************************\n", "坚果干货 商品数量：1\n", "Using proxy: ************************************************\n", "沙拉酱 商品数量：6\n", "Using proxy: ************************************************\n", "果酱 商品数量：8\n", "Using proxy: ************************************************\n", "果汁原料 商品数量：4\n", "Using proxy: ***********************************************\n", "罐头 商品数量：6\n", "Using proxy: *************************************************\n", "雪媚娘 商品数量：1\n", "Using proxy: ***********************************************\n", "椰蓉 商品数量：1\n", "Using proxy: ***********************************************\n", "常规品 商品数量：15\n", "Using proxy: *************************************************\n", "装饰 商品数量：15\n", "Using proxy: ************************************************\n", "可可豆 商品数量：17\n", "Using proxy: *************************************************\n", "饼干 商品数量：4\n", "Using proxy: *************************************************\n", "寿桃 商品数量：11\n", "Using proxy: ***********************************************\n", "装饰用碎 商品数量：7\n", "Using proxy: **********************************************\n", "立体类 商品数量：26\n", "Using proxy: ************************************************\n", "餐具 商品数量：5\n", "Using proxy: ***********************************************\n", "工具 商品数量：3\n", "Using proxy: ************************************************\n", "刀叉盘子 商品数量：3\n", "Using proxy: *************************************************\n", "蛋糕盒 商品数量：8\n", "Using proxy: **********************************************\n", "没有商品！！丝带\n", "Using proxy: ************************************************\n", "没有商品！！生日帽\n"]}], "source": ["import pandas as pd\n", "pid_list_url='https://bd.dayinyuncang.com/api/product/list'\n", "params={'category_id':102,\n", "'limit':50,\n", "'page':1,\n", "'new_order':1,\n", "'price_order':None,}\n", "\n", "all_products=[]\n", "\n", "for cate in all_second_cate_list:\n", "    params[\"category_id\"]=cate[\"id\"]\n", "    cate_obj={\"first_category_id\":cate[\"first_category_id\"],\"first_category_name\":cate[\"first_category_name\"],'second_category_id':cate[\"id\"],'second_category_name':cate[\"name\"]}\n", "    products=json.loads(post_remote_data_with_proxy(pid_list_url, data=params, headers=None).text)['data']['data']\n", "    for product in products:\n", "        product.update(cate_obj)\n", "    if len(products)>0:\n", "        all_products.extend(products)\n", "        print(f\"{cate['name']} 商品数量：{len(products)}\")\n", "    else:\n", "        print(f\"没有商品！！{cate['name']}\")\n", "\n", "all_products_df=pd.DataFrame(all_products)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>cover_images</th>\n", "      <th>current_price</th>\n", "      <th>original_price</th>\n", "      <th>new_user_price</th>\n", "      <th>old_user_price</th>\n", "      <th>count</th>\n", "      <th>tags</th>\n", "      <th>s<PERSON><PERSON><PERSON></th>\n", "      <th>description</th>\n", "      <th>stock_order</th>\n", "      <th>price_type</th>\n", "      <th>stock</th>\n", "      <th>first_category_id</th>\n", "      <th>first_category_name</th>\n", "      <th>second_category_id</th>\n", "      <th>second_category_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>735</td>\n", "      <td>海南水仙芒果二级/大果/700g+（10-10.5斤）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20231127/e...</td>\n", "      <td>70.00</td>\n", "      <td>80.00</td>\n", "      <td>70.00</td>\n", "      <td>70.00</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>[{'name': '重量', 'list': ['/大果10-10.5斤']}]</td>\n", "      <td>大果单果700g+</td>\n", "      <td>8</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "      <td>93</td>\n", "      <td>芒果</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>722</td>\n", "      <td>越南金煌芒400g+（10-10.5斤）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20240303/2...</td>\n", "      <td>55.00</td>\n", "      <td>60.00</td>\n", "      <td>55.00</td>\n", "      <td>55.00</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td>[{'name': '重量', 'list': ['10-10.5斤']}]</td>\n", "      <td>果肉细腻 香甜爽口</td>\n", "      <td>23</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "      <td>93</td>\n", "      <td>芒果</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>628</td>\n", "      <td>【净重】越南大青芒果/单果400g+（10-10.5斤）</td>\n", "      <td>https://bd.dayinyuncang.com/uploads/20230417/7...</td>\n", "      <td>45.00</td>\n", "      <td>59.88</td>\n", "      <td>45.00</td>\n", "      <td>45.00</td>\n", "      <td>1</td>\n", "      <td>热销</td>\n", "      <td>[{'name': '斤', 'list': ['10-10.5斤']}]</td>\n", "      <td>二级果/口感香甜</td>\n", "      <td>98380</td>\n", "      <td>2</td>\n", "      <td>98380</td>\n", "      <td>92</td>\n", "      <td>鲜果鸡蛋</td>\n", "      <td>93</td>\n", "      <td>芒果</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id                          name  \\\n", "0  735   海南水仙芒果二级/大果/700g+（10-10.5斤）   \n", "1  722          越南金煌芒400g+（10-10.5斤）   \n", "2  628  【净重】越南大青芒果/单果400g+（10-10.5斤）   \n", "\n", "                                        cover_images current_price  \\\n", "0  https://bd.dayinyuncang.com/uploads/20231127/e...         70.00   \n", "1  https://bd.dayinyuncang.com/uploads/20240303/2...         55.00   \n", "2  https://bd.dayinyuncang.com/uploads/20230417/7...         45.00   \n", "\n", "  original_price new_user_price old_user_price  count tags  \\\n", "0          80.00          70.00          70.00      1        \n", "1          60.00          55.00          55.00      1        \n", "2          59.88          45.00          45.00      1   热销   \n", "\n", "                                     sku<PERSON>son description stock_order  \\\n", "0  [{'name': '重量', 'list': ['/大果10-10.5斤']}]   大果单果700g+           8   \n", "1     [{'name': '重量', 'list': ['10-10.5斤']}]   果肉细腻 香甜爽口          23   \n", "2      [{'name': '斤', 'list': ['10-10.5斤']}]    二级果/口感香甜       98380   \n", "\n", "   price_type  stock  first_category_id first_category_name  \\\n", "0           2      8                 92                鲜果鸡蛋   \n", "1           2     23                 92                鲜果鸡蛋   \n", "2           2  98380                 92                鲜果鸡蛋   \n", "\n", "   second_category_id second_category_name  \n", "0                  93                   芒果  \n", "1                  93                   芒果  \n", "2                  93                   芒果  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(all_products_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:海南水仙芒果二级/大果/700g+（10-10.5斤）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:541.8546199798584ms\n", "inputText:越南金煌芒400g+（10-10.5斤）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:509.8404884338379ms\n", "inputText:【净重】越南大青芒果/单果400g+（10-10.5斤）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:510.7424259185791ms\n", "inputText:【净重】三级果/芒果（10-10.5斤）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:645.038366317749ms\n", "inputText:【净重】越南大青芒果/单果500g+（10-10.5斤）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:634.9883079528809ms\n", "inputText:越南大青芒整件（58-60斤）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:573.6343860626221ms\n", "inputText:海南水仙芒果二级300g+（10-10.5斤）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:613.755464553833ms\n", "inputText:【净重】小台芒果60g+/（10-10.5斤）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:895.183801651001ms\n", "inputText:【净重】大台芒果150g+（10-10.5斤）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:526.2038707733154ms\n", "inputText:【净重】越南白心火龙果/大果（5斤±0.2）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:595.2534675598145ms\n", "inputText:【毛重】越南红心火龙果整件/大果（31-33斤）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:554.6822547912598ms\n", "inputText:【净重】越南红心火龙果/大果（5斤*1包）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:1129.9304962158203ms\n", "inputText:【毛重36-39斤】越南白心火龙果整件/大果, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:541.7726039886475ms\n", "inputText:【净重】国产红心火龙果/大果（5斤*1包）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:531.3394069671631ms\n", "inputText:【毛重】国产红心火龙果整件/大果（30-32）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:574.0828514099121ms\n", "inputText:【净重】国产红心火龙果/中果（5斤*1包）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:533.2598686218262ms\n", "inputText:【毛重】国产红心火龙果整件/中果（30-32斤）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:771.0645198822021ms\n", "inputText:【净重】巧克力红颜甜草莓（3斤-3.5/箱）, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:615.135669708252ms\n", "inputText:【净重】巧克力红颜甜草莓（6斤-6.5/箱）, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:640.1047706604004ms\n", "inputText:巧克力甜草莓（10盒*1件）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:581.132173538208ms\n", "inputText:【盒装】红颜甜草莓（4*6/1盒）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:511.95240020751953ms\n", "inputText:【盒装】红颜甜草莓（4*5/1盒）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:541.024923324585ms\n", "inputText:巧克力红颜甜草莓（1斤*1盒）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:504.55498695373535ms\n", "inputText:桑葚黑莓/一级（125g*1盒）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:542.3107147216797ms\n", "inputText:红树莓/一级（125g/盒）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:545.163631439209ms\n", "inputText:甜蓝莓/一级14-16mm（125G*12盒）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:516.648530960083ms\n", "inputText:甜蓝莓/一级14-16mm（125G*1盒）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:527.4105072021484ms\n", "inputText:【盒装】红颜甜草莓（3*5/1盒）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:519.4697380065918ms\n", "inputText:甜蓝莓12-14mm（125g*12盒）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:531.181812286377ms\n", "inputText:【净重】巧克力红颜甜草莓（小果/1斤装）, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:566.4443969726562ms\n", "inputText:【净重】三级果蒙特瑞草莓（4.5-5斤）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:551.9485473632812ms\n", "inputText:甜蓝莓12-14mm（125g*1盒）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:536.6737842559814ms\n", "inputText:香蕉（毛重24-26斤）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:1229.3837070465088ms\n", "inputText:网纹瓜2个, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:568.7766075134277ms\n", "inputText:香蕉（3斤*1袋）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:685.6036186218262ms\n", "inputText:一级奶香蜜（17-19斤）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:754.4398307800293ms\n", "inputText:一级奶香蜜（3.4-3.8斤）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:889.7595405578613ms\n", "inputText:整件小哈密瓜（15-17斤）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:820.2543258666992ms\n", "inputText:【净重】小哈密瓜（3.5-4斤）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:582.7248096466064ms\n", "inputText:网纹瓜16-18斤, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:639.7042274475098ms\n", "inputText:【净重】久泰北纬23度蜜瓜（4-4.5斤）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:659.1873168945312ms\n", "inputText:久泰北纬23度蜜瓜（22-24斤）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:1742.2938346862793ms\n", "inputText:【毛重】无籽西瓜大号/35-38斤, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:702.3911476135254ms\n", "inputText:美都无籽西瓜毛重40-43斤/一级（4粒装）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:540.5664443969727ms\n", "inputText:美都无籽西瓜毛重25-27斤/一级（2粒装）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:678.2219409942627ms\n", "inputText:千禧果（2斤*1盒）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:542.9677963256836ms\n", "inputText:【毛重】圣女果（7斤）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:577.1462917327881ms\n", "inputText:【净重】圣女果（2斤±0.1）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:587.7861976623535ms\n", "inputText:【净重】千禧果（7.5-8斤）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:578.1309604644775ms\n", "inputText:阳山水蜜桃/大果（10斤*件）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:1202.458381652832ms\n", "inputText:阳山水蜜桃/大果（5斤*袋）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:703.3727169036865ms\n", "inputText:【净重】圣女果（7斤*1箱）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:537.9738807678223ms\n", "inputText:【大码】白壳鸡蛋360枚（47-49斤）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:532.6728820800781ms\n", "inputText:【大码】红心鸡蛋360枚（47-49斤）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:503.25894355773926ms\n", "inputText:白壳鸡蛋/大码（1盒30个）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:1147.3605632781982ms\n", "inputText:软籽石榴/一级（5斤±0.2）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:650.4364013671875ms\n", "inputText:【整件】进口无籽青提/大颗（9-10斤）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:551.9087314605713ms\n", "inputText:泰国椰青/一级/2个, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:497.61462211608887ms\n", "inputText:【毛重】泰国椰青19-23斤/一级/9个, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:642.2889232635498ms\n", "inputText:【净重】进口无籽青提/大颗（2斤±0.2*1盒）, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:562.1867179870605ms\n", "inputText:【净重】一级/新疆红提（2-2.5斤）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:604.1522026062012ms\n", "inputText:【净重】红提（10-12斤）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:658.5850715637207ms\n", "inputText:【净重】一级/黑加仑黑提（2-2.5斤）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:582.8390121459961ms\n", "inputText:阳光玫瑰/普通（净重2斤±0.2）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:524.4572162628174ms\n", "inputText:二级阳光玫瑰（毛重8-9斤）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:512.3369693756104ms\n", "inputText:阳光玫瑰/普通（净重6-6.5斤）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:594.5148468017578ms\n", "inputText:二级阳光玫瑰（净重2斤±0.2）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:596.9741344451904ms\n", "inputText:【净重】陕西葡萄（2-2.5斤）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:526.573657989502ms\n", "inputText:【净重】陕西葡萄（6-6.5斤）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:1674.2892265319824ms\n", "inputText:【净重】桑提娜车厘子/2JD（1斤/盒±0.1）, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:614.8388385772705ms\n", "inputText:【净重】桑提娜车厘子/2JD（5斤/箱±0.3）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:732.5618267059326ms\n", "inputText:泰国龙眼/一级（5斤*1盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:566.6601657867432ms\n", "inputText:泰国龙眼/一级（2斤*1盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:536.1113548278809ms\n", "cache matched:软籽石榴/一级（5斤±0.2）\n", "cache matched:阳山水蜜桃/大果（10斤*件）\n", "cache matched:阳山水蜜桃/大果（5斤*袋）\n", "inputText:【净重】徐香猕猴桃/一级（2斤±0.1）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:3631.1073303222656ms\n", "inputText:【净重】徐香猕猴桃（20斤*1箱）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:707.8218460083008ms\n", "inputText:【净重】徐香猕猴桃/一级（5斤*1盒）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:580.8331966400146ms\n", "inputText:【净重】国产绿心猕猴桃（2斤）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:516.796350479126ms\n", "inputText:【净重】国产绿心猕猴桃（5斤*1盒）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:873.7409114837646ms\n", "inputText:【净重】国产绿心猕猴桃（10-11斤）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:1467.6191806793213ms\n", "inputText:脆皮黄金桔/一级（3斤*1袋）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:638.500452041626ms\n", "inputText:【净重】百香果（一级）（5斤*1包）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:572.8609561920166ms\n", "inputText:【净重】奉节脐橙橙/一级（5斤±0.2*1包）, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:641.2932872772217ms\n", "inputText:【净重】沙糖桔/一级（3斤*1包）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:640.178918838501ms\n", "inputText:【净重】有籽青柠檬（3斤*1包）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:524.3573188781738ms\n", "inputText:海南小金桔/一级（3斤*1袋）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:605.1905155181885ms\n", "inputText:【净重】广东香水柠檬90-150克（2斤±0.2）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:584.6133232116699ms\n", "inputText:【净重】广东香水柠檬/标准规格（5斤±0.2）, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:1071.5868473052979ms\n", "inputText:【净重】百香果（5斤*1包）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:637.4588012695312ms\n", "inputText:【净重】黄柠檬/大果110-115g（3斤*1）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:570.6791877746582ms\n", "inputText:【净重】无籽青柠檬（3斤*1包）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:659.4860553741455ms\n", "inputText:【净重】黄柠檬90-110g（3斤*1）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:547.6095676422119ms\n", "inputText:进口澳洲脐橙（5斤±0.2）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:580.9757709503174ms\n", "inputText:红富士苹果（5斤±0.2）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:525.4883766174316ms\n", "inputText:【净重】河北皇冠梨/一级（9-10斤）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:3764.2147541046143ms\n", "inputText:【净重】河北皇冠梨/一级（5斤±0.2）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:631.2458515167236ms\n", "inputText:【毛重】河北皇冠梨/一级28-30斤, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:634.3486309051514ms\n", "inputText:红富士苹果（32-33斤）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:565.6895637512207ms\n", "inputText:红富士苹果（10斤±0.3）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:564.2707347869873ms\n", "inputText:广东菠萝（32-36斤）, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:711.0507488250732ms\n", "inputText:广东菠萝（2个*1）, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:1060.9419345855713ms\n", "inputText:菠萝（27-30斤）, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:533.4694385528564ms\n", "inputText:菠萝4.7-5.2斤（2个*1包）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:639.7221088409424ms\n", "inputText:都乐无冠凤梨（21-22斤）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:568.3107376098633ms\n", "inputText:都乐无冠凤梨（2个*1包）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:609.1718673706055ms\n", "inputText:即食秘鲁牛油果 /单果130g+（2个*1袋）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:654.4919013977051ms\n", "inputText:即食秘鲁牛油果 /单果130g+（20个*1箱）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:681.0259819030762ms\n", "inputText:【带拉环】砀联/一级黄桃罐头/体验装（820g*1罐）, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:632.6088905334473ms\n", "inputText:【带拉环】砀联/一级黄桃罐头（820g*24罐）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:592.9703712463379ms\n", "inputText:唐祖颗粒草莓果馅（5KG*1桶）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:543.5547828674316ms\n", "inputText:唐祖颗粒草莓果馅（5KG*4桶/件）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:538.0287170410156ms\n", "inputText:唐祖菠萝果馅（5kg*1桶）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:583.5604667663574ms\n", "inputText:唐祖菠萝果馅（5kg*4桶/件）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:866.8732643127441ms\n", "inputText:唐祖杂果果馅（5KG*1桶）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:558.1443309783936ms\n", "inputText:唐祖杂果果馅（5KG*4桶/件）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:629.7500133514404ms\n", "inputText:【颗粒】唐祖蓝莓酱（5KG*1桶）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:610.6338500976562ms\n", "inputText:【颗粒】唐祖蓝莓酱（5KG*4桶）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:678.626537322998ms\n", "inputText:3A优质金枕头无核榴莲(6斤*6包）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:534.4767570495605ms\n", "inputText:【砀联】一级黄桃罐头（820g*24罐）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:539.1519069671631ms\n", "inputText:红西柚罐头/体验装（800g*1瓶）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:575.1440525054932ms\n", "inputText:红西柚罐头（800g*12瓶）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:568.779468536377ms\n", "inputText:【砀联】一级黄桃罐头/体验装（820g*1罐）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:591.5811061859131ms\n", "inputText:3A优质金枕头无核榴莲(6斤*1包）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:599.9677181243896ms\n", "inputText:【碎】唐祖碎蓝莓酱（5KG*1桶）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:600.0063419342041ms\n", "inputText:【碎】唐祖碎蓝莓酱（5KG*4桶/件）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:871.6914653778076ms\n", "inputText:福百润杂果果馅（5KG*1桶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:555.4366111755371ms\n", "inputText:福百润草莓馅（5KG*1桶）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:546.135663986206ms\n", "inputText:福百润草莓馅（5KG*4桶）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:1411.3383293151855ms\n", "inputText:福百润杂果果馅（5KG*4桶/件）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:594.9265956878662ms\n", "inputText:菠萝果馅（5KG*4桶/件）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:574.6822357177734ms\n", "inputText:菠萝果馅（5KG*1桶）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:629.615068435669ms\n", "inputText:立高360pro稀奶油（1L*12瓶/1箱）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:527.3287296295166ms\n", "inputText:蓝宝石牛奶奶油（1L*12瓶/1箱）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:586.8806838989258ms\n", "inputText:和牧牛奶奶油（907克*12瓶/1箱）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:690.7942295074463ms\n", "inputText:安佳淡奶油（1L*12瓶/1箱）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:921.1678504943848ms\n", "inputText:爱乐薇（紫铁塔）淡奶油（1L*12瓶/1箱）, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:674.769401550293ms\n", "inputText:侨艺800淡奶油（1L*12瓶/1箱）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:529.1786193847656ms\n", "inputText:雀巢烘烤淡奶油（1L*12盒/1箱）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:651.3960361480713ms\n", "inputText:爱真300Pro稀奶油（1kg*12盒/1箱）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:561.6705417633057ms\n", "inputText:爱尔薇 铁塔淡奶油（1L*12盒/1箱）, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:667.9317951202393ms\n", "inputText:蓝风车蓝米吉稀奶油（1L*12盒/1箱）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:807.7149391174316ms\n", "inputText:卓美植物奶油（1L*12瓶/1箱）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:543.5259342193604ms\n", "inputText:奥夫淡奶油（1L*12瓶/1箱）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:581.2993049621582ms\n", "inputText:立高360pro稀奶油（1L*1瓶/）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:676.4864921569824ms\n", "inputText:卓美植物奶油（1L*1瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:1553.844928741455ms\n", "inputText:奥夫淡奶油（1L*1瓶）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:649.0604877471924ms\n", "inputText:和牧牛奶奶油（907克*1瓶）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:1586.7702960968018ms\n", "inputText:爱尔薇 铁塔淡奶油（1L*1瓶）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:550.891637802124ms\n", "inputText:爱乐薇（紫铁塔）淡奶油（1L*1瓶）, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:1056.9989681243896ms\n", "inputText:侨艺800淡奶油（1L*1瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:568.1631565093994ms\n", "inputText:爱真300Pro稀奶油（1kg*1瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:551.3262748718262ms\n", "inputText:蓝风车蓝米吉稀奶油（1L*1瓶）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:586.2975120544434ms\n", "inputText:蓝宝石牛奶奶油（1L*1瓶）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:566.0758018493652ms\n", "inputText:安佳淡奶油（1L*1盒）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:1688.6286735534668ms\n", "inputText:君乐宝纯牛奶（1L*1瓶）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:547.0061302185059ms\n", "inputText:君乐宝纯牛奶（1L*12瓶/1件）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:559.8320960998535ms\n", "inputText:啡悦纯牛奶（1L*1瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:532.8009128570557ms\n", "inputText:艾蕊思全脂纯牛奶（1L*12瓶/1件）, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:578.6876678466797ms\n", "inputText:福成纯牛奶（1L*1瓶）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:525.747537612915ms\n", "inputText:艾蕊思全脂纯牛奶（1L*1瓶）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:595.0355529785156ms\n", "inputText:完达山纯牛奶（1L*1瓶）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:565.3111934661865ms\n", "inputText:爱氏晨曦纯牛奶（1L*12瓶/1箱）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:537.9788875579834ms\n", "inputText:伊利纯牛奶（250ml*24瓶/1箱）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:610.9483242034912ms\n", "inputText:爱氏晨曦纯牛奶（1L*1瓶）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:530.7111740112305ms\n", "inputText:啡悦纯牛奶（1L*12瓶/1件）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:554.2776584625244ms\n", "inputText:福成纯牛奶（1L*12瓶/1箱）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:3261.9283199310303ms\n", "inputText:牧四季纯牛奶（1L*11瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:574.3401050567627ms\n", "inputText:琪雷萨马斯卡彭（500g*6盒）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:582.6988220214844ms\n", "inputText:安佳奶油奶酪（1KG*1盒）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:553.5016059875488ms\n", "inputText:辛尼琪马斯卡彭（500g*1盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:709.022045135498ms\n", "inputText:辛尼琪马斯卡彭（500g*6盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:545.0043678283691ms\n", "inputText:琪雷萨马斯卡彭（500g*1盒）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:576.1618614196777ms\n", "inputText:安佳无盐黄油（5KG*1块）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:574.9201774597168ms\n", "inputText:安佳黄油（227g*40块）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:561.309814453125ms\n", "inputText:安佳黄油（227g*1块）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:723.0417728424072ms\n", "inputText:爱乐薇（铁塔）黄油（1KG*1块）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:563.9371871948242ms\n", "inputText:安佳无盐大黄油（25KG*1箱）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:574.7394561767578ms\n", "inputText:NZ<PERSON>新西兰全脂奶粉（25KG*1包）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:583.2557678222656ms\n", "cache matched:唐祖颗粒草莓果馅（5KG*1桶）\n", "cache matched:唐祖颗粒草莓果馅（5KG*4桶/件）\n", "cache matched:唐祖菠萝果馅（5kg*1桶）\n", "cache matched:唐祖菠萝果馅（5kg*4桶/件）\n", "cache matched:唐祖杂果果馅（5KG*1桶）\n", "cache matched:唐祖杂果果馅（5KG*4桶/件）\n", "cache matched:【颗粒】唐祖蓝莓酱（5KG*1桶）\n", "cache matched:【颗粒】唐祖蓝莓酱（5KG*4桶）\n", "cache matched:【碎】唐祖碎蓝莓酱（5KG*1桶）\n", "cache matched:【碎】唐祖碎蓝莓酱（5KG*4桶/件）\n", "cache matched:福百润杂果果馅（5KG*1桶）\n", "cache matched:福百润草莓馅（5KG*1桶）\n", "cache matched:福百润草莓馅（5KG*4桶）\n", "cache matched:福百润杂果果馅（5KG*4桶/件）\n", "cache matched:菠萝果馅（5KG*4桶/件）\n", "cache matched:菠萝果馅（5KG*1桶）\n", "cache matched:【带拉环】砀联/一级黄桃罐头/体验装（820g*1罐）\n", "cache matched:【带拉环】砀联/一级黄桃罐头（820g*24罐）\n", "cache matched:【砀联】一级黄桃罐头（820g*24罐）\n", "cache matched:红西柚罐头/体验装（800g*1瓶）\n", "cache matched:红西柚罐头（800g*12瓶）\n", "cache matched:【砀联】一级黄桃罐头/体验装（820g*1罐）\n", "inputText:奥利奥饼干碎（400G*24包/件）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:1172.0550060272217ms\n", "inputText:奥利奥饼干碎（400G*10包）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:1165.945291519165ms\n", "inputText:奥利奥饼干碎（400G*1包）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:1334.531545639038ms\n", "inputText:安诺手指饼干（200G*3包）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:648.6523151397705ms\n", "inputText:可可百利薄脆片（2.5KG*.1盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:554.6646118164062ms\n", "inputText:【紫色】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:541.3618087768555ms\n", "inputText:【黄色】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:550.3149032592773ms\n", "inputText:【黑色】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:957.805871963501ms\n", "inputText:【白色】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:540.9038066864014ms\n", "inputText:【棕色】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:521.0661888122559ms\n", "inputText:【绿色】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:554.5532703399658ms\n", "inputText:【高级灰】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:610.3770732879639ms\n", "inputText:【蓝色】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:547.8918552398682ms\n", "inputText:【大红色】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:2689.6517276763916ms\n", "inputText:【橙色】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:554.2500019073486ms\n", "inputText:【粉色】凯贝色水（250g*1瓶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:575.937032699585ms\n", "inputText:赤色（250g*1瓶）, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:524.9967575073242ms\n", "inputText:贝一防潮糖粉（1KG*1罐）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:691.3862228393555ms\n", "inputText:朱师傅防潮糖粉（1KG*1罐）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:617.5751686096191ms\n", "inputText:一级白砂糖（50KG*1包）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:536.8819236755371ms\n", "inputText:TS 韩国幼砂糖（30KG*1包）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:538.4364128112793ms\n", "inputText:张味记幼砂糖（25KG*1包）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:562.3698234558105ms\n", "inputText:三井细砂糖（25kg*1袋）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:623.6493587493896ms\n", "inputText:【Q润版】贝琪戚风预拌粉（25kg*1包）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:1908.5135459899902ms\n", "inputText:沪粉麻薯预拌粉（5kg*1袋）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:536.7043018341064ms\n", "inputText:【大客户版】贝琪戚风预拌粉（25kg*1包）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:812.0863437652588ms\n", "inputText:王后精制低筋粉（25KG*1包）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:579.0557861328125ms\n", "inputText:五得利低筋面粉, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:1493.7610626220703ms\n", "inputText:玉星玉米淀粉（25kg*1袋）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:647.1230983734131ms\n", "inputText:王后精致高筋小麦粉（25KG*1包）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:599.3890762329102ms\n", "inputText:沪粉雪媚娘预拌粉（5kg*1袋）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:504.6391487121582ms\n", "inputText:国维玉米淀粉（25kg*1袋）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:812.7131462097168ms\n", "inputText:沪粉 戚风预拌粉（25KG*1袋）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:532.3350429534912ms\n", "inputText:【破带】王后精制低筋粉（25KG*1包）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:896.3079452514648ms\n", "inputText:王后柔风吐司粉（25KG*1包）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:567.6043033599854ms\n", "inputText:王后日式面包粉（25KG*1包）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:1050.5311489105225ms\n", "inputText:味斯美蟹黄味酥脆松（2KG*8包）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:542.7207946777344ms\n", "inputText:味斯美蟹黄味酥脆松（2KG*1包）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:554.0771484375ms\n", "inputText:味斯美3A海苔酥脆松（2KG*5盒）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:559.3206882476807ms\n", "inputText:味斯美3A海苔酥脆松（2KG*1盒）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:903.4194946289062ms\n", "inputText:臻行者3A海苔酥肉松, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:637.789249420166ms\n", "inputText:【辣味】味斯美装饰松松（1KG*5包）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:1406.5425395965576ms\n", "inputText:【辣味】味斯美装饰松松（1KG*1包）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:1841.1633968353271ms\n", "inputText:【原味】味斯美装饰松松（1KG*5包）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:689.8424625396729ms\n", "inputText:【原味】味斯美装饰松松（1KG*1包）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:563.4653568267822ms\n", "inputText:君度力娇酒（700ml*1瓶）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:589.3921852111816ms\n", "inputText:长城干葡萄酒（750ml*1瓶）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:690.7711029052734ms\n", "inputText:摩根船长牙买加朗姆酒（700ml*1瓶）, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:675.1534938812256ms\n", "inputText:张裕金奖白兰地葡萄酒（700ml*1瓶）, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:651.7288684844971ms\n", "inputText:甘露咖啡力娇酒（700ml*1瓶）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:625.0905990600586ms\n", "inputText:波士荔枝味力娇酒（700ml*1瓶）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:508.5608959197998ms\n", "inputText:波士蜜瓜味力娇酒（700ml*1瓶）, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:757.8818798065186ms\n", "inputText:朗姆酒（50ml*2瓶）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:541.2094593048096ms\n", "inputText:波士樱桃味力娇酒（700ml*1瓶）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:570.8572864532471ms\n", "cache matched:贝一防潮糖粉（1KG*1罐）\n", "cache matched:朱师傅防潮糖粉（1KG*1罐）\n", "inputText:青百利吉利丁（5g*10片）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:534.3470573425293ms\n", "inputText:大卫吉利丁（2.5g*400片）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:1129.0757656097412ms\n", "inputText:SP贝琪快客蛋糕油(5kg*1桶）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:726.5806198120117ms\n", "inputText:早苗塔塔粉（1.35KG*1罐）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:558.8662624359131ms\n", "inputText:早苗泡打粉（2.7KG*1桶）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:550.2851009368896ms\n", "inputText:SP万研金牌蛋糕油（5KG*1桶）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:512.1135711669922ms\n", "inputText:【元宝】一级大豆油（20L*1桶）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:1082.202434539795ms\n", "inputText:南桥酥油（16KG）, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:573.1000900268555ms\n", "inputText:【四海】一级大豆油（20L*1桶）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:565.7494068145752ms\n", "inputText:南桥液态酥油（20kg*1桶）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:537.421703338623ms\n", "cache matched:南桥酥油（16KG）\n", "inputText:壹鹏蛋挞皮（300个*1件）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:567.2969818115234ms\n", "inputText:壹鹏蛋挞皮（60个*1提）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:619.0483570098877ms\n", "cache matched:3A优质金枕头无核榴莲(6斤*6包）\n", "cache matched:3A优质金枕头无核榴莲(6斤*1包）\n", "inputText:新疆葡萄干（20斤*1袋）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:601.2749671936035ms\n", "inputText:元果美味沙拉酱(3KG*4袋), usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:553.6139011383057ms\n", "inputText:元果美味沙拉酱(3KG*1袋), usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:588.3119106292725ms\n", "inputText:元果香甜沙拉酱(1KG*12袋), usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:516.542911529541ms\n", "inputText:元果香甜沙拉酱(1KG*1袋), usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:575.0551223754883ms\n", "inputText:百利中式沙拉酱（1KG*12包）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:571.0117816925049ms\n", "inputText:百利中式沙拉酱（1KG*1包）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:537.456750869751ms\n", "inputText:【芒果】安德鲁果溶（1KG*8盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:541.135311126709ms\n", "inputText:【草莓】安德鲁果溶（1KG*8盒）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:550.3926277160645ms\n", "inputText:【树莓】安德鲁果溶（1KG*8盒）\t, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:566.154956817627ms\n", "inputText:【树莓】安德鲁果溶（1KG*1盒）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:595.3919887542725ms\n", "cache matched:【树莓】安德鲁果溶（1KG*1盒）\n", "inputText:【草莓】安德鲁果溶（1KG*1盒）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:535.4268550872803ms\n", "inputText:【蓝莓】进口安德鲁果溶（1KG*1盒）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:1333.1620693206787ms\n", "inputText:【芒果】安德鲁果溶（1KG*1盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:533.4715843200684ms\n", "inputText:金牌高达椰浆（400ml*5瓶）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:560.3272914886475ms\n", "inputText:佳乐纯正椰浆（1L*12盒）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:585.7470035552979ms\n", "inputText:菲诺厚椰乳有盖（1L*12盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:557.2268962860107ms\n", "inputText:菲诺厚椰乳无盖（1L*12盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:1162.8730297088623ms\n", "cache matched:【带拉环】砀联/一级黄桃罐头/体验装（820g*1罐）\n", "cache matched:【带拉环】砀联/一级黄桃罐头（820g*24罐）\n", "cache matched:【砀联】一级黄桃罐头（820g*24罐）\n", "cache matched:红西柚罐头/体验装（800g*1瓶）\n", "cache matched:红西柚罐头（800g*12瓶）\n", "cache matched:【砀联】一级黄桃罐头/体验装（820g*1罐）\n", "cache matched:沪粉雪媚娘预拌粉（5kg*1袋）\n", "inputText:赫丽特奇椰蓉（500G*1袋）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:636.7576122283936ms\n", "inputText:巧克力黑弹簧（12卷/盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:554.497241973877ms\n", "inputText:【皇冠】巧克力生日快乐牌（72片/盒）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:569.439172744751ms\n", "inputText:巧克力白弹簧（12卷/盒）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:799.710750579834ms\n", "inputText:巧克力黑白吸塑扇（108片*1盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:615.5045032501221ms\n", "inputText:巧克力淋白三角（120片/盒）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:600.3379821777344ms\n", "inputText:纯黑三角形巧克力（120片/盒）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:558.3775043487549ms\n", "inputText:巧克力彩吸塑扇（108片/盒）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:535.883903503418ms\n", "inputText:巧克力D黑三角形（120片/盒）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:569.0004825592041ms\n", "inputText:巧克力生日牌（108片/盒）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:607.7568531036377ms\n", "inputText:巧克力淋白长方形（100片/盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:625.2655982971191ms\n", "inputText:巧克力纯白长方形（100片/盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:661.5476608276367ms\n", "inputText:巧克力纯黑长方形（100片/盒）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:545.4285144805908ms\n", "inputText:巧克力手工烟卷（160根/盒）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:564.232349395752ms\n", "inputText:巧克力黑网片（112片/盒）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:548.9387512207031ms\n", "inputText:巧克力白网片（112片/盒）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:542.6878929138184ms\n", "inputText:小彩虹（72片*1盒）, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:638.3976936340332ms\n", "inputText:大红色玫瑰花瓣（8格*1盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:577.2337913513184ms\n", "inputText:【大红色】翻糖小花（108个*1盒）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:544.5966720581055ms\n", "inputText:我几岁了（12个*1盒）, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:614.9997711181641ms\n", "inputText:翻糖手工小花（108个*1盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:526.045560836792ms\n", "inputText:巧克力元宝（500g*1袋）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:641.8046951293945ms\n", "inputText:巧克力金条（500g*1袋）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:587.6796245574951ms\n", "inputText:巧克力金球（500g*1袋）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:562.9076957702637ms\n", "inputText:巧克力金币（500g*1袋）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:540.1926040649414ms\n", "inputText:手工四色小雏菊（96个/盒）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:507.40718841552734ms\n", "inputText:巧克力手工蝴蝶结（48个/盒）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:627.0513534545898ms\n", "inputText:巧克力手工雏菊（36个/盒）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:610.4216575622559ms\n", "inputText:手工立体樱桃（48个/盒）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:549.9434471130371ms\n", "inputText:手工太阳花（24个/盒）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:557.5082302093506ms\n", "inputText:巧克力手工小花（72个/盒）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:571.4209079742432ms\n", "inputText:【粉色草莓味】巧克力淋面酱（1KG*1块）, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:645.8070278167725ms\n", "inputText:【白色牛奶】巧克力淋面酱（1KG*1块）, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:536.2410545349121ms\n", "inputText:【黑色】巧克力淋面酱（1KG*1块）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:634.1507434844971ms\n", "inputText:【蜜瓜绿】巧克力整块（1KG*1块）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:503.1769275665283ms\n", "inputText:【大红】巧克力整块（1KG*1块）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:516.7157649993896ms\n", "inputText:【柠檬黄】巧克力整块（1KG*1块）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:607.7685356140137ms\n", "inputText:【白色】巧克力整块（1KG*1块）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:1053.8208484649658ms\n", "inputText:【黑色】巧克力整块（1KG*1块）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:525.0375270843506ms\n", "inputText:法芙娜黑巧克力豆52%（6KG*1袋）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:596.2939262390137ms\n", "inputText:法芙娜可可粉（1KG*1包）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:538.0215644836426ms\n", "inputText:可可百利可可粉（1KG*1包）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:503.7543773651123ms\n", "inputText:法芙娜巧克力豆70%（3KG*1袋）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:974.9314785003662ms\n", "inputText:嘉利宝28%白巧克力（2.5KG*1包）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:639.5370960235596ms\n", "inputText:嘉利宝32%白巧克力（2.5KG*1包）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:540.3501987457275ms\n", "inputText:嘉利宝牛奶巧克力33.6%（2.5KG*1包）, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:547.3551750183105ms\n", "inputText:嘉利宝黑巧克力54.5%（2.5KG*1包）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:590.5303955078125ms\n", "inputText:嘉利宝黑巧克力70.5%（2.5KG*1包）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:576.8771171569824ms\n", "cache matched:奥利奥饼干碎（400G*24包/件）\n", "cache matched:奥利奥饼干碎（400G*10包）\n", "cache matched:奥利奥饼干碎（400G*1包）\n", "cache matched:安诺手指饼干（200G*3包）\n", "inputText:大寿字（28片/盒）, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:567.3580169677734ms\n", "inputText:对联福如东海寿比南山（72片/盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:2051.3052940368652ms\n", "inputText:福如东海 寿比南山（32个字*1盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:553.1442165374756ms\n", "inputText:巧克力圆形寿字（16个*1盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:569.6706771850586ms\n", "inputText:巧克力正方形福字（16个*1盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:637.6383304595947ms\n", "inputText:5cm巧克力带叶寿桃（12粒/盒）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:524.7147083282471ms\n", "inputText:15cm巧克力带叶寿桃（1粒/盒）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:555.6478500366211ms\n", "inputText:14cm巧克力带叶寿桃（1粒/盒）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:609.8458766937256ms\n", "inputText:18cm巧克力带叶寿桃（1粒/盒）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:607.9607009887695ms\n", "inputText:4cm巧克力带叶寿桃（15粒/盒）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:642.1551704406738ms\n", "inputText:2.5cm巧克力带叶寿桃（24粒/盒）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:827.6059627532959ms\n", "inputText:【咖色】巧克力碎（500g*1盒）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:1333.4391117095947ms\n", "inputText:巧克力白碎（500g*1盒）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:553.2615184783936ms\n", "inputText:巧克力黑碎（500g*1盒）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:570.1785087585449ms\n", "inputText:韩式草莓味粒粒脆（1KG*1包）, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:577.2640705108643ms\n", "inputText:韩式芝士味粒粒脆(1kg*1包）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:548.6745834350586ms\n", "inputText:韩式巧克力味粒粒脆（1KG*1袋）, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:549.1805076599121ms\n", "cache matched:可可百利薄脆片（2.5KG*.1盒）\n", "inputText:【白板】巧克力麻将（32玫/盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:1131.401538848877ms\n", "cache matched:小彩虹（72片*1盒）\n", "cache matched:大红色玫瑰花瓣（8格*1盒）\n", "cache matched:【大红色】翻糖小花（108个*1盒）\n", "cache matched:我几岁了（12个*1盒）\n", "inputText:【东南西北】巧克力麻将（32玫/盒）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:568.6609745025635ms\n", "inputText:【筒字】巧克力麻将（32玫/盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:657.7410697937012ms\n", "inputText:【红中】巧克力麻将（32玫/盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:565.187931060791ms\n", "inputText:【幺鸡】巧克力麻将（32玫/盒）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:571.880578994751ms\n", "inputText:薄片发财麻将（108片/盒）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:541.3222312927246ms\n", "cache matched:翻糖手工小花（108个*1盒）\n", "inputText:纯发财巧克力麻将（28个/盒）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:524.7418880462646ms\n", "cache matched:巧克力元宝（500g*1袋）\n", "cache matched:巧克力金条（500g*1袋）\n", "cache matched:巧克力金球（500g*1袋）\n", "cache matched:巧克力金币（500g*1袋）\n", "inputText:巧克力爱心棒棒糖（50个*1盒）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:554.3732643127441ms\n", "inputText:巧克力手工梅花（72个/盒）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:662.376880645752ms\n", "inputText:手工大小樱花(36个/盒）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:526.8874168395996ms\n", "inputText:【混装】巧克力仿真麻将（28个/盒）, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:539.46852684021ms\n", "inputText:手工爱心小熊组合（48个/盒）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:616.6031360626221ms\n", "inputText:手工立体华夫饼（60个/盒）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:556.6251277923584ms\n", "inputText:手工立体纽扣（144个/盒）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:631.1666965484619ms\n", "inputText:手工立体绿叶小花（24个/盒）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:534.7886085510254ms\n", "inputText:手工大小爱心大红（132个/盒）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:696.5479850769043ms\n", "inputText:【万字】巧克力麻将（28颗*1盒）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:637.012243270874ms\n", "inputText:绿色勺子独立包装（100个*1包）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:613.6445999145508ms\n", "inputText:粉色勺子独立包装（100个*1包）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:1406.4750671386719ms\n", "inputText:黑色勺子独立包装（100个*1包）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:671.0615158081055ms\n", "inputText:5盘5叉二合一（100套*1件）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:529.395580291748ms\n", "inputText:蓝色勺子独立包装（100个*1包）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:561.753511428833ms\n", "inputText:裱花袋大号特厚（100个*1袋）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:540.6510829925537ms\n", "inputText:裱花袋中号加厚（100个*1袋）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:615.727424621582ms\n", "inputText:裱花袋大号（100个*1袋）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:582.263708114624ms\n", "inputText:5合一5盘5叉（350套*1箱）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:587.5229835510254ms\n", "inputText:5合一10盘10叉（300套*1箱）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:547.4088191986084ms\n", "inputText:5合一5盘5叉（400套*1箱）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:580.507755279541ms\n", "inputText:【全透明】8寸蛋糕盒双层26cm（50*1件）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:702.0561695098877ms\n", "inputText:【全透明】6寸蛋糕盒双层22cm（100*1件）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:545.3305244445801ms\n", "inputText:【全透明】6寸蛋糕盒单层22cm（100*1件）, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:726.3104915618896ms\n", "inputText:【半透明】8寸蛋糕盒单层26cm（50*1件）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:539.0560626983643ms\n", "inputText:【半透明】8寸蛋糕盒三层26cm（50*1件）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:693.8865184783936ms\n", "inputText:【半透明】6寸蛋糕盒三层21cm（100*1件）, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:588.6015892028809ms\n", "inputText:【半透明白色】6寸蛋糕盒单层22cm（100*1件）, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:612.7073764801025ms\n", "inputText:【半透明蓝色】6寸蛋糕盒单层22cm（100*1件）, usage:{\"prompt_tokens\": 33, \"total_tokens\": 33}, time cost:569.0197944641113ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['name'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
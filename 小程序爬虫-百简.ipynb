{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-02-21 12:03:20, date_of_now:2024-02-21, brand_name:百简\n", "2024-02-21 12:03:20\n"]}], "source": ["import requests\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "headers={\"domainhost\":\"cdbj.sdongpo.com\"}\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "brand_name=\"百简\"\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}\")\n", "root_path=f'./data/{brand_name}'\n", "create_directory_if_not_exists(root_path)\n", "\n", "print(time_of_now)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 先登录\n", "\n", "获取token并保存"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3b73c3d9b816243dec11038f688e7295\n"]}], "source": ["# 登录\n", "url='https://scm.shudongpoo.com/commonApi/wapData/checkLogin?username=13883824673&password=bj123456&version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=0&group_user_id=0&site_id=6&token='\n", "token=requests.get(url,headers=headers).json()['data']['token']\n", "\n", "print(token)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取一级类目列表"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# 获取一级类目列表\n", "\n", "url=f'https://scm.shudongpoo.com/commonApi/wapData/categoryList?version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=1019&group_user_id=0&site_id=6&token={token}'\n", "\n", "categoryList=requests.get(url, headers=headers).json()['data']['categoryList']\n", "\n", "pd.<PERSON><PERSON><PERSON><PERSON>(categoryList).to_csv(f'{root_path}/一级类目列表.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级类目获取所有二级类目"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["298\n", "303\n", "[{'id': '305', 'level': '2', 'name': '柑橘橙柚柠', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '998', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '306', 'level': '2', 'name': '桃李枣杏梅', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '997', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '307', 'level': '2', 'name': '荔枝菠萝热带水果', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '996', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '308', 'level': '2', 'name': '板栗马蹄瓜果', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '995', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '309', 'level': '2', 'name': '草莓圣女果浆果', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '994', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '329', 'level': '2', 'name': '葡提奇异果', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '993', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '328', 'level': '2', 'name': '运输费用', 'pic_category': '', 'pic_path_big': '', 'pid': '298', 'sequence': '992', 'alias_name': '', '一级类目ID': '298', '一级类目名': '水果'}, {'id': '323', 'level': '2', 'name': '香草', 'pic_category': '', 'pic_path_big': '', 'pid': '303', 'sequence': '999', 'alias_name': '', '一级类目ID': '303', '一级类目名': '香料'}]\n"]}], "source": ["# 根据一级类目获取所有二级类目\n", "all_second_category = []\n", "for first_cate in categoryList:\n", "    pid=first_cate['id']\n", "    first_cate_name = first_cate['name']\n", "    print(pid)\n", "    url=f'https://scm.shudongpoo.com/commonApi/wapData/categoryList?pid={pid}&version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=1019&group_user_id=0&site_id=6&token={token}'\n", "    second_category_list=requests.get(url,headers=headers).json()['data']['categoryList']\n", "    for second_cate in second_category_list:\n", "        second_cate['一级类目ID']=pid\n", "        second_cate['一级类目名']=first_cate_name\n", "        all_second_category.append(second_cate)\n", "    \n", "print(all_second_category)\n", "pd.DataFrame(all_second_category).to_csv(f'{root_path}/二级类目列表.csv',index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["298 305 柑橘橙柚柠\n", "商品个数： 柑橘橙柚柠 3\n", "298 306 桃李枣杏梅\n", "商品个数： 桃李枣杏梅 1\n", "298 307 荔枝菠萝热带水果\n", "商品个数： 荔枝菠萝热带水果 3\n", "298 308 板栗马蹄瓜果\n", "商品个数： 板栗马蹄瓜果 1\n", "298 309 草莓圣女果浆果\n", "商品个数： 草莓圣女果浆果 3\n", "298 329 葡提奇异果\n", "商品个数： 葡提奇异果 2\n", "298 328 运输费用\n", "商品个数： 运输费用 1\n", "303 323 香草\n", "商品个数： 香草 1\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>is_sell</th>\n", "      <th>is_sell_stock</th>\n", "      <th>sell_stock</th>\n", "      <th>commodity_mark</th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>notice</th>\n", "      <th>price</th>\n", "      <th>unit</th>\n", "      <th>summary</th>\n", "      <th>...</th>\n", "      <th>min_price</th>\n", "      <th>section_price</th>\n", "      <th>section_price_desc</th>\n", "      <th>detail_pic</th>\n", "      <th>video</th>\n", "      <th>within_operation_time</th>\n", "      <th>operating_time</th>\n", "      <th>provider_name</th>\n", "      <th>quaImg</th>\n", "      <th>quaImg_arr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>3</td>\n", "      <td>154</td>\n", "      <td>广东香水柠檬</td>\n", "      <td>广东香水柠檬露天果子会有个别果子有花皮，介意者请谨慎下单哦</td>\n", "      <td>12.50</td>\n", "      <td>斤</td>\n", "      <td>季节原因，果子会偏黄一级轻微碰伤处于正常情况</td>\n", "      <td>...</td>\n", "      <td>12.50</td>\n", "      <td>12.50</td>\n", "      <td>12.50</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>True</td>\n", "      <td>{'default_delivery_date': '2024-02-22', 'is_su...</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>[]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 75 columns</p>\n", "</div>"], "text/plain": ["  is_sell is_sell_stock sell_stock commodity_mark   id    name  \\\n", "0       1             0       0.00              3  154  广东香水柠檬   \n", "\n", "                          notice  price unit                 summary  ...  \\\n", "0  广东香水柠檬露天果子会有个别果子有花皮，介意者请谨慎下单哦  12.50    斤  季节原因，果子会偏黄一级轻微碰伤处于正常情况  ...   \n", "\n", "  min_price section_price section_price_desc detail_pic video  \\\n", "0     12.50         12.50              12.50         []    []   \n", "\n", "  within_operation_time                                     operating_time  \\\n", "0                  True  {'default_delivery_date': '2024-02-22', 'is_su...   \n", "\n", "  provider_name quaImg quaImg_arr  \n", "0                              []  \n", "\n", "[1 rows x 75 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 根据一级和二级类目ID爬取商品信息, 返回array\n", "def get_products_for_category(cateLevel1Id=298, cateLevel2Id=305):\n", "    url=f'https://scm.shudongpoo.com/commonApi/wapData/goodsList?type_id={cateLevel1Id}&type_id2={cateLevel2Id}&page=1&pageSize=50&page_size=50&version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=1019&group_user_id=0&site_id=6&token={token}'\n", "    products=requests.get(url, headers=headers).json()['data']['list']\n", "    return products\n", "\n", "all_product_raw=[]\n", "for cate in all_second_category:\n", "    print(cate['一级类目ID'], cate['id'],cate['name'])\n", "    sub_list = get_products_for_category(cate['一级类目ID'], cate['id'])\n", "    print('商品个数：',cate['name'],len(sub_list))\n", "    all_product_raw.extend(sub_list)\n", "\n", "all_product_raw_df=pd.DataFrame(all_product_raw)\n", "all_product_raw_df.to_csv(f'{root_path}/所有商品列表raw.csv', index=False)\n", "\n", "all_product_raw_df.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 清洗数据并保存"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>alias</th>\n", "      <th>brand</th>\n", "      <th>create_time</th>\n", "      <th>logo</th>\n", "      <th>price</th>\n", "      <th>min_price</th>\n", "      <th>max_price</th>\n", "      <th>order_quantity</th>\n", "      <th>summary</th>\n", "      <th>unit</th>\n", "      <th>unit_num</th>\n", "      <th>数据获取时间</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>154</td>\n", "      <td>广东香水柠檬</td>\n", "      <td>香水柠檬</td>\n", "      <td></td>\n", "      <td>2022-05-24 12:51:03</td>\n", "      <td>https://base-image.shudongpoo.com/base_1320/up...</td>\n", "      <td>12.50</td>\n", "      <td>12.50</td>\n", "      <td>12.50</td>\n", "      <td>1.00</td>\n", "      <td>季节原因，果子会偏黄一级轻微碰伤处于正常情况</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>170</td>\n", "      <td>奉节脐橙</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2022-05-24 12:51:06</td>\n", "      <td>https://base-image.shudongpoo.com/base_1190/up...</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>0.00</td>\n", "      <td></td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>172</td>\n", "      <td>耙耙柑</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2022-05-24 12:51:07</td>\n", "      <td>https://base-image.shudongpoo.com/base_1190/up...</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>3.00</td>\n", "      <td></td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1637</td>\n", "      <td>车厘子-XL</td>\n", "      <td>进口大樱桃</td>\n", "      <td></td>\n", "      <td>2024-01-26 16:32:43</td>\n", "      <td>https://base-image.shudongpoo.com/base_1380/up...</td>\n", "      <td>30.00</td>\n", "      <td>30.00</td>\n", "      <td>30.00</td>\n", "      <td>1.00</td>\n", "      <td>XL</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>89</td>\n", "      <td>小台芒</td>\n", "      <td>芒果</td>\n", "      <td></td>\n", "      <td>2022-04-28 15:33:01</td>\n", "      <td>https://base-image.shudongpoo.com/base_1180/up...</td>\n", "      <td>8.00</td>\n", "      <td>8.00</td>\n", "      <td>8.00</td>\n", "      <td>1.00</td>\n", "      <td></td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>114</td>\n", "      <td>大台芒</td>\n", "      <td>芒果</td>\n", "      <td></td>\n", "      <td>2022-05-08 18:04:45</td>\n", "      <td>https://base-image.shudongpoo.com/sdpcloud/upl...</td>\n", "      <td>7.30</td>\n", "      <td>7.30</td>\n", "      <td>7.30</td>\n", "      <td>1.00</td>\n", "      <td>单果≥150g</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>152</td>\n", "      <td>无冠都乐金菠萝</td>\n", "      <td>菠萝</td>\n", "      <td>都乐</td>\n", "      <td>2022-05-24 12:51:03</td>\n", "      <td>https://base-image.shudongpoo.com/base_1230/up...</td>\n", "      <td>7.50</td>\n", "      <td>7.00</td>\n", "      <td>7.50</td>\n", "      <td>3.50</td>\n", "      <td>每个约2.7-3.6斤</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>30</td>\n", "      <td>无籽西瓜</td>\n", "      <td>西瓜</td>\n", "      <td></td>\n", "      <td>2022-03-01 09:39:32</td>\n", "      <td>https://base-image.shudongpoo.com/base_1180/up...</td>\n", "      <td>5.00</td>\n", "      <td>5.00</td>\n", "      <td>5.00</td>\n", "      <td>7.00</td>\n", "      <td>毛重带箱，一个西瓜约7-15斤。</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>315</td>\n", "      <td>德昌草莓（黔莓）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2022-12-03 00:16:19</td>\n", "      <td>https://base-image.shudongpoo.com/base_1250/up...</td>\n", "      <td>75.00</td>\n", "      <td>73.33</td>\n", "      <td>73.33</td>\n", "      <td>1.00</td>\n", "      <td></td>\n", "      <td>件</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>317</td>\n", "      <td>红颜草莓4X7</td>\n", "      <td>草莓</td>\n", "      <td></td>\n", "      <td>2022-12-03 02:51:45</td>\n", "      <td>https://base-image.shudongpoo.com/base_1250/up...</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>1.00</td>\n", "      <td></td>\n", "      <td>盒</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>355</td>\n", "      <td>红颜草莓4X5</td>\n", "      <td>红颜草莓</td>\n", "      <td></td>\n", "      <td>2022-12-31 19:03:18</td>\n", "      <td>https://base-image.shudongpoo.com/base_1360/up...</td>\n", "      <td>12.50</td>\n", "      <td>12.50</td>\n", "      <td>12.50</td>\n", "      <td>1.00</td>\n", "      <td>红颜草莓盒装4X5（纸盒）</td>\n", "      <td>盒</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>162</td>\n", "      <td>夏黑葡萄</td>\n", "      <td>葡萄</td>\n", "      <td></td>\n", "      <td>2022-05-24 12:51:05</td>\n", "      <td>https://base-image.shudongpoo.com/base_1190/up...</td>\n", "      <td>10.50</td>\n", "      <td>10.50</td>\n", "      <td>10.50</td>\n", "      <td>3.00</td>\n", "      <td></td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>1411</td>\n", "      <td>阳光玫瑰（一级）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2023-07-12 19:01:29</td>\n", "      <td>https://base-image.shudongpoo.com/base_1320/up...</td>\n", "      <td>19.00</td>\n", "      <td>19.00</td>\n", "      <td>19.00</td>\n", "      <td>3.00</td>\n", "      <td>一级果</td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>134</td>\n", "      <td>配送费</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2022-05-24 12:35:31</td>\n", "      <td>https://base-image.shudongpoo.com/base_1190/up...</td>\n", "      <td>15.00</td>\n", "      <td>15.00</td>\n", "      <td>15.00</td>\n", "      <td>1.00</td>\n", "      <td>订单未满配送要求则收取15元配送费。</td>\n", "      <td>次</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>157</td>\n", "      <td>香茅</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2022-05-24 12:51:04</td>\n", "      <td>https://base-image.shudongpoo.com/base_1190/up...</td>\n", "      <td>4.50</td>\n", "      <td>4.50</td>\n", "      <td>4.50</td>\n", "      <td>1.00</td>\n", "      <td></td>\n", "      <td>斤</td>\n", "      <td>1.00</td>\n", "      <td>2024-02-21 12:03:20</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id      name  alias brand          create_time  \\\n", "0    154    广东香水柠檬   香水柠檬        2022-05-24 12:51:03   \n", "1    170      奉节脐橙               2022-05-24 12:51:06   \n", "2    172       耙耙柑               2022-05-24 12:51:07   \n", "3   1637    车厘子-<PERSON><PERSON>  进口大樱桃        2024-01-26 16:32:43   \n", "4     89       小台芒     芒果        2022-04-28 15:33:01   \n", "5    114       大台芒     芒果        2022-05-08 18:04:45   \n", "6    152   无冠都乐金菠萝     菠萝    都乐  2022-05-24 12:51:03   \n", "7     30      无籽西瓜     西瓜        2022-03-01 09:39:32   \n", "8    315  德昌草莓（黔莓）               2022-12-03 00:16:19   \n", "9    317   红颜草莓4X7     草莓        2022-12-03 02:51:45   \n", "10   355   红颜草莓4X5   红颜草莓        2022-12-31 19:03:18   \n", "11   162      夏黑葡萄     葡萄        2022-05-24 12:51:05   \n", "12  1411  阳光玫瑰（一级）               2023-07-12 19:01:29   \n", "13   134       配送费               2022-05-24 12:35:31   \n", "14   157        香茅               2022-05-24 12:51:04   \n", "\n", "                                                 logo  price min_price  \\\n", "0   https://base-image.shudongpoo.com/base_1320/up...  12.50     12.50   \n", "1   https://base-image.shudongpoo.com/base_1190/up...   4.00      4.00   \n", "2   https://base-image.shudongpoo.com/base_1190/up...   4.00      4.00   \n", "3   https://base-image.shudongpoo.com/base_1380/up...  30.00     30.00   \n", "4   https://base-image.shudongpoo.com/base_1180/up...   8.00      8.00   \n", "5   https://base-image.shudongpoo.com/sdpcloud/upl...   7.30      7.30   \n", "6   https://base-image.shudongpoo.com/base_1230/up...   7.50      7.00   \n", "7   https://base-image.shudongpoo.com/base_1180/up...   5.00      5.00   \n", "8   https://base-image.shudongpoo.com/base_1250/up...  75.00     73.33   \n", "9   https://base-image.shudongpoo.com/base_1250/up...  10.00     10.00   \n", "10  https://base-image.shudongpoo.com/base_1360/up...  12.50     12.50   \n", "11  https://base-image.shudongpoo.com/base_1190/up...  10.50     10.50   \n", "12  https://base-image.shudongpoo.com/base_1320/up...  19.00     19.00   \n", "13  https://base-image.shudongpoo.com/base_1190/up...  15.00     15.00   \n", "14  https://base-image.shudongpoo.com/base_1190/up...   4.50      4.50   \n", "\n", "   max_price order_quantity                 summary unit unit_num  \\\n", "0      12.50           1.00  季节原因，果子会偏黄一级轻微碰伤处于正常情况    斤     1.00   \n", "1       4.00           0.00                            斤     1.00   \n", "2       4.00           3.00                            斤     1.00   \n", "3      30.00           1.00                      XL    斤     1.00   \n", "4       8.00           1.00                            斤     1.00   \n", "5       7.30           1.00                 单果≥150g    斤     1.00   \n", "6       7.50           3.50             每个约2.7-3.6斤    斤     1.00   \n", "7       5.00           7.00        毛重带箱，一个西瓜约7-15斤。    斤     1.00   \n", "8      73.33           1.00                            件     1.00   \n", "9      10.00           1.00                            盒     1.00   \n", "10     12.50           1.00           红颜草莓盒装4X5（纸盒）    盒     1.00   \n", "11     10.50           3.00                            斤     1.00   \n", "12     19.00           3.00                     一级果    斤     1.00   \n", "13     15.00           1.00      订单未满配送要求则收取15元配送费。    次     1.00   \n", "14      4.50           1.00                            斤     1.00   \n", "\n", "                 数据获取时间  \n", "0   2024-02-21 12:03:20  \n", "1   2024-02-21 12:03:20  \n", "2   2024-02-21 12:03:20  \n", "3   2024-02-21 12:03:20  \n", "4   2024-02-21 12:03:20  \n", "5   2024-02-21 12:03:20  \n", "6   2024-02-21 12:03:20  \n", "7   2024-02-21 12:03:20  \n", "8   2024-02-21 12:03:20  \n", "9   2024-02-21 12:03:20  \n", "10  2024-02-21 12:03:20  \n", "11  2024-02-21 12:03:20  \n", "12  2024-02-21 12:03:20  \n", "13  2024-02-21 12:03:20  \n", "14  2024-02-21 12:03:20  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# print(all_product_raw_df.columns)\n", "all_product_clean_df=all_product_raw_df[['id','name','alias','brand','create_time','logo','price','min_price','max_price','order_quantity','summary','unit','unit_num']].copy()\n", "all_product_clean_df['数据获取时间']=time_of_now\n", "all_product_clean_df.to_csv(f\"{root_path}/所有商品列表-清洗后-{time_of_now.split(' ')[0]}.csv\")\n", "all_product_clean_df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["19\n"]}], "source": ["print(len('FEISHU_NOTIFY_TOKEN'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
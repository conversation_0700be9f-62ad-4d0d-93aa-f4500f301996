#!/usr/bin/env python3
"""
处理多品牌/多地区爬虫脚本的特殊更新
"""

import os
import re
import glob
from pathlib import Path

def handle_multi_brand_patterns():
    """处理多品牌模式的特殊情况"""
    
    # 特殊模式的映射
    special_patterns = {
        # 模式1: join方式的多品牌输出
        r'logging\.info\(f"===new_record===\{[^}]*\.join\([^)]+\)[^}]*\}, 商品数:\{([^}]+)\}"\)': {
            'type': 'multi_brand_join',
            'description': '使用join连接多个品牌名称的输出'
        },
        
        # 模式2: 直接包含逗号的多品牌
        r'logging\.info\(f"===new_record===([^"]*,[^"]*), 商品数:\{([^}]+)\}"\)': {
            'type': 'multi_brand_comma',
            'description': '直接包含逗号分隔的多品牌输出'
        },
        
        # 模式3: 变量形式的品牌名
        r'logging\.info\(f"===new_record===\{([^}]+)\}, 商品数:\{([^}]+)\}"\)': {
            'type': 'variable_brand',
            'description': '使用变量的品牌名输出'
        }
    }
    
    return special_patterns

def analyze_multi_brand_file(file_path):
    """分析多品牌文件的结构"""
    print(f"\n分析文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经更新过
    if 'spider_reporter.report_' in content:
        print("  ✅ 已经使用新的结构化输出")
        return None
    
    # 查找所有===new_record===模式
    patterns = handle_multi_brand_patterns()
    found_patterns = []
    
    for pattern, info in patterns.items():
        matches = list(re.finditer(pattern, content))
        if matches:
            found_patterns.append({
                'pattern': pattern,
                'matches': matches,
                'type': info['type'],
                'description': info['description']
            })
    
    if not found_patterns:
        print("  ⚠️  未找到可识别的多品牌模式")
        return None
    
    # 分析品牌信息
    analysis = {
        'file_path': file_path,
        'patterns': found_patterns,
        'content': content
    }
    
    for pattern_info in found_patterns:
        print(f"  📋 找到模式: {pattern_info['description']}")
        for match in pattern_info['matches']:
            print(f"     匹配: {match.group(0)}")
    
    return analysis

def generate_multi_brand_update(analysis):
    """为多品牌文件生成更新代码"""
    if not analysis:
        return None
    
    file_path = analysis['file_path']
    file_name = os.path.basename(file_path)
    content = analysis['content']
    
    # 推断品牌名称
    brand_name = "多品牌"
    if "biaoguo" in file_name.lower():
        brand_name = "标果多地区"
    elif "youzan" in file_name.lower():
        brand_name = "有赞多品牌"
    
    updates = []
    
    # 1. 检查是否需要添加import
    if 'create_spider_reporter' not in content:
        # 找到proxy_setup的import
        import_pattern = r'from proxy_setup import \((.*?)\)'
        match = re.search(import_pattern, content, re.DOTALL)
        if match:
            imports = match.group(1)
            new_imports = imports.rstrip() + ',\n    create_spider_reporter,\n'
            updates.append({
                'type': 'import',
                'old': match.group(1),
                'new': new_imports,
                'start': match.start(1),
                'end': match.end(1)
            })
    
    # 2. 添加spider_reporter创建
    # 寻找合适的位置（通常在变量定义之后）
    insert_patterns = [
        r'competitor_name_en\s*=\s*["\'][^"\']+["\']',
        r'time_of_now\s*=\s*datetime\.now\(\)\.strftime\([^)]+\)',
        r'brand_name\s*=\s*["\'][^"\']+["\']'
    ]
    
    insert_pos = None
    for pattern in insert_patterns:
        match = re.search(pattern, content)
        if match:
            insert_pos = match.end()
            break
    
    if insert_pos:
        reporter_code = f'\n\n# 创建爬虫结果报告器\nspider_reporter = create_spider_reporter("{file_name}", "{brand_name}")'
        updates.append({
            'type': 'add_reporter',
            'position': insert_pos,
            'code': reporter_code
        })
    
    # 3. 替换===new_record===输出
    for pattern_info in analysis['patterns']:
        for match in pattern_info['matches']:
            if pattern_info['type'] == 'multi_brand_join':
                # 处理join模式
                count_var = match.group(1) if match.groups() else 'result_cnt'
                new_code = f'''# 使用新的结构化输出方式
if {count_var} > 0:
    spider_reporter.report_success(
        product_count={count_var},
        additional_info={{
            "regions": region_names if 'region_names' in locals() else [],
            "region_count": len(region_names) if 'region_names' in locals() else 0,
            "processed_regions": ','.join(region_names) if 'region_names' in locals() else ""
        }}
    )
else:
    spider_reporter.report_failure(
        error_message="未爬取到任何数据或所有地区都失败",
        error_type="no_data_or_all_regions_failed",
        additional_info={{
            "attempted_regions": region_names if 'region_names' in locals() else []
        }}
    )'''
            else:
                # 处理其他模式
                count_var = match.group(2) if len(match.groups()) >= 2 else match.group(1)
                new_code = f'''# 使用新的结构化输出方式
if {count_var} > 0:
    spider_reporter.report_success(
        product_count={count_var},
        additional_info={{
            "odps_table": table_name if 'table_name' in locals() else "unknown",
            "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }}
    )
else:
    spider_reporter.report_failure(
        error_message="写入ODPS失败或未爬取到数据",
        error_type="odps_write_error_or_no_data"
    )'''
            
            updates.append({
                'type': 'replace_output',
                'old': match.group(0),
                'new': new_code,
                'start': match.start(),
                'end': match.end()
            })
    
    return updates

def apply_updates(file_path, updates):
    """应用更新到文件"""
    if not updates:
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 按位置倒序排序，避免位置偏移
    updates.sort(key=lambda x: x.get('start', x.get('position', 0)), reverse=True)
    
    for update in updates:
        if update['type'] == 'import':
            content = content.replace(update['old'], update['new'])
        elif update['type'] == 'add_reporter':
            pos = update['position']
            content = content[:pos] + update['code'] + content[pos:]
        elif update['type'] == 'replace_output':
            content = content.replace(update['old'], update['new'])
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def main():
    """主函数"""
    scripts_dir = Path(__file__).parent / "scripts"
    
    # 查找可能的多品牌脚本
    potential_files = [
        "biaoguo_with_prop_spider.py",
        "biaoguo_spider_multi-region_new_app.py",
        "youzan_spider.py"  # 已经处理过，但可以检查
    ]
    
    # 也搜索包含multi, region, 多等关键词的文件
    all_spider_files = list(scripts_dir.glob("*spider*.py"))
    multi_keywords = ['multi', 'region', '多', 'with_prop']
    
    for file_path in all_spider_files:
        if any(keyword in file_path.name.lower() for keyword in multi_keywords):
            if file_path.name not in potential_files:
                potential_files.append(file_path.name)
    
    print(f"检查 {len(potential_files)} 个可能的多品牌脚本...")
    
    updated_count = 0
    for file_name in potential_files:
        file_path = scripts_dir / file_name
        if not file_path.exists():
            continue
            
        analysis = analyze_multi_brand_file(file_path)
        if analysis:
            updates = generate_multi_brand_update(analysis)
            if updates and apply_updates(file_path, updates):
                print(f"  ✅ 已更新 {file_name}")
                updated_count += 1
            else:
                print(f"  ⚠️  {file_name} - 无法生成或应用更新")
        else:
            print(f"  ℹ️  {file_name} - 无需更新或已更新")
    
    print(f"\n总结: 成功更新了 {updated_count} 个多品牌脚本")

if __name__ == "__main__":
    main()

# Import the base64 encoding library.
import base64, os, time
import logging
import httpx
import json

# Configure the logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

proxy_object = {"http://": "http://127.0.0.1:7890", "https://": "http://127.0.0.1:7890"}

from openai import AzureOpenAI, OpenAI

my_gpt4o = OpenAI(base_url="https://api.gptsapi.net/v1")

def parse_json_string(json_str)->dict[str:str]:
    try:
        # Remove the prefix '```json' and suffix '```' if present
        cleaned_str = json_str.strip()
        if cleaned_str.startswith("```json") and cleaned_str.endswith("```"):
            cleaned_str = cleaned_str[7:-3].strip()
        elif cleaned_str.startswith("```") and cleaned_str.endswith("```"):
            cleaned_str = cleaned_str[3:-3].strip()

        # Parse the cleaned JSON string into a dictionary
        parsed_data = json.loads(cleaned_str)
        return parsed_data
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return {"error": str(e), "original_input": json_str}

def call_open_ai_directly(
    text_input,
    system_prompt=None,
    is_json=False,
    max_tokens=4096,
    model_to_use="gpt-4o-2024-08-06",
) -> (str, bool):
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": text_input})

    try:
        completion = my_gpt4o.chat.completions.create(
            model=model_to_use,  # Assuming GPT-4 is available, adjust if needed
            messages=messages,
            max_tokens=max_tokens,
            temperature=0.1,
            response_format={"type": "json_object"} if is_json else {"type": "text"},
        )
        response = completion.choices[0].message.content
        logging.info(f"Usage:{completion.usage}")
        if response is None:
            logging.info(f"OpenAI API returned an exception: {completion}")
            return "OpenAI API returned an empty response", False
        return response, True
    except Exception as e:
        logging.error(f"Error calling OpenAI API: {str(e)}")
        return f"Error: {str(e)}", False


client_gpt4o = AzureOpenAI(
    api_version="2024-03-01-preview",
    azure_endpoint="https://xm-ai-us2.openai.azure.com",
    api_key=os.getenv("AZURE_GPT4O_API_KEY", ""),
    http_client=httpx.Client(proxies=proxy_object),
)

client_gpt4o_mini = AzureOpenAI(
    api_version="2024-03-01-preview",
    azure_endpoint="https://xm-ai-us.openai.azure.com",
    api_key=os.getenv("AZURE_GPT4O_MINI_API_KEY", ""),
)


def call_azure_openai_inner(
    messages: list[dict], retrying=1, is_gpt4o=False, json=True, max_tokens=4096
) -> (str, bool):
    if retrying < 0:
        return "超过了最大重试次数", False
    completion = None
    ## gpt3.5:  gpt-35-turbo-16k,
    ## got4o:   gpt-4o
    ## got4o-mini:   gpt-4o-mini
    model = "gpt-4o-mini"
    client_to_use = client_gpt4o_mini
    if is_gpt4o:
        logging.info(f"using GPT-4o...:{messages}")
        model = "gpt-4o"
        client_to_use = client_gpt4o
    try:
        completion = client_to_use.chat.completions.create(
            model=model,
            temperature=0.1,
            max_tokens=max_tokens,
            messages=messages,
            response_format={"type": "json_object"} if json else {"type": "text"},
        )
        response = completion.choices[0].message.content
        if (
            len(completion.choices) <= 0
            or f"{completion.choices[0].finish_reason}" == "content_filter"
        ):
            return f"azure过滤了本次请求:{completion.choices[0].to_dict()}", False
        if response is None:
            logging.info(f"azure API返回了异常:{completion.to_dict()}")
            time.sleep(10)
            return call_azure_openai_inner(
                messages=messages,
                retrying=retrying - 1,
                is_gpt4o=is_gpt4o,
            )
        logging.info(f"total usage:{completion.usage}")
        return response, True
    except Exception as e:
        logging.info(
            f"请求azure接口报错了:{e}\n messages:{messages}, completion:{completion}"
        )
        if retrying <= 0 or "Error code: 400" in f"{e}":
            return f"{e}", False
        logging.info(f"重试中...{retrying}, messages:{messages}")
        return call_azure_openai_inner(
            messages=messages,
            retrying=retrying - 1,
            is_gpt4o=is_gpt4o,
        )


def call_azure_openai(
    text_input: str,
    system_prompt: str = None,
    retrying: int = 1,
    is_gpt4o: bool = False,
    json: bool = True,
    max_tokens: int = 4096,
) -> (str, bool):
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": text_input})
    return call_azure_openai_inner(
        messages=messages,
        retrying=retrying,
        is_gpt4o=is_gpt4o,
        json=json,
        max_tokens=max_tokens,
    )

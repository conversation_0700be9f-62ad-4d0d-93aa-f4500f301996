{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 文件头，准备各类API"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import shutil\n", "import pandas as pd\n", "from odps import ODPS, DataFrame\n", "from datetime import datetime, timedelta\n", "import argparse\n", "import logging\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID = os.getenv(\"ALIBABA_CLOUD_ACCESS_KEY_ID\")\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET = os.getenv(\"ALIBABA_CLOUD_ACCESS_KEY_SECRET\")\n", "AZURE_GPT4O_API_KEY = os.getenv(\"AZURE_GPT4O_API_KEY\")\n", "AZURE_API_KEY = os.getenv(\"AZURE_API_KEY\")\n", "CALL_AI_SERVICE = os.getenv(\"CALL_AI_SERVICE\", \"false\")\n", "STAFFNAME_TO_ANALYTIC = os.getenv(\"STAFFNAME_TO_ANALYTIC\", \"白津源,陈汉文\")\n", "STAFFNAME_TO_ANALYTIC=set(STAFFNAME_TO_ANALYTIC.split(\",\"))\n", "\n", "ds_to_run = datetime.now().strftime(\"%Y-%m-%d 00:00:00\")\n", "ds_to_run = datetime.strptime(ds_to_run, \"%Y-%m-%d 00:00:00\") - timed<PERSON>ta(days=1)\n", "ds_to_run = ds_to_run.strftime(\"%Y%m%d\")\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--ds_to_run\",\n", "    default=ds_to_run,\n", "    help=\"指定跑哪一天的数据，格式: 20250520\",\n", ")\n", "parser.add_argument(\n", "    \"--ACCESS_KEY_ID\",\n", "    default=ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    help=\"ALIBABA_CLOUD_ACCESS_KEY_ID\",\n", ")\n", "parser.add_argument(\n", "    \"--ACCESS_KEY_SECRET\",\n", "    default=ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    help=\"ALIBABA_CLOUD_ACCESS_KEY_SECRET\",\n", ")\n", "parser.add_argument(\n", "    \"--AZURE_API_KEY\",\n", "    default=AZURE_API_KEY,\n", "    help=\"AZURE_API_KEY\",\n", ")\n", "parser.add_argument(\n", "    \"--AZURE_GPT4O_API_KEY\", default=AZURE_GPT4O_API_KEY, help=\"AZURE_GPT4O_API_KEY\"\n", ")\n", "\n", "args, unknown = parser.parse_known_args()\n", "\n", "ds_to_run = args.ds_to_run\n", "# Configure the logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s - %(levelname)s - %(message)s\",\n", "    datefmt=\"%Y-%m-%d %H:%M:%S\",\n", "    handlers=[\n", "        logging.FileHandler(f\"ds_{ds_to_run}_app.log\"),\n", "        logging.StreamHandler(),\n", "    ],\n", ")\n", "\n", "logging.info(args)\n", "\n", "DATA_PATH = f\"./data/{ds_to_run}\"\n", "\n", "logging.info(f\"ds_to_run:{ds_to_run}\")\n", "\n", "default_segment_duration = int(os.getenv(\"SEGMENT_DURATION\", \"45\"))\n", "\n", "odps = ODPS(\n", "    args.ACCESS_KEY_ID,\n", "    args.ACCESS_KEY_SECRET,\n", "    project=\"summerfarm_ds_dev\",\n", "    endpoint=\"http://service.cn-hangzhou.maxcompute.aliyun.com/api\",\n", ")\n", "\n", "\n", "def create_dir_if_not_exist(path):\n", "    if os.path.exists(path):\n", "        shutil.rmtree(path)\n", "    os.makedirs(path)\n", "\n", "\n", "create_dir_if_not_exist(DATA_PATH)\n", "\n", "\n", "def get_odps_sql_result_as_df(sql) -> pd.DataFrame:\n", "    logging.info(f\"ODPS SQL:\\n{sql}\")\n", "    instance = odps.execute_sql(\n", "        sql,\n", "        hints={\"odps.sql.hive.compatible\": True, \"odps.sql.type.system.odps2\": True},\n", "    )\n", "    instance.wait_for_success()\n", "    pd_df = None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        logging.info(f\"columns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "\n", "\n", "def add_new_column_to_table(table_name, column_name):\n", "    if \"summerfarm_ds.\" not in table_name:\n", "        table_name = f\"summerfarm_ds.{table_name}\"\n", "    sql = f\"ALTER TABLE {table_name} ADD COLUMNS ({column_name} STRING);\"\n", "    instance = odps.execute_sql(sql)\n", "    instance.wait_for_success()\n", "    logging.info(f\"添加新字段成功:{table_name}, {column_name}\")\n", "\n", "\n", "def ensure_all_df_columns_in_odps_table(df, table_name):\n", "    if \"summerfarm_ds.\" not in table_name:\n", "        table_name = f\"summerfarm_ds.{table_name}\"\n", "    if not odps.exist_table(table_name):\n", "        logging.info(f\"表不存在:{table_name}\")\n", "        return True\n", "    table = odps.get_table(table_name)\n", "    column_names = set([column.name for column in table.table_schema])\n", "    column_names_out = \",\\n\".join(column_names)\n", "    logging.info(f\"DaraFrame字段合集:\\n\\n{column_names_out}\")\n", "    df_columns = df.columns.tolist()\n", "    for df_col in df_columns:\n", "        df_col = df_col.lower()\n", "        if df_col not in column_names:\n", "            logging.info(f\"新字段:{df_col}, ODPS全部的字段:{column_names}\")\n", "            add_new_column_to_table(table_name, df_col)\n", "    return True\n", "\n", "\n", "def write_pandas_df_into_odps_overwrite(df, table_name, partition_spec):\n", "    if df is None or len(df) <= 0:\n", "        logging.info(f\"数据DF为空, table:{table_name}\")\n", "        return False\n", "    ensure_all_df_columns_in_odps_table(df, table_name)\n", "    exception = None\n", "    try:\n", "        odps_df = DataFrame(df)\n", "        odps_df.persist(\n", "            table_name,\n", "            partition=partition_spec,\n", "            drop_partition=False,\n", "            create_partition=True,\n", "            overwrite=True,\n", "            lifecycle=365,\n", "        )\n", "        logging.info(f\"成功写入odps:{table_name}, partition_spec:{partition_spec}\")\n", "        return True\n", "    except Exception as e:\n", "        exception = e\n", "        logging.info(f\"写入ODPS不成功:{table_name}\", e)\n", "        raise exception"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取ODPS数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recordurl_df=get_odps_sql_result_as_df(f\"\"\"\n", "SELECT  *\n", "FROM summerfarm_ds.crm_qiyu_call_feishu_result_raw_di\n", "WHERE ds='{ds_to_run}';\"\"\")\n", "\n", "logging.info(f\"数据量大小:{len(recordurl_df)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 请求azure进行语音分析，还原对话"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the base64 encoding library.\n", "import base64\n", "import httpx\n", "\n", "proxy_object = {\"http\": \"http://127.0.0.1:8001\", \"https\": \"http://127.0.0.1:8001\"}\n", "\n", "\n", "from openai import AzureOpenAI, OpenAI, Completion\n", "\n", "# gets the API Key from environment variable AZURE_OPENAI_API_KEY\n", "client = AzureOpenAI(\n", "    api_version=\"2023-07-01-preview\",\n", "    azure_endpoint=\"https://xm-ai.openai.azure.com\",\n", "    api_key=args.AZURE_API_KEY,\n", ")\n", "\n", "client_ollama = OpenAI(\n", "    base_url=\"http://localhost:11434/v1/\",\n", "    # required but ignored\n", "    api_key=\"sk-ollama\",\n", "    http_client=httpx.Client(proxies={\"http://\": None, \"https://\": None}),\n", ")\n", "\n", "client_gpt4o = AzureOpenAI(\n", "    api_version=\"2024-02-15-preview\",\n", "    azure_endpoint=\"https://xm-ai-us2.openai.azure.com\",\n", "    api_key=args.AZURE_GPT4O_API_KEY,\n", ")\n", "\n", "USING_OLLAMA = os.getenv(\"USING_OLLAMA\", \"false\")\n", "\n", "\n", "def call_azure_openai(content=\"\", command=\"\", retrying=1, is_gpt4o=False) -> str:\n", "    if retrying < 0:\n", "        return \"超过了最大重试次数\", False\n", "    completion = None\n", "    ## gpt3.5:  gpt-35-turbo-16k,\n", "    ## got4o:   gpt-4o\n", "    model = \"gpt-35-turbo-16k\"\n", "    client_to_use = client\n", "    if \"true\" == f\"{USING_OLLAMA}\":\n", "        logging.warning(f\"使用本地Ollama..., command:{command}\")\n", "        model = \"phi3\"\n", "        client_to_use = client_ollama\n", "    elif is_gpt4o:\n", "        logging.info(f\"using GPT-4o..., command:{command}\")\n", "        model = \"gpt-4o\"\n", "        client_to_use = client_gpt4o\n", "    try:\n", "        messages = [\n", "            # {\n", "            #     \"role\": \"system\",\n", "            #     \"content\": f\"你是一个资深的销售主管。{command}\",\n", "            # },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"你是一个智能AI销售员。{command}.\\n\\n```{content}```\",\n", "            },\n", "        ]\n", "        logging.info(f\"请求azure获取结果中...\\n{messages}\")\n", "        completion = client_to_use.chat.completions.create(\n", "            model=model, temperature=0.1, max_tokens=4095, messages=messages\n", "        )\n", "        response = completion.choices[0].message.content\n", "        if (\n", "            len(completion.choices) <= 0\n", "            or f\"{completion.choices[0].finish_reason}\" == \"content_filter\"\n", "        ):\n", "            return f\"azure过滤了本次请求:{completion.choices[0].to_dict()}\", False\n", "        if response is None:\n", "            logging.info(f\"azure API返回了异常:{completion.to_dict()}\")\n", "            return call_azure_openai(\n", "                content=content,\n", "                command=command,\n", "                retrying=retrying - 1,\n", "                is_gpt4o=is_gpt4o,\n", "            )\n", "        return response, True\n", "    except Exception as e:\n", "        logging.info(\n", "            f\"请求azure接口报错了:{e}\\n content:{content}, completion:{completion}\"\n", "        )\n", "        if retrying <= 0 or \"Error code: 400\" in f\"{e}\":\n", "            return f\"{e}\", False\n", "    logging.info(f\"重试中...{retrying}, content:{content}\")\n", "    return call_azure_openai(\n", "        content=content, command=command, retrying=retrying - 1, is_gpt4o=is_gpt4o\n", "    )\n", "\n", "\n", "commands = [\n", "    {\n", "        \"name\": \"对话总结\",\n", "        \"text\": \"以下文本是我公司销售员/客服和客户之间的通话录音。请你总结对话的内容，提炼出核心事件。请你用“事件一：”,“事件二：”这样的格式回答。\",\n", "    },\n", "    {\n", "        \"name\": \"客情判断\",\n", "        \"text\": \"以下文本是我公司销售员/客服和客户之间的通话录音。请你分析客户对我司的评价是正面的，还是负面的，并给出至少3点分析依据。\\n请你用“评价是正面的/负面的。分析依据依据是：” 这样的格式回答。\",\n", "    },\n", "    {\n", "        \"name\": \"销售达成情况\",\n", "        \"text\": \"\"\"请你分析销售员与客户之间的对话内容，判断我们的销售员是否达成了销售目标。\n", "如果客户问到了任意商品的具体价格且表示认可，也算是销售成功。\n", "请用“销售成功。销售成功的商品是：ABC”。\n", "或者“销售失败。原因是：”这样的格式回答。\n", "如果客户提到了竞争对手，请你在回答的最后一部分加上“竞争对手：对手名字”\"\"\",\n", "    },\n", "]\n", "\n", "\n", "dialog_recover_command = \"\"\"以下文本是销售员和客户之间的对话。\n", "请你还原对话过程，区分哪些内容是销售员说的、哪些是客户说的。\n", "将销售员说的内容用\"销售员：\"表示。将客户说的内容用\"客户：\"表示。\n", "\n", "**请注意:**\n", "- 请你完全基于给出的内容进行还原，未出现的内容不要出现在你的回答里。\n", "- 通常来说对话都是销售员发起的。\n", "\"\"\"\n", "\n", "\n", "def call_ai_api_to_get_insigns(feishu_text):\n", "    result = {}\n", "    dialog, is_ok = call_azure_openai(\n", "        is_gpt4o=True, content=feishu_text, command=dialog_recover_command\n", "    )\n", "    result[\"对话还原\"] = dialog\n", "    if not is_ok:\n", "        logging.info(\"失败:\", dialog)\n", "        return result, is_ok\n", "    if dialog is None or len(dialog) <= 30 or \"对话内容过于简短，无需还原\" in dialog:\n", "        result[\"error\"] = (\n", "            f\"AI返回的对话内容太短了，疑似出错了:{dialog}, 飞书文本:{feishu_text}\"\n", "        )\n", "        logging.error(f\"{result}\")\n", "        return result, is_ok\n", "    for command in commands:\n", "        result[command[\"name\"]], is_ok = call_azure_openai(dialog, command[\"text\"])\n", "        logging.info(f'{command[\"name\"]}:{result[command[\"name\"]]}')\n", "    return result, is_ok\n", "\n", "\n", "def get_gemini_result(row):\n", "    feishu_file_recognize_result = row[\"feishu_file_recognize_result\"]\n", "    logging.info(f\"feishu_file_recognize_result: {feishu_file_recognize_result}\")\n", "    if \"504 Gateway Time-out\" in f\"{feishu_file_recognize_result}\":\n", "        logging.error(f\"飞书文本异常：{feishu_file_recognize_result}\")\n", "        return {\"对话还原\": feishu_file_recognize_result}, False\n", "\n", "    logging.info(\n", "        f\"sessionid:{row['sessionid']}, 飞书识别语音:{feishu_file_recognize_result}\"\n", "    )\n", "    ai_response, is_ok = call_ai_api_to_get_insigns(feishu_file_recognize_result)\n", "    logging.info(f\"{row['sessionid']}, is_ok:{is_ok} API分析:\\n{ai_response}\")\n", "    return ai_response, is_ok"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_records = []\n", "\n", "\n", "def append_error_message_to_row_dict(row_dict={}, msg=\"\"):\n", "    row_dict[\"对话还原\"] = msg\n", "    row_dict[\"对话总结\"] = msg\n", "    row_dict[\"销售达成情况\"] = msg\n", "    row_dict[\"客情判断\"] = msg\n", "\n", "\n", "for index, row in recordurl_df.iterrows():\n", "    if len(all_records) > 2:\n", "        logging.warning(\"测试，已手动截止\")\n", "        break\n", "\n", "    if row[\"staffname\"] not in STAFFNAME_TO_ANALYTIC:\n", "        logging.warning(f\"这个用户暂不分析：{row['staffname']}\")\n", "        continue\n", "\n", "    communication_time_in_seconds = int(row[\"communication_time_in_seconds\"])\n", "    feishu_file_recognize_result = row[\"feishu_file_recognize_result\"]\n", "    row_dict = row.to_dict()\n", "    del row_dict[\"ds\"]\n", "    if \"Max retries exceeded with url\" in feishu_file_recognize_result:\n", "        append_error_message_to_row_dict(\n", "            row_dict, f\"飞书识别异常:{feishu_file_recognize_result}\"\n", "        )\n", "    elif len(feishu_file_recognize_result) <= 80:\n", "        logging.warning(\n", "            f'会话太短了:{communication_time_in_seconds}秒, 会话字数:{len(feishu_file_recognize_result)}, 会话:{row[\"feishu_file_recognize_result\"]}'\n", "        )\n", "        too_short_conv = (\n", "            f\"对话时长太短，字数太少，共{len(feishu_file_recognize_result)}字\"\n", "        )\n", "        append_error_message_to_row_dict(row_dict, too_short_conv)\n", "    else:\n", "        # 请求AI服务\n", "        ai_response_of_row, is_ok = get_gemini_result(row_dict)\n", "        if ai_response_of_row is None or not is_ok:\n", "            append_error_message_to_row_dict(\n", "                row_dict, f\"AI服务返回异常:{ai_response_of_row}\"\n", "            )\n", "        else:\n", "            row_dict[\"对话还原\"] = ai_response_of_row.get(\"对话还原\")\n", "            row_dict[\"对话总结\"] = ai_response_of_row.get(\"对话总结\")\n", "            row_dict[\"销售达成情况\"] = ai_response_of_row.get(\"销售达成情况\")\n", "            row_dict[\"客情判断\"] = ai_response_of_row.get(\"客情判断\")\n", "    all_records.append(row_dict)\n", "\n", "all_records_df = pd.DataFrame(all_records)\n", "print(\n", "    all_records_df[\n", "        [\n", "            \"staffname\",\n", "            \"feishu_file_recognize_result\",\n", "            \"对话还原\",\n", "            \"对话总结\",\n", "            \"销售达成情况\",\n", "            \"客情判断\",\n", "        ]\n", "    ]\n", "    .head(5)\n", "    .to_string()\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 写入ODPS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not \"true\" == USING_OLLAMA:\n", "    logging.warning(\"只有非Ollama的结果才写入ODPS\")\n", "    write_pandas_df_into_odps_overwrite(\n", "        all_records_df,\n", "        \"summerfarm_ds.crm_qiyu_call_ai_analytics_di\",\n", "        f\"ds={ds_to_run}\",\n", "    )\n", "\n", "    days_15d = (datetime.now() - <PERSON><PERSON><PERSON>(15)).strftime(\"%Y%m%d\")\n", "    df = get_odps_sql_result_as_df(\n", "        f\"select ds,count(1) cnt from summerfarm_ds.crm_qiyu_call_ai_analytics_di where ds>='{days_15d}' group by ds order by ds desc\"\n", "    )\n", "    logging.info(f\"成功了吧!\\n{df.to_string()}\")\n", "\n", "logging.info(f\"成功了！\\n>>>>>>>>>>>>\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 写到本地HTML文件"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_from_odps = get_odps_sql_result_as_df(\n", "    f\"select * from summerfarm_ds.crm_qiyu_call_ai_analytics_di where ds='{ds_to_run}' order by staffname;\"\n", ")\n", "if len(df_from_odps) > 0:\n", "    all_records_df = df_from_odps\n", "\n", "css = \"\"\"\n", "<link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css\" integrity=\"sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\" crossorigin=\"anonymous\">\n", "<style type=\\\"text/css\\\">\n", "table {\n", "    color: #333;\n", "    font-family: unset;\n", "    font-size: 12px;\n", "    line-height: 1.5;\n", "    width: 1024px;\n", "    border-collapse:\n", "    collapse; \n", "    border-spacing: 0;\n", "}\n", "\n", "tr{\n", "    border-bottom: 1px solid #C1C3D1;\n", "}\n", "\n", ".large-text-container{\n", "    max-height: 200px; /* set the desired max height value */\n", "    overflow: auto; /* add this to enable scrolling when content exceeds max height */\n", "    max-width: 300px;\n", "}\n", "\n", "tr:nth-child(even) {\n", "    background-color: #F8F8F8;\n", "}\n", "\n", "td, th {\n", "    border: 1px solid transparent; /* No more visible border */\n", "    height: 30px;\n", "}\n", "\n", "th {\n", "    background-color: #DFDFDF; /* Darken header a bit */\n", "    font-weight: bolder;\n", "    min-width: 100px;\n", "    text-align: center;\n", "}\n", "\n", "td {\n", "    background-color: #FAFAFA;\n", "    text-align: left;\n", "}\n", "\n", "ol li{\n", "    text-align: left;\n", "}\n", "</style>\n", "\"\"\"\n", "from IPython.display import HTML\n", "\n", "\n", "# Function to add HTML line breaks\n", "def add_line_breaks(text):\n", "    inner_html = f\"{text}\".replace(\"\\n\", \"<br>\")\n", "    return f\"<div class='large-text-container'>{inner_html}</div>\"\n", "\n", "\n", "# Apply the function to specified columns\n", "all_records_df[\"飞书识别结果\"] = all_records_df[\"feishu_file_recognize_result\"]\n", "all_records_df.sort_values(by=\"staffname\", inplace=True)\n", "columns_to_modify = [\"飞书识别结果\", \"对话还原\", \"对话总结\", \"销售达成情况\", \"客情判断\"]\n", "for column in columns_to_modify:\n", "    all_records_df[column] = all_records_df[column].apply(add_line_breaks)\n", "\n", "html_content = css + all_records_df[\n", "    [\n", "        \"direction\",\n", "        \"staffname\",\n", "        \"飞书识别结果\",\n", "        \"对话还原\",\n", "        \"对话总结\",\n", "        \"销售达成情况\",\n", "        \"客情判断\",\n", "    ]\n", "].to_html(escape=False, index=False, classes=\"table dataframe\")\n", "html_content = f'<html><head><meta charset=\"UTF-8\"><meta name=\"title\" content=\"七鱼电话语音分析_{ds_to_run}\"></head><body>{html_content}</body></html>'\n", "\n", "# 保存HTML到本地文件：\n", "with open(f\"{DATA_PATH}/七鱼电话语音分析_{ds_to_run}.html\", \"w\", encoding=\"utf-8\") as f:\n", "    f.write(html_content)\n", "\n", "# display(HTML(html_content))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import httpx\n", "\n", "# client_ollama = OpenAI(\n", "#     base_url=\"http://localhost:11434/v1/\",\n", "#     # required but ignored\n", "#     api_key=\"sk-ollama\",\n", "#     http_client=httpx.Client(proxies={\"http://\": None, \"https://\": None}),\n", "# )\n", "\n", "# chat_completion = client_ollama.chat.completions.create(\n", "#     messages=[\n", "#         {\n", "#             \"role\": \"user\",\n", "#             \"content\": '你是一个智能AI助手。用尽可能简短的风格回复用户的请求. 说\"这是一个测试\"',\n", "#         }\n", "#     ],\n", "#     model=\"phi3\",\n", "# )\n", "\n", "# logging.info(chat_completion.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
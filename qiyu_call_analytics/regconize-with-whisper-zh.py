#!/usr/bin/env python
# coding: utf-8

# In[ ]:


from transformers import pipeline

import os
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'

transcriber = pipeline(
    "automatic-speech-recognition",
    model="/Users/<USER>/Documents/github/Belle-whisper-large-v3-zh",
    device="mps",
)

transcriber.model.config.forced_decoder_ids = (
    transcriber.tokenizer.get_decoder_prompt_ids(
        language="zh",
        task="transcribe",
    )
)

transcription = transcriber(
    "./data/20240710/mono_9046898292_24ad9ac05fa62ba605c08e332e915911.wav"
)
print(transcription)


# In[ ]:


# # Path to the local directory containing model files
# model_directory = "/Users/<USER>/Documents/github/Belle-whisper-large-v3-zh"

# from transformers import (
#     pipeline,
#     WhisperForConditionalGeneration,
#     WhisperTokenizer,
#     WhisperFeatureExtractor,
#     WhisperProcessor,
# )

# # Load the model
# model = WhisperForConditionalGeneration.from_pretrained(model_directory)

# # Load the tokenizer and feature extractor separately
# tokenizer = WhisperTokenizer.from_pretrained(model_directory)
# feature_extractor = WhisperFeatureExtractor.from_pretrained(model_directory)

# # Set the forced decoder IDs for the specified language and task
# forced_decoder_ids = tokenizer.get_decoder_prompt_ids(language="zh", task="transcribe")
# model.config.forced_decoder_ids = forced_decoder_ids

# # Configure the model to return timestamps
# model.generation_config.return_timestamps = True
# model.generation_config.no_timestamps_token_id = tokenizer.convert_tokens_to_ids(
#     "<|notimestamps|>"
# )

# # Initialize the ASR pipeline with explicit tokenizer and feature extractor
# transcriber = pipeline(
#     "automatic-speech-recognition",
#     model=model,
#     tokenizer=tokenizer,  # Use tokenizer directly
#     device="mps",
#     feature_extractor=feature_extractor,  # Use feature extractor directly
# )

# # Perform the transcription with timestamps
# transcription = transcriber(
#     "./data/20240710/mono_9046898292_24ad9ac05fa62ba605c08e332e915911.wav",
#     return_timestamps=True,
# )

# # Print the transcription result
# print(transcription)


# In[ ]:





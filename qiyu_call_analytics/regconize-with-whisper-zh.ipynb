{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import pipeline\n", "\n", "import os\n", "os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'\n", "\n", "transcriber = pipeline(\n", "    \"automatic-speech-recognition\",\n", "    model=\"/Users/<USER>/Documents/github/Belle-whisper-large-v3-zh\",\n", "    device=\"mps\",\n", ")\n", "\n", "transcriber.model.config.forced_decoder_ids = (\n", "    transcriber.tokenizer.get_decoder_prompt_ids(\n", "        language=\"zh\",\n", "        task=\"transcribe\",\n", "    )\n", ")\n", "\n", "transcription = transcriber(\n", "    \"./data/20240710/mono_9046898292_24ad9ac05fa62ba605c08e332e915911.wav\"\n", ")\n", "print(transcription)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Path to the local directory containing model files\n", "# model_directory = \"/Users/<USER>/Documents/github/Belle-whisper-large-v3-zh\"\n", "\n", "# from transformers import (\n", "#     pipeline,\n", "#     WhisperForConditionalGeneration,\n", "#     WhisperTokenizer,\n", "#     WhisperFeatureExtractor,\n", "#     WhisperProcessor,\n", "# )\n", "\n", "# # Load the model\n", "# model = WhisperForConditionalGeneration.from_pretrained(model_directory)\n", "\n", "# # Load the tokenizer and feature extractor separately\n", "# tokenizer = WhisperTokenizer.from_pretrained(model_directory)\n", "# feature_extractor = WhisperFeatureExtractor.from_pretrained(model_directory)\n", "\n", "# # Set the forced decoder IDs for the specified language and task\n", "# forced_decoder_ids = tokenizer.get_decoder_prompt_ids(language=\"zh\", task=\"transcribe\")\n", "# model.config.forced_decoder_ids = forced_decoder_ids\n", "\n", "# # Configure the model to return timestamps\n", "# model.generation_config.return_timestamps = True\n", "# model.generation_config.no_timestamps_token_id = tokenizer.convert_tokens_to_ids(\n", "#     \"<|notimestamps|>\"\n", "# )\n", "\n", "# # Initialize the ASR pipeline with explicit tokenizer and feature extractor\n", "# transcriber = pipeline(\n", "#     \"automatic-speech-recognition\",\n", "#     model=model,\n", "#     tokenizer=tokenizer,  # Use tokenizer directly\n", "#     device=\"mps\",\n", "#     feature_extractor=feature_extractor,  # Use feature extractor directly\n", "# )\n", "\n", "# # Perform the transcription with timestamps\n", "# transcription = transcriber(\n", "#     \"./data/20240710/mono_9046898292_24ad9ac05fa62ba605c08e332e915911.wav\",\n", "#     return_timestamps=True,\n", "# )\n", "\n", "# # Print the transcription result\n", "# print(transcription)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
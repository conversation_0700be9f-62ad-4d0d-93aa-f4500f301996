{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 文件头，准备各类API"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "from typing import List\n", "import shutil\n", "\n", "import pandas as pd\n", "from odps import ODPS, DataFrame\n", "from datetime import datetime, timedelta\n", "from concurrent.futures import ThreadPoolExecutor\n", "import concurrent.futures\n", "import argparse\n", "import logging\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID = os.getenv(\"ALIBABA_CLOUD_ACCESS_KEY_ID\")\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET = os.getenv(\"ALIBABA_CLOUD_ACCESS_KEY_SECRET\")\n", "FEISHU_XGPT_APP_SECRET = os.getenv(\"FEISHU_XGPT_APP_SECRET\")\n", "AZURE_GPT4O_API_KEY = os.getenv(\"AZURE_GPT4O_API_KEY\")\n", "AZURE_API_KEY = os.getenv(\"AZURE_API_KEY\")\n", "CALL_AI_SERVICE = os.getenv(\"CALL_AI_SERVICE\", \"false\")\n", "\n", "ds_to_run = datetime.now().strftime(\"%Y-%m-%d 00:00:00\")\n", "ds_to_run = datetime.strptime(ds_to_run, \"%Y-%m-%d 00:00:00\") - timed<PERSON>ta(days=1)\n", "ds_to_run = ds_to_run.strftime(\"%Y%m%d\")\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--ds_to_run\",\n", "    default=ds_to_run,\n", "    help=\"指定跑哪一天的数据，格式: 20250520\",\n", ")\n", "parser.add_argument(\n", "    \"--ACCESS_KEY_ID\",\n", "    default=ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    help=\"ALIBABA_CLOUD_ACCESS_KEY_ID\",\n", ")\n", "parser.add_argument(\n", "    \"--ACCESS_KEY_SECRET\",\n", "    default=ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    help=\"ALIBABA_CLOUD_ACCESS_KEY_SECRET\",\n", ")\n", "parser.add_argument(\n", "    \"--FEISHU_XGPT_APP_SECRET\",\n", "    default=FEISHU_XGPT_APP_SECRET,\n", "    help=\"飞书的FEISHU_XGPT_APP_SECRET\",\n", ")\n", "parser.add_argument(\n", "    \"--AZURE_API_KEY\",\n", "    default=AZURE_API_KEY,\n", "    help=\"AZURE_API_KEY\",\n", ")\n", "parser.add_argument(\n", "    \"--AZURE_GPT4O_API_KEY\", default=AZURE_GPT4O_API_KEY, help=\"AZURE_GPT4O_API_KEY\"\n", ")\n", "\n", "args, unknown = parser.parse_known_args()\n", "\n", "ds_to_run = args.ds_to_run\n", "# Configure the logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s - %(levelname)s - %(message)s\",\n", "    datefmt=\"%Y-%m-%d %H:%M:%S\",\n", "    handlers=[\n", "        logging.FileHandler(f\"ds_{ds_to_run}_app.log\"),\n", "        logging.StreamHandler(),\n", "    ],\n", ")\n", "\n", "logging.info(args)\n", "\n", "DATA_PATH = f\"./data/{ds_to_run}\"\n", "\n", "logging.info(f\"ds_to_run:{ds_to_run}\")\n", "\n", "default_segment_duration = int(os.getenv(\"SEGMENT_DURATION\", \"120\"))\n", "\n", "odps = ODPS(\n", "    args.ACCESS_KEY_ID,\n", "    args.ACCESS_KEY_SECRET,\n", "    project=\"summerfarm_ds_dev\",\n", "    endpoint=\"http://service.cn-hangzhou.maxcompute.aliyun.com/api\",\n", ")\n", "\n", "\n", "def create_dir_if_not_exist(path):\n", "    if os.path.exists(path):\n", "        shutil.rmtree(path)\n", "    os.makedirs(path)\n", "\n", "\n", "create_dir_if_not_exist(DATA_PATH)\n", "\n", "\n", "def get_odps_sql_result_as_df(sql) -> pd.DataFrame:\n", "    logging.info(f\"ODPS SQL:\\n{sql}\")\n", "    instance = odps.execute_sql(\n", "        sql,\n", "        hints={\"odps.sql.hive.compatible\": True, \"odps.sql.type.system.odps2\": True},\n", "    )\n", "    instance.wait_for_success()\n", "    pd_df = None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        logging.info(f\"columns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "\n", "\n", "def add_new_column_to_table(table_name, column_name):\n", "    if \"summerfarm_ds.\" not in table_name:\n", "        table_name = f\"summerfarm_ds.{table_name}\"\n", "    sql = f\"ALTER TABLE {table_name} ADD COLUMNS ({column_name} STRING);\"\n", "    instance = odps.execute_sql(sql)\n", "    instance.wait_for_success()\n", "    logging.info(f\"添加新字段成功:{table_name}, {column_name}\")\n", "\n", "\n", "def ensure_all_df_columns_in_odps_table(df, table_name):\n", "    if \"summerfarm_ds.\" not in table_name:\n", "        table_name = f\"summerfarm_ds.{table_name}\"\n", "    if not odps.exist_table(table_name):\n", "        logging.info(f\"表不存在:{table_name}\")\n", "        return True\n", "    table = odps.get_table(table_name)\n", "    column_names = set([column.name for column in table.table_schema])\n", "    column_names_out = \",\\n\".join(column_names)\n", "    logging.info(f\"DaraFrame字段合集:\\n\\n{column_names_out}\")\n", "    df_columns = df.columns.tolist()\n", "    for df_col in df_columns:\n", "        df_col = df_col.lower()\n", "        if df_col not in column_names:\n", "            logging.info(f\"新字段:{df_col}, ODPS全部的字段:{column_names}\")\n", "            add_new_column_to_table(table_name, df_col)\n", "    return True\n", "\n", "\n", "def write_pandas_df_into_odps_overwrite(df, table_name, partition_spec):\n", "    if df is None or len(df) <= 0:\n", "        logging.info(f\"数据DF为空, table:{table_name}\")\n", "        return False\n", "    ensure_all_df_columns_in_odps_table(df, table_name)\n", "    exception = None\n", "    try:\n", "        odps_df = DataFrame(df)\n", "        odps_df.persist(\n", "            table_name,\n", "            partition=partition_spec,\n", "            drop_partition=False,\n", "            create_partition=True,\n", "            overwrite=True,\n", "            lifecycle=365,\n", "        )\n", "        logging.info(f\"成功写入odps:{table_name}, partition_spec:{partition_spec}\")\n", "        return True\n", "    except Exception as e:\n", "        exception = e\n", "        logging.info(f\"写入ODPS不成功:{table_name}\", e)\n", "        raise exception"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 飞书接口认证"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import base64\n", "import wave\n", "import requests\n", "import re\n", "\n", "def get_feishu_token():\n", "    url = \"https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal\"\n", "    token = requests.post(\n", "        url=url,\n", "        json={\"app_id\": \"cli_a450bff26fbbd00d\", \"app_secret\": args.FEISHU_XGPT_APP_SECRET},\n", "    ).json()\n", "    logging.info(token)\n", "    return token\n", "\n", "\n", "feishu_token = get_feishu_token()\n", "feishu_headers_with_token = {\n", "    \"Authorization\": f'Bearer {feishu_token[\"tenant_access_token\"]}',\n", "    \"Content-Type\": \"application/json\",\n", "}\n", "\n", "logging.info(feishu_headers_with_token)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## wav文件下载和识别接口"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import ffmpeg\n", "import os\n", "import base64\n", "import requests\n", "import logging\n", "import time\n", "import string\n", "import random\n", "\n", "text_map = {}\n", "failed_wav_files = {}\n", "\n", "\n", "def generate_random_string(length=16):\n", "    letters_and_digits = string.ascii_letters + string.digits\n", "    return \"\".join(random.choice(letters_and_digits) for _ in range(length))\n", "\n", "\n", "random_string = generate_random_string()\n", "print(random_string)\n", "\n", "\n", "def dump_failed_json_to_file(json_object, file_id):\n", "    with open(f\"{DATA_PATH}/failed_{file_id}.json\", \"w\") as f:\n", "        json.dump(json_object, f, indent=4)\n", "\n", "\n", "def get_file_id_from_base64_content(s, num=16):\n", "    # return \"\".join(re.findall(r\"[a-zA-Z0-9]\", s))[:num]\n", "    return generate_random_string(num)\n", "\n", "\n", "def get_dir_from_path(file_path):\n", "    return os.path.dirname(file_path)\n", "\n", "\n", "def split_wav_file(input_file, segment_duration=default_segment_duration):\n", "    output_files = []\n", "    basename, _ = os.path.splitext(os.path.basename(input_file))\n", "\n", "    try:\n", "        output_dir = get_dir_from_path(input_file)\n", "        # Open the input file\n", "        stream = ffmpeg.input(input_file)\n", "\n", "        # Set the output format and codec\n", "        stream = ffmpeg.output(\n", "            stream,\n", "            os.path.join(output_dir, f\"{basename}_segment_%03d.wav\"),\n", "            codec=\"copy\",\n", "            f=\"segment\",\n", "            segment_time=segment_duration,\n", "        )\n", "\n", "        # Run the FFmpeg command\n", "        ffmpeg.run(stream, overwrite_output=True, quiet=True)\n", "\n", "        # Get the list of output files\n", "        output_files = [\n", "            os.path.join(output_dir, f)\n", "            for f in os.listdir(output_dir)\n", "            if f.startswith(f\"{basename}_segment_\") and f.endswith(\".wav\")\n", "        ]\n", "    except Exception as e:\n", "        logging.error(f\"切分wav文件:{input_file}出错了:{e}\")\n", "\n", "    output_files.sort()\n", "    return output_files\n", "\n", "\n", "def get_base64encoded_content_of_wav(wav_file):\n", "    logging.info(f\"wav_file:{wav_file}\")\n", "\n", "    output = \"\"\n", "\n", "    # Create the input stream\n", "    input_stream = ffmpeg.input(wav_file)\n", "\n", "    # Configure the output stream\n", "    output_stream = input_stream.output(\n", "        \"pipe:\", acodec=\"pcm_s16le\", format=\"s16le\", ac=1, ar=16000\n", "    )\n", "\n", "    # Run the FFmpeg command and capture the output\n", "    output, _ = ffmpeg.run(output_stream, capture_stdout=True, quiet=True)\n", "\n", "    # The output is a bytes object containing the raw PCM data\n", "    pcm_data = output\n", "    return base64.b64encode(pcm_data).decode(\"utf-8\")\n", "\n", "\n", "def recognize_feishu_api_base64_content(base64_pcm_data, wav_file, retry=True):\n", "    file_id = get_file_id_from_base64_content(base64_pcm_data)\n", "    feishu_json = {\n", "        \"config\": {\n", "            \"engine_type\": \"16k_auto\",\n", "            \"file_id\": file_id,\n", "            \"format\": \"pcm\",\n", "        },\n", "        \"speech\": {\"speech\": f\"{base64_pcm_data}\"},\n", "    }\n", "\n", "    text = \"\"\n", "    try:\n", "        text = requests.post(\n", "            \"https://open.feishu.cn/open-apis/speech_to_text/v1/speech/file_recognize\",\n", "            json=feishu_json,\n", "            headers=feishu_headers_with_token,\n", "            timeout=(20, 180),\n", "        ).text\n", "        logging.info(f\"wav_file:{wav_file}, 飞书response:{text}\")\n", "        text = json.loads(text)\n", "        return text[\"data\"][\"recognition_text\"]\n", "    except Exception as e:\n", "        logging.error(\n", "            f\"调用飞书接口错误wav_file:{wav_file}, 接口返回:{text}, 异常信息:{e}\"\n", "        )\n", "        if retry:\n", "            logging.warning(\"20s后重试1次:\")\n", "            time.sleep(20)\n", "            return recognize_feishu_api_base64_content(\n", "                base64_pcm_data=base64_pcm_data, wav_file=wav_file, retry=False\n", "            )\n", "        else:\n", "            global failed_wav_files\n", "            dump_failed_json_to_file(feishu_json, file_id)\n", "            failed_wav_files[\"wav_file\"] = file_id\n", "            logging.error(f\"不再重试:{retry}\")\n", "            return f\"调用飞书异常:{wav_file}, {e}\"\n", "\n", "\n", "def is_feishu504_error(api_text):\n", "    return \"504 Gateway Time-out\" in api_text\n", "\n", "\n", "def file_recognize_feishu_api(wav_file):\n", "    # 先切分成2分钟一段：\n", "    sub_files = split_wav_file(wav_file)\n", "\n", "    if sub_files is None or len(sub_files) < 0:\n", "        logging.info(f\"切分失败：{wav_file}\")\n", "        return\n", "    if len(sub_files) > 1:\n", "        logging.info(f\"切分成了多个小文件：{','.join(sub_files)}\")\n", "    all_text = []\n", "    for file in sub_files:\n", "        base64_pcm_data = get_base64encoded_content_of_wav(file)\n", "        logging.info(f\"wav_file:{file}, base64_pcm_data: {base64_pcm_data[-50:]}\")\n", "        all_text.append(\n", "            recognize_feishu_api_base64_content(\n", "                base64_pcm_data=base64_pcm_data, wav_file=file\n", "            )\n", "        )\n", "    return \"\".join(all_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取ODPS数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "recordurl_df = get_odps_sql_result_as_df(\n", "    f\"\"\"\n", "SELECT  *,DATEDIFF(CAST(endtime AS TIMESTAMP),CAST(createtime AS TIMESTAMP),'ss') communication_time_in_seconds\n", "FROM    (\n", "            SELECT  JSON_TUPLE(body,\"eventtype\",\"sessionid\",\"direction\",\"createtime\",\"endtime\",\"connectionbeginetime\",\"connectionendtime\",\"from\",\"to\",\"user\",\"category\",\"staffid\",\"staffname\",\"status\",\"visittimes\",\"duration\",\"evaluation\",\"recordurl\",\"overflowFrom\",\"shuntGroupName\",\"ivrPath\",\"mobileArea\",\"waitDuration\",\"ringDuration\",\"sessionIdFrom\",\"firstEndDirection\") AS (\"eventtype\",\"sessionid\",\"direction\",\"createtime\",\"endtime\",\"connectionbeginetime\",\"connectionendtime\",\"from\",\"to\",\"user\",\"category\",\"staffid\",\"staffname\",\"status\",\"visittimes\",\"duration\",\"evaluation\",\"recordurl\",\"overflowFrom\",\"shuntGroupName\",\"ivrPath\",\"mobileArea\",\"waitDuration\",\"ringDuration\",\"sessionIdFrom\",\"firstEndDirection\")\n", "            FROM    summerfarm_tech.ods_qiyu_call_log_di\n", "            WHERE   ds = '{ds_to_run}'\n", "            AND     GET_JSON_OBJECT(body,'$.eventtype') = '5'\n", "        ) \n", "WHERE   recordurl LIKE 'https://hzxmkjyxgs7.%';\"\"\"\n", ")\n", "\n", "length_of_source = len(recordurl_df)\n", "logging.info(f\"数据量大小:{length_of_source}\")\n", "if length_of_source <= 0:\n", "    raise Exception(\"summerfarm_tech.ods_qiyu_call_log_di 的数据为空\")\n", "\n", "overwrite = os.getenv(\"QIYU_CALL_OVERWRITE_DS\", \"false\")\n", "overwrite = \"true\" == overwrite\n", "if not overwrite:\n", "    # 如果不是overwrite，则检查一下是否已经跑过了。跑过了则无需重试\n", "    is_data_exists_df = get_odps_sql_result_as_df(\n", "        f\"\"\"\n", "    select ds,count(1) cnt,count(case when feishu_file_recognize_result is null or length(feishu_file_recognize_result)<=1 then 1 end) as empty_result_cnt\n", "    from summerfarm_ds.crm_qiyu_call_feishu_result_raw_di \n", "    where ds = '{ds_to_run}' group by ds\n", "    \"\"\"\n", "    )\n", "    existing_data_length = len(is_data_exists_df)\n", "    if len(is_data_exists_df) > 0:\n", "        first_cnt = is_data_exists_df.iloc[0][\"cnt\"]\n", "        if first_cnt > length_of_source / 2:\n", "            logging.info(f\"已经跑过啦，结束吧大兄弟, is_data_exists_df:\\n{is_data_exists_df.to_string()}\")\n", "            sys.exit()\n", "        else:\n", "            print(\n", "                f\"线上的数据大小太小啦，重新跑吧, first_cnt:{first_cnt}, length_of_source:{length_of_source}\"\n", "            )\n", "    else:\n", "        logging.info(f\"开始运行ds:{ds_to_run}\")\n", "else:\n", "    logging.warning(\"您选择了覆盖已有的数据...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import os\n", "\n", "\n", "def download_wav_file(url, sessionid, communication_time_in_seconds=0):\n", "    logging.info(f\"下载语音文件:{url}, session:{sessionid}, 通话时长:{communication_time_in_seconds}s\")\n", "    file_name = os.path.basename(url)\n", "    response = requests.get(url)\n", "    local_file = f\"{DATA_PATH}/{sessionid}_{file_name}\"\n", "    if os.path.exists(local_file):\n", "        logging.info(f\"The file {local_file} already exists.\")\n", "        return\n", "\n", "    # Check if the request was successful\n", "    if response.status_code == 200:\n", "        # Open a file in binary mode to write the content\n", "        with open(local_file, \"wb\") as f:\n", "            f.write(response.content)\n", "        logging.info(f\"File {file_name} downloaded successfully.\")\n", "    else:\n", "        logging.info(\"Failed to download the file.\")\n", "\n", "\n", "with ThreadPoolExecutor(max_workers=20) as executor:\n", "    futures = [\n", "        executor.submit(download_wav_file, row[\"recordurl\"], row[\"sessionid\"], row['communication_time_in_seconds'])\n", "        for index, row in recordurl_df.iterrows()\n", "    ]\n", "    concurrent.futures.wait(futures)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_feishu_file_recognize_result_for_row(row_dict):\n", "    try:\n", "        row = row_dict\n", "        logging.info(f\"sessionid:{row['sessionid']}, recordurl:{row['recordurl']}\")\n", "        text = \"\"\n", "        text = file_recognize_feishu_api(\n", "            f\"{DATA_PATH}/{row['sessionid']}_{os.path.basename(row['recordurl'])}\"\n", "        )\n", "        logging.info(f\"{row['sessionid']} 的文本:{text}\")\n", "        row[\"feishu_file_recognize_result\"] = text\n", "        return text\n", "    except Exception as e:\n", "        row[\"feishu_file_recognize_result\"] = f\"错误:{e}\"\n", "        return \"ERROR\"\n", "\n", "\n", "row_dict_list = [row.to_dict() for _, row in recordurl_df.iterrows()]\n", "\n", "with ThreadPoolExecutor(max_workers=10) as executor:\n", "    # Submit tasks to the executor\n", "    futures = [\n", "        executor.submit(get_feishu_file_recognize_result_for_row, row_dict)\n", "        for row_dict in row_dict_list\n", "    ]\n", "    concurrent.futures.wait(futures)\n", "\n", "recordurl_with_text_df = pd.DataFrame(row_dict_list)\n", "\n", "has_no_text_result_df = recordurl_with_text_df[\n", "    recordurl_with_text_df[\"feishu_file_recognize_result\"].apply(len) == 0\n", "][[\"sessionid\", \"user\", \"staffname\", \"feishu_file_recognize_result\",\"recordurl\",\"communication_time_in_seconds\"]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 写入ODPS"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recordurl_with_text_df.to_csv(\n", "    f\"{DATA_PATH}/qiyu_records_with_feishu_recognize_result.csv\", index=False\n", ")\n", "# 先保存一份仅仅包含飞书语音识别的结果\n", "write_pandas_df_into_odps_overwrite(\n", "    recordurl_with_text_df.astype(str),\n", "    \"summerfarm_ds.crm_qiyu_call_feishu_result_raw_di\",\n", "    f\"ds={ds_to_run}\",\n", ")\n", "if len(has_no_text_result_df) > 0:\n", "    logging.warning(\"====>>>>>这些对话未能解析:\")\n", "    for index, row in has_no_text_result_df.iterrows():\n", "        logging.warning(f\"{index}对话解析失败:{row.to_dict()}\")\n", "    logging.warning(\">>>>>>=====对话解析失败打印完成\")\n", "\n", "days_15d = (datetime.now() - <PERSON><PERSON><PERSON>(15)).strftime(\"%Y%m%d\")\n", "df = get_odps_sql_result_as_df(\n", "    f\"\"\"select ds,count(1) cnt,count(case when feishu_file_recognize_result is null or length(feishu_file_recognize_result)<=1 then 1 end) as empty_result_cnt\n", "    from summerfarm_ds.crm_qiyu_call_feishu_result_raw_di \n", "    where ds>='{days_15d}' \n", "    group by ds \n", "    order by ds desc\"\"\"\n", ")\n", "logging.info(f\"成功了吧\\n{df.to_string()}\")\n", "\n", "logging.info(f\"成功了！\\n>>>>>>>>>>>>\\n失败的wav文件列表:{failed_wav_files}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}
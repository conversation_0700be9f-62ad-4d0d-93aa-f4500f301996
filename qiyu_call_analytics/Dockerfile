FROM python:3.9-slim as custom-python-base

RUN apt update && apt install ffmpeg -y

WORKDIR /app

ADD ./requirements_qiyu.txt ./requirements.txt

RUN pip install --index-url=https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host=mirrors.aliyun.com --default-timeout=30 -r requirements.txt

# Stage 2: Final deployment image
FROM custom-python-base

ADD 七鱼话务语音分析.py ./qiyu_run.py
ADD 七鱼话务语音-AI分析部分.py ./qiyu_run_ai_analytics.py

ENV ALIBABA_CLOUD_ACCESS_KEY_ID ''
ENV ALIBABA_CLOUD_ACCESS_KEY_SECRET ''
ENV FEISHU_XGPT_APP_SECRET ''
ENV AZURE_API_KEY ''
ENV AZURE_GPT4O_API_KEY ''
ENV TZ Asia/Shanghai

ENTRYPOINT ["/bin/sh", "-c", "printenv && python ./qiyu_run.py && python ./qiyu_run_ai_analytics.py"]
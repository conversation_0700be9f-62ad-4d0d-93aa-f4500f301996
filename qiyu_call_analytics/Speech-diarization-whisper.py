#!/usr/bin/env python
# coding: utf-8

# In[ ]:


# pip install torchvision torchaudio


# In[ ]:


import glob, os, subprocess
import datetime
import torch
import whisper
import pyannote.audio
from sklearn.cluster import AgglomerativeClustering
from pyannote.audio import Audio
from pyannote.core import Segment
import wave
import contextlib
import numpy as np
import pandas as pd

# Check if MPS is available and set the device accordingly
device = (
    torch.device("mps") if torch.backends.mps.is_available() else torch.device("cpu")
)

from pyannote.audio.pipelines.speaker_verification import PretrainedSpeakerEmbedding

embedding_model = PretrainedSpeakerEmbedding(
    "pyannote/embedding",
    use_auth_token="*************************************",
    device=device,
)

whisper_result = None


def extract_speakers(model: whisper.Whisper, path, num_speakers=2):
    global whisper_result
    """Do diarization with speaker names"""

    mono = "mono.wav"
    cmd = "ffmpeg -i {} -y -ac 1 mono.wav".format(path)
    subprocess.check_output(cmd, shell=True)
    result = model.transcribe(
        mono,
        verbose=True,
        temperature=0.1,
        decode_options={"language": "zh"},
        initial_prompt="这是销售员和客户之间的对话录音",
    )
    whisper_result = result
    print(f"whisper_result:{whisper_result}")
    segments = result["segments"]

    with contextlib.closing(wave.open(mono, "r")) as f:
        frames = f.getnframes()
        rate = f.getframerate()
        duration = frames / float(rate)

    audio = Audio()

    def segment_embedding(segment):
        start = segment["start"]
        # Whisper overshoots the end timestamp in the last segment
        end = min(duration, segment["end"])
        clip = Segment(start, end)
        waveform, sample_rate = audio.crop(mono, clip)
        return embedding_model(waveform[None])

    embeddings = np.zeros(shape=(len(segments), 512))
    for i, segment in enumerate(segments):
        embeddings[i] = segment_embedding(segment)
    embeddings = np.nan_to_num(embeddings)

    clustering = AgglomerativeClustering(num_speakers).fit(embeddings)
    labels = clustering.labels_
    for i in range(len(segments)):
        segments[i]["speaker"] = "SPEAKER " + str(labels[i] + 1)
    print(segments)
    return segments


def write_segments(segments, outfile):
    """write out segments to file"""

    def time(secs):
        return datetime.timedelta(seconds=round(secs))

    f = open(outfile, "w")
    for i, segment in enumerate(segments):
        if i == 0 or segments[i - 1]["speaker"] != segment["speaker"]:
            f.write(
                "\n" + segment["speaker"] + " " + str(time(segment["start"])) + "\n"
            )
        f.write(segment["text"][1:] + " ")
    f.close()


# In[ ]:


# model = whisper.load_model('/Users/<USER>/Documents/github/whisper-large-v3/pytorch_model.bin')
model = whisper.load_model("large-v3")
seg = extract_speakers(model, './data/20240701/9020988353_6940b16c474b25ed16090ad1b7070e70.wav')
write_segments(seg, 'transcript.txt')


# In[ ]:





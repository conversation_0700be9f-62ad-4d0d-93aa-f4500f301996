#!/bin/bash

# Function to run the Python script for a given date
run_python_script() {
    echo "running for date: $1"
    local date="$1"
    python3 七鱼话务语音分析.py --ds_to_run "$date"
}

# Start and end dates in the YYYYMMDD format
start_date=20240524
end_date=20240603

# Convert start and end dates to epoch time
start_epoch=$(date -j -f "%Y%m%d" "$start_date" "+%s")
end_epoch=$(date -j -f "%Y%m%d" "$end_date" "+%s")

# Loop through the date range
current_epoch=$start_epoch
while [ $current_epoch -le $end_epoch ]; do
    current_date=$(date -j -r "$current_epoch" "+%Y%m%d")
    run_python_script "$current_date"
    current_epoch=$((current_epoch + 86400)) # Add one day in seconds
done
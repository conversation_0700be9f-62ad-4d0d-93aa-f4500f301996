#!/usr/bin/env python
# coding: utf-8

# ## 文件头，准备各类API

# In[ ]:


import os
import shutil
import pandas as pd
from odps import ODPS, DataFrame
from datetime import datetime, timedelta
import argparse
import logging

ALIBABA_CLOUD_ACCESS_KEY_ID = os.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID")
ALIBABA_CLOUD_ACCESS_KEY_SECRET = os.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET")
AZURE_GPT4O_API_KEY = os.getenv("AZURE_GPT4O_API_KEY")
AZURE_API_KEY = os.getenv("AZURE_API_KEY")
CALL_AI_SERVICE = os.getenv("CALL_AI_SERVICE", "false")
STAFFNAME_TO_ANALYTIC = os.getenv("STAFFNAME_TO_ANALYTIC", "白津源,陈汉文")
STAFFNAME_TO_ANALYTIC=set(STAFFNAME_TO_ANALYTIC.split(","))

ds_to_run = datetime.now().strftime("%Y-%m-%d 00:00:00")
ds_to_run = datetime.strptime(ds_to_run, "%Y-%m-%d 00:00:00") - timedelta(days=1)
ds_to_run = ds_to_run.strftime("%Y%m%d")

parser = argparse.ArgumentParser()
parser.add_argument(
    "--ds_to_run",
    default=ds_to_run,
    help="指定跑哪一天的数据，格式: 20250520",
)
parser.add_argument(
    "--ACCESS_KEY_ID",
    default=ALIBABA_CLOUD_ACCESS_KEY_ID,
    help="ALIBABA_CLOUD_ACCESS_KEY_ID",
)
parser.add_argument(
    "--ACCESS_KEY_SECRET",
    default=ALIBABA_CLOUD_ACCESS_KEY_SECRET,
    help="ALIBABA_CLOUD_ACCESS_KEY_SECRET",
)
parser.add_argument(
    "--AZURE_API_KEY",
    default=AZURE_API_KEY,
    help="AZURE_API_KEY",
)
parser.add_argument(
    "--AZURE_GPT4O_API_KEY", default=AZURE_GPT4O_API_KEY, help="AZURE_GPT4O_API_KEY"
)

args, unknown = parser.parse_known_args()

ds_to_run = args.ds_to_run
# Configure the logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[
        logging.FileHandler(f"ds_{ds_to_run}_app.log"),
        logging.StreamHandler(),
    ],
)

logging.info(args)

DATA_PATH = f"./data/{ds_to_run}"

logging.info(f"ds_to_run:{ds_to_run}")

default_segment_duration = int(os.getenv("SEGMENT_DURATION", "45"))

odps = ODPS(
    args.ACCESS_KEY_ID,
    args.ACCESS_KEY_SECRET,
    project="summerfarm_ds_dev",
    endpoint="http://service.cn-hangzhou.maxcompute.aliyun.com/api",
)


def create_dir_if_not_exist(path):
    if os.path.exists(path):
        shutil.rmtree(path)
    os.makedirs(path)


create_dir_if_not_exist(DATA_PATH)


def get_odps_sql_result_as_df(sql) -> pd.DataFrame:
    logging.info(f"ODPS SQL:\n{sql}")
    instance = odps.execute_sql(
        sql,
        hints={"odps.sql.hive.compatible": True, "odps.sql.type.system.odps2": True},
    )
    instance.wait_for_success()
    pd_df = None
    with instance.open_reader(tunnel=True) as reader:
        # type of pd_df is pandas DataFrame
        pd_df = reader.to_pandas()

    if pd_df is not None:
        logging.info(f"columns:{pd_df.columns}")
        return pd_df
    return None


def add_new_column_to_table(table_name, column_name):
    if "summerfarm_ds." not in table_name:
        table_name = f"summerfarm_ds.{table_name}"
    sql = f"ALTER TABLE {table_name} ADD COLUMNS ({column_name} STRING);"
    instance = odps.execute_sql(sql)
    instance.wait_for_success()
    logging.info(f"添加新字段成功:{table_name}, {column_name}")


def ensure_all_df_columns_in_odps_table(df, table_name):
    if "summerfarm_ds." not in table_name:
        table_name = f"summerfarm_ds.{table_name}"
    if not odps.exist_table(table_name):
        logging.info(f"表不存在:{table_name}")
        return True
    table = odps.get_table(table_name)
    column_names = set([column.name for column in table.table_schema])
    column_names_out = ",\n".join(column_names)
    logging.info(f"DaraFrame字段合集:\n\n{column_names_out}")
    df_columns = df.columns.tolist()
    for df_col in df_columns:
        df_col = df_col.lower()
        if df_col not in column_names:
            logging.info(f"新字段:{df_col}, ODPS全部的字段:{column_names}")
            add_new_column_to_table(table_name, df_col)
    return True


def write_pandas_df_into_odps_overwrite(df, table_name, partition_spec):
    if df is None or len(df) <= 0:
        logging.info(f"数据DF为空, table:{table_name}")
        return False
    ensure_all_df_columns_in_odps_table(df, table_name)
    exception = None
    try:
        odps_df = DataFrame(df)
        odps_df.persist(
            table_name,
            partition=partition_spec,
            drop_partition=False,
            create_partition=True,
            overwrite=True,
            lifecycle=365,
        )
        logging.info(f"成功写入odps:{table_name}, partition_spec:{partition_spec}")
        return True
    except Exception as e:
        exception = e
        logging.info(f"写入ODPS不成功:{table_name}", e)
        raise exception


# ## 获取ODPS数据

# In[ ]:


recordurl_df=get_odps_sql_result_as_df(f"""
SELECT  *
FROM summerfarm_ds.crm_qiyu_call_feishu_result_raw_di
WHERE ds='{ds_to_run}';""")

logging.info(f"数据量大小:{len(recordurl_df)}")


# ## 请求azure进行语音分析，还原对话

# In[ ]:


# Import the base64 encoding library.
import base64
import httpx

proxy_object = {"http": "http://127.0.0.1:8001", "https": "http://127.0.0.1:8001"}


from openai import AzureOpenAI, OpenAI, Completion

# gets the API Key from environment variable AZURE_OPENAI_API_KEY
client = AzureOpenAI(
    api_version="2023-07-01-preview",
    azure_endpoint="https://xm-ai.openai.azure.com",
    api_key=args.AZURE_API_KEY,
)

client_ollama = OpenAI(
    base_url="http://localhost:11434/v1/",
    # required but ignored
    api_key="sk-ollama",
    http_client=httpx.Client(proxies={"http://": None, "https://": None}),
)

client_gpt4o = AzureOpenAI(
    api_version="2024-02-15-preview",
    azure_endpoint="https://xm-ai-us2.openai.azure.com",
    api_key=args.AZURE_GPT4O_API_KEY,
)

USING_OLLAMA = os.getenv("USING_OLLAMA", "false")


def call_azure_openai(content="", command="", retrying=1, is_gpt4o=False) -> str:
    if retrying < 0:
        return "超过了最大重试次数", False
    completion = None
    ## gpt3.5:  gpt-35-turbo-16k,
    ## got4o:   gpt-4o
    model = "gpt-35-turbo-16k"
    client_to_use = client
    if "true" == f"{USING_OLLAMA}":
        logging.warning(f"使用本地Ollama..., command:{command}")
        model = "phi3"
        client_to_use = client_ollama
    elif is_gpt4o:
        logging.info(f"using GPT-4o..., command:{command}")
        model = "gpt-4o"
        client_to_use = client_gpt4o
    try:
        messages = [
            # {
            #     "role": "system",
            #     "content": f"你是一个资深的销售主管。{command}",
            # },
            {
                "role": "user",
                "content": f"你是一个智能AI销售员。{command}.\n\n```{content}```",
            },
        ]
        logging.info(f"请求azure获取结果中...\n{messages}")
        completion = client_to_use.chat.completions.create(
            model=model, temperature=0.1, max_tokens=4095, messages=messages
        )
        response = completion.choices[0].message.content
        if (
            len(completion.choices) <= 0
            or f"{completion.choices[0].finish_reason}" == "content_filter"
        ):
            return f"azure过滤了本次请求:{completion.choices[0].to_dict()}", False
        if response is None:
            logging.info(f"azure API返回了异常:{completion.to_dict()}")
            return call_azure_openai(
                content=content,
                command=command,
                retrying=retrying - 1,
                is_gpt4o=is_gpt4o,
            )
        return response, True
    except Exception as e:
        logging.info(
            f"请求azure接口报错了:{e}\n content:{content}, completion:{completion}"
        )
        if retrying <= 0 or "Error code: 400" in f"{e}":
            return f"{e}", False
    logging.info(f"重试中...{retrying}, content:{content}")
    return call_azure_openai(
        content=content, command=command, retrying=retrying - 1, is_gpt4o=is_gpt4o
    )


commands = [
    {
        "name": "对话总结",
        "text": "以下文本是我公司销售员/客服和客户之间的通话录音。请你总结对话的内容，提炼出核心事件。请你用“事件一：”,“事件二：”这样的格式回答。",
    },
    {
        "name": "客情判断",
        "text": "以下文本是我公司销售员/客服和客户之间的通话录音。请你分析客户对我司的评价是正面的，还是负面的，并给出至少3点分析依据。\n请你用“评价是正面的/负面的。分析依据依据是：” 这样的格式回答。",
    },
    {
        "name": "销售达成情况",
        "text": """请你分析销售员与客户之间的对话内容，判断我们的销售员是否达成了销售目标。
如果客户问到了任意商品的具体价格且表示认可，也算是销售成功。
请用“销售成功。销售成功的商品是：ABC”。
或者“销售失败。原因是：”这样的格式回答。
如果客户提到了竞争对手，请你在回答的最后一部分加上“竞争对手：对手名字”""",
    },
]


dialog_recover_command = """以下文本是销售员和客户之间的对话。
请你还原对话过程，区分哪些内容是销售员说的、哪些是客户说的。
将销售员说的内容用"销售员："表示。将客户说的内容用"客户："表示。

**请注意:**
- 请你完全基于给出的内容进行还原，未出现的内容不要出现在你的回答里。
- 通常来说对话都是销售员发起的。
"""


def call_ai_api_to_get_insigns(feishu_text):
    result = {}
    dialog, is_ok = call_azure_openai(
        is_gpt4o=True, content=feishu_text, command=dialog_recover_command
    )
    result["对话还原"] = dialog
    if not is_ok:
        logging.info("失败:", dialog)
        return result, is_ok
    if dialog is None or len(dialog) <= 30 or "对话内容过于简短，无需还原" in dialog:
        result["error"] = (
            f"AI返回的对话内容太短了，疑似出错了:{dialog}, 飞书文本:{feishu_text}"
        )
        logging.error(f"{result}")
        return result, is_ok
    for command in commands:
        result[command["name"]], is_ok = call_azure_openai(dialog, command["text"])
        logging.info(f'{command["name"]}:{result[command["name"]]}')
    return result, is_ok


def get_gemini_result(row):
    feishu_file_recognize_result = row["feishu_file_recognize_result"]
    logging.info(f"feishu_file_recognize_result: {feishu_file_recognize_result}")
    if "504 Gateway Time-out" in f"{feishu_file_recognize_result}":
        logging.error(f"飞书文本异常：{feishu_file_recognize_result}")
        return {"对话还原": feishu_file_recognize_result}, False

    logging.info(
        f"sessionid:{row['sessionid']}, 飞书识别语音:{feishu_file_recognize_result}"
    )
    ai_response, is_ok = call_ai_api_to_get_insigns(feishu_file_recognize_result)
    logging.info(f"{row['sessionid']}, is_ok:{is_ok} API分析:\n{ai_response}")
    return ai_response, is_ok


# In[ ]:


all_records = []


def append_error_message_to_row_dict(row_dict={}, msg=""):
    row_dict["对话还原"] = msg
    row_dict["对话总结"] = msg
    row_dict["销售达成情况"] = msg
    row_dict["客情判断"] = msg


for index, row in recordurl_df.iterrows():
    if len(all_records) > 2:
        logging.warning("测试，已手动截止")
        break

    if row["staffname"] not in STAFFNAME_TO_ANALYTIC:
        logging.warning(f"这个用户暂不分析：{row['staffname']}")
        continue

    communication_time_in_seconds = int(row["communication_time_in_seconds"])
    feishu_file_recognize_result = row["feishu_file_recognize_result"]
    row_dict = row.to_dict()
    del row_dict["ds"]
    if "Max retries exceeded with url" in feishu_file_recognize_result:
        append_error_message_to_row_dict(
            row_dict, f"飞书识别异常:{feishu_file_recognize_result}"
        )
    elif len(feishu_file_recognize_result) <= 80:
        logging.warning(
            f'会话太短了:{communication_time_in_seconds}秒, 会话字数:{len(feishu_file_recognize_result)}, 会话:{row["feishu_file_recognize_result"]}'
        )
        too_short_conv = (
            f"对话时长太短，字数太少，共{len(feishu_file_recognize_result)}字"
        )
        append_error_message_to_row_dict(row_dict, too_short_conv)
    else:
        # 请求AI服务
        ai_response_of_row, is_ok = get_gemini_result(row_dict)
        if ai_response_of_row is None or not is_ok:
            append_error_message_to_row_dict(
                row_dict, f"AI服务返回异常:{ai_response_of_row}"
            )
        else:
            row_dict["对话还原"] = ai_response_of_row.get("对话还原")
            row_dict["对话总结"] = ai_response_of_row.get("对话总结")
            row_dict["销售达成情况"] = ai_response_of_row.get("销售达成情况")
            row_dict["客情判断"] = ai_response_of_row.get("客情判断")
    all_records.append(row_dict)

all_records_df = pd.DataFrame(all_records)
print(
    all_records_df[
        [
            "staffname",
            "feishu_file_recognize_result",
            "对话还原",
            "对话总结",
            "销售达成情况",
            "客情判断",
        ]
    ]
    .head(5)
    .to_string()
)


# ## 写入ODPS

# In[ ]:


if not "true" == USING_OLLAMA:
    logging.warning("只有非Ollama的结果才写入ODPS")
    write_pandas_df_into_odps_overwrite(
        all_records_df,
        "summerfarm_ds.crm_qiyu_call_ai_analytics_di",
        f"ds={ds_to_run}",
    )

    days_15d = (datetime.now() - timedelta(15)).strftime("%Y%m%d")
    df = get_odps_sql_result_as_df(
        f"select ds,count(1) cnt from summerfarm_ds.crm_qiyu_call_ai_analytics_di where ds>='{days_15d}' group by ds order by ds desc"
    )
    logging.info(f"成功了吧!\n{df.to_string()}")

logging.info(f"成功了！\n>>>>>>>>>>>>")


# ## 写到本地HTML文件

# In[ ]:


df_from_odps = get_odps_sql_result_as_df(
    f"select * from summerfarm_ds.crm_qiyu_call_ai_analytics_di where ds='{ds_to_run}' order by staffname;"
)
if len(df_from_odps) > 0:
    all_records_df = df_from_odps

css = """
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
<style type=\"text/css\">
table {
    color: #333;
    font-family: unset;
    font-size: 12px;
    line-height: 1.5;
    width: 1024px;
    border-collapse:
    collapse; 
    border-spacing: 0;
}

tr{
    border-bottom: 1px solid #C1C3D1;
}

.large-text-container{
    max-height: 200px; /* set the desired max height value */
    overflow: auto; /* add this to enable scrolling when content exceeds max height */
    max-width: 300px;
}

tr:nth-child(even) {
    background-color: #F8F8F8;
}

td, th {
    border: 1px solid transparent; /* No more visible border */
    height: 30px;
}

th {
    background-color: #DFDFDF; /* Darken header a bit */
    font-weight: bolder;
    min-width: 100px;
    text-align: center;
}

td {
    background-color: #FAFAFA;
    text-align: left;
}

ol li{
    text-align: left;
}
</style>
"""
from IPython.display import HTML


# Function to add HTML line breaks
def add_line_breaks(text):
    inner_html = f"{text}".replace("\n", "<br>")
    return f"<div class='large-text-container'>{inner_html}</div>"


# Apply the function to specified columns
all_records_df["飞书识别结果"] = all_records_df["feishu_file_recognize_result"]
all_records_df.sort_values(by="staffname", inplace=True)
columns_to_modify = ["飞书识别结果", "对话还原", "对话总结", "销售达成情况", "客情判断"]
for column in columns_to_modify:
    all_records_df[column] = all_records_df[column].apply(add_line_breaks)

html_content = css + all_records_df[
    [
        "direction",
        "staffname",
        "飞书识别结果",
        "对话还原",
        "对话总结",
        "销售达成情况",
        "客情判断",
    ]
].to_html(escape=False, index=False, classes="table dataframe")
html_content = f'<html><head><meta charset="UTF-8"><meta name="title" content="七鱼电话语音分析_{ds_to_run}"></head><body>{html_content}</body></html>'

# 保存HTML到本地文件：
with open(f"{DATA_PATH}/七鱼电话语音分析_{ds_to_run}.html", "w", encoding="utf-8") as f:
    f.write(html_content)

# display(HTML(html_content))


# In[ ]:


# import httpx

# client_ollama = OpenAI(
#     base_url="http://localhost:11434/v1/",
#     # required but ignored
#     api_key="sk-ollama",
#     http_client=httpx.Client(proxies={"http://": None, "https://": None}),
# )

# chat_completion = client_ollama.chat.completions.create(
#     messages=[
#         {
#             "role": "user",
#             "content": '你是一个智能AI助手。用尽可能简短的风格回复用户的请求. 说"这是一个测试"',
#         }
#     ],
#     model="phi3",
# )

# logging.info(chat_completion.choices[0].message.content)


# In[ ]:





{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import os\n", "import logging\n", "\n", "import sys\n", "import pandas as pd\n", "\n", "# Add the scripts directory to the sys.path\n", "sys.path.append(\"../scripts\")\n", "\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s - %(levelname)s - %(message)s\",\n", "    datefmt=\"%Y-%m-%d %H:%M:%S\",\n", ")\n", "\n", "started_at = datetime.now()\n", "ds_to_run = (started_at - timedelta(days=1)).strftime(\"%Y%m%d\")\n", "print(f\"ds_to_run:{ds_to_run}\")\n", "\n", "DATA_PATH = f\"./data/{ds_to_run}\"\n", "\n", "\n", "def create_dir_if_not_exist(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "\n", "create_dir_if_not_exist(DATA_PATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import mlx_whisper\n", "import numpy as np\n", "\n", "\n", "def convert_np_floats(data):\n", "    if isinstance(data, dict):\n", "        # Recursively process dictionaries\n", "        return {key: convert_np_floats(value) for key, value in data.items()}\n", "    elif isinstance(data, list):\n", "        # Recursively process lists\n", "        return [convert_np_floats(item) for item in data]\n", "    elif isinstance(data, np.float64):\n", "        # Convert np.float64 to Python float\n", "        return float(data)\n", "    else:\n", "        # Return the data as is for non-dict and non-float64 values\n", "        return data\n", "\n", "\n", "# Example usage\n", "\n", "\n", "def transcribe_with_local_whisper_model(\n", "    audio_file: str = \"/Users/<USER>/Documents/work@sf/codes/spiderman/qiyu_call_analytics/data/20241008/9290914718_81c14ca01e2441932987fc01f99c711b.wav\",\n", ") -> dict:\n", "    logging.info(f\"使用本地模型进行transcribe, audio_file:{audio_file}\")\n", "    result = mlx_whisper.transcribe(\n", "        audio_file,\n", "        path_or_hf_repo=\"/Users/<USER>/Documents/github/mlx-whisper\",\n", "        initial_prompt=\"鲜沐农场的销售员和客户的电话录音：\",\n", "    )\n", "    converted_data = convert_np_floats(result)\n", "    # print(converted_data)\n", "    return converted_data\n", "\n", "\n", "transcribe_with_local_whisper_model()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 切分wav文件为左右两个声道"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os, subprocess\n", "\n", "\n", "def split_wav_into_left_and_right_channels(file_path: str):\n", "    file_base_name = os.path.basename(file_path)\n", "    directory = os.path.dirname(file_path)\n", "    left_path = f\"{directory}/left_{file_base_name}\"\n", "    right_path = f\"{directory}/right_{file_base_name}\"\n", "    cmd = f'ffmpeg -loglevel error -y -i {file_path} -filter_complex \"[0:a]channelsplit=channel_layout=stereo[left][right]\" -map \"[left]\" {left_path} -map \"[right]\" {right_path}'\n", "    subprocess.check_output(cmd, shell=True)\n", "    return [left_path, right_path]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 这是Groq模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "USE_LOCAL_WHISPER = os.getenv(\"USE_LOCAL_WHISPER\", \"true\")\n", "USE_LOCAL_WHISPER = \"true\" == USE_LOCAL_WHISPER\n", "\n", "\n", "def call_whisper_with_wav_file(row: pd.Series):\n", "    logging.info(\n", "        f\"请求Groq进行语音识别, 文件:{row['recordurl']}, session:{row['sessionid']}, 通话时长:{row['communication_time_in_seconds']}s\"\n", "    )\n", "\n", "    file_name = os.path.basename(row[\"recordurl\"])\n", "    local_file = f\"{DATA_PATH}/{row['sessionid']}_{file_name}\"\n", "    if not os.path.exists(local_file):\n", "        logging.info(f\"The file {local_file} does not exists.\")\n", "        return f\"File not exists:{local_file}\"\n", "\n", "    if int(row[\"communication_time_in_seconds\"]) <= 10:\n", "        error_text = (\n", "            f\"通话时长只有{row['communication_time_in_seconds']}s, 无需进行语音识别\"\n", "        )\n", "        return {\n", "            \"text\": error_text,\n", "            \"segments\": error_text,\n", "        }\n", "\n", "    # 如果不使用本地模型，则使用GROQ远程模型\n", "    logging.info(f\"GROQ远程模型...local_file:{local_file}\")\n", "    return call_whisper_with_local_file(local_file)\n", "\n", "\n", "import requests, time\n", "\n", "GROQ_API_KEY = os.getenv(\"GROQ_API_KEY\")\n", "\n", "groq_api_url = \"https://api.groq.com/openai/v1/audio/transcriptions\"\n", "\n", "data = {\n", "    \"model\": \"whisper-large-v3\",\n", "    \"language\": \"zh\",\n", "    \"response_format\": \"verbose_json\",\n", "}\n", "\n", "\n", "def call_whisper_with_local_file(\n", "    filename=\"/Users/<USER>/Downloads/白津源_3d341fed6a4637ad614830dc9d1a6b97.wav\",\n", "    is_retrying=False,\n", "):\n", "    if USE_LOCAL_WHISPER:\n", "        # 使用本地模型进行转录\n", "        return transcribe_with_local_whisper_model(local_file)\n", "    with open(filename, \"rb\") as audio_file:\n", "        files = {\"file\": (audio_file.name, audio_file)}\n", "\n", "        headers = {\n", "            \"Authorization\": f\"Bearer {GROQ_API_KEY}\",\n", "        }\n", "\n", "        response = requests.post(\n", "            groq_api_url,\n", "            headers=headers,\n", "            data=data,\n", "            files=files,\n", "            proxies={\"http\": \"http://127.0.0.1:7890\", \"https\": \"http://127.0.0.1:7890\"},\n", "        )\n", "\n", "        if response.status_code == 200:\n", "            transcription = response.json()\n", "            print(f\"Transcription:{transcription}\")  # Example access\n", "            return transcription\n", "        else:\n", "            if not is_retrying and 400 <= response.status_code < 500:\n", "                print(f\"HTTP {response.status_code} error. Retrying in 45 seconds...\")\n", "                time.sleep(int(os.getenv(\"GROQ_API_RETRY_INTERVAL\", \"60\")))\n", "                return call_whisper_with_local_file(filename, is_retrying=True)\n", "            else:\n", "                print(\n", "                    f\"is_retrying:{is_retrying}, Error:\",\n", "                    response.status_code,\n", "                    response.text,\n", "                )\n", "                return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def merge_left_and_right_channel_segments(left_segments=[], right_segments=[]):\n", "    merge = [None] * (\n", "        len(left_segments) + len(right_segments)\n", "    )  # Initialize the merge list\n", "\n", "    l, r = 0, 0\n", "\n", "    for i in range(len(left_segments) + len(right_segments)):\n", "        seg_l = left_segments[l] if l < len(left_segments) else None\n", "        seg_r = right_segments[r] if r < len(right_segments) else None\n", "\n", "        if seg_r:\n", "            print(f\"seg_r: {seg_r['text']}\")\n", "            seg_r[\"speaker\"] = \"right\"\n", "        if seg_l:\n", "            print(f\"seg_l: {seg_l['text']}\")\n", "            seg_l[\"speaker\"] = \"left\"\n", "\n", "        if seg_l is None:\n", "            merge[i] = seg_r\n", "            r += 1\n", "            continue\n", "        if seg_r is None:\n", "            merge[i] = seg_l\n", "            l += 1\n", "            continue\n", "        if seg_l[\"end\"] < seg_r[\"end\"]:\n", "            merge[i] = seg_l\n", "            l += 1\n", "        else:\n", "            merge[i] = seg_r\n", "            r += 1\n", "    return merge"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from proxy_setup import get_odps_sql_result_as_df, logging\n", "\n", "staffname_list_to_analytics = os.getenv(\n", "    \"STAFF_NAME_LIST\", \"白津源,陈汉文,李梦婷,宋懿航\"\n", ")\n", "staffname_list = \"','\".join(staffname_list_to_analytics.split(\",\"))\n", "rows_count_to_analytics = os.getenv(\"ROWS_TO_ANALYTICS\", \"20000\")\n", "print(\n", "    f\"staffname_list:{staffname_list}, rows_count_to_analytics:{rows_count_to_analytics}\"\n", ")\n", "\n", "recordurl_df = get_odps_sql_result_as_df(\n", "    f\"\"\"\n", "SELECT  *,DATEDIFF(CAST(endtime AS TIMESTAMP),CAST(createtime AS TIMESTAMP),'ss') communication_time_in_seconds,'{ds_to_run}' as ds\n", "FROM    (\n", "            SELECT  JSON_TUPLE(body,\"eventtype\",\"sessionid\",\"direction\",\"createtime\",\"endtime\",\"connectionbeginetime\",\"connectionendtime\",\"from\",\"to\",\"user\",\"category\",\"staffid\",\"staffname\",\"status\",\"visittimes\",\"duration\",\"evaluation\",\"recordurl\",\"overflowFrom\",\"shuntGroupName\",\"ivrPath\",\"mobileArea\",\"waitDuration\",\"ringDuration\",\"sessionIdFrom\",\"firstEndDirection\") AS (\"eventtype\",\"sessionid\",\"direction\",\"createtime\",\"endtime\",\"connectionbeginetime\",\"connectionendtime\",\"from\",\"to\",\"user\",\"category\",\"staffid\",\"staffname\",\"status\",\"visittimes\",\"duration\",\"evaluation\",\"recordurl\",\"overflowFrom\",\"shuntGroupName\",\"ivrPath\",\"mobileArea\",\"waitDuration\",\"ringDuration\",\"sessionIdFrom\",\"firstEndDirection\")\n", "            FROM    summerfarm_tech.ods_qiyu_call_log_di\n", "            WHERE   ds = '{ds_to_run}'\n", "            AND     GET_JSON_OBJECT(body,'$.eventtype') = '5'\n", "        ) \n", "WHERE   recordurl LIKE 'https://hzxmkjyxgs7.%'\n", "and staffname in('{staffname_list}')\n", "limit {rows_count_to_analytics};\"\"\"\n", ")\n", "\n", "# Assuming recordurl_df is your DataFrame\n", "pd.set_option(\"display.max_colwidth\", None)\n", "\n", "length_of_source = len(recordurl_df)\n", "logging.info(f\"数据量大小:{length_of_source}\")\n", "if length_of_source <= 0:\n", "    raise Exception(\"summerfarm_tech.ods_qiyu_call_log_di 的数据为空\")\n", "\n", "recordurl_df.head(2)[\n", "    [\"sessionid\", \"recordurl\", \"staffname\", \"ds\", \"communication_time_in_seconds\"]\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 下载wav文件到本地，用于whisper模型识别"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 下载wav文件到本地，用于whisper模型识别\n", "\n", "from concurrent.futures import ThreadPoolExecutor\n", "import concurrent\n", "import requests\n", "import os\n", "from multiprocessing import cpu_count\n", "\n", "\n", "def download_wav_file(url, sessionid, communication_time_in_seconds=0):\n", "    logging.info(\n", "        f\"下载语音文件:{url}, session:{sessionid}, 通话时长:{communication_time_in_seconds}s\"\n", "    )\n", "    file_name = os.path.basename(url)\n", "    response = requests.get(url)\n", "    local_file = f\"{DATA_PATH}/{sessionid}_{file_name}\"\n", "    if os.path.exists(local_file):\n", "        logging.info(f\"The file {local_file} already exists.\")\n", "        return\n", "\n", "    # Check if the request was successful\n", "    if response.status_code == 200:\n", "        # Open a file in binary mode to write the content\n", "        with open(local_file, \"wb\") as f:\n", "            f.write(response.content)\n", "        logging.info(f\"File {file_name} downloaded successfully.\")\n", "    else:\n", "        logging.info(\"Failed to download the file.\")\n", "\n", "\n", "with ThreadPoolExecutor(max_workers=cpu_count() * 2) as executor:\n", "    futures = [\n", "        executor.submit(\n", "            download_wav_file,\n", "            row[\"recordurl\"],\n", "            row[\"sessionid\"],\n", "            row[\"communication_time_in_seconds\"],\n", "        )\n", "        for index, row in recordurl_df.iterrows()\n", "    ]\n", "    concurrent.futures.wait(futures)\n", "    \n", "recordurl_df[\"local_wav_file\"] = recordurl_df.apply(\n", "    lambda row: f\"{DATA_PATH}/{row['sessionid']}_{os.path.basename(row['recordurl'])}\",\n", "    axis=1,\n", ")\n", "\n", "recordurl_df[[\"local_wav_file\", \"sessionid\", \"recordurl\",'staffname']].head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def split_wav_and_call_whisper(local_wav_file: str) -> (str, dict):\n", "    channels = split_wav_into_left_and_right_channels(local_wav_file)\n", "    segment_result = []\n", "    for file in channels:\n", "        whisper_result = call_whisper_with_local_file(filename=file)\n", "        if whisper_result is not None:\n", "            segment_result.append(whisper_result.get(\"segments\", []))\n", "        else:\n", "            segment_result.append([])\n", "\n", "    if len(segment_result) != 2 or not all(segment_result):\n", "        return (f\"未能解析所有声道的数据: {local_wav_file}\", {})\n", "\n", "    merge_result = merge_left_and_right_channel_segments(\n", "        segment_result[0], segment_result[1]\n", "    )\n", "    merged_text = \"\\n\".join(\n", "        [\n", "            f\"[{segment['start']}~{segment['end']}] {segment['speaker']}: {segment['text']}\"\n", "            for segment in merge_result\n", "        ]\n", "    )\n", "    print(f\"local_wav_file: {local_wav_file}, \\nmerged_text{merged_text}\")\n", "    return (merged_text, merge_result)\n", "\n", "\n", "def transcribe_row(row: pd.Series):\n", "    try:\n", "        # communication_time_in_seconds = row[\"communication_time_in_seconds\"]\n", "        # if communication_time_in_seconds <= 30:\n", "        #     return pd.Series(\n", "        #         {\n", "        #             \"merged_text\": f\"沟通时间太短了，无需解析:{communication_time_in_seconds}s\",\n", "        #             \"merge_result\": None,\n", "        #         }\n", "        #     )\n", "\n", "        print(f\"transcribing file:{row['local_wav_file']} with whisper...\")\n", "        merged_text, merge_result = split_wav_and_call_whisper(row[\"local_wav_file\"])\n", "        return pd.Series({\"merged_text\": merged_text, \"merge_result\": merge_result})\n", "    except Exception as e:\n", "        return pd.Series({\"merged_text\": f\"ERROR:{e}\", \"merge_result\": None})\n", "\n", "\n", "recordurl_df.sort_values(by=\"communication_time_in_seconds\", ascending=False)\n", "recordurl_df[[\"merged_text\", \"merge_result\"]] = recordurl_df.apply(\n", "    transcribe_row, axis=1\n", ")\n", "recordurl_df.head(2)[[\"sessionid\", \"local_wav_file\", \"merged_text\", \"merge_result\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openai_client import call_azure_openai, parse_json_string\n", "\n", "\n", "def which_is_salesman(merged_text):\n", "    prompt = f\"\"\"以下是销售员和客户之间的对话，请你根据对话的内容，判断left和right分别是销售员还是客户。请以json格式输出，比如{{\"left\": \"[销售员或客户]\", \"right\": \"[销售员或客户]\"}}\n", "    \n", "{merged_text}\n", "    \"\"\"\n", "    response, success = call_azure_openai(text_input=prompt)\n", "    if not success:\n", "        return merged_text\n", "\n", "    salesman_json = parse_json_string(response)\n", "    for key, value in salesman_json.items():\n", "        merged_text = merged_text.replace(key, value)\n", "\n", "    return merged_text\n", "\n", "\n", "recordurl_df.sort_values(\n", "    by=[\"communication_time_in_seconds\"], ascending=False, inplace=True\n", ")\n", "recordurl_df[\"对话还原\"] = recordurl_df[\"merged_text\"].apply(which_is_salesman)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recordurl_df[\n", "    [\"sessionid\", \"recordurl\", \"对话还原\", \"staffname\", \"connectionbeginetime\", \"to\"]\n", "].to_csv(f\"./{DATA_PATH}/七鱼对话还原{ds_to_run}.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from proxy_setup import write_pandas_df_into_odps\n", "\n", "partition_spec = f\"ds={ds_to_run}\"\n", "table_name = f\"summerfarm_ds.crm_qiyu_call_analytics_whisper_di\"\n", "\n", "result = write_pandas_df_into_odps(recordurl_df.astype(str), table_name, partition_spec)\n", "\n", "print(f\"started_at:{started_at}, finished at:{datetime.now()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["whisper_di_df=get_odps_sql_result_as_df(\"select * from summerfarm_ds.crm_qiyu_call_analytics_whisper_di where ds=max_pt('summerfarm_ds.crm_qiyu_call_analytics_whisper_di')\")\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pip install torchvision torchaudio"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime,timedelta\n", "import os\n", "import shutil\n", "\n", "ds_to_run=(datetime.now() - timed<PERSON>ta(days=1)).strftime(\"%Y%m%d\")\n", "print(f\"ds_to_run:{ds_to_run}\")\n", "\n", "DATA_PATH = f\"./data/{ds_to_run}\"\n", "\n", "def create_dir_if_not_exist(path):\n", "    if os.path.exists(path):\n", "        shutil.rmtree(path)\n", "    os.makedirs(path)\n", "\n", "\n", "create_dir_if_not_exist(DATA_PATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob, os, subprocess\n", "import datetime\n", "import torch\n", "import whisper\n", "import pyannote.audio\n", "from sklearn.cluster import AgglomerativeClustering\n", "from pyannote.audio import Audio\n", "from pyannote.audio.pipelines.utils import PipelineModel\n", "from pyannote.core import Segment\n", "import wave\n", "import contextlib\n", "import numpy as np\n", "import pandas as pd\n", "import traceback\n", "\n", "# Check if MPS is available and set the device accordingly\n", "device = (\n", "    torch.device(\"mps\") if torch.backends.mps.is_available() else torch.device(\"cpu\")\n", ")\n", "\n", "print(f\"using device:{device}\")\n", "\n", "from pyannote.audio.pipelines.speaker_verification import PretrainedSpeakerEmbedding\n", "\n", "embedding_model = PretrainedSpeakerEmbedding(\n", "    # \"pyannote/embedding\",\n", "    \"/Users/<USER>/.cache/torch/pyannote/models--pyannote--embedding/blobs/4bcec986de13da7af7ac88736572692359950df63669989c4f78b294934c9089\",\n", "    # use_auth_token=\"*************************************\",\n", "    device=device,\n", ")\n", "\n", "whisper_result = None\n", "\n", "\n", "def extract_speakers(model: whisper.Whisper, path, num_speakers=2):\n", "    global whisper_result\n", "    \"\"\"Do diarization with speaker names\"\"\"\n", "\n", "    file_base_name = os.path.basename(path)\n", "\n", "    mono = f\"{DATA_PATH}/mono_{file_base_name}\"\n", "    cmd = f\"ffmpeg -loglevel error -i {path} -y -ac 1 {mono}\"\n", "    subprocess.check_output(cmd, shell=True)\n", "    result = model.transcribe(\n", "        mono,\n", "        verbose=True,\n", "        temperature=0.5,\n", "        language=\"zh\",\n", "        initial_prompt=\"这是鲜沐农场的销售员和客户之间的对话录音\",\n", "    )\n", "    whisper_result = result\n", "    print(f\"whisper_result:{whisper_result}\")\n", "    segments = result[\"segments\"]\n", "\n", "    with contextlib.closing(wave.open(mono, \"r\")) as f:\n", "        frames = f.getnframes()\n", "        rate = f.getframerate()\n", "        duration = frames / float(rate)\n", "    audio = Audio()\n", "\n", "    def segment_embedding(segment):\n", "        start = segment[\"start\"]\n", "        # Whis<PERSON> overshoots the end timestamp in the last segment\n", "        end = min(duration, segment[\"end\"])\n", "        clip = Segment(start, end)\n", "        waveform, sample_rate = audio.crop(mono, clip)\n", "        \n", "        # Debugging: Print the shape of the waveform\n", "        print(f\"Segment start: {start}, end: {end}\")\n", "        print(f\"Waveform shape before model input: {waveform.shape}\")\n", "        \n", "        # Check if the waveform is too small\n", "        if waveform.shape[1] < 7:  # Assuming the kernel size is 7\n", "            print(f\"Skipping segment: Waveform length {waveform.shape[1]} is smaller than the kernel size 7.\")\n", "            return None\n", "        \n", "        return embedding_model(waveform[None])\n", "\n", "    embeddings = np.zeros(shape=(len(segments), 512))\n", "    try:\n", "        for i, segment in enumerate(segments):\n", "            embedding = segment_embedding(segment)\n", "            if embedding is not None:  # Only assign if embedding is valid\n", "                embeddings[i] = embedding\n", "            else:\n", "                embeddings[i] = np.nan  # Mark invalid embeddings with NaN\n", "    except Exception as e:\n", "        print(f\"embeddings segment exception: {e}\")\n", "        traceback.print_exc()  # Print the full stack trace of the error\n", "        return segments\n", "\n", "    # Remove rows with NaN values\n", "    embeddings = embeddings[~np.isnan(embeddings).any(axis=1)]\n", "\n", "    # Ensure there are enough embeddings to cluster\n", "    if len(embeddings) > 0:\n", "        clustering = AgglomerativeClustering(num_speakers).fit(embeddings)\n", "        labels = clustering.labels_\n", "        valid_indices = 0\n", "        for i in range(len(segments)):\n", "            if not np.isnan(embeddings[i]).any():\n", "                segments[i][\"speaker\"] = \"SPEAKER \" + str(labels[valid_indices] + 1)\n", "                valid_indices += 1\n", "    else:\n", "        print(\"No valid embeddings to cluster.\")\n", "        return segments\n", "\n", "    print(segments)\n", "    return segments\n", "\n", "def write_segments(segments, outfile):\n", "    \"\"\"write out segments to file\"\"\"\n", "    transcribe_result = \"\"\n", "\n", "    # Check if the file already exists\n", "    if os.path.exists(outfile):\n", "        with open(outfile, \"r\") as f:\n", "            transcribe_result = f.read()\n", "        return transcribe_result\n", "\n", "    def time(secs):\n", "        return datetime.<PERSON><PERSON>ta(seconds=round(secs))\n", "\n", "    f = open(outfile, \"w\")\n", "    for i, segment in enumerate(segments):\n", "        transcribe_result = f'{transcribe_result}[{segment.get(\"speaker\")} --> {str(time(segment[\"start\"]))}] {segment[\"text\"]}\\n'\n", "    f.write(transcribe_result)\n", "    f.close()\n", "    return transcribe_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import shutil\n", "\n", "# Add the scripts directory to the sys.path\n", "sys.path.append('../scripts')\n", "\n", "# Import the function from proxy_setup.py\n", "from proxy_setup import get_odps_sql_result_as_df,logging\n", "\n", "recordurl_df = get_odps_sql_result_as_df(\n", "    f\"\"\"\n", "SELECT  *,DATEDIFF(CAST(endtime AS TIMESTAMP),CAST(createtime AS TIMESTAMP),'ss') communication_time_in_seconds\n", "FROM    (\n", "            SELECT  JSON_TUPLE(body,\"eventtype\",\"sessionid\",\"direction\",\"createtime\",\"endtime\",\"connectionbeginetime\",\"connectionendtime\",\"from\",\"to\",\"user\",\"category\",\"staffid\",\"staffname\",\"status\",\"visittimes\",\"duration\",\"evaluation\",\"recordurl\",\"overflowFrom\",\"shuntGroupName\",\"ivrPath\",\"mobileArea\",\"waitDuration\",\"ringDuration\",\"sessionIdFrom\",\"firstEndDirection\") AS (\"eventtype\",\"sessionid\",\"direction\",\"createtime\",\"endtime\",\"connectionbeginetime\",\"connectionendtime\",\"from\",\"to\",\"user\",\"category\",\"staffid\",\"staffname\",\"status\",\"visittimes\",\"duration\",\"evaluation\",\"recordurl\",\"overflowFrom\",\"shuntGroupName\",\"ivrPath\",\"mobileArea\",\"waitDuration\",\"ringDuration\",\"sessionIdFrom\",\"firstEndDirection\")\n", "            FROM    summerfarm_tech.ods_qiyu_call_log_di\n", "            WHERE   ds = '{ds_to_run}'\n", "            AND     GET_JSON_OBJECT(body,'$.eventtype') = '5'\n", "        ) \n", "WHERE   recordurl LIKE 'https://hzxmkjyxgs7.%';\"\"\"\n", ")\n", "\n", "# Assuming recordurl_df is your DataFrame\n", "pd.set_option('display.max_colwidth', None)\n", "\n", "length_of_source = len(recordurl_df)\n", "logging.info(f\"数据量大小:{length_of_source}\")\n", "if length_of_source <= 0:\n", "    raise Exception(\"summerfarm_tech.ods_qiyu_call_log_di 的数据为空\")\n", "\n", "recordurl_df.head(1)[['sessionid','recordurl']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "\n", "def download_wav_for_row(row: pd.Series):\n", "    return download_wav(row[\"recordurl\"], row[\"sessionid\"])\n", "\n", "\n", "def download_wav(recordurl, sessionid, save_dir=DATA_PATH):\n", "    # Create the save directory if it doesn't exist\n", "    if not os.path.exists(save_dir):\n", "        os.makedirs(save_dir)\n", "\n", "    # Extract the base name of the file from the URL\n", "    file_base_name = os.path.basename(recordurl)\n", "\n", "    # Create the full path to save the file\n", "    file_path = os.path.join(save_dir, f\"{sessionid}_{file_base_name}\")\n", "\n", "    if os.path.exists(file_path):\n", "        print(f\"File already exists at {file_path}\")\n", "        return file_path\n", "\n", "    # Download the file\n", "    response = requests.get(recordurl)\n", "    if response.status_code == 200:\n", "        with open(file_path, \"wb\") as file:\n", "            file.write(response.content)\n", "        print(f\"File downloaded and saved to {file_path}\")\n", "        return file_path\n", "    else:\n", "        print(f\"Failed to download file. HTTP status code: {response.status_code}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recordurl_df['local_wav_file']=recordurl_df.apply(download_wav_for_row, axis=1)\n", "recordurl_df.head(2)[['sessionid','local_wav_file']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = whisper.load_model(\"medium\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def transcribe_row(row: pd.Series):\n", "    try:\n", "        # Check if the file already exists\n", "        communication_time_in_seconds=row['communication_time_in_seconds']\n", "        if communication_time_in_seconds<=30:\n", "            return f\"沟通时间太短了，无需解析:{communication_time_in_seconds}s\"\n", "        outfile = f\"{DATA_PATH}/{row['sessionid']}_transcript.txt\"\n", "        if os.path.exists(outfile):\n", "            with open(outfile, \"r\") as f:\n", "                transcribe_result = f.read()\n", "            return transcribe_result\n", "        print(f\"transcribing file:{row['local_wav_file']} with whisper...\")\n", "        seg = extract_speakers(model, row[\"local_wav_file\"])\n", "        return write_segments(seg, outfile)\n", "    except Exception as e:\n", "        return f\"ERROR:{e}\"\n", "\n", "\n", "recordurl_df[\"whisper_transcript_result\"] = recordurl_df.apply(transcribe_row, axis=1)\n", "recordurl_df.head(2)[[\"sessionid\", \"local_wav_file\", \"whisper_transcript_result\"]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["recordurl_df[recordurl_df['communication_time_in_seconds']>30][[\"sessionid\", \"local_wav_file\", \"whisper_transcript_result\",'communication_time_in_seconds','staffname']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from proxy_setup import write_pandas_df_into_odps\n", "\n", "partition_spec = f\"ds={ds_to_run}\"\n", "table_name = f\"summerfarm_ds.crm_qiyu_call_analytics_whisper_di\"\n", "\n", "result = write_pandas_df_into_odps(recordurl_df.astype(str), table_name, partition_spec)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 以下代码用来区分对话的说话者是谁"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [], "source": ["# instantiate the pipeline\n", "from pyannote.audio import Pipeline\n", "import torchaudio\n", "\n", "pipeline = Pipeline.from_pretrained(\n", "    \"pyannote/speaker-diarization-3.1\",\n", "    use_auth_token=\"*************************************\",\n", ")"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pipeline.to(torch.device(\"mps\"))\n", "\n", "from pyannote.audio.pipelines.utils.hook import ProgressHook\n", "with <PERSON><PERSON><PERSON>() as hook:\n", "    diarization = pipeline(\"./data/20240707/9037286642_5992abdbb64f8e7df772fd35e0d6dea3.wav\", hook=hook, num_speakers=2)\n", "    # dump the diarization output to disk using RTTM format\n", "    with open(\"audio.rttm\", \"w\") as rttm:\n", "        diarization.write_rttm(rttm)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}
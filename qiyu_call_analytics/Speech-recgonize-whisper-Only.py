#!/usr/bin/env python
# coding: utf-8

# In[ ]:


from datetime import datetime,timedelta
import os
import shutil

started_at=datetime.now()
ds_to_run=(started_at - timedelta(days=1)).strftime("%Y%m%d")
print(f"ds_to_run:{ds_to_run}")

DATA_PATH = f"./data/{ds_to_run}"

def create_dir_if_not_exist(path):
    if os.path.exists(path):
        shutil.rmtree(path)
    os.makedirs(path)


create_dir_if_not_exist(DATA_PATH)


# In[ ]:


import os, subprocess
import torch
import whisper

# Check if MPS is available and set the device accordingly
device = (
    torch.device("mps") if torch.backends.mps.is_available() else torch.device("cpu")
)

print(f"using device:{device}")

model = whisper.load_model("large-v3")

def do_speech_recgonize_whisper(model: whisper.Whisper = model, path=""):
    file_base_name = os.path.basename(path)

    mono = f"{DATA_PATH}/mono_{file_base_name}"
    cmd = f"ffmpeg -loglevel error -i {path} -y -ac 1 {mono}"
    subprocess.check_output(cmd, shell=True)
    result = model.transcribe(
        mono,
        verbose=True,
        temperature=0.5,
        language="zh",
        initial_prompt="鲜沐农场的销售员和客户之间的对话",
    )
    print(f"whisper_result:{result}")
    return result


# In[ ]:


import sys
import os
import pandas as pd

# Add the scripts directory to the sys.path
sys.path.append("../scripts")

# Import the function from proxy_setup.py
from proxy_setup import get_odps_sql_result_as_df, logging

staffname_list_to_analytics = os.getenv("STAFF_NAME_LIST", "白津源,陈汉文")
staffname_list = "','".join(staffname_list_to_analytics.split(","))
rows_count_to_analytics=os.getenv("ROWS_TO_ANALYTICS","20000")
print(f"staffname_list:{staffname_list}, rows_count_to_analytics:{rows_count_to_analytics}")

recordurl_df = get_odps_sql_result_as_df(
    f"""
SELECT  *,DATEDIFF(CAST(endtime AS TIMESTAMP),CAST(createtime AS TIMESTAMP),'ss') communication_time_in_seconds,'{ds_to_run}' as ds
FROM    (
            SELECT  JSON_TUPLE(body,"eventtype","sessionid","direction","createtime","endtime","connectionbeginetime","connectionendtime","from","to","user","category","staffid","staffname","status","visittimes","duration","evaluation","recordurl","overflowFrom","shuntGroupName","ivrPath","mobileArea","waitDuration","ringDuration","sessionIdFrom","firstEndDirection") AS ("eventtype","sessionid","direction","createtime","endtime","connectionbeginetime","connectionendtime","from","to","user","category","staffid","staffname","status","visittimes","duration","evaluation","recordurl","overflowFrom","shuntGroupName","ivrPath","mobileArea","waitDuration","ringDuration","sessionIdFrom","firstEndDirection")
            FROM    summerfarm_tech.ods_qiyu_call_log_di
            WHERE   ds = '{ds_to_run}'
            AND     GET_JSON_OBJECT(body,'$.eventtype') = '5'
        ) 
WHERE   recordurl LIKE 'https://hzxmkjyxgs7.%'
and staffname in('{staffname_list}')
limit {rows_count_to_analytics};"""
)

# Assuming recordurl_df is your DataFrame
pd.set_option("display.max_colwidth", None)

length_of_source = len(recordurl_df)
logging.info(f"数据量大小:{length_of_source}")
if length_of_source <= 0:
    raise Exception("summerfarm_tech.ods_qiyu_call_log_di 的数据为空")

recordurl_df.head(1)[
    ["sessionid", "recordurl", "staffname", "ds", "communication_time_in_seconds"]
]


# In[ ]:


import requests


def download_wav_for_row(row: pd.Series):
    return download_wav(row["recordurl"], row["sessionid"])


def download_wav(recordurl, sessionid, save_dir=DATA_PATH):
    # Create the save directory if it doesn't exist
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Extract the base name of the file from the URL
    file_base_name = os.path.basename(recordurl)

    # Create the full path to save the file
    file_path = os.path.join(save_dir, f"{sessionid}_{file_base_name}")

    if os.path.exists(file_path):
        print(f"File already exists at {file_path}")
        return file_path

    # Download the file
    response = requests.get(recordurl)
    if response.status_code == 200:
        with open(file_path, "wb") as file:
            file.write(response.content)
        print(f"File downloaded and saved to {file_path}")
        return file_path
    else:
        print(f"Failed to download file. HTTP status code: {response.status_code}")
        return None


# In[ ]:


# download wav file for each record.

recordurl_df['local_wav_file']=recordurl_df.apply(download_wav_for_row, axis=1)
recordurl_df.head(2)[['sessionid','local_wav_file']]


# In[ ]:


def transcribe_row(row: pd.Series):
    try:
        # Check if the file already exists
        communication_time_in_seconds = row["communication_time_in_seconds"]
        if communication_time_in_seconds <= 30:
            return f"沟通时间太短了，无需解析:{communication_time_in_seconds}s"
        outfile = f"{DATA_PATH}/{row['sessionid']}_transcript.txt"
        if os.path.exists(outfile):
            with open(outfile, "r") as f:
                transcribe_result = f.read()
            return transcribe_result
        print(f"transcribing file:{row['local_wav_file']} with whisper...")
        return do_speech_recgonize_whisper(path=row["local_wav_file"])
    except Exception as e:
        return f"ERROR:{e}"


recordurl_df["whisper_transcript_result"] = recordurl_df.apply(transcribe_row, axis=1)
recordurl_df.head(2)[["sessionid", "local_wav_file", "whisper_transcript_result"]]


# In[ ]:


from datetime import datetime
from proxy_setup import write_pandas_df_into_odps

partition_spec = f"ds={ds_to_run}"
table_name = f"summerfarm_ds.crm_qiyu_call_analytics_whisper_di"

result = write_pandas_df_into_odps(recordurl_df.astype(str), table_name, partition_spec)

print(f"started_at:{started_at}, finished at:{datetime.now()}")


# In[ ]:





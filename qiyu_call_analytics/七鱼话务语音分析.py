#!/usr/bin/env python
# coding: utf-8

# ## 文件头，准备各类API

# In[ ]:


import os
import json
from typing import List
import shutil

import pandas as pd
from odps import ODPS, DataFrame
from datetime import datetime, timedelta
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
import concurrent.futures
import argparse
import logging

ALIBABA_CLOUD_ACCESS_KEY_ID = os.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID")
ALIBABA_CLOUD_ACCESS_KEY_SECRET = os.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET")
FEISHU_XGPT_APP_SECRET = os.getenv("FEISHU_XGPT_APP_SECRET")
AZURE_GPT4O_API_KEY = os.getenv("AZURE_GPT4O_API_KEY")
AZURE_API_KEY = os.getenv("AZURE_API_KEY")
CALL_AI_SERVICE = os.getenv("CALL_AI_SERVICE", "false")

ds_to_run = datetime.now().strftime("%Y-%m-%d 00:00:00")
ds_to_run = datetime.strptime(ds_to_run, "%Y-%m-%d 00:00:00") - timedelta(days=1)
ds_to_run = ds_to_run.strftime("%Y%m%d")

parser = argparse.ArgumentParser()
parser.add_argument(
    "--ds_to_run",
    default=ds_to_run,
    help="指定跑哪一天的数据，格式: 20250520",
)
parser.add_argument(
    "--ACCESS_KEY_ID",
    default=ALIBABA_CLOUD_ACCESS_KEY_ID,
    help="ALIBABA_CLOUD_ACCESS_KEY_ID",
)
parser.add_argument(
    "--ACCESS_KEY_SECRET",
    default=ALIBABA_CLOUD_ACCESS_KEY_SECRET,
    help="ALIBABA_CLOUD_ACCESS_KEY_SECRET",
)
parser.add_argument(
    "--FEISHU_XGPT_APP_SECRET",
    default=FEISHU_XGPT_APP_SECRET,
    help="飞书的FEISHU_XGPT_APP_SECRET",
)
parser.add_argument(
    "--AZURE_API_KEY",
    default=AZURE_API_KEY,
    help="AZURE_API_KEY",
)
parser.add_argument(
    "--AZURE_GPT4O_API_KEY", default=AZURE_GPT4O_API_KEY, help="AZURE_GPT4O_API_KEY"
)

args, unknown = parser.parse_known_args()

ds_to_run = args.ds_to_run
# Configure the logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[
        logging.FileHandler(f"ds_{ds_to_run}_app.log"),
        logging.StreamHandler(),
    ],
)

logging.info(args)

DATA_PATH = f"./data/{ds_to_run}"

logging.info(f"ds_to_run:{ds_to_run}")

default_segment_duration = int(os.getenv("SEGMENT_DURATION", "120"))

odps = ODPS(
    args.ACCESS_KEY_ID,
    args.ACCESS_KEY_SECRET,
    project="summerfarm_ds_dev",
    endpoint="http://service.cn-hangzhou.maxcompute.aliyun.com/api",
)


def create_dir_if_not_exist(path):
    if os.path.exists(path):
        shutil.rmtree(path)
    os.makedirs(path)


create_dir_if_not_exist(DATA_PATH)


def get_odps_sql_result_as_df(sql) -> pd.DataFrame:
    logging.info(f"ODPS SQL:\n{sql}")
    instance = odps.execute_sql(
        sql,
        hints={"odps.sql.hive.compatible": True, "odps.sql.type.system.odps2": True},
    )
    instance.wait_for_success()
    pd_df = None
    with instance.open_reader(tunnel=True) as reader:
        # type of pd_df is pandas DataFrame
        pd_df = reader.to_pandas()

    if pd_df is not None:
        logging.info(f"columns:{pd_df.columns}")
        return pd_df
    return None


def add_new_column_to_table(table_name, column_name):
    if "summerfarm_ds." not in table_name:
        table_name = f"summerfarm_ds.{table_name}"
    sql = f"ALTER TABLE {table_name} ADD COLUMNS ({column_name} STRING);"
    instance = odps.execute_sql(sql)
    instance.wait_for_success()
    logging.info(f"添加新字段成功:{table_name}, {column_name}")


def ensure_all_df_columns_in_odps_table(df, table_name):
    if "summerfarm_ds." not in table_name:
        table_name = f"summerfarm_ds.{table_name}"
    if not odps.exist_table(table_name):
        logging.info(f"表不存在:{table_name}")
        return True
    table = odps.get_table(table_name)
    column_names = set([column.name for column in table.table_schema])
    column_names_out = ",\n".join(column_names)
    logging.info(f"DaraFrame字段合集:\n\n{column_names_out}")
    df_columns = df.columns.tolist()
    for df_col in df_columns:
        df_col = df_col.lower()
        if df_col not in column_names:
            logging.info(f"新字段:{df_col}, ODPS全部的字段:{column_names}")
            add_new_column_to_table(table_name, df_col)
    return True


def write_pandas_df_into_odps_overwrite(df, table_name, partition_spec):
    if df is None or len(df) <= 0:
        logging.info(f"数据DF为空, table:{table_name}")
        return False
    ensure_all_df_columns_in_odps_table(df, table_name)
    exception = None
    try:
        odps_df = DataFrame(df)
        odps_df.persist(
            table_name,
            partition=partition_spec,
            drop_partition=False,
            create_partition=True,
            overwrite=True,
            lifecycle=365,
        )
        logging.info(f"成功写入odps:{table_name}, partition_spec:{partition_spec}")
        return True
    except Exception as e:
        exception = e
        logging.info(f"写入ODPS不成功:{table_name}", e)
        raise exception


# ## 飞书接口认证

# In[ ]:


import base64
import wave
import requests
import re

def get_feishu_token():
    url = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
    token = requests.post(
        url=url,
        json={"app_id": "cli_a450bff26fbbd00d", "app_secret": args.FEISHU_XGPT_APP_SECRET},
    ).json()
    logging.info(token)
    return token


feishu_token = get_feishu_token()
feishu_headers_with_token = {
    "Authorization": f'Bearer {feishu_token["tenant_access_token"]}',
    "Content-Type": "application/json",
}

logging.info(feishu_headers_with_token)


# ## wav文件下载和识别接口

# In[ ]:


import ffmpeg
import os
import base64
import requests
import logging
import time
import string
import random

text_map = {}
failed_wav_files = {}


def generate_random_string(length=16):
    letters_and_digits = string.ascii_letters + string.digits
    return "".join(random.choice(letters_and_digits) for _ in range(length))


random_string = generate_random_string()
print(random_string)


def dump_failed_json_to_file(json_object, file_id):
    with open(f"{DATA_PATH}/failed_{file_id}.json", "w") as f:
        json.dump(json_object, f, indent=4)


def get_file_id_from_base64_content(s, num=16):
    # return "".join(re.findall(r"[a-zA-Z0-9]", s))[:num]
    return generate_random_string(num)


def get_dir_from_path(file_path):
    return os.path.dirname(file_path)


def split_wav_file(input_file, segment_duration=default_segment_duration):
    output_files = []
    basename, _ = os.path.splitext(os.path.basename(input_file))

    try:
        output_dir = get_dir_from_path(input_file)
        # Open the input file
        stream = ffmpeg.input(input_file)

        # Set the output format and codec
        stream = ffmpeg.output(
            stream,
            os.path.join(output_dir, f"{basename}_segment_%03d.wav"),
            codec="copy",
            f="segment",
            segment_time=segment_duration,
        )

        # Run the FFmpeg command
        ffmpeg.run(stream, overwrite_output=True, quiet=True)

        # Get the list of output files
        output_files = [
            os.path.join(output_dir, f)
            for f in os.listdir(output_dir)
            if f.startswith(f"{basename}_segment_") and f.endswith(".wav")
        ]
    except Exception as e:
        logging.error(f"切分wav文件:{input_file}出错了:{e}")

    output_files.sort()
    return output_files


def get_base64encoded_content_of_wav(wav_file):
    logging.info(f"wav_file:{wav_file}")

    output = ""

    # Create the input stream
    input_stream = ffmpeg.input(wav_file)

    # Configure the output stream
    output_stream = input_stream.output(
        "pipe:", acodec="pcm_s16le", format="s16le", ac=1, ar=16000
    )

    # Run the FFmpeg command and capture the output
    output, _ = ffmpeg.run(output_stream, capture_stdout=True, quiet=True)

    # The output is a bytes object containing the raw PCM data
    pcm_data = output
    return base64.b64encode(pcm_data).decode("utf-8")


def recognize_feishu_api_base64_content(base64_pcm_data, wav_file, retry=True):
    file_id = get_file_id_from_base64_content(base64_pcm_data)
    feishu_json = {
        "config": {
            "engine_type": "16k_auto",
            "file_id": file_id,
            "format": "pcm",
        },
        "speech": {"speech": f"{base64_pcm_data}"},
    }

    text = ""
    try:
        text = requests.post(
            "https://open.feishu.cn/open-apis/speech_to_text/v1/speech/file_recognize",
            json=feishu_json,
            headers=feishu_headers_with_token,
            timeout=(20, 180),
        ).text
        logging.info(f"wav_file:{wav_file}, 飞书response:{text}")
        text = json.loads(text)
        return text["data"]["recognition_text"]
    except Exception as e:
        logging.error(
            f"调用飞书接口错误wav_file:{wav_file}, 接口返回:{text}, 异常信息:{e}"
        )
        if retry:
            logging.warning("20s后重试1次:")
            time.sleep(20)
            return recognize_feishu_api_base64_content(
                base64_pcm_data=base64_pcm_data, wav_file=wav_file, retry=False
            )
        else:
            global failed_wav_files
            dump_failed_json_to_file(feishu_json, file_id)
            failed_wav_files["wav_file"] = file_id
            logging.error(f"不再重试:{retry}")
            return f"调用飞书异常:{wav_file}, {e}"


def is_feishu504_error(api_text):
    return "504 Gateway Time-out" in api_text


def file_recognize_feishu_api(wav_file):
    # 先切分成2分钟一段：
    sub_files = split_wav_file(wav_file)

    if sub_files is None or len(sub_files) < 0:
        logging.info(f"切分失败：{wav_file}")
        return
    if len(sub_files) > 1:
        logging.info(f"切分成了多个小文件：{','.join(sub_files)}")
    all_text = []
    for file in sub_files:
        base64_pcm_data = get_base64encoded_content_of_wav(file)
        logging.info(f"wav_file:{file}, base64_pcm_data: {base64_pcm_data[-50:]}")
        all_text.append(
            recognize_feishu_api_base64_content(
                base64_pcm_data=base64_pcm_data, wav_file=file
            )
        )
    return "".join(all_text)


# ## 获取ODPS数据

# In[ ]:


import sys

recordurl_df = get_odps_sql_result_as_df(
    f"""
SELECT  *,DATEDIFF(CAST(endtime AS TIMESTAMP),CAST(createtime AS TIMESTAMP),'ss') communication_time_in_seconds
FROM    (
            SELECT  JSON_TUPLE(body,"eventtype","sessionid","direction","createtime","endtime","connectionbeginetime","connectionendtime","from","to","user","category","staffid","staffname","status","visittimes","duration","evaluation","recordurl","overflowFrom","shuntGroupName","ivrPath","mobileArea","waitDuration","ringDuration","sessionIdFrom","firstEndDirection") AS ("eventtype","sessionid","direction","createtime","endtime","connectionbeginetime","connectionendtime","from","to","user","category","staffid","staffname","status","visittimes","duration","evaluation","recordurl","overflowFrom","shuntGroupName","ivrPath","mobileArea","waitDuration","ringDuration","sessionIdFrom","firstEndDirection")
            FROM    summerfarm_tech.ods_qiyu_call_log_di
            WHERE   ds = '{ds_to_run}'
            AND     GET_JSON_OBJECT(body,'$.eventtype') = '5'
        ) 
WHERE   recordurl LIKE 'https://hzxmkjyxgs7.%';"""
)

length_of_source = len(recordurl_df)
logging.info(f"数据量大小:{length_of_source}")
if length_of_source <= 0:
    raise Exception("summerfarm_tech.ods_qiyu_call_log_di 的数据为空")

overwrite = os.getenv("QIYU_CALL_OVERWRITE_DS", "false")
overwrite = "true" == overwrite
if not overwrite:
    # 如果不是overwrite，则检查一下是否已经跑过了。跑过了则无需重试
    is_data_exists_df = get_odps_sql_result_as_df(
        f"""
    select ds,count(1) cnt,count(case when feishu_file_recognize_result is null or length(feishu_file_recognize_result)<=1 then 1 end) as empty_result_cnt
    from summerfarm_ds.crm_qiyu_call_feishu_result_raw_di 
    where ds = '{ds_to_run}' group by ds
    """
    )
    existing_data_length = len(is_data_exists_df)
    if len(is_data_exists_df) > 0:
        first_cnt = is_data_exists_df.iloc[0]["cnt"]
        if first_cnt > length_of_source / 2:
            logging.info(f"已经跑过啦，结束吧大兄弟, is_data_exists_df:\n{is_data_exists_df.to_string()}")
            sys.exit()
        else:
            print(
                f"线上的数据大小太小啦，重新跑吧, first_cnt:{first_cnt}, length_of_source:{length_of_source}"
            )
    else:
        logging.info(f"开始运行ds:{ds_to_run}")
else:
    logging.warning("您选择了覆盖已有的数据...")


# In[ ]:


import requests
import os


def download_wav_file(url, sessionid, communication_time_in_seconds=0):
    logging.info(f"下载语音文件:{url}, session:{sessionid}, 通话时长:{communication_time_in_seconds}s")
    file_name = os.path.basename(url)
    response = requests.get(url)
    local_file = f"{DATA_PATH}/{sessionid}_{file_name}"
    if os.path.exists(local_file):
        logging.info(f"The file {local_file} already exists.")
        return

    # Check if the request was successful
    if response.status_code == 200:
        # Open a file in binary mode to write the content
        with open(local_file, "wb") as f:
            f.write(response.content)
        logging.info(f"File {file_name} downloaded successfully.")
    else:
        logging.info("Failed to download the file.")


with ThreadPoolExecutor(max_workers=20) as executor:
    futures = [
        executor.submit(download_wav_file, row["recordurl"], row["sessionid"], row['communication_time_in_seconds'])
        for index, row in recordurl_df.iterrows()
    ]
    concurrent.futures.wait(futures)


# In[ ]:


def get_feishu_file_recognize_result_for_row(row_dict):
    try:
        row = row_dict
        logging.info(f"sessionid:{row['sessionid']}, recordurl:{row['recordurl']}")
        text = ""
        text = file_recognize_feishu_api(
            f"{DATA_PATH}/{row['sessionid']}_{os.path.basename(row['recordurl'])}"
        )
        logging.info(f"{row['sessionid']} 的文本:{text}")
        row["feishu_file_recognize_result"] = text
        return text
    except Exception as e:
        row["feishu_file_recognize_result"] = f"错误:{e}"
        return "ERROR"


row_dict_list = [row.to_dict() for _, row in recordurl_df.iterrows()]

with ThreadPoolExecutor(max_workers=10) as executor:
    # Submit tasks to the executor
    futures = [
        executor.submit(get_feishu_file_recognize_result_for_row, row_dict)
        for row_dict in row_dict_list
    ]
    concurrent.futures.wait(futures)

recordurl_with_text_df = pd.DataFrame(row_dict_list)

has_no_text_result_df = recordurl_with_text_df[
    recordurl_with_text_df["feishu_file_recognize_result"].apply(len) == 0
][["sessionid", "user", "staffname", "feishu_file_recognize_result","recordurl","communication_time_in_seconds"]]


# ## 写入ODPS

# In[ ]:


recordurl_with_text_df.to_csv(
    f"{DATA_PATH}/qiyu_records_with_feishu_recognize_result.csv", index=False
)
# 先保存一份仅仅包含飞书语音识别的结果
write_pandas_df_into_odps_overwrite(
    recordurl_with_text_df.astype(str),
    "summerfarm_ds.crm_qiyu_call_feishu_result_raw_di",
    f"ds={ds_to_run}",
)
if len(has_no_text_result_df) > 0:
    logging.warning("====>>>>>这些对话未能解析:")
    for index, row in has_no_text_result_df.iterrows():
        logging.warning(f"{index}对话解析失败:{row.to_dict()}")
    logging.warning(">>>>>>=====对话解析失败打印完成")

days_15d = (datetime.now() - timedelta(15)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,count(1) cnt,count(case when feishu_file_recognize_result is null or length(feishu_file_recognize_result)<=1 then 1 end) as empty_result_cnt
    from summerfarm_ds.crm_qiyu_call_feishu_result_raw_di 
    where ds>='{days_15d}' 
    group by ds 
    order by ds desc"""
)
logging.info(f"成功了吧\n{df.to_string()}")

logging.info(f"成功了！\n>>>>>>>>>>>>\n失败的wav文件列表:{failed_wav_files}")


# In[ ]:





# In[ ]:





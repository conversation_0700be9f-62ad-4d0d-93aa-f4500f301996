{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "*************, headers:{'uniacid': '2595', 'appType': 'mini'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "THREAD_CNT = int(os.environ.get('THREAD_CNT', 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "hints={'odps.sql.hive.compatible':True,'odps.sql.type.system.odps2':True}\n", "def get_odps_sql_result_as_df(sql):\n", "    instance=odps.execute_sql(sql, hints=hints)\n", "    instance.wait_for_success()\n", "    pd_df=None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={'uniacid':'2595','appType':'mini',}\n", "brand_name='汉口北市场店'\n", "competitor_name_en='hanko<PERSON><PERSON>'\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'errcode': 268697609,\n", " 'graphid': 33578945,\n", " 'hint': '4tluyu',\n", " 'msg': '效验登录态失败',\n", " 'time': '2024-02-22 11:38:23'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["body={\n", "    # \"authtype\": \"MCHAPP\",\n", "    \"appid\": \"wxbeb0fbb09d5388f9\",\n", "    \"v\": \"0.48.9\",\n", "    # \"shelf_info\": \"idc_SLF_jW5DZ3_muqY8UjkbhDhIXg--\",\n", "    \"page_size\": 50,\n", "    # \"sid\": \"AQC-UTmyeSpxovuZ-gC1j_BEy8msN0j9vSLCWUzZ5pMJRg\"\n", "}\n", "\n", "url='https://payapp.weixin.qq.com/smbpappslf/shelf/detail?sid=AQC-UTmyeSpxovuZ-gC1j_BEy8msN0j9vSLCWUzZ5pMJRg&authtype=MCHAPP&appid=wxbeb0fbb09d5388f9&v=0.48.9'\n", "category=requests.post(url, json=body).json()\n", "category"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["131\n"]}], "source": ["print(len(goods))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "123.96.138.212:37732\n", "112.195.46.74:39365\n", "58.219.85.216:47852\n", "117.69.11.218:40779\n", "221.227.100.223:31348\n", "42.57.151.133:30477\n", "121.205.212.185:32450\n", "122.230.38.114:44814\n", "175.155.181.82:40974\n", "222.190.163.46:32568\n", "['123.96.138.212:37732', '112.195.46.74:39365', '58.219.85.216:47852', '117.69.11.218:40779', '221.227.100.223:31348', '42.57.151.133:30477', '121.205.212.185:32450', '122.230.38.114:44814', '175.155.181.82:40974', '222.190.163.46:32568']\n", "成功写入odps:summerfarm_ds.spider_kunyaohongbei_product_result_df, partition_spec:ds=20240222,competitor_name=kunyaohongbei, attemp:0\n", "sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_kunyaohongbei_product_result_df\n", "                             where ds>='20240123' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240222</td>\n", "      <td>kun<PERSON><PERSON><PERSON>bei</td>\n", "      <td>131</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ds competitor_name  recods\n", "0  20240222   kunyaohongbei     131"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "product_list_all_df['competitor']=brand_name\n", "all_products_df=product_list_all_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
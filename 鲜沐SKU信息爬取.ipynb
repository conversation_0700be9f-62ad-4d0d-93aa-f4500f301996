{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from pandasql import sqldf\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "\n", "from odps import ODPS\n", "from odps.accounts import StsAccount\n", "\n", "path_to_csv='./data/鲜沐/xianmu_sku_with_name_and_price.csv'\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["instance=odps.execute_sql(\"\"\"\n", "--odps sql \n", "--********************************************************************--\n", "--author:研发-唐鹏\n", "--create time:2023-12-18 13:15:54\n", "--********************************************************************--\n", "SELECT  sku.sku\n", "        ,ar.area_name\n", "        ,ar.area_no\n", "        ,ar.administrative_area\n", "        ,ar.map_section\n", "        ,ak.price AS area_sku_price\n", "        ,cost.avg_cost\n", "        ,CONCAT('https://azure.summerfarm.net/',COALESCE(sku.sku_pic,pd.picture_path,pd.detail_picture,'')) AS img\n", "        ,ak.ds AS area_sku_date\n", "        ,CASE   WHEN sku.create_type = 0 THEN '平台'\n", "                WHEN sku.create_type = 1 THEN '大客户'\n", "        END AS create_type\n", "        ,CASE   WHEN pd.pd_name IS NOT NULL\n", "                    AND sku.sku_name IS NOT NULL THEN CONCAT(pd.pd_name,',',sku.sku_name)\n", "                WHEN pd.pd_name IS NOT NULL THEN pd.pd_name\n", "                WHEN sku.sku_name IS NOT NULL THEN sku.sku_name\n", "                ELSE CONCAT('ERR:',sku.sku)\n", "        END AS sku_full_name\n", "        ,CONCAT(\n", "               CASE    WHEN pd.pd_name IS NOT NULL AND sku.sku_name IS NOT NULL THEN CONCAT(pd.pd_name,',',sku.sku_name)\n", "                       WHEN pd.pd_name IS NOT NULL THEN pd.pd_name\n", "                       WHEN sku.sku_name IS NOT NULL THEN sku.sku_name\n", "                       ELSE CONCAT('ERR:',sku.sku)\n", "               END\n", "        ,' ',sku.weight) AS sku_full_name_with_weight\n", "        ,sku.sku_name\n", "        ,pd.pd_name\n", "        ,category.root_category\n", "        ,category.parent_category\n", "        ,category.category\n", "FROM    summerfarm_tech.ods_inventory_df sku\n", "LEFT JOIN   (\n", "                SELECT  sku\n", "                        ,CASE   WHEN SUM(quantity) > 0 THEN round(SUM(cost * quantity) / SUM(quantity),2)\n", "                                ELSE 0.0\n", "                        END AS avg_cost\n", "                FROM    summerfarm_tech.ods_store_record_df\n", "                WHERE   ds >= '20231201'\n", "                AND     type IN (51,52,53,54)\n", "                GROUP BY sku\n", "            ) cost\n", "ON      cost.sku = sku.sku\n", "INNER JOIN summerfarm_tech.ods_products_df pd\n", "ON      sku.pd_id = pd.pd_id\n", "AND     pd.ds = MAX_PT('summerfarm_tech.ods_products_df')\n", "INNER JOIN summerfarm_tech.ods_area_sku_df ak\n", "ON      sku.sku = ak.sku\n", "AND     ak.ds = MAX_PT('summerfarm_tech.ods_area_sku_df')\n", "AND     ak.area_no in (1001,9585) -- 9585:苏州，1001:杭州\n", "AND     ak.on_sale = 1\n", "INNER JOIN  (\n", "                SELECT  pd.pd_id\n", "                        ,pd.pd_name\n", "                        ,ct.category\n", "                        ,ct.id\n", "                        ,ct.parent_id AS level3_parent_id\n", "                        ,ct2.category AS parent_category\n", "                        ,ct2.parent_id AS level2_category_id\n", "                        ,ct3.category AS root_category\n", "                        ,ct3.id AS root_category_id\n", "                FROM    summerfarm_tech.ods_products_df pd\n", "                INNER JOIN summerfarm_tech.ods_category_df ct\n", "                ON      ct.id = pd.category_id\n", "                AND     ct.ds = MAX_PT('summerfarm_tech.ods_category_df')\n", "                INNER JOIN summerfarm_tech.ods_category_df ct2\n", "                ON      ct.parent_id = ct2.id\n", "                AND     ct2.ds = MAX_PT('summerfarm_tech.ods_category_df')\n", "                INNER JOIN summerfarm_tech.ods_category_df ct3\n", "                ON      ct2.parent_id = ct3.id\n", "                AND     ct3.ds = MAX_PT('summerfarm_tech.ods_category_df')\n", "                WHERE   pd.ds = MAX_PT('summerfarm_tech.ods_products_df')\n", "                AND     ct3.category NOT LIKE '%帆台%'\n", "                AND     ct3.category NOT LIKE '%测试%'\n", "            ) category\n", "ON      category.pd_id = pd.pd_id\n", "LEFT JOIN summerfarm_tech.ods_area_df ar\n", "ON      ar.ds = MAX_PT('summerfarm_tech.ods_area_df')\n", "AND     ar.area_no = ak.area_no\n", "WHERE   sku.ds = MAX_PT('summerfarm_tech.ods_inventory_df')\n", "AND     sku.tenant_id = 1\n", "AND     sku.type = 0\n", ";\n", "\"\"\");\n", "\n", "instance.wait_for_success()\n", "pd_df=None\n", "with instance.open_reader(tunnel=True) as reader:\n", "    pd_df = reader.to_pandas()\n", "\n", "pd_df.fillna('-', inplace=True)\n", "pd_df.to_csv(path_to_csv, index=False)\n", "pd_df=pd.read_csv(path_to_csv)\n", "pd_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "import time\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "cache_file_path = '/Users/<USER>/Documents/github/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=os.environ['AZURE_API_KEY_XM']\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "print(getEmbeddingsFromAzure(\"越南大青芒\"))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd_df=pd.read_csv(path_to_csv)\n", "pd_df['sku_full_name_with_weight_embedding']=pd_df['sku_full_name_with_weight'].apply(getEmbeddingsFromAzure)\n", "pd_df.to_csv(f\"./data/鲜沐/xianmu_sku_with_name_and_price_with_embedding.csv\", index=False)\n", "\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
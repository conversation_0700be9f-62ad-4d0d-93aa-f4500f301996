#!/usr/bin/env python
# coding: utf-8

# In[ ]:


# 写入odps
from datetime import datetime, timedelta
import pandas as pd
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
from proxy_setup import get_remote_data_with_proxy_json,logging,create_spider_reporter

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235

headers = {
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN",
}
brand_name = "重庆宝丰"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("baofeng_spider.py", brand_name)
competitor_name_en = "baofeng"

logging.info("即将爬取:%s, %s", brand_name, competitor_name_en)


# In[ ]:


import pymysql
import os

mysql_host = os.getenv("COSFODB_HOST_NAME", "mysql-8.summerfarm.net")
logging.info(f'using mysql host:{mysql_host}')

# Function to establish a database connection
def get_data_from_mysql(query: str = ""):
    conn = pymysql.connect(
        host=mysql_host,
        user="test",
        password="xianmu619",
        port=3307,
        db="front_db",
        charset="utf8mb4",
        cursorclass=pymysql.cursors.DictCursor,
    )
    try:
        with conn.cursor() as cursor:
            cursor.execute(query)
            rows = cursor.fetchall()
            return rows
    except Exception as e:
        logging.error(f"从数据库获取登录token失败:{e}")
        raise e
    finally:
        conn.close()


query = "select * from app_req_record where app_name = 'baofeng' limit 50"
req_info_list = get_data_from_mysql(query)
logging.info(f"data from mysql:{req_info_list}")
if len(req_info_list) <= 0:
    raise Exception(f"未能从数据库获取到登录信息,SQL:{query}")


# In[ ]:


import json
from urllib.parse import parse_qs

param_string = json.loads(req_info_list[0]["req_info"]).get("body")

logging.info(f"param_string:\n{param_string}")

headers = json.loads(req_info_list[0]["req_info"]).get("headers")


def params_to_json(param_string=""):
    # Parse the parameter string into a dictionary
    parsed_params = parse_qs(param_string)

    # Convert single-element lists to values
    params_dict = {k: v[0] if len(v) == 1 else v for k, v in parsed_params.items()}

    return params_dict


login_data = params_to_json(param_string)
logging.info(
    f"pasred data:{json.dumps(login_data, indent=2, ensure_ascii=False)}\n\nheaders:\n{json.dumps(headers, indent=2, ensure_ascii=False)}"
)

data = {
    "FKFlag": "2",
    "FKId": "2938",
    "ProprietorId": "2938",
    "PageIndex": "2",
    "PageSize": "20",
    "ShowNoStock": "1",
    "IsProduct": "1",
    "storeisopenb2c": "1",
    "sortField": "sortorder",
    "sortDirect": "desc",
    "Orderby": "<sortorder>desc</sortorder>",
    "UserId": "4141086",
    # "Token": "4B38F1DF6FF1A96C2B30C18D4EB4B340B9D2DE9D34AB8BA35CBE9046E2BBCE7D1A2BA606BCE181EA6BA218DEA6D099C29051FAB9C3C5239C61354954D91FAE3D4080C807CC56AC1BF2FF59044639D4479A301CBFA0773BA6F275EADB3A968AADFA7E76CA46A49F3D2C3C5E6D8DBEC29ED87B67BAA19AAD7B955EE1472250B956C59B27922C2785774A1EA828EDF3B05B88E7262973B2247F1371D2907F1BEDA0B66B319BBFA48051FC6875495C6C8B19A5242EBD2482C9EDE4B7B8D687BB0B18B1374EF3B92933EDFA8C60DCFC14775DBEC9DD290083DA317F96FC570B6493874D69ADD3BBACB03C92443769FE369FDAEC5618A0688A884AD649D4E8C9A3A21D22B7DD79483E51AF7048FEDC1E215EB8133758AC8F76E438C9F79E95FBA97A2591763C3133B2C91C",
    "v": "3.0",
    "method": "vast.mall.product.page",
    "appid": "4a364f2d1f1fb842",
    "timestamp": "1722493651",
    # "sign": "c066e8f380fc95bbf2e0db5039db9fcb",
}

data['sign']=login_data['sign']
data['Token']=login_data['Token']


# In[ ]:


from proxy_setup import choice_proxy_randomly, proxy_list

print(choice_proxy_randomly(proxy_list=proxy_list))


# In[ ]:


import requests
from datetime import datetime
import brotli
import json
from proxy_setup import choice_proxy_randomly, proxy_list


def get_all_product(page_index: int = 1) -> str:
    url = "https://km5.366kmpf.com/m7ck/Route.axd"
    data["PageIndex"] = f"{page_index}"
    data["timestamp"] = f"{int(datetime.now().timestamp())}"

    proxies = choice_proxy_randomly(proxy_list)
    logging.info("proxies:%s", proxies)

    response = requests.post(url, headers=headers, data=data, proxies=proxies)
    logging.info(f"response headers:{response.headers}")

    json_data = {}
    try:
        json_data = response.json()
        if not json_data.get("Success", False):
            logging.error(f"爬取失败，错误信息:{json_data}")
            raise Exception(f"{brand_name}爬取失败:{json_data}")

        all_product = json_data.get("Content", {}).get("Data")
        if len(all_product) >= 20:
            logging.info(f"获取到了20个商品, page_index:{page_index}, 本次调用返回的第一个商品:{all_product[0]}")
            all_product.extend(get_all_product(page_index=page_index + 1))
            return all_product
        else:
            logging.info(f"也许是最后一个批次咯:{page_index}, size:{len(all_product)}")
            return all_product
    except Exception as e:
        logging.error(
            f"解压失败:{e}, resposne:{response.text}, status:{response.status_code}, json_data:{json_data}"
        )
        raise e


# Usage
all_products = []
try:
    all_products = get_all_product()
except Exception as e:
    logging.error(f"{brand_name} 爬取失败了,{e}")
    raise e
if len(all_products) <= 0:
    raise Exception(f"{brand_name} 爬取了0个商品！")
all_products_df = pd.DataFrame(all_products)


# In[ ]:


from proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df
# 写入odps
all_products_df=all_products_df.astype(str)
all_products_df['competitor']=brand_name

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50""")
logging.info(df.to_string())
# 使用新的结构化输出方式
spider_reporter.report_success(
    product_count=len(all_products_df),
    additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
    }
)


# In[ ]:





# In[ ]:





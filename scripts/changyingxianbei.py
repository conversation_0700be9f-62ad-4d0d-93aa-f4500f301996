import requests
import pandas as pd
from datetime import datetime, timedelta
from proxy_setup import (
    write_pandas_df_into_odps,
    logging,
    get_odps_sql_result_as_df,
    create_spider_reporter,
)

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# 新的API请求头配置
api_headers = {
    'Host': 'bshopwx27.guanmai.cn',
    'X-Guanmai-Request-Id': '504ac2c0-a616-4144-8920-d0b28991f9c9',
    'X-Guanmai-Timeout': '30000',
    'Sec-Fetch-Site': 'same-origin',
    'X-Guanmai-Client': 'GmBshop/4.0.0 c26fd8ec03535b4cc14977d2a2a35fa9',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Sec-Fetch-Mode': 'cors',
    'Accept': 'application/json',
    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.60(0x18003c31) NetType/4G Language/zh_CN miniProgram/wx4be5b27a78cb3627',
    'Referer': 'https://bshopwx27.guanmai.cn/v587/?cms_key=cygyl',
    'X-Guanmai-Success-Code': '0',
    'Connection': 'keep-alive',
    'Sec-Fetch-Dest': 'empty'
}

# Cookie配置
api_cookies = {
    '9beedda875b5420f_gr_cs1': '3815732',
    '9beedda875b5420f_gr_last_sent_cs1': '3815732',
    '9beedda875b5420f_gr_last_sent_sid_with_cs1': '418065b8-8098-4e6a-9910-82c1b2aabfce',
    '9beedda875b5420f_gr_session_id': '418065b8-8098-4e6a-9910-82c1b2aabfce',
    '9beedda875b5420f_gr_session_id_sent_vst': '418065b8-8098-4e6a-9910-82c1b2aabfce',
    'HMACCOUNT': 'E14F94BD1A38DFCB',
    'Hm_lpvt_d02cd7e3028015e0088f63c017c81147': '**********',
    'Hm_lvt_d02cd7e3028015e0088f63c017c81147': '**********,**********',
    'gr_user_id': '8db24fe9-16e9-4013-9b0e-50cdca71c927',
    'cms_key': 'cygyl',
    'group_id': '3252',
    'open_id': 'ob1x35dD3WJRNmEYtUINQH_IGqnw',
    'sessionid': 'e8byar7o8pseqqyf1hm9jlbmtr4961r4',
    'b3Blbmlk': '**********.3523095'
}

brand_name = "长盈鲜焙"
competitor_name_en = "changyingxianbei"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("changyingxianbei.py", brand_name)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")

logging.info(time_of_now)

# 获取类目列表 - 使用新的API
category_url = "https://bshopwx27.guanmai.cn/product/category/get?type=1"

try:
    category_response = requests.get(category_url, headers=api_headers, cookies=api_cookies, verify=False)
    category_data = category_response.json()

    if category_data.get("code") != 0:
        logging.error(f"获取类目失败: {category_data}")
        exit(-1)

    categoryList = category_data["data"]
    logging.info(f"成功获取到 {len(categoryList)} 个一级类目")

except Exception as e:
    logging.error(f"请求类目API失败: {e}")
    exit(-1)

cate_list_df = pd.DataFrame(categoryList)
logging.info(f"一级类目的字段名字：{cate_list_df.columns}")

# 创建类目映射字典，用于后续回填类目名称
category_mapping = {}
all_second_category = []

for first in categoryList:
    category_mapping[first["id"]] = {
        "first_category_name": first["name"],
        "first_category_url": first.get("url", ""),
        "first_category_id": first["id"]
    }

    # 处理二级类目
    for second in first.get("children", []):
        second_category_info = {
            "id": second["id"],
            "name": second["name"],
            "first_category_id": first["id"],
            "first_category_name": first["name"],
            "first_category_url": first.get("url", ""),
            "rank": second.get("rank", 0)
        }
        all_second_category.append(second_category_info)
        logging.info(f"二级类目: {second_category_info}")


# 根据一级类目获取商品列表 - 使用新的API
def get_products_for_first_cate(category_id):
    """根据一级类目ID获取该类目下的所有商品"""
    url = f"https://bshopwx27.guanmai.cn/product/sku/get?level=1&type=1&category_id={category_id}"

    try:
        response = requests.get(url, headers=api_headers, cookies=api_cookies, verify=False)
        response_data = response.json()

        if response_data.get("code") != 0:
            logging.error(f"获取类目 {category_id} 商品失败: {response_data}")
            return []

        return response_data.get("data", [])
    except Exception as e:
        logging.error(f"请求类目 {category_id} 商品API失败: {e}")
        return []


# 获取所有商品
all_products = []
all_skus = []

for first_category in categoryList:
    category_id = first_category["id"]
    category_name = first_category["name"]

    logging.info(f"开始获取类目 {category_name}({category_id}) 的商品...")

    sub_product_list = get_products_for_first_cate(category_id)

    sku_list = []
    for product in sub_product_list:
        # 为每个商品添加类目信息
        product["first_category_id"] = category_id
        product["first_category_name"] = category_name
        product["first_category_url"] = first_category.get("url", "")

        # 处理SKU列表
        for sku in product.get("skus", []):
            # 为SKU添加类目信息
            sku["first_category_id"] = category_id
            sku["first_category_name"] = category_name
            sku["first_category_url"] = first_category.get("url", "")

            # 如果SKU有二级类目信息，尝试匹配二级类目名称
            if "category_id_2" in sku:
                second_cat_info = next(
                    (cat for cat in all_second_category if cat["id"] == sku["category_id_2"]),
                    None
                )
                if second_cat_info:
                    sku["second_category_name"] = second_cat_info["name"]
                    sku["second_category_id"] = second_cat_info["id"]

            sku_list.append(sku)
            all_skus.append(sku)

    product_names = [p.get("name", "未知商品") for p in sub_product_list]
    logging.info(
        f"类目:{category_name}, 商品数:{len(sub_product_list)}, SKU数:{len(sku_list)}, 商品名称:{product_names[:5]}..."
    )
    all_products.extend(sub_product_list)


# 处理SKU数据 - 直接使用已经处理好的all_skus
all_sku_list = []

for sku in all_skus:
    # 确保SKU有必要的字段
    if "id" in sku:
        sku["sku_id"] = sku["id"]

    # 从spu_id字段获取商品ID
    if "spu_id" in sku:
        # 查找对应的商品信息
        matching_product = next(
            (p for p in all_products if p.get("id") == sku["spu_id"]),
            None
        )
        if matching_product:
            # 将商品信息合并到SKU中（避免覆盖SKU已有的字段）
            for key, value in matching_product.items():
                if key not in sku or key == "skus":  # 跳过skus字段避免循环引用
                    if key != "skus":
                        sku[f"product_{key}"] = value

    all_sku_list.append(sku)

logging.info(f"总共获取到 {len(all_sku_list)} 个SKU, {len(all_products)} 个商品")

# 创建DataFrame
all_sku_list_df = pd.DataFrame(all_sku_list)
all_products_df = pd.DataFrame(all_products)

# 显示数据样例
if len(all_sku_list_df) > 0:
    sample_columns = []
    for col in ["sku_id", "sale_price", "std_sale_price", "spu_id", "stocks", "name", "first_category_name"]:
        if col in all_sku_list_df.columns:
            sample_columns.append(col)

    logging.info("SKU数据样例:")
    logging.info(all_sku_list_df.head(5)[sample_columns])

logging.info(f"SKU字段: {list(all_sku_list_df.columns)}")
logging.info(f"商品字段: {list(all_products_df.columns)}")

# 添加元数据字段
all_sku_list_df["competitor"] = brand_name
all_products_df["competitor"] = brand_name
all_sku_list_df["request_time"] = time_of_now
all_products_df["request_time"] = time_of_now

# 转换数据类型
all_sku_list_df = all_sku_list_df.astype(str)
all_products_df = all_products_df.astype(str)

# 写入ODPS
today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = "summerfarm_ds.spider_guanmai_product_result_df"
raw_table_name = "summerfarm_ds.spider_guanmai_product_raw_result_df"

logging.info(f"开始写入ODPS，分区: {partition_spec}")

write_result = write_pandas_df_into_odps(all_sku_list_df, table_name, partition_spec)
write_result = write_result and write_pandas_df_into_odps(
    all_products_df, raw_table_name, partition_spec
)

# 查询统计信息
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(spu_id) spu_cnt
                                  ,count(distinct sku_id) sku_cnt
                                  ,count(distinct case when sale_price>0 then sku_id end) has_price_skus
                                  from {table_name}
                                  where ds>='{days_30}'
                                  group by ds,competitor_name
                                  order by ds,competitor_name"""
)

logging.info(f"爬取完成！{datetime.now()}")
logging.info(f"统计信息:\n{df}")

if write_result:
    # 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count=len(all_sku_list),
        additional_info={
            "odps_table": table_name,
            "partition": partition_spec,
            "sku_count": len(all_sku_list),
            "spu_count": len(all_products),
            "recent_records": str(df) if df is not None else "无历史记录"
        }
    )
else:
    # 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={
            "odps_table": table_name,
            "partition": partition_spec,
            "sku_count": len(all_sku_list) if 'all_sku_list' in locals() else 0,
            "spu_count": len(all_products) if 'all_products' in locals() else 0
        }
    )

import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,
    logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
    create_spider_reporter,
)
import os

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

headers = {"Referer": "https://servicewechat.com/wx430dcb474584da26/12/page-frame.html"}
brand_name = "焙店"
competitor_name_en = "beidian"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("beidian_spider.py", brand_name)

logging.info(time_of_now)

# 登录
url = "https://api.hpsmartcloud.com/jmart/v1.0.3/shoplogin/MemberVerify"
data = {"Account": "***********", "Password": "111111"}
res = requests.post(url, headers=headers, data=data, verify=False)
logging.info(res.headers["clerk_sid"])
data = res.json()["Detail"]["ClerkIdentity"]

logging.info(data)

shop_name = data["MemberName"]
ShopRegionPath = data["ShopRegionPath"]
ShopAddress = data["ShopAddress"]

headers["clerk_sid"] = res.headers["clerk_sid"]

logging.info(data, f"\nnew headers:{headers}", shop_name, ShopAddress, ShopRegionPath)

# 获取一级类目列表

url = f"https://api.hpsmartcloud.com/jmart/v1.0.3/product/Categories?storeKey=8pcba5n2"

categoryList = json.loads(get_remote_data_with_proxy(url, headers=headers))["Detail"]


# 根据一级和二级类目ID爬取商品信息, 返回array
def get_products_for_category(cateID=43, storeKey="8pcba5n2", PageIndex=0):
    url = f"https://api.hpsmartcloud.com/jmart/v1.0.3/product/GetProducts?storeKey={storeKey}"
    res = json.loads(
        get_remote_data_with_proxy(
            url,
            headers=headers,
            data={"PageIndex": PageIndex, "PageSize": 50, "CategoryID": cateID},
        )
    )
    products = res["Detail"]["List"]
    return products


all_product_raw = []
for cate in categoryList:
    PageIndex = 0
    while PageIndex >= 0:
        sub_list = get_products_for_category(
            cateID=cate["CategoryID"], PageIndex=PageIndex
        )
        for product in sub_list:
            product["CategoryID"] = cate["CategoryID"]
            product["CategoryName"] = cate["Name"]
            product["CategoryNamePath"] = cate["NamePath"]
        logging.info(f"类目:{cate['Name']} 的商品个数:{len(sub_list)}")
        all_product_raw.extend(sub_list)
        PageIndex = PageIndex + 1
        if len(sub_list) < 50:
            break


def get_product_detail(product, storeKey="8pcba5n2"):
    url = f'https://api.hpsmartcloud.com/jmart/v1.0.3/product/QueryProductCascade?storeKey={storeKey}&productMainID={product["ProductMainID"]}'
    Detail = json.loads(
        get_remote_data_with_proxy(url=url, headers=headers, post=True)
    )["Detail"]
    product["SKU_LIST"] = Detail["SKUs"]
    return Detail["SKUs"]


sku_list_raw = []


def process_product(product):
    product_info = {
        "BaseUnit": product["BaseUnit"],
        "Brand": product["Brand"],
        "BuySeed": product["BuySeed"],
        "CategoryID": product["CategoryID"],
        "CategoryName": product["CategoryName"],
        "CategoryNamePath": product["CategoryNamePath"],
        "Descriptions": product["Descriptions"],
        "Details": product["Details"],
        "Features": product["Features"],
        "FirstCategoryID": product["FirstCategoryID"],
        "FirstCategoryName": product["FirstCategoryName"],
        "ImageNormal": product["ImageNormal"],
        "IsBest": product["IsBest"],
        "IsHot": product["IsHot"],
        "IsNew": product["IsNew"],
        "IsPromotionPrice": product["IsPromotionPrice"],
        "IsReturnable": product["IsReturnable"],
        "IsSoldOut": product["IsSoldOut"],
        "MainSupplierID": product["MainSupplierID"],
        "MarketPrice": product["MarketPrice"],
        "Name": product["Name"],
        "PerUnitPrice": product["PerUnitPrice"],
        "PointsInfo": product["PointsInfo"],
        "PriceBackUrl": product["PriceBackUrl"],
        "ProductMainID": product["ProductMainID"],
        "SKUCount": product["SKUCount"],
    }
    sku_list = []
    if "SKU_LIST" in product:
        logging.info("已经获取过了")
        sku_list = product["SKU_LIST"]
    else:
        sku_list = get_product_detail(product)
    for sku in sku_list:
        sku_clean = {
            "SkuAddTime": sku["AddTime"],
            "SkuBarCode": sku["BarCode"],
            "SkuBaseValue": sku["BaseValue"],
            "SkuDescriptions": sku["Descriptions"],
            "SkuDetails": sku["Details"],
            "SkuIsSoldOut": sku["IsSoldOut"],
            "SkuLastMonthSales": sku["LastMonthSales"],
            "SkuMarketPrice": sku["MarketPrice"],
            "SkuMonthSales": sku["MonthSales"],
            "SkuMonthViews": sku["MonthViews"],
            "SkuOnlineBeginTime": sku["OnlineBeginTime"],
            "SkuOnlineEndTime": sku["OnlineEndTime"],
            "SkuPerUnitPrice": sku["PerUnitPrice"],
            "SkuPoints": sku["Points"],
            "SkuPointsInfo": sku["PointsInfo"],
            "SkuPriceType": sku["PriceType"],
            "SkuProductCode": sku["ProductCode"],
            "SkuRetailPrice": sku["RetailPrice"],
            "SkuSKUID": sku["SKUID"],
            "SkuSaleCount": sku["SaleCount"],
            "SkuSalePrice": sku["SalePrice"],
            "SkuSalePriceStr": sku["SalePriceStr"],
            "SkuShopPrice": sku["ShopPrice"],
            "SkuShopPriceStr": sku["ShopPriceStr"],
            "SkuSpec": sku["Spec"],
            "SkuState": sku["State"],
            "SkuStepPrices": sku["StepPrices"],
            "SkuStockQuantity": sku["StockQuantity"],
            "SkuStockString": sku["StockString"],
        }
        sku_clean.update(product_info)
        sku_list_raw.append(sku_clean)


THREAD_CNT = 5
with concurrent.futures.ThreadPoolExecutor(max_workers=THREAD_CNT) as executor:
    # Submit tasks to the executor
    futures = [executor.submit(process_product, product) for product in all_product_raw]

    # Wait for all tasks to complete
    concurrent.futures.wait(futures)


if len(sku_list_raw) <= 0:
    raise Exception(f"爬虫失败了！{shop_name}, {ShopRegionPath}")
sku_list_raw_df = pd.DataFrame(sku_list_raw)


sku_list_raw_df["competitor"] = brand_name
raw_data_df = pd.DataFrame(all_product_raw)
raw_data_df["competitor"] = brand_name

sku_list_raw_df["shop_name"] = shop_name
sku_list_raw_df["ShopRegionPath"] = ShopRegionPath
sku_list_raw_df["ShopAddress"] = ShopAddress

raw_data_df["shop_name"] = shop_name
raw_data_df["ShopRegionPath"] = ShopRegionPath
raw_data_df["ShopAddress"] = ShopAddress


sku_list_raw_df = sku_list_raw_df.astype(str)
logging.info(sku_list_raw_df.head(2))
raw_data_df = raw_data_df.astype(str)
today = datetime.now().strftime("%Y%m%d")
days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = "summerfarm_ds.spider_beidian_product_result_df"
raw_table_name = "summerfarm_ds.spider_beidian_product_raw_result_df"

result = write_pandas_df_into_odps(
    sku_list_raw_df, table_name, partition_spec=partition_spec
)
result = result and write_pandas_df_into_odps(
    raw_data_df, raw_table_name, partition_spec=partition_spec
)

df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(1) skus
                                  ,count(distinct ProductMainID)Products
                                  ,count(distinct case when skusaleprice>0 then skuskuid end) has_price_skus
                                  from summerfarm_ds.spider_beidian_product_result_df 
                                  where ds>='{days_30}'
                                  group by ds,competitor_name
                                  order by ds,competitor_name"""
)

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    # 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count=len(sku_list_raw),
        additional_info={
            "odps_table": table_name,
            "partition": partition_spec,
            "recent_records": str(df) if df is not None else "无历史记录"
        }
    )
else:
    # 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={
            "odps_table": table_name,
            "partition": partition_spec,
            "product_count": len(sku_list_raw) if 'sku_list_raw' in locals() else 0
        }
    )

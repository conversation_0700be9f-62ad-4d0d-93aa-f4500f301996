import requests
import json
import pandas as pd
from datetime import datetime,timedelta
import concurrent.futures
from proxy_setup import get_remote_data_with_proxy,write_pandas_df_into_odps,logging,get_odps_sql_result_as_df,THREAD_CNT,create_spider_reporter
import os

os.environ['PYTHONIOENCODING'] = 'UTF-8'

time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')

headers={'token':'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJMb2dpblRpbWUiOjE3MDI1MjA4ODk0OTcsIlRZUEUiOiJYQ1hfQVBQIiwib3BlbmlkIjo3MTI0NTF9.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q',
'appcodenew':'7798c1f4306b4f89a9fc2a4c2cdc47ac',
'uid':'712451',
'time':'1702521175012',}
brand_name='广州可心烘焙'

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("kexinhongbei_spider.py", brand_name)
competitor_name_en='kexinhongbei'

logging.info(f"{time_of_now}, headers:{headers}")

days_30=(datetime.now()-timedelta(30)).strftime('%Y%m%d')
logging.info(f"time_of_now:{time_of_now},headers:{headers}")

# 获取类目列表
cate_list=requests.get("https://chengxu.97jindianzi.com/addons/zjhj_bd/web/index.php?_mall_id=10297&r=api/cat/list&cat_id=&select_cat_id=", verify=False).json()['data']['list']
cate_list_all=[]
for first_cate in cate_list:
    cate_list_all.extend(first_cate['child'])

# 获取所有商品列表
product_list_all=[]
for cate in cate_list_all:
    cat_id=cate['id']
    logging.info(cate)
    url=f'https://chengxu.97jindianzi.com/addons/zjhj_bd/web/index.php?_mall_id=10297&r=api/default/goods-list&mch_id=&page=1&cat_id={cat_id}&sort=1&sort_type=1&keyword=&coupon_id=0&sign='
    product_list=requests.get(url, verify=False).json()['data']["list"]
    product_list_all.extend(product_list)

product_list_all_df=pd.DataFrame(product_list_all)

# 写入odps
product_list_all_df['competitor']=brand_name
all_products_df=product_list_all_df.astype(str)

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

result=write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50""")

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    # 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count=len(product_list_all),
        additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )
else:
    # 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={
            "odps_table": table_name if 'table_name' in locals() else "unknown",
            "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )
#!/usr/bin/env python
# coding: utf-8

# In[ ]:


# 写入odps
from datetime import datetime, timedelta
import pandas as pd
import requests
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
from proxy_setup import get_remote_data_with_proxy_json, logging,create_spider_reporter

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235

headers = {
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN",
}
brand_name = "果速送-杭州"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("guosusong_spider.py", brand_name)
competitor_name_en = "guosusong"

logging.info(f"{timestamp_of_now}, headers:{headers}")
server_url = "https://app.guoss.cn/gss_api/server/api.do"


# login first

data = {
    "method": "user_login3",
    "websiteNode": "3301",
    "mobile": "***********",
    "password": "92f60e437c7d8414b23b14e18024049d",
    "version": "2.1.19",
}

token = requests.post(server_url, headers=headers, data=data).json()
logging.info(f"登陆信息:{token}")
if "data" not in token or len(token.get("data")) <= 1:
    logging.error(f"登陆失败!{token}")
    exit(-1)
token = token.get("data")

logging.info(f"user token:{token}")


# In[ ]:


import hashlib


def generate_md5_string(token=token):
    # Concatenate the required strings
    string_to_hash = (
        "firmId" + token.get("firmInfo").get("id") + "key" + token.get("secretKey")
    )

    # Generate MD5 hash
    md5_hash = hashlib.md5(string_to_hash.encode()).hexdigest()

    return md5_hash.upper()


sign_string = generate_md5_string()
logging.info(f"sign_string:{sign_string}")

hashlib.md5(
    "firmId33941keywr2fqc4af72ch2b6j5dgh3fp1epdepq".encode()
).hexdigest().upper()


# In[ ]:


# 获取类目列表;
# 一级：


import requests
from proxy_setup import get_remote_data_with_proxy_json

headers = {
    "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Mobile Safari/537.36",
}

data = {
    "method": "goods_type_first",
    "firmId": "33941",
    "websiteNode": "3301",
    "version": "2.1.19",
}

response = requests.post(url=server_url, headers=headers, data=data).json()

logging.info(f"一级类目response:{response}")

root_category_list = response["data"]

# 二级
second_category_list_all = []
for root in root_category_list:
    logging.info(f"获取一级类目的二级类目列表:{root}")
    second_category_list = requests.post(
        url=server_url,
        headers=headers,
        data={
            "method": "goods_type_second",
            "firmId": "33941",
            "websiteNode": "3301",
            "version": "2.1.19",
            "typeCode": root["typeCode"],
        },
    ).json()
    logging.info(f"second_category_list:{second_category_list}")
    second_category_list = second_category_list["data"]
    second_category_list_all.extend(second_category_list)

logging.info(f"全部二级类目:{second_category_list_all}")


# In[ ]:


def get_products_for_second_category(typeCode=6717, pageNo=1):
    products = requests.post(
        url=server_url,
        headers=headers,
        data={
            "method": "goods_info_list_three",
            "firmId": "33941",
            "websiteNode": "3301",
            "typeCode": typeCode,
            "pageSize": "50",
            "eyeId": "",
            "pageNo": pageNo,
            "keyWord": "",
            "source": "firmId33941",
            "sign": sign_string,
            "tokenId": token.get("tokenId"),
            "version": "2.1.19",
        },
    ).json()
    logging.info(f"二级类目:{typeCode} 的商品:{products}")
    products = products["data"]["page"]["objects"]
    if products is not None and len(products) > 20:
        sub_list = get_products_for_second_category(
            typeCode=typeCode, pageNo=pageNo + 1
        )
        if sub_list is not None:
            products.extend(sub_list)
    return products


all_products = []
product_id_map = {}
for cate in second_category_list_all:
    typeName = cate["typeName"]
    sub_list = get_products_for_second_category(cate["typeCode"])
    for product in sub_list:
        if "typeName" not in product:
            product["typeName"] = typeName
        else:
            product["typeName"] = f"{typeName},{product['typeName']}"
        if product["goodsCode"] in product_id_map:
            print(f"重复的goodsCode:{product['goodsCode']}")
        else:
            product_id_map[product["goodsCode"]] = True
            all_products.append(product)

print(len(all_products), all_products[0])


# In[ ]:


import html2text
import concurrent.futures

all_product_details = []


def get_product_detail(goods={}):
    goods_id = goods["id"]
    global all_product_details
    # url=f"{server_url}method=goods_details_message_four&goodsId={goods_id}&firmId=33941"
    goodsDetails = requests.post(
        url=server_url,
        headers=headers,
        data={
            "method": "goods_details_message_four",
            "goodsId": goods_id,
            "firmId": "33941",
            "source": "firmId33941",
            "sign": sign_string,
            "tokenId": token.get("tokenId"),
            "version": "2.1.19",
        },
    ).json()
    logging.info(f"goods_id:{goods_id} goodsDetails:{goodsDetails}")
    goodsDetails = goodsDetails["data"]["goodsDetails"]
    goodsDetails["goodsContextMarkdown"] = html2text.html2text(
        goodsDetails["goodsContext"]
    )
    if "typeName" in goods:
        print(f'{goods["typeName"]}:{goodsDetails["goodsName"]}')
        goodsDetails["typeName"] = goods["typeName"]
    all_product_details.append(goodsDetails)
    return goodsDetails


with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    # Submit tasks to the executor
    futures = [
        executor.submit(get_product_detail, goods=goods) for goods in all_products
    ]
    # Wait for all tasks to complete
    concurrent.futures.wait(futures)

all_product_details_df = pd.DataFrame(all_product_details)


# In[33]:


from proxy_setup import write_pandas_df_into_odps, get_odps_sql_result_as_df

all_sku_list_df = pd.DataFrame(all_product_details)
# 写入odps
all_sku_list_df["competitor"] = brand_name
all_products_df = all_sku_list_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_df"

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50"""
)

logging.info(df.to_string())
# 使用新的结构化输出方式
spider_reporter.report_success(
    product_count=len(all_products_df),
    additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
    }
)


# In[ ]:


# In[ ]:

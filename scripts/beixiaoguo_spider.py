#!/usr/bin/env python
# coding: utf-8

# In[ ]:


# 写入odps
import requests
import json
from datetime import datetime, timedelta
import pandas as pd
import os
from proxy_setup import get_remote_data_with_proxy_json,logging,create_spider_reporter

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235

headers = {
    "content-type": "application/x-www-form-urlencoded",
    "Connection": "keep-alive",
    "Referer": "https://servicewechat.com/wx467c5c95f5a7d792/1/page-frame.html",
    "X-Token": "aHR0cHM6Ly93d3cuempoZWppYW5nLmNvbRNWGREfVnBGE1VNH09SWVFGSwIcAk8XCgk3FQBSSQsYHEVGQB1CUl5JSBwSCxVxCgtPUkxGR0oDFxYK",
    "X-App-Version": "5.11.9",
    "Accept-Encoding": "gzip,compress,br,deflate",
    "Mch-Access-Token": "[object Undefined]",
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.53(0x18003531) NetType/4G Language/zh_CN",
    "X-Access-Token": "zYvLz6QYbtLqrW23BFgVA5bRqSlKBX_H",
    "X-Form-Id-List": '[{"value":"requestFormId:fail deprecated","type":0,"remains":1,"expires_at":"2024-11-28 15:23:47"}]',
    "X-App-Platform": "wxapp",
    "Host": "www.mosyy.com",
    "X-Requested-With": "XMLHttpRequest",
}
brand_name = "焙小菓"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("beixiaoguo_spider.py", brand_name)
competitor_name_en = "beixiaoguo"

logging.info(f"{timestamp_of_now}, headers:{headers}")


# In[ ]:


category_list_url = "https://www.mosyy.com/web/index.php?_mall_id=15814&r=api/cat/list&cat_id=&select_cat_id="
category_list = (
    get_remote_data_with_proxy_json(url=category_list_url, headers=headers)
    .get("data", {})
    .get("list", [])
)
logging.info(f"category_list:{category_list}")


# In[ ]:


def get_products_of_category(id=362258, currentPage=1):
    url = f"https://www.mosyy.com/web/index.php?_mall_id=15814&r=api/default/goods-list&page={currentPage}&cat_id={id}"
    products = (
        get_remote_data_with_proxy_json(url=url, headers=headers)
        .get("data", {})
        .get("list", [])
    )
    if products is None or len(products) <= 0:
        return []
    elif len(products) < 10:
        return products
    # 递归查询
    products.extend(get_products_of_category(id, currentPage + 1))
    return products


print(get_products_of_category())

all_products = []
for cate in category_list:
    currentPage = 1
    sub_list = get_products_of_category(cate["id"], currentPage=currentPage)
    if sub_list is None:
        continue
    if len(sub_list) > 0:
        for product in sub_list:
            product["category_name"] = cate["name"]
        all_products.extend(sub_list)


all_products_df = pd.DataFrame(all_products)
all_products_df.head(5)


# In[ ]:


logging.info(f"爬取了：{len(all_products_df)}个商品。")


# In[ ]:


from proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df
# 写入odps
all_products_df['competitor']=brand_name
all_products_df=all_products_df.astype(str)

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name  order by ds desc limit 50""")
logging.info(f"{df}")
# 使用新的结构化输出方式
spider_reporter.report_success(
    product_count=len(all_products_df),
    additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
    }
)





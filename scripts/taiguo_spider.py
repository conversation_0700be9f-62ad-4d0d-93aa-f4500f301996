from datetime import datetime, timedelta
import pandas as pd
import json
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
from proxy_setup import (
    get_remote_data_with_proxy_json,
    write_pandas_df_into_odps,logging,
    get_odps_sql_result_as_df,
    create_spider_reporter,
)
import requests

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235

headers = {
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN",
}
brand_name = "钛果-苏皖"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("taiguo_spider.py", brand_name)
competitor_name_en = "taiguo"
# ***********
# 984474

# 先登录

login_url = "https://www.tigo1000.com/login.php?0.*****************"

import random


def randomString(length):
    length = length or 32
    chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678"
    maxPos = len(chars)
    pwd = ""
    for i in range(length):
        pwd += chars[int(random.random() * maxPos)]
    return pwd


logging.info(randomString(3))

username = "***********"
pwd = "0b5e0b72c9864a6d348ea9fc72cad5129b102d3dbd399307765ffc417ad21b40"
login_object = {
    "u": f"{username}{randomString(5)}",
    "su": None,
    "p": f"{pwd}{randomString(6)}",
    "c": None,
    "sc": None,
    "d": None,
    "al": True,
    "e": f"{randomString(3)}0{randomString(6)}",
}


logging.info(f"login_object:{login_object}")

response = requests.post(
    url=login_url,
    params=login_object,
    headers={
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "accept-language": "en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6",
        "sec-ch-ua": '"Google Chrome";v="123", "Not:A-Brand";v="8", "Chromium";v="123"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "document",
        "sec-fetch-mode": "navigate",
        "sec-fetch-site": "same-site",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
    },
)

logging.info(response.status_code, response.text)
new_cookies = response.cookies.get_dict()
response_headers = response.headers

logging.info(
    f"new_cookies:{json.dumps(new_cookies, indent=4)}",
    f"response_headers:{response_headers}",
)

## 请求首页，确保登录成功:
response = requests.get(
    "https://app.tigo1000.com",
    cookies=new_cookies,
    allow_redirects=False,
    headers={
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "accept-language": "en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6",
        "sec-ch-ua": '"Google Chrome";v="123", "Not:A-Brand";v="8", "Chromium";v="123"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "document",
        "sec-fetch-mode": "navigate",
        "sec-fetch-site": "same-site",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
    },
)

logging.info(
    f"首页:response.status_code:{response.status_code},response.text{response.text}\n\n\n"
)
logging.info(f"首页:cookies:{response.cookies.get_dict()}, headers:{response.headers}")


## 获取商品列表：

headers = {
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
    "supid": "A7169348",
}

goods_url = "https://d2p-api.tigo1000.com/goods/v2/list"

params = {
    "q": "",
    "barCode": "",
    "bn": "",
    "cats": "",
    "supId": "A7169348",
    "basic": "",
    "label": "",
    "pageNo": 0,
    "pageSize": 100,
}


def get_all_products(page_no=0, all_product_list=[]):
    params["pageNo"] = page_no
    logging.info(f"params:{params}")
    response = requests.get(
        url=goods_url, params=params, headers=headers, cookies=new_cookies
    )
    data = {}
    try:
        data = json.loads(response.text)["data"]
    except Exception as e:
        raise Exception(
            f"params:{params}, status:{response.status_code}, text:{response.text}"
        )
    totalCount = data["totalCount"]
    if len(data["dataList"]) > 0:
        dataList = data["dataList"]
        skuIds = [sku["skuId"] for sku in dataList]
        goodsPrices = get_good_prices_by_id(skuIds)
        logging.info(f"goodsPrices length::{len(goodsPrices)}")
        for sku in dataList:
            sku["goodsPrices"] = goodsPrices[sku["skuId"]]
        all_product_list.extend(dataList)
    if len(all_product_list) < totalCount:
        get_all_products(page_no=page_no + 1, all_product_list=all_product_list)


def get_good_prices_by_id(goods_id=[]):
    logging.info(f"goods_id:{goods_id}")
    prices_url = (
        f"https://d2p-api.tigo1000.com/goods/v2/prices?goodsIds={','.join(goods_id)}"
    )
    return requests.get(
        url=prices_url, params=params, headers=headers, cookies=new_cookies
    ).json()["data"]


# Print the response content
all_product_list = []
get_all_products(page_no=0, all_product_list=all_product_list)
all_product_list_df = pd.DataFrame(all_product_list)

# 写入odps
all_sku_list_df = all_product_list_df
all_sku_list_df["competitor"] = brand_name
all_products_df = all_sku_list_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_df"

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50"""
)
logging.info(df)

# 使用新的结构化输出方式
spider_reporter.report_success(
    product_count=len(all_product_list_df),
    additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
    }
)

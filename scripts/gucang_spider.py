# 写入odps
from datetime import datetime, timedelta
import pandas as pd
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
from proxy_setup import get_remote_data_with_proxy_json,write_pandas_df_into_odps,logging,get_odps_sql_result_as_df,create_spider_reporter

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235

headers = {
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN",
}
brand_name = "谷仓-上海"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("gucang_spider.py", brand_name)
competitor_name_en = "gucang"

logging.info(f"{timestamp_of_now}, headers:{headers}")

import requests
url="https://www.gucangfarm.com/index.php?s=/api/goods/listAll&wxapp_id=10001&category_id=1&sortPrice=0&page=1&area_id=2&token=90901de26e782ba48053b6d0107a8979"
products=get_remote_data_with_proxy_json(url=url, headers=headers)['data']["list"]

all_sku_list_df=pd.DataFrame(products)
result_cnt=len(products)

# 写入odps
all_sku_list_df['competitor']=brand_name
all_products_df=all_sku_list_df.astype(str)

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50""")
logging.info(df)
# 使用新的结构化输出方式
spider_reporter.report_success(
    product_count=result_cnt,
    additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
    }
)
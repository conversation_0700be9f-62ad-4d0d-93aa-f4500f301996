# 写入odps
from datetime import datetime, timedelta
import pandas as pd
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
from proxy_setup import get_remote_data_with_proxy_json,write_pandas_df_into_odps,logging,get_odps_sql_result_as_df,create_spider_reporter

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235

headers = {
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN",
}
brand_name = "鲜成派-成都"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("xianchengpai_spider.py", brand_name)
competitor_name_en = "xianchengpai"

logging.info(f"{timestamp_of_now}, headers:{headers}")

cat_list_url = "https://xinv4.youdawangluo.com/web/index.php?_mall_id=11463&r=api/cat/list&cat_id=&select_cat_id="
cat_list = get_remote_data_with_proxy_json(url=cat_list_url)["data"]["list"]


def get_products_of_cat(cat={}, page=1):
    logging.info(f"获取类目:{cat}")
    product_list_url = f"https://xinv4.youdawangluo.com/web/index.php?_mall_id=11463&r=api/default/goods-list&page={page}&cat_id={cat['id']}"
    data = get_remote_data_with_proxy_json(url=product_list_url)["data"]
    logging.info(data)
    product_list = data["list"]
    if len(product_list) < data["pagination"]["totalCount"]:
        product_list.extend(get_products_of_cat(cat=cat, page=page + 1))
    return product_list


all_product_list = []
for cat in cat_list:
    all_product_list.extend(get_products_of_cat(cat))

def get_product_detail_by_id(product_id=970592):
    detail_url=f"https://xinv4.youdawangluo.com/web/index.php?_mall_id=11463&r=api/goods/detail&id={product_id}&plugin=mall"
    return get_remote_data_with_proxy_json(url=detail_url)['data']["goods"]

all_sku_list=[]
all_goods_ids={}
for product in all_product_list:
    if product['id'] in all_goods_ids:
        logging.info(f"已经获取过了：{product['id']}")
        continue
    product_detail=get_product_detail_by_id(product['id'])
    attr=product_detail['attr']
    all_goods_ids[product['id']]=True
    for attr_detail in attr:
        sku={}
        for key, value in attr_detail.items():
            sku[f"sku_{key}"]=value
        sku.update(product_detail)
        del sku["attr"]
        all_sku_list.append(sku)

all_sku_list_df=pd.DataFrame(all_sku_list)
result_cnt=len(all_sku_list_df)

# 写入odps
all_sku_list_df['competitor']=brand_name
all_products_df=all_sku_list_df.astype(str)

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50""")
logging.info(df)
# 使用新的结构化输出方式
spider_reporter.report_success(
    product_count=result_cnt,
    additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
    }
)
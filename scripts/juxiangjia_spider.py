import requests
import json
import pandas as pd
from datetime import datetime,timedelta
import concurrent.futures
from proxy_setup import get_remote_data_with_proxy,write_pandas_df_into_odps,logging,get_odps_sql_result_as_df,THREAD_CNT,create_spider_reporter
import os

os.environ['PYTHONIOENCODING'] = 'UTF-8'

time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')

headers={'appCode':'wxc0416f9c9c965355',
         'oemId':'100188',
         'merchantId':'100001',
         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
         'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}
brand_name='橘祥家'

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("juxiangjia_spider.py", brand_name)
competitor_name_en='juxiangjia'

days_30=(datetime.now()-timedelta(30)).strftime('%Y%m%d')
logging.info(time_of_now)
# 获取所有商品列表


pid_list_url='https://gateway.jipaibuy.com/zuul/am-user-center-appweb/api/shop/product/getProductList/-1/1/1000?markingTime=1708250022564'
product_list=requests.get(pid_list_url, headers=headers, verify=False).json()['REP_BODY']['list']
product_list_df=pd.DataFrame(product_list)

# 写入odps
product_list_df['competitor']=brand_name
all_products_df=product_list_df.astype(str)

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = 'summerfarm_ds.spider_juxiangjia_product_result_df'

result=write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods
                                  from summerfarm_ds.spider_juxiangjia_product_result_df 
                                  where ds>='{days_30}'
                                  group by ds,competitor_name
                                  order by ds,competitor_name""")

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    # 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count=len(product_list),
        additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )
else:
    # 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={
            "odps_table": table_name if 'table_name' in locals() else "unknown",
            "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )
import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy_json,
    write_pandas_df_into_odps,
    logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
    create_spider_reporter,
)
import os

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

headers = {
    "uniacid": "2595",
    "appType": "mini",
    "Referer": "https://servicewechat.com/wx92c8f2cd458916b5/39/page-frame.html",
}
brand_name = "佳焙"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("jiabei_spider.py", brand_name)
competitor_name_en = "jiabei"

logging.info(f"{time_of_now}, headers:{headers}")

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
logging.info(f"time_of_now:{time_of_now},headers:{headers}")

cookies = {
    "1186pcck304": "true",
    "loginIntegralTip283846051186": "true",
    "_filterVisitTime": "fehfhlhnjmlt",
    "_siteStatVisit": "visit_28384605",
    "_siteStatVisitTime": "1715739387116",
    "_siteStatRedirectUv": "redirectUv_28384605",
    "_siteStatDay": "20240515",
    "_siteStatId": "7f33ea20-b42c-4af4-b951-13a338f0ff0e",
    "_FSESSIONID": "MWyQd2pwQfC0BAVj",
    "lastLoginTime283846051186": "2024-05-15",
    "_siteStatVisitorType": "visitorType_28384605",
    "loginMemberAcct": "xcx_gL3fI8UjsGARKolg",
    "_cliid": "DS-QQ0v0EFL9hBS-",
}

# 获取所有类目列表：
def get_products_by_category_id(groupId=9):
    url = (
        f"https://wx5.jzapp.yswebportal.cc/28384605/1//api/guest/product/getProductByGroupV2?isFrom=mp-weixin&groupId={groupId}"
        + '&st=desc&sn=sales&size=200&sc=["pdName","mallPrice","marketingPrice","pdProp","saleNum","stock"]&lng=&lat=&selfTakeId=0&merchantId=0&addrInfo={"prc":"","cic":"","coc":""}'
    )
    goods = get_remote_data_with_proxy_json(url=url, headers=headers, cookies=cookies)
    goods = goods["rtPdList"]
    logging.info(f"类目ID:{groupId}, 商品个数:{len(goods)}")
    return goods


url = "https://wx5.jzapp.yswebportal.cc/28384605/1//api/guest/col/getModuleDataFromColV3?isFrom=mp-weixin&colId=19&posCtrl="
response = get_remote_data_with_proxy_json(url=url, headers=headers, cookies=cookies)
logging.info(response)
if not "moduleList" in response:
    raise Exception(f"爬虫失败:{response}")
moduleList = response["moduleList"]
catelist = None
product_list_all = []
for module in moduleList:
    if 334 == module["id"]:
        # logging.info(module)
        catelist = module["content"]["pclGroup"]["level1"]
        # logging.info(catelist)
        for cate in catelist:
            product_list_all.extend(get_products_by_category_id(cate["ci"]))
product_list_all_df = pd.DataFrame(product_list_all)
logging.info(product_list_all_df.head(5))
# 写入odps
product_list_all_df["competitor"] = brand_name
all_products_df = product_list_all_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_df"

result = write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50"""
)

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    # 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count=len(product_list_all),
        additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )
else:
    # 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={
            "odps_table": table_name if 'table_name' in locals() else "unknown",
            "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )

import requests
import json as jsonlib
from datetime import datetime, timedelta
import os
import time
import re
import logging

app_log_dir = os.environ.get("APP_LOG_DIR", "./")

# Configure the logging
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s][%(levelname)s][%(filename)s_%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[
        logging.FileHandler(f"{app_log_dir}/app.log"),  # Logs to a file named 'app.log'
        logging.StreamHandler(),  # Logs to the console
    ],
)


def get_logger(name):
    logger = logging.getLogger(name)
    if not logger.hasHandlers():  # Ensure we don't add multiple handlers
        handler = logging.StreamHandler()
        formatter = logging.Formatter("%(name)s - %(levelname)s - %(message)s")
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    return logger


import json

logging.info(json.dumps(dict(os.environ), indent=2))

THREAD_CNT = int(os.environ.get("THREAD_CNT", 20))
USE_PROXY = bool(os.environ.get("USE_PROXY", True))

print(f"USE_PROXY:{USE_PROXY}")

DEFAULT_MAX_RETRY_NUM = 5

logging.info(f"Thread count: {THREAD_CNT}")


def create_directory_if_not_exists(path):
    if not os.path.exists(path):
        os.makedirs(path)


from datetime import datetime

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
date_of_now = datetime.now().strftime("%Y-%m-%d")


def get_proxy_list_from_server():
    all_proxies_respons = requests.get(
        "http://v2.api.juliangip.com/company/postpay/getips?auto_white=1&num=1&pt=1&result_type=json&trade_no=6021385944600691&sign=9b27b5ab5b1c7b0460c076d008b60888",
        verify=False,
        proxies={},
    )
    all_proxies = all_proxies_respons.text
    if not all_proxies_respons.status_code == 200:
        logging.info(f"获取代理IP时出错误:{all_proxies}")
        return []

    try:
        json_data = jsonlib.loads(all_proxies)
        if json_data["code"] != 200:
            logging.info(f"获取代理IP时出错误:{all_proxies}")
            return []
        proxy_list = json_data["data"]["proxy_list"]
        logging.info(f"获取了新的代理列表:{proxy_list}")
        return proxy_list
    except (jsonlib.JSONDecodeError, KeyError, TypeError) as e:
        logging.error(f"解析代理IP响应时出错: {e}, 响应内容: {all_proxies}")
        return []


import requests
import random

two_min = 2 * 60
PROXY_ERROR_CNT = 0

last_time_fetch_server_list = int(datetime.timestamp(datetime.now()) / two_min)
proxy_list = get_proxy_list_from_server()
# proxy_list.extend(get_proxy_list_from_server()) # No need to call twice
logging.info(proxy_list)

import re


def encode_http_proxy_info(
    proxies={
        "http": "http://18258841203:8gTcEKLs@",  # This is just a placeholder
    }
):
    if proxies is None or len(proxies) <= 0 or not "http" in proxies:
        return f"illegal_proxy:{proxies}"
    string = proxies["http"]
    masked_string = re.sub(r"\d{11}", lambda match: "*" * len(match.group()), string)
    return masked_string


def choice_proxy_randomly(proxy_list=proxy_list, retrying_num=0):
    len_of_proxy = len(proxy_list)
    if len_of_proxy == 0:  # Handle empty proxy list
        return {}
    random_number = random.randint(0, len_of_proxy - 1)
    proxy_host_and_port = proxy_list[(retrying_num + random_number) % len_of_proxy]
    logging.info(f"随机的使用一个代理IP:{proxy_host_and_port}")
    # Assuming the proxy format is "ip:port"
    # return {
    #     "http": f"http://18258841203:8gTcEKLs@{proxy_host_and_port}",
    # }
    return {
        "http": f"http://{proxy_host_and_port}",
        "https": f"http://{proxy_host_and_port}",
    }


def get_remote_data_with_proxy_json(
    url,
    json=None,
    data=None,
    post=False,
    headers={},
    cookies={},
    max_retries=DEFAULT_MAX_RETRY_NUM,
    timeout=10,
) -> dict:
    return jsonlib.loads(
        get_remote_data_with_proxy(
            url=url,
            json=json,
            data=data,
            post=post,
            headers=headers,
            cookies=cookies,
            max_retries=max_retries,
            timeout=timeout,
        )
    )


def get_remote_data_with_proxy(
    url,
    json=None,
    data=None,
    post=False,
    headers={},
    cookies={},
    max_retries=DEFAULT_MAX_RETRY_NUM,
    timeout=10,
):
    global last_time_fetch_server_list
    global proxy_list
    global PROXY_ERROR_CNT
    if data is not None:
        json = data

    for i in range(max_retries):
        proxies = {}
        if len(proxy_list) > 0 and USE_PROXY:
            proxies = choice_proxy_randomly(proxy_list=proxy_list, retrying_num=i)
            logging.info(
                f"url:{url}, using proxy: {encode_http_proxy_info(proxies)}, retry_cnt:{i}, headers:{headers}"
            )
        else: # Don't use a default hardcoded proxy
            logging.warning(f"不使用代理，直接连接:{url}")
        status_code = -1
        try:
            response = None
            if json is not None:
                response = requests.post(
                    url,
                    json=json,
                    proxies=proxies,
                    headers=headers,
                    cookies=cookies,
                    timeout=timeout,
                    verify=False,
                )
            elif post:
                response = requests.post(
                    url,
                    proxies=proxies,
                    headers=headers,
                    cookies=cookies,
                    timeout=timeout,
                    verify=False,
                )
            else:
                response = requests.get(
                    url,
                    proxies=proxies,
                    headers=headers,
                    cookies=cookies,
                    timeout=timeout,
                    verify=False,
                )
            status_code = response.status_code
            if status_code == 200:
                logging.info(
                    f"response status:{response.status_code}, proxy used:{encode_http_proxy_info(proxies)}"
                )
                return response.text
            else:
                if "很抱歉，由于您访问的URL有可能对网站造成安全威胁，您的访问被阻断。" in response.text:
                    sleep_time = random.uniform(1, 10)  # 随机休眠1到5秒
                    logging.info(f"检测到访问被阻断, 休眠 {sleep_time:.2f} 秒后重新获取代理列表")
                    time.sleep(sleep_time)
                    logging.info("检测到访问被阻断，正在重新获取代理列表")
                    last_time_fetch_server_list = 0  # 强制立即刷新代理列表
                    proxy_list = get_proxy_list_from_server()  # 获取新的代理
                    logging.info(f"获取到新的代理列表: {proxy_list}")
                else:
                    logging.warning(f"返回了非200的结果{response.text}")
        except requests.exceptions.ConnectionError as e: # Catch connection errors
            logging.info(f"Proxy connection error: {e}")
            last_time_fetch_server_list = 0 # Force refresh proxy list immediately
            proxy_list = get_proxy_list_from_server() # Fetch new proxies
            logging.info(f"Fetched new proxy list due to connection error: {proxy_list}")
            if i == max_retries - 1:
                logging.info(f"重试已达上限:{max_retries}, 已重试次数:{i+1}")
                raise e # Re-raise after exhausting retries
        except Exception as e: # Catch other exceptions
            if status_code >= 400 and status_code < 500:
                logging.info(f"http 4xx错误:{status_code}")
                raise e  # Re-raise client errors
            time_to_fetch_new_server = int(datetime.timestamp(datetime.now()) / two_min)
            if time_to_fetch_new_server > last_time_fetch_server_list:
                last_time_fetch_server_list = time_to_fetch_new_server
                proxy_list = get_proxy_list_from_server()
                logging.info(f"new proxy server:{proxy_list}")

            logging.info(
                f"Error getting \n{e}\nurl:{url}\nproxy used:{encode_http_proxy_info(proxies)}\nretrying:{i}"
            )
            if i == max_retries - 1:
                logging.info(f"重试已达上限:{max_retries}, 已重试次数:{i+1}")
                raise e

    return None

import requests
import json
import pandas as pd
from datetime import datetime,timedelta
import concurrent.futures
from proxy_setup import get_remote_data_with_proxy_json,write_pandas_df_into_odps,logging,get_odps_sql_result_as_df,THREAD_CNT,create_spider_reporter
import os

os.environ['PYTHONIOENCODING'] = 'UTF-8'

time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')

headers={'token':'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJMb2dpblRpbWUiOjE3MDI1MjA4ODk0OTcsIlRZUEUiOiJYQ1hfQVBQIiwib3BlbmlkIjo3MTI0NTF9.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q',
'appcodenew':'7798c1f4306b4f89a9fc2a4c2cdc47ac',
'uid':'712451',
'time':'1702521175012',}
brand_name='添品'

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("tianpin_spider.py", brand_name)
competitor_name_en='tianpin'

logging.info(f"{time_of_now}, headers:{headers}")

days_30=(datetime.now()-timedelta(30)).strftime('%Y%m%d')
logging.info(f"time_of_now:{time_of_now},headers:{headers}")
# 获取所有商品列表


product_list_all=[]
for offset in range(-1,50):
    if offset==0:
        continue
    url=f'http://m.txpchain.com/product?offset={offset}'
    logging.info(url)
    product_list=requests.get(url).json()
    if product_list is None or len(product_list)<=0:
        break
    else:
        logging.info(product_list[0])
        product_list_all.extend(product_list)
product_list_all_df=pd.DataFrame(product_list_all)
product_list_all_df.head(10)

# 写入odps
product_list_all_df['competitor']=brand_name
all_products_df=product_list_all_df.astype(str)

today = datetime.now().strftime('%Y%m%d')
partition_spec = f'ds={today},competitor_name={competitor_name_en}'
table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'

result=write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30=(datetime.now() - timedelta(30)).strftime('%Y%m%d')
df=get_odps_sql_result_as_df(f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50""")

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    # 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count=len(all_products_df),
        additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )
else:
    # 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={
            "odps_table": table_name if 'table_name' in locals() else "unknown",
            "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )
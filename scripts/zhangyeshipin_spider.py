import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy_json,
    write_pandas_df_into_odps,
    logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
    create_spider_reporter,
)
import os

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

headers = {
    "STOREID": "3821371",
}
brand_name = "张业食品"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("zhangyeshipin_spider.py", brand_name)
competitor_name_en = "zhangyeshipin"

logging.info(f"{time_of_now}, headers:{headers}")

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
logging.info(f"time_of_now:{time_of_now},headers:{headers}")
# 获取所有类目列表：
body = {
    "storeId": 3821371,
    "includeAttributes": False,
    "includeAllProducts": False,
    "includeHotSale": True,
    "includeCombo": True,
    "isMultiCategory": True,
    "multiLevel": "",
    "includePresale": True,
    "includeGroupSale": True,
    "includeBargainSale": True,
    "includeSeckillSale": True,
    "includePeriod": True,
    "includeNew": True,
    "isRefresh": True,
    "v": 2,
    "hideProductByMode": True,
}

url = "https://wxservice-stg.pospal.cn/wxapi/product/categories"
categories = requests.post(url, headers=headers, json=body, verify=False).json()[
    "categories"
]
for category in categories:
    logging.info(
        f"{category['CategoryUid']}, {category['CategoryId']},{category['DisplayName']}"
    )


# 获取所有商品列表
p_body = {
    "storeId": 3821371,
    "tags": "",
    "brandUid": "",
    "key": "",
    "cUids": "1597041880147263124",
    "pageIdx": 0,
    "size": 100,
    "includeTag": True,
    "includeFuncTag": True,
    "ignoreCart": True,
    "isSeries": True,
    "pUids": "",
    "orderType": 0,
    "minprice": "",
    "maxprice": "",
    "times": False,
    "includeSale": 0,
    "checkCatSaleTime": True,
    "hideProductByMode": True,
    "nextCustomerCategoryUid": "10000",
}

product_list_all = []
for category in categories:
    CategoryUid = category["CategoryUid"]
    logging.info(
        category["CategoryUid"], category["CategoryId"], category["DisplayName"]
    )
    p_body["cUids"] = CategoryUid
    url = "https://wxservice-stg.pospal.cn/wxapi/product/listmulti"
    products = get_remote_data_with_proxy_json(url, headers=headers, json=p_body)[
        "data"
    ]
    product_list_all.extend(products)
    logging.info(f"{category['DisplayName']} 商品个数:{len(products)}")

product_list_all_df = pd.DataFrame(product_list_all)
product_list_all_df.head(10)
# 写入odps
product_list_all_df["competitor"] = brand_name
all_products_df = product_list_all_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_df"

result = write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50"""
)

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    # 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count=len(product_list_all),
        additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )
else:
    # 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={
            "odps_table": table_name if 'table_name' in locals() else "unknown",
            "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 写入odps\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json,logging\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN\",\n", "}\n", "brand_name = \"壹生鲜果-杭州\"\n", "competitor_name_en = \"yishengxiang<PERSON>\"\n", "\n", "logging.info(\"即将爬取:%s, %s\", brand_name, competitor_name_en)\n", "\n", "import requests\n", "\n", "def login_dinghuoyuan(username, password, code, uuid):\n", "    \"\"\"\n", "    Login to dinghuoyuan with username, password, code, and uuid.\n", "\n", "    Args:\n", "        username (str): username\n", "        password (str): password\n", "        code (str): code\n", "        uuid (str): uuid\n", "\n", "    Returns:\n", "        requests.Response: response object\n", "    \"\"\"\n", "    url = \"https://www.dinghuoyuan.com/c_concentrate/login/login4Customer\"\n", "    data = {\"username\": username, \"password\": password, \"code\": code, \"uuid\": uuid}\n", "    headers = {\n", "        \"content-type\": \"application/x-www-form-urlencoded\",\n", "    }\n", "    response = requests.post(url, data=data, headers=headers)\n", "    return response\n", "\n", "\n", "# Example usage:\n", "response = login_dinghuoyuan(\n", "    username=\"18258841203\",\n", "    password=\"wjf123456\",\n", "    code=\"0c1R8Ikl2Dl62f4Gwwll2QRJlQ0R8IkV\",\n", "    uuid=\"null\",\n", ")\n", "token = response.json().get(\"data\", {}).get(\"token\", \"\")\n", "logging.info(f\"login token:{token}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["headers = {\n", "    \"LoginSource\": \"WSC_APPLET\",\n", "    \"Authorization\": f\"Bearer {token}\",\n", "    \"User-Agent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/6.8.0(0x16080000) NetType/WIFI MiniProgramEnv/Mac MacWechat/WMPF MacWechat/3.8.7(0x13080712) XWEB/1191\",\n", "    \"TenantId\": \"1506987423156953090\",\n", "    \"Content-Type\": \"application/x-www-form-urlencoded\",\n", "    \"xweb_xhr\": \"1\",\n", "    \"AppId\": \"wx873dd045bfa773c7\",\n", "    \"version\": \"\",\n", "    \"Accept\": \"*/*\",\n", "    \"Sec-Fetch-Site\": \"cross-site\",\n", "    \"Sec-Fetch-Mode\": \"cors\",\n", "    \"Sec-Fetch-Dest\": \"empty\",\n", "    \"Referer\": \"https://servicewechat.com/wx873dd045bfa773c7/32/page-frame.html\",\n", "    \"Accept-Encoding\": \"gzip, deflate, br\",\n", "    \"Accept-Language\": \"en-US,en;q=0.9\",\n", "}\n", "cate_url = \"https://www.dinghuoyuan.com/c_shop/category/getCategoryTallies\"\n", "\n", "category_list_response = get_remote_data_with_proxy_json(url=cate_url, headers=headers)\n", "logging.info(f\"获取到的类目信息:{category_list_response}\")\n", "\n", "category_list = category_list_response.get(\"data\")\n", "\n", "logging.info(f\"category_list:{category_list}\")\n", "if category_list is None or len(category_list) <= 0:\n", "    logging.error(f\"获取到的category_list为空:{category_list_response}\")\n", "    exit(-1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import List, Dict, Any\n", "\n", "sub_category_list = []\n", "[\n", "    sub_category_list.extend(category.get(\"subcategoryTallies\"))\n", "    for category in category_list\n", "]\n", "sub_category_list_df = pd.DataFrame(sub_category_list)\n", "sub_category_list_df.head(10)\n", "\n", "\n", "def fetch_category_product(\n", "    category_id=\"1507954717705568257\", page=1\n", ") -> List[Dict[str, Any]]:\n", "    url = f\"https://www.dinghuoyuan.com/c_shop/goods/findPage?page={page}&size=50&categoryId={category_id}&headId=&keyword=&tallyId=\"\n", "    products = (\n", "        get_remote_data_with_proxy_json(url=url, headers=headers)\n", "        .get(\"data\", {})\n", "        .get(\"records\", [])\n", "    )\n", "    if len(products) >= 50:\n", "        logging.info(f\"类目有超过50个商品, 继续爬取...page:{page+1}\")\n", "        products_more = fetch_category_product(category_id=category_id, page=page + 1)\n", "        products.extend(products_more)\n", "    return products\n", "\n", "\n", "all_products = []\n", "for cate in sub_category_list:\n", "    cate_name = cate[\"subCategoryName\"]\n", "    products = fetch_category_product(category_id=cate[\"subCategoryId\"])\n", "    if len(products) <= 0:\n", "        logging.error(f\"爬取失败:{cate}\")\n", "        continue\n", "    for prod in products:\n", "        prod[\"categoryName\"] = cate_name\n", "    all_products.extend(products)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["all_products_exploded=[]\n", "\n", "for prod in all_products:\n", "    for item in prod.get(\"itemVOS\",[]):\n", "        new_item = {}\n", "        for key, value in item.items():\n", "            new_item[\"item_vo_\" + key] = value\n", "        new_item.update(prod)\n", "        del new_item[\"itemVOS\"]\n", "        all_products_exploded.append(new_item)\n", "\n", "all_products_df = pd.DataFrame(all_products_exploded)\n", "\n", "import re\n", "\n", "\n", "def camel_to_snake(name):\n", "    \"\"\"Convert camel case string to snake case string.\"\"\"\n", "    s1 = re.sub(\"(.)([A-Z][a-z]+)\", r\"\\1_\\2\", name)\n", "    return re.sub(\"([a-z0-9])([A-Z])\", r\"\\1_\\2\", s1).lower()\n", "\n", "\n", "def rename_columns_to_snake_case(df):\n", "    \"\"\"Rename DataFrame columns from camel case to snake case.\"\"\"\n", "    df.rename(columns=lambda x: camel_to_snake(x), inplace=True)\n", "\n", "\n", "rename_columns_to_snake_case(all_products_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "all_products_df.drop_duplicates(subset='goods_id', inplace=True)\n", "all_products_df=all_products_df.astype(str)\n", "all_products_df['competitor']=brand_name\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "logging.info(df.to_string())\n", "logging.info(f\"===new_record==={brand_name}, 商品数:{len(all_products_df)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}
# 多品牌/多地区爬虫处理指南

## 问题背景

在爬虫项目中，有一些特殊的脚本处理多个品牌或多个地区的数据，这些脚本的输出格式与标准的单品牌脚本不同，需要特殊处理。

## 典型的多品牌脚本模式

### 1. Join方式的多品牌输出
```python
# 示例：biaoguo_with_prop_spider.py
region_names = ["标果-杭州", "标果-长沙", "标果-广东", "标果-川渝"]
logger.info(f"===new_record==={','.join(region_names)}, 商品数:{result_cnt}")
```

### 2. 直接包含逗号的多品牌
```python
# 示例：youzan_spider.py
logger.info(f"===new_record===[优享鲜焙,料料活子], 商品数:{count}")
```

### 3. 变量形式的品牌名
```python
# 示例：某些动态品牌脚本
brand_list = get_brand_list()
logger.info(f"===new_record==={brand_list}, 商品数:{product_count}")
```

## 解决方案

### 1. 手动处理特殊脚本

对于复杂的多品牌脚本，建议手动更新：

#### 步骤1：添加导入
```python
from proxy_setup import (
    # ... 其他导入
    create_spider_reporter,
)
```

#### 步骤2：创建报告器
```python
# 为多品牌脚本创建报告器
spider_reporter = create_spider_reporter("script_name.py", "多品牌描述")
```

#### 步骤3：替换输出逻辑
```python
# 原有代码
# logger.info(f"===new_record==={','.join(region_names)}, 商品数:{result_cnt}")

# 新的结构化输出
if result_cnt > 0:
    spider_reporter.report_success(
        product_count=result_cnt,
        additional_info={
            "regions": region_names,
            "region_count": len(region_names),
            "processed_regions": ','.join(region_names)
        }
    )
else:
    spider_reporter.report_failure(
        error_message="未爬取到任何数据或所有地区都失败",
        error_type="no_data_or_all_regions_failed",
        additional_info={
            "attempted_regions": region_names,
            "region_count": len(region_names)
        }
    )
```

### 2. 使用专门的处理脚本

我们提供了 `handle_multi_brand_spiders.py` 脚本来自动处理一些常见的多品牌模式：

```bash
python handle_multi_brand_spiders.py
```

## 已处理的多品牌脚本

### 1. biaoguo_with_prop_spider.py
- **类型**: 多地区标果品牌
- **地区**: 杭州、长沙、广东、川渝
- **处理方式**: 手动更新
- **状态**: ✅ 已完成

### 2. biaoguo_spider_multi-region_new_app.py  
- **类型**: 标果工厂多地区
- **地区**: 长沙等
- **处理方式**: 手动更新
- **状态**: ✅ 已完成

### 3. youzan_spider.py
- **类型**: 有赞多品牌
- **品牌**: 优享鲜焙、料料活子
- **处理方式**: 手动更新
- **状态**: ✅ 已完成

## 多品牌脚本的特殊考虑

### 1. 品牌名称处理
```python
# 对于多品牌，建议在additional_info中详细记录
additional_info = {
    "brands": ["品牌1", "品牌2", "品牌3"],
    "brand_count": 3,
    "processed_brands": "品牌1,品牌2,品牌3"
}
```

### 2. 地区信息处理
```python
# 对于多地区，记录地区相关信息
additional_info = {
    "regions": region_names,
    "region_count": len(region_names),
    "processed_regions": ','.join(region_names),
    "successful_regions": successful_regions,
    "failed_regions": failed_regions
}
```

### 3. 错误处理
```python
# 多品牌/多地区的错误可能更复杂
if total_count == 0:
    spider_reporter.report_failure(
        error_message="所有品牌/地区都失败",
        error_type="all_brands_failed"
    )
elif total_count < expected_count:
    spider_reporter.report_partial_success(
        product_count=total_count,
        warning_message=f"部分品牌/地区失败，成功{success_count}/{total_brands}个"
    )
else:
    spider_reporter.report_success(product_count=total_count)
```

## 识别多品牌脚本的方法

### 1. 文件名特征
- 包含 `multi`, `region`, `多` 等关键词
- 包含 `with_prop` 等表示多属性的词
- 文件名较长，暗示复杂功能

### 2. 代码特征
- 使用 `join()` 连接品牌名
- 包含品牌或地区列表
- 循环处理多个数据源
- 输出中包含逗号分隔的多个名称

### 3. 输出特征
```python
# 典型的多品牌输出模式
"===new_record===品牌1,品牌2,品牌3, 商品数:123"
"===new_record==={','.join(brands)}, 商品数:{count}"
"===new_record===[品牌1,品牌2], 商品数:{total}"
```

## 测试多品牌脚本

### 1. 验证JSON输出
```bash
# 运行脚本并检查JSON输出
python scripts/biaoguo_with_prop_spider.py | grep "SPIDER_RESULT_JSON"
```

### 2. 验证向后兼容性
```bash
# 检查是否仍然输出原有格式
python scripts/biaoguo_with_prop_spider.py | grep "===new_record==="
```

### 3. 验证run_all_docker.sh解析
```bash
# 运行完整流程测试
FILE_TO_EXECUTE=biaoguo_with_prop_spider.py ./run_all_docker.sh
```

## 最佳实践

### 1. 命名规范
- 多品牌脚本的spider_reporter应该使用描述性的品牌名
- 例如："标果多地区"、"有赞多品牌"、"多地区配送"

### 2. 信息记录
- 在additional_info中详细记录品牌/地区信息
- 包含成功和失败的详细统计
- 记录处理时间和性能指标

### 3. 错误分类
- 使用具体的错误类型：`all_regions_failed`、`partial_regions_failed`
- 区分网络错误、数据错误、配置错误等

### 4. 监控友好
- 确保JSON输出包含足够的信息用于监控
- 保持与单品牌脚本一致的数据结构
- 便于后续的数据分析和报表生成

## 总结

多品牌/多地区爬虫脚本需要特殊处理，但通过合理的设计和实现，可以很好地集成到新的结构化输出系统中。关键是要：

1. 正确识别多品牌模式
2. 手动或半自动地进行更新
3. 充分利用additional_info记录详细信息
4. 保持向后兼容性
5. 进行充分的测试验证

{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*************, headers:{'uniacid': '2595', 'appType': 'mini', 'Referer': 'https://servicewechat.com/wx92c8f2cd458916b5/36/page-frame.html'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={'uniacid':'2595','appType':'mini','Referer':'https://servicewechat.com/wx92c8f2cd458916b5/36/page-frame.html',}\n", "brand_name='品尚坊'\n", "competitor_name_en='pinshangfang'\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["url:https://sso.dinghuo123.com/jwtToken, using proxy: http://***********:8gTcEKLs@**************:47240, headers:{}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@**************:47240', 'https': 'http://127.0.0.1:8888'}\n", "{'code': 200, 'data': {'createTime': None, 'create_time': *************, 'dbid': 2529095, 'enabledPassPort': True, 'expiresIn': None, 'expires_in': 2592000, 'hasMoreAccount': False, 'isPassPortAccount': False, 'isRegister': None, 'jwtToken': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY', 'loginUserName': None, 'ot': None, 'passPortAccount': '***********', 'passPortRealName': None, 'realName': None, 'serviceProduct': None, 'serviceUserType': None, 'userName': 'C1010336019', 'userType': 2}, 'message': '操作成功'}\n"]}], "source": ["# 先登录：\n", "login_json={\n", "  \"userName\": \"***********\",\n", "  \"password\": \"chenmo1125\",\n", "  \"companyName\": \"品尚坊水果批发配送\",\n", "  \"themeColor\": \"9EC741\",\n", "  \"groupDbid\": \"2529095\",\n", "  \"openId\": \"oFiQ45ZGN7TKZTZGPKs7RYN-a8KA\",\n", "  \"appId\": \"wxda0ba60b90801190\",\n", "  \"client\": \"miniApp\",\n", "  \"relayState\": \"/app/index.html\",\n", "  \"service\": \"ydh-agent\",\n", "  \"nickName\": \"\",\n", "  \"tag\": \"2529095\",\n", "  \"dbid\": \"2529095\",\n", "  \"subDbid\": \"\",\n", "  \"showBack\": \"1\",\n", "  \"backhash\": \"page%3DMine\",\n", "  \"loginLogoImage\": \"\",\n", "  \"loginBgImage\": \"\",\n", "  \"loginBtnColor\": \"#9EC741\",\n", "  \"loginBtnTextColor\": \"#FFFFFF\",\n", "  \"loginTextColor\": \"#666666\",\n", "  \"extraAction\": \"binding\",\n", "  \"limitClient\": 1,\n", "  \"isWechat\": <PERSON><PERSON><PERSON>,\n", "  \"loginServerType\": 1\n", "}\n", "\n", "url='https://sso.dinghuo123.com/jwtToken'\n", "token=get_remote_data_with_proxy_json(url=url, json=login_json)\n", "print(token)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["url:https://agent.dinghuo123.com/app/goods?action=productTypeWithImg&_=*************, using proxy: http://***********:8gTcEKLs@**************:47240, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@**************:47240', 'https': 'http://127.0.0.1:8888'}\n"]}, {"data": {"text/plain": ["{'code': 200,\n", " 'message': '操作成功',\n", " 'data': [{'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '香蕉/圣女果/黄瓜',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '椰子/龙眼',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '柠檬/金桔/百香果',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '葡萄黑提',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '莓果',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '芒果',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '瓜类',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '奇异果/猕猴桃',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '火龙果',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '苹果/梨/桃',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '柑橘橙柚',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '凤梨',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '杂类',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '精品水果',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '礼盒专区',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '售后专拍',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'},\n", "  {'mainImg': None,\n", "   'parentTypeId': 0,\n", "   'src': '',\n", "   'typeLevel': 1,\n", "   'imgUrl_200': '',\n", "   'leafTypeFlag': '1',\n", "   'subTypeTotalNum': 0,\n", "   'advanceCutSize': '',\n", "   'isDefault': '1',\n", "   'modifyTime': '',\n", "   'showPageType': 1,\n", "   'dbid': 2529095,\n", "   'mainImgUrl': '',\n", "   'name': '通用',\n", "   'id': ********,\n", "   'subTypeList': [],\n", "   'status': '0'}]}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["jwtToken=token['data'][\"jwtToken\"]\n", "headers={\n", "    \"referer\":f\"https://agent.dinghuo123.com/app/index?loginToken={jwtToken}&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6\",\n", "    \"authorization\":f\"{jwtToken}\",\n", "}\n", "category_list_url=\"https://agent.dinghuo123.com/app/goods?action=productTypeWithImg&_=*************\"\n", "\n", "category_list=get_remote_data_with_proxy_json(url=category_list_url, headers=headers)\n", "category_list"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: ************************************************, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n", "[{'isCombination': False, 'mulspecTemplateId': 0, 'enableOrderUnitNum': 1, 'minPromotionOrderPrice': -1, 'code': 'P2367864815', 'productVideoId': None, 'maxQuantity': 0.0, 'productSummaryId': *********, 'unitList': [{'barcode': '', 'costPrice': 0, 'createTime': None, 'dbid': 0, 'forbidden': '0', 'id': 0, 'marketPrice': 0, 'modifyTime': None, 'orderPrice': 0, 'productSummaryId': *********, 'rate': 1.0, 'returnOrderPrice': 0, 'unitId': 6791700, 'unitName': '件', 'version': 0}], 'productBrandName': None, 'mulspec3Value': '', 'price_multiunit': 0, 'isCart': False, 'productCount': 1, 'productName': '凤仙果小番茄2.5斤/件', 'PRODUCT_FPROPERTY8': '', 'PRODUCT_FPROPERTY7': '', 'PRODUCT_FPROPERTY9': '', 'minQuantity': 0.0, 'PRODUCT_FPROPERTY4': '', 'PRODUCT_FPROPERTY3': '', 'PRODUCT_FPROPERTY6': '', 'PRODUCT_FPROPERTY5': '', 'prepareCount': 0.0, 'unitId': 6791700, 'sortNum': -2147483648, 'PRODUCT_FPROPERTY2': '', 'PRODUCT_FPROPERTY1': '', 'sortNums': '-2147483648', 'mulspec3Name': '', 'minMarketPrice': 29.0, 'mulspec2Value': '', 'unitName': '件', 'inventoryCount': 99999.0, 'mulspec2Name': '', 'imgUrl': '', 'minOrderPrice': 29.0, 'productIds': '906800338', 'mulspec1Value': '', 'isPromote': '0', 'priceCount': 1, 'productOrderMultiple': None, 'PRODUCT_FPROPERTY10': '', 'mulspec1Name': ''}, {'isCombination': False, 'mulspecTemplateId': 0, 'enableOrderUnitNum': 1, 'minPromotionOrderPrice': -1, 'code': 'P3565055541', 'productVideoId': 0, 'maxQuantity': 0.0, 'productSummaryId': 747938179, 'unitList': [{'barcode': '', 'costPrice': 0, 'createTime': None, 'dbid': 0, 'forbidden': '0', 'id': 0, 'marketPrice': 0, 'modifyTime': None, 'orderPrice': 0, 'productSummaryId': 747938179, 'rate': 1.0, 'returnOrderPrice': 0, 'unitId': 46411901, 'unitName': '公斤', 'version': 0}], 'productBrandName': None, 'mulspec3Value': '', 'price_multiunit': 0, 'isCart': False, 'productCount': 1, 'productName': '千禧圣女果/公斤', 'PRODUCT_FPROPERTY8': '', 'PRODUCT_FPROPERTY7': '', 'PRODUCT_FPROPERTY9': '', 'minQuantity': 0.0, 'PRODUCT_FPROPERTY4': '', 'PRODUCT_FPROPERTY3': '', 'PRODUCT_FPROPERTY6': '', 'PRODUCT_FPROPERTY5': '', 'prepareCount': 0.5, 'unitId': 46411901, 'sortNum': -2147483648, 'PRODUCT_FPROPERTY2': '', 'PRODUCT_FPROPERTY1': '', 'sortNums': '-2147483648', 'mulspec3Name': '', 'minMarketPrice': 20.0, 'mulspec2Value': '', 'unitName': '公斤', 'inventoryCount': 99999.0, 'mulspec2Name': '', 'imgUrl': '', 'minOrderPrice': 20.0, 'productIds': '747938180', 'mulspec1Value': '', 'isPromote': '0', 'priceCount': 1, 'productOrderMultiple': 0.0, 'PRODUCT_FPROPERTY10': '', 'mulspec1Name': ''}, {'isCombination': False, 'mulspecTemplateId': 0, 'enableOrderUnitNum': 1, 'minPromotionOrderPrice': -1, 'code': 'P9664447627', 'productVideoId': 0, 'maxQuantity': 0.0, 'productSummaryId': 690697173, 'unitList': [{'barcode': '', 'costPrice': 0, 'createTime': None, 'dbid': 0, 'forbidden': '0', 'id': 0, 'marketPrice': 0, 'modifyTime': None, 'orderPrice': 0, 'productSummaryId': 690697173, 'rate': 1.0, 'returnOrderPrice': 0, 'unitId': 6791700, 'unitName': '件', 'version': 0}], 'productBrandName': None, 'mulspec3Value': '', 'price_multiunit': 0, 'isCart': False, 'productCount': 1, 'productName': '进口香蕉/件', 'PRODUCT_FPROPERTY8': '', 'PRODUCT_FPROPERTY7': '', 'PRODUCT_FPROPERTY9': '', 'minQuantity': 0.0, 'PRODUCT_FPROPERTY4': '', 'PRODUCT_FPROPERTY3': '', 'PRODUCT_FPROPERTY6': '', 'PRODUCT_FPROPERTY5': '', 'prepareCount': 0.0, 'unitId': 6791700, 'sortNum': -2147483648, 'PRODUCT_FPROPERTY2': '', 'PRODUCT_FPROPERTY1': '', 'imgUrl_cut': '2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.png', 'sortNums': '-2147483648', 'mulspec3Name': '', 'minMarketPrice': 140.0, 'mulspec2Value': '', 'unitName': '件', 'inventoryCount': 99999.0, 'imgUrl_480': '2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.png@480w_480h.png', 'imgUrl_200': '2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.png@200w_200h.png', 'mulspec2Name': '', 'imgUrl': '2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.png', 'minOrderPrice': 140.0, 'productIds': '690697174', 'mulspec1Value': '', 'imgUrl_60': '2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.png@60w_60h.png', 'isPromote': '0', 'priceCount': 1, 'productOrderMultiple': 0.0, 'PRODUCT_FPROPERTY10': '', 'mulspec1Name': ''}, {'isCombination': False, 'mulspecTemplateId': 0, 'enableOrderUnitNum': 1, 'minPromotionOrderPrice': -1, 'code': 'P9364248060', 'productVideoId': 0, 'maxQuantity': 0.0, 'productSummaryId': 673381180, 'unitList': [{'barcode': '', 'costPrice': 0, 'createTime': None, 'dbid': 0, 'forbidden': '0', 'id': 0, 'marketPrice': 0, 'modifyTime': None, 'orderPrice': 0, 'productSummaryId': 673381180, 'rate': 1.0, 'returnOrderPrice': 0, 'unitId': 6791700, 'unitName': '件', 'version': 0}], 'productBrandName': None, 'mulspec3Value': '', 'price_multiunit': 0, 'isCart': False, 'productCount': 1, 'productName': '圣女果毛重9-10斤装/件', 'PRODUCT_FPROPERTY8': '', 'PRODUCT_FPROPERTY7': '', 'PRODUCT_FPROPERTY9': '', 'minQuantity': 0.0, 'PRODUCT_FPROPERTY4': '', 'PRODUCT_FPROPERTY3': '', 'PRODUCT_FPROPERTY6': '', 'PRODUCT_FPROPERTY5': '', 'prepareCount': 0.0, 'unitId': 6791700, 'sortNum': -2147483648, 'PRODUCT_FPROPERTY2': '', 'PRODUCT_FPROPERTY1': '', 'imgUrl_cut': '2529095/<EMAIL>', 'sortNums': '-2147483648', 'mulspec3Name': '', 'minMarketPrice': 40.0, 'mulspec2Value': '', 'unitName': '件', 'inventoryCount': 99999.0, 'imgUrl_480': '2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.jpg@0-0-640-427a_480w_480h.jpg', 'imgUrl_200': '2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.jpg@0-0-640-427a_200w_200h.jpg', 'mulspec2Name': '', 'imgUrl': '2529095/<EMAIL>', 'minOrderPrice': 40.0, 'productIds': '673381182', 'mulspec1Value': '', 'imgUrl_60': '2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.jpg@0-0-640-427a_60w_60h.jpg', 'isPromote': '0', 'priceCount': 1, 'productOrderMultiple': 0.0, 'PRODUCT_FPROPERTY10': '', 'mulspec1Name': ''}, {'isCombination': False, 'mulspecTemplateId': 0, 'enableOrderUnitNum': 1, 'minPromotionOrderPrice': -1, 'code': 'P064231640', 'productVideoId': 0, 'maxQuantity': 0.0, 'productSummaryId': 673373015, 'unitList': [{'barcode': '', 'costPrice': 0, 'createTime': None, 'dbid': 0, 'forbidden': '0', 'id': 0, 'marketPrice': 0, 'modifyTime': None, 'orderPrice': 0, 'productSummaryId': 673373015, 'rate': 1.0, 'returnOrderPrice': 0, 'unitId': 46411901, 'unitName': '公斤', 'version': 0}], 'productBrandName': None, 'mulspec3Value': '', 'price_multiunit': 0, 'isCart': False, 'productCount': 1, 'productName': '圣女果/公斤', 'PRODUCT_FPROPERTY8': '', 'PRODUCT_FPROPERTY7': '', 'PRODUCT_FPROPERTY9': '', 'minQuantity': 0.0, 'PRODUCT_FPROPERTY4': '', 'PRODUCT_FPROPERTY3': '', 'PRODUCT_FPROPERTY6': '', 'PRODUCT_FPROPERTY5': '', 'prepareCount': 0.0, 'unitId': 46411901, 'sortNum': -2147483648, 'PRODUCT_FPROPERTY2': '', 'PRODUCT_FPROPERTY1': '', 'imgUrl_cut': '2529095/<EMAIL>', 'sortNums': '-2147483648', 'mulspec3Name': '', 'minMarketPrice': 12.0, 'mulspec2Value': '', 'unitName': '公斤', 'inventoryCount': 99999.0, 'imgUrl_480': '2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.jpeg@0-0-816-586a_480w_480h.jpeg', 'imgUrl_200': '2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.jpeg@0-0-816-586a_200w_200h.jpeg', 'mulspec2Name': '', 'imgUrl': '2529095/<EMAIL>', 'minOrderPrice': 12.0, 'productIds': '673373016', 'mulspec1Value': '', 'imgUrl_60': '2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.jpeg@0-0-816-586a_60w_60h.jpeg', 'isPromote': '0', 'priceCount': 1, 'productOrderMultiple': 0.0, 'PRODUCT_FPROPERTY10': '', 'mulspec1Name': ''}, {'isCombination': False, 'mulspecTemplateId': 0, 'enableOrderUnitNum': 1, 'minPromotionOrderPrice': -1, 'code': '28701', 'productVideoId': None, 'maxQuantity': 0.0, 'productSummaryId': 672534451, 'unitList': [{'barcode': '', 'costPrice': 0, 'createTime': None, 'dbid': 0, 'forbidden': '0', 'id': 0, 'marketPrice': 0, 'modifyTime': None, 'orderPrice': 0, 'productSummaryId': 672534451, 'rate': 1.0, 'returnOrderPrice': 0, 'unitId': 46411901, 'unitName': '公斤', 'version': 0}], 'productBrandName': None, 'mulspec3Value': '', 'price_multiunit': 0, 'isCart': False, 'productCount': 1, 'productName': '进口香蕉/公斤', 'PRODUCT_FPROPERTY8': '', 'PRODUCT_FPROPERTY7': '', 'PRODUCT_FPROPERTY9': '', 'minQuantity': 0.0, 'PRODUCT_FPROPERTY4': '', 'PRODUCT_FPROPERTY3': '', 'PRODUCT_FPROPERTY6': '', 'PRODUCT_FPROPERTY5': '', 'prepareCount': 6.5, 'unitId': 46411901, 'sortNum': -2147483648, 'PRODUCT_FPROPERTY2': '', 'PRODUCT_FPROPERTY1': '', 'imgUrl_cut': '2529095/f63c6ce0-c9d0-4a56-b11c-5f1e26547680.jpeg', 'sortNums': '-2147483648', 'mulspec3Name': '', 'minMarketPrice': 10.0, 'mulspec2Value': '', 'unitName': '公斤', 'inventoryCount': 99999.0, 'imgUrl_480': '2529095/f63c6ce0-c9d0-4a56-b11c-5f1e26547680.jpeg@480w_480h.jpeg', 'imgUrl_200': '2529095/f63c6ce0-c9d0-4a56-b11c-5f1e26547680.jpeg@200w_200h.jpeg', 'mulspec2Name': '', 'imgUrl': '2529095/f63c6ce0-c9d0-4a56-b11c-5f1e26547680.jpeg', 'minOrderPrice': 10.0, 'productIds': '672534437', 'mulspec1Value': '', 'imgUrl_60': '2529095/f63c6ce0-c9d0-4a56-b11c-5f1e26547680.jpeg@60w_60h.jpeg', 'isPromote': '0', 'priceCount': 1, 'productOrderMultiple': 0.0, 'PRODUCT_FPROPERTY10': '', 'mulspec1Name': ''}]\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: http://***********:8gTcEKLs@*************:49211, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@*************:49211', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: http://***********:8gTcEKLs@59.58.149.134:46137, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@59.58.149.134:46137', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: ************************************************, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: http://***********:8gTcEKLs@111.72.132.52:40146, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@111.72.132.52:40146', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: ************************************************, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: http://***********:8gTcEKLs@111.72.132.52:40146, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@111.72.132.52:40146', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: http://***********:8gTcEKLs@114.229.240.226:32321, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@114.229.240.226:32321', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: ************************************************, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: http://***********:8gTcEKLs@**************:47240, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@**************:47240', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: ************************************************, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: http://***********:8gTcEKLs@218.86.66.130:33003, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@218.86.66.130:33003', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: ************************************************, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: http://***********:8gTcEKLs@59.58.149.134:46137, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@59.58.149.134:46137', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: http://***********:8gTcEKLs@59.58.149.134:46137, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@59.58.149.134:46137', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: ************************************************, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: http://***********:8gTcEKLs@**************:47240, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': 'http://***********:8gTcEKLs@**************:47240', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=2&productTypeId=********&_=*************, using proxy: ************************************************, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n", "url:https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage=1&productTypeId=********&_=*************, using proxy: ************************************************, headers:{'referer': 'https://agent.dinghuo123.com/app/index?loginToken=eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY&client=miniApp&dbid=2529095&subDbid=&groupStoreDbid=&groupAccount=&groupDbid=2529095&fromPage=&openId=oFiQ45ZGN7TKZTZGPKs7RYN-a8KA&appId=wxda0ba60b90801190&groupType=0&time=*************&groupAccount=&version=1.8.6', 'authorization': 'eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.SR-u82Z7W-xbCtBbV6Mif5VGlsfcN6Kgm_81RTg5xdY'}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>isCombination</th>\n", "      <th>mulspecTemplateId</th>\n", "      <th>enableOrderUnitNum</th>\n", "      <th>minPromotionOrderPrice</th>\n", "      <th>code</th>\n", "      <th>productVideoId</th>\n", "      <th>maxQuantity</th>\n", "      <th>productSummaryId</th>\n", "      <th>unitList</th>\n", "      <th>productBrandName</th>\n", "      <th>...</th>\n", "      <th>productOrderMultiple</th>\n", "      <th>PRODUCT_FPROPERTY10</th>\n", "      <th>mulspec1Name</th>\n", "      <th>categoryName</th>\n", "      <th>imgUrl_cut</th>\n", "      <th>imgUrl_480</th>\n", "      <th>imgUrl_200</th>\n", "      <th>imgUrl_60</th>\n", "      <th>shoppingCartProductType</th>\n", "      <th>shoppingCart</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>P2367864815</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>*********</td>\n", "      <td>[{'barcode': '', 'costPrice': 0, 'createTime':...</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>香蕉/圣女果/黄瓜</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>P3565055541</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>747938179</td>\n", "      <td>[{'barcode': '', 'costPrice': 0, 'createTime':...</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>香蕉/圣女果/黄瓜</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>P9664447627</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>690697173</td>\n", "      <td>[{'barcode': '', 'costPrice': 0, 'createTime':...</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>香蕉/圣女果/黄瓜</td>\n", "      <td>2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.png</td>\n", "      <td>2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.p...</td>\n", "      <td>2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.p...</td>\n", "      <td>2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.p...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>P9364248060</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>673381180</td>\n", "      <td>[{'barcode': '', 'costPrice': 0, 'createTime':...</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>香蕉/圣女果/黄瓜</td>\n", "      <td>2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.j...</td>\n", "      <td>2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.j...</td>\n", "      <td>2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.j...</td>\n", "      <td>2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.j...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>P064231640</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>673373015</td>\n", "      <td>[{'barcode': '', 'costPrice': 0, 'createTime':...</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>香蕉/圣女果/黄瓜</td>\n", "      <td>2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.j...</td>\n", "      <td>2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.j...</td>\n", "      <td>2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.j...</td>\n", "      <td>2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.j...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 51 columns</p>\n", "</div>"], "text/plain": ["   isCombination  mulspecTemplateId  enableOrderUnitNum  \\\n", "0          False                  0                   1   \n", "1          False                  0                   1   \n", "2          False                  0                   1   \n", "3          False                  0                   1   \n", "4          False                  0                   1   \n", "\n", "   minPromotionOrderPrice         code  productVideoId  maxQuantity  \\\n", "0                      -1  P2367864815             NaN          0.0   \n", "1                      -1  P3565055541             0.0          0.0   \n", "2                      -1  P9664447627             0.0          0.0   \n", "3                      -1  P9364248060             0.0          0.0   \n", "4                      -1   P064231640             0.0          0.0   \n", "\n", "   productSummaryId                                           unitList  \\\n", "0         *********  [{'barcode': '', 'costPrice': 0, 'createTime':...   \n", "1         747938179  [{'barcode': '', 'costPrice': 0, 'createTime':...   \n", "2         690697173  [{'barcode': '', 'costPrice': 0, 'createTime':...   \n", "3         673381180  [{'barcode': '', 'costPrice': 0, 'createTime':...   \n", "4         673373015  [{'barcode': '', 'costPrice': 0, 'createTime':...   \n", "\n", "  productBrandName  ... productOrderMultiple  PRODUCT_FPROPERTY10  \\\n", "0             None  ...                  NaN                        \n", "1             None  ...                  0.0                        \n", "2             None  ...                  0.0                        \n", "3             None  ...                  0.0                        \n", "4             None  ...                  0.0                        \n", "\n", "   mulspec1Name  categoryName  \\\n", "0                   香蕉/圣女果/黄瓜   \n", "1                   香蕉/圣女果/黄瓜   \n", "2                   香蕉/圣女果/黄瓜   \n", "3                   香蕉/圣女果/黄瓜   \n", "4                   香蕉/圣女果/黄瓜   \n", "\n", "                                          imgUrl_cut  \\\n", "0                                                NaN   \n", "1                                                NaN   \n", "2   2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.png   \n", "3  2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.j...   \n", "4  2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.j...   \n", "\n", "                                          imgUrl_480  \\\n", "0                                                NaN   \n", "1                                                NaN   \n", "2  2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.p...   \n", "3  2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.j...   \n", "4  2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.j...   \n", "\n", "                                          imgUrl_200  \\\n", "0                                                NaN   \n", "1                                                NaN   \n", "2  2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.p...   \n", "3  2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.j...   \n", "4  2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.j...   \n", "\n", "                                           imgUrl_60  shoppingCartProductType  \\\n", "0                                                NaN                      NaN   \n", "1                                                NaN                      NaN   \n", "2  2529095/522e312d-b076-4473-86a4-80fd9acd0ec1.p...                      NaN   \n", "3  2529095/f4455a43-ae8a-4c31-bae4-1ab425f586b0.j...                      NaN   \n", "4  2529095/2f78a6ad-8032-4a1e-b0b2-c3b1fc084f41.j...                      NaN   \n", "\n", "  shoppingCart  \n", "0          NaN  \n", "1          NaN  \n", "2          NaN  \n", "3          NaN  \n", "4          NaN  \n", "\n", "[5 rows x 51 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_products_of_category(id=********, currentPage=1):\n", "    url = f\"https://agent.dinghuo123.com/app/goods?action=goodsSummary&pageSize=20&promotionStatus=2&productBrandIds=%5B%5D&tagIds=%5B%5D&orderby=0&sortType=0&currentPage={currentPage}&productTypeId={id}&_=*************\"\n", "    products=get_remote_data_with_proxy_json(url=url, headers=headers)\n", "    if products is None:\n", "        return []\n", "    products=products['data']['summaryList']\n", "    if products is None or len(products)<20:\n", "        return products\n", "    # 递归查询\n", "    products.extend(get_products_of_category(id, currentPage+1))\n", "    return products\n", "\n", "print(get_products_of_category())\n", "\n", "all_products=[]\n", "for cate in category_list['data']:\n", "    currentPage=1\n", "    sub_list=get_products_of_category(cate['id'], currentPage=currentPage)\n", "    if sub_list is None:\n", "        continue\n", "    if len(sub_list)>0:\n", "        for product in sub_list:\n", "            product['categoryName'] = cate['name']\n", "        all_products.extend(sub_list)\n", "\n", "\n", "all_products_df=pd.DataFrame(all_products)\n", "all_products_df.head(5)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["121"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["len(all_products_df)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["表不存在:summerfarm_ds.spider_pinshangfang_product_result_df\n", "成功写入odps:summerfarm_ds.spider_pinshangfang_product_result_df, partition_spec:ds=20240409,competitor_name=pinshangfang, attemp:0\n", "sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_pinshangfang_product_result_df\n", "                             where ds>='20240310' group by ds,competitor_name  order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240409</td>\n", "      <td>pinshangfang</td>\n", "      <td>121</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ds competitor_name  recods\n", "0  20240409    pinshangfang     121"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "all_products_df['competitor']=brand_name\n", "all_products_df=all_products_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name  order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
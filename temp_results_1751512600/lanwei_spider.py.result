{
    "file_name": "lanwei_spider.py",
    "brand_name": "lanwei",
    "exit_code": 0,
    "success_count": 0,
    "duration": 1,
    "timestamp": "2025-07-03 11:18:18",
    "last_output": "  File "/Users/<USER>/Documents/work@sf/codes/spiderman/./scripts/lanwei_spider.py", line 99     spider_reporter.report_success( IndentationError: unexpected indent ",
    "has_error_indicators": true,
    "status": "failure",
    "error_message": "",
    "warning_message": "",
    "log_file": "./temp_logs_1751512600/lanwei_spider.py.log"
}

{
    "file_name": "biaoguo_with_prop_spider.py",
    "brand_name": "标果多地区",
    "exit_code": 0,
    "success_count": 0,
    "duration": 46,
    "timestamp": "2025-07-03 11:17:53",
    "last_output": "SPIDER_RESULT_JSON:{"status": "failure", "spider_name": "biaoguo_with_prop_spider.py", "brand_name": "标果多地区", "product_count": 0, "error_message": "未爬取到任何数据或所有地区都失败", "error_type": "no_data_or_all_regions_failed", "start_time": "2025-07-03 11:17:08", "end_time": "2025-07-03 11:17:53", "duration_seconds": 44, "timestamp": "2025-07-03 11:17:53", "attempted_regions": ["标果-杭州", "标果-长沙", "标果-广东", "标果-川渝"], "region_count": 4} === 结束时间: Thu Jul  3 11:17:53 CST 2025 === === 退出码: 0 === ",
    "has_error_indicators": true,
    "status": "failure",
    "error_message": "未爬取到任何数据或所有地区都失败",
    "warning_message": "",
    "log_file": "./temp_logs_1751512600/biaoguo_with_prop_spider.py.log"
}

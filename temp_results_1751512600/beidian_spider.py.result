{
    "file_name": "beidian_spider.py",
    "brand_name": "beidian",
    "exit_code": 0,
    "success_count": 0,
    "duration": 3,
    "timestamp": "2025-07-03 11:16:44",
    "last_output": "  File "/Users/<USER>/Documents/work@sf/codes/spiderman/.venv/lib/python3.9/site-packages/requests/structures.py", line 52, in __getitem__     return self._store[key.lower()][1] KeyError: 'clerk_sid' ",
    "has_error_indicators": true,
    "status": "failure",
    "error_message": "",
    "warning_message": "",
    "log_file": "./temp_logs_1751512600/beidian_spider.py.log"
}

{
    "file_name": "beixiaoguo_spider.py",
    "brand_name": "beixiaoguo",
    "exit_code": 0,
    "success_count": 0,
    "duration": 0,
    "timestamp": "2025-07-03 11:16:44",
    "last_output": "  File "/Users/<USER>/Documents/work@sf/codes/spiderman/./scripts/beixiaoguo_spider.py", line 118     spider_reporter.report_success( IndentationError: unexpected indent ",
    "has_error_indicators": true,
    "status": "failure",
    "error_message": "",
    "warning_message": "",
    "log_file": "./temp_logs_1751512600/beixiaoguo_spider.py.log"
}

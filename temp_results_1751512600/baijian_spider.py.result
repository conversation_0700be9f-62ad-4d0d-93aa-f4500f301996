{
    "file_name": "baijian_spider.py",
    "brand_name": "百简",
    "exit_code": 0,
    "success_count": 53,
    "duration": 26,
    "timestamp": "2025-07-03 11:17:06",
    "last_output": "SPIDER_RESULT_JSON:{"status": "success", "spider_name": "baijian_spider.py", "brand_name": "百简", "product_count": 53, "start_time": "2025-07-03 11:16:43", "end_time": "2025-07-03 11:17:06", "duration_seconds": 22, "timestamp": "2025-07-03 11:17:06", "odps_table": "summerfarm_ds.spider_baijian_product_result_df", "partition": "ds=20250703,competitor_name=baijian", "recent_records": "          ds competitor_name  recods\n0   20250703         baijian     159\n1   20250702         baijian     265\n2   20250701         baijian     160\n3   20250630         baijian     317\n4   20250629         baijian     156\n5   20250628         baijian     156\n6   20250627         baijian     156\n7   20250626         baijian     156\n8   20250625         baijian     156\n9   20250624         baijian     364\n10  20250623         baijian     409\n11  20250622         baijian     151\n12  20250621         baijian     153\n13  20250620         baijian     255\n14  20250619         baijian     408\n15  20250618         baijian     103\n16  20250617         baijian     153\n17  20250616         baijian     154\n18  20250615         baijian     153\n19  20250614         baijian     153\n20  20250613         baijian     153\n21  20250612         baijian     153\n22  20250611         baijian     153\n23  20250610         baijian     153\n24  20250609         baijian     153\n25  20250608         baijian     153\n26  20250607         baijian     153\n27  20250606         baijian     151\n28  20250605         baijian     150\n29  20250604         baijian     150\n30  20250603         baijian     150"} === 结束时间: Thu Jul  3 11:17:06 CST 2025 === === 退出码: 0 === ",
    "has_error_indicators": false,
    "status": "success",
    "error_message": "",
    "warning_message": "",
    "log_file": "./temp_logs_1751512600/baijian_spider.py.log"
}

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 写入odps\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN\",\n", "}\n", "brand_name = \"鲜成派-成都\"\n", "competitor_name_en = \"xianchengpai\"\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cat_list_url = \"https://xinv4.youdawangluo.com/web/index.php?_mall_id=11463&r=api/cat/list&cat_id=&select_cat_id=\"\n", "cat_list = get_remote_data_with_proxy_json(url=cat_list_url)[\"data\"][\"list\"]\n", "\n", "\n", "def get_products_of_cat(cat={}, page=1):\n", "    print(f\"获取类目:{cat}\")\n", "    product_list_url = f\"https://xinv4.youdawangluo.com/web/index.php?_mall_id=11463&r=api/default/goods-list&page={page}&cat_id={cat['id']}\"\n", "    data = get_remote_data_with_proxy_json(url=product_list_url)[\"data\"]\n", "    print(data)\n", "    product_list = data[\"list\"]\n", "    if len(product_list) < data[\"pagination\"][\"totalCount\"]:\n", "        product_list.extend(get_products_of_cat(cat=cat, page=page + 1))\n", "    return product_list\n", "\n", "\n", "all_product_list = []\n", "for cat in cat_list:\n", "    all_product_list.extend(get_products_of_cat(cat))\n", "\n", "\n", "all_product_list_df = pd.DataFrame(all_product_list)\n", "all_product_list_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_product_detail_by_id(product_id=970592):\n", "    detail_url=f\"https://xinv4.youdawangluo.com/web/index.php?_mall_id=11463&r=api/goods/detail&id={product_id}&plugin=mall\"\n", "    return get_remote_data_with_proxy_json(url=detail_url)['data'][\"goods\"]\n", "\n", "all_sku_list=[]\n", "all_goods_ids={}\n", "for product in all_product_list:\n", "    if product['id'] in all_goods_ids:\n", "        print(f\"已经获取过了：{product['id']}\")\n", "        continue\n", "    product_detail=get_product_detail_by_id(product['id'])\n", "    attr=product_detail['attr']\n", "    all_goods_ids[product['id']]=True\n", "    for attr_detail in attr:\n", "        sku={}\n", "        for key, value in attr_detail.items():\n", "            sku[f\"sku_{key}\"]=value\n", "        sku.update(product_detail)\n", "        del sku[\"attr\"]\n", "        all_sku_list.append(sku)\n", "\n", "all_sku_list_df=pd.DataFrame(all_sku_list)\n", "all_sku_list_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "all_sku_list_df['competitor']=brand_name\n", "all_products_df=all_sku_list_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["http://***********:8gTcEKLs@************\n"]}], "source": ["import re\n", "\n", "string = \"****************************************\"\n", "masked_string = re.sub(r'\\d{11}', lambda match: '*' * len(match.group()), string)\n", "print(masked_string)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*************, headers:{'uniacid': '2595', 'appType': 'mini', 'Referer': 'https://weixin.dinghuo365.com/h5fw/', 'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}\n"]}], "source": ["# 写入odps\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"uniacid\": \"2595\",\n", "    \"appType\": \"mini\",\n", "    \"Referer\": \"https://weixin.dinghuo365.com/h5fw/\",\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1\",\n", "}\n", "brand_name = \"蓝微\"\n", "competitor_name_en = \"lanwei\"\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["url:https://weixin.dinghuo365.com/login/mobileLoginBySmsToWeChat.action?mobile=***********&clientType=client-android-000000&clientVersion=2.0.4&wxLoginType=h5&showRegister=0&password=*********, using proxy: *************************************************, headers:{}\n", "response status:200, proxy used:{'http': '*************************************************', 'https': 'http://127.0.0.1:8888'}\n", "new token:\n", "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJqd3QiLCJpYXQiOjE3MTI4MTQxMDcsInN1YiI6IntcImFwcElkXCI6XCJcIixcImNsaWVudFR5cGVcIjpcImNsaWVudC1hbmRyb2lkLTAwMDAwMFwiLFwiY3JlYXRlVGltZVwiOjE3MTI4MTQxMDc3MDMsXCJsb2dpblR5cGVcIjpcIlNPVVJDRV9UWVBFX0NMSUVOVFwiLFwib3BlbklkXCI6XCJcIixcInJlYWxSZWZyZXNoVG9rZW5FeHBpcmVNaW51dGVcIjo4NjQwMCxcInJlYWxUb2tlbkV4cGlyZU1pbnV0ZVwiOjQzMjAwLFwicmVmcmVzaFRva2VuRXhwaXJlSG91cnNcIjoxNDQwLFwidG9rZW5FeHBpcmVIb3Vyc1wiOjcyMCxcInVpZFwiOjQ5MDQ3NjY0NjM1NzI2OTAzMTV9IiwiaXNzIjoiNDkwNDc2NjQ2MzU3MjY5MDMxNV9fQ0xJRU5UIiwiZXhwIjoxNzE1NDA2MTA3fQ.MyJfRbe6WAy5dLRPPGNG3ir7qUQvKnowQlcxy3Gn1HI\n"]}], "source": ["# 先登录：\n", "\n", "token = get_remote_data_with_proxy_json(\n", "    url=\"https://weixin.dinghuo365.com/login/mobileLoginBySmsToWeChat.action?mobile=***********&clientType=client-android-000000&clientVersion=2.0.4&wxLoginType=h5&showRegister=0&password=*********\"\n", ")\n", "jwtToken = token[\"data\"][\"token\"]\n", "print(f\"new token:\\n{jwtToken}\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["url:https://weixin.dinghuo365.com/biz/std_mendian/pd/client/v1/queryPds.action, using proxy: *************************************************, headers:{}\n", "response status:200, proxy used:{'http': '*************************************************', 'https': 'http://127.0.0.1:8888'}\n", "未取完:1,继续...\n", "url:https://weixin.dinghuo365.com/biz/std_mendian/pd/client/v1/queryPds.action, using proxy: **********************************************, headers:{}\n", "response status:200, proxy used:{'http': '**********************************************', 'https': 'http://127.0.0.1:8888'}\n", "未取完:2,继续...\n", "url:https://weixin.dinghuo365.com/biz/std_mendian/pd/client/v1/queryPds.action, using proxy: ***********************************************, headers:{}\n", "response status:200, proxy used:{'http': '***********************************************', 'https': 'http://127.0.0.1:8888'}\n", "未取完:3,继续...\n", "url:https://weixin.dinghuo365.com/biz/std_mendian/pd/client/v1/queryPds.action, using proxy: ************************************************, headers:{}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n", "未取完:4,继续...\n", "url:https://weixin.dinghuo365.com/biz/std_mendian/pd/client/v1/queryPds.action, using proxy: ************************************************, headers:{}\n", "response status:200, proxy used:{'http': '************************************************', 'https': 'http://127.0.0.1:8888'}\n", "未取完:5,继续...\n", "url:https://weixin.dinghuo365.com/biz/std_mendian/pd/client/v1/queryPds.action, using proxy: **********************************************, headers:{}\n", "response status:200, proxy used:{'http': '**********************************************', 'https': 'http://127.0.0.1:8888'}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '169', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '7499525929302531969', 'barcode': '', 'unit_id': '7499525929302531969', 'pd_id': '4725148277493283979', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 0.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '9.5', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '7985287539193615898', 'barcode': '', 'unit_id': '7985287539193615898', 'pd_id': '8215724191887211946', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '10', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '6722209585543791632', 'barcode': '', 'unit_id': '6722209585543791632', 'pd_id': '7120032610902016321', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '54', 'unit_name': '板', 'is_base': '1', 'in_shopCart': '0', 'name': '板', 'width': None, 'id': '6990136563874795173', 'barcode': '', 'unit_id': '6990136563874795173', 'pd_id': '6563834908684802903', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '215', 'unit_name': '框', 'is_base': '1', 'in_shopCart': '0', 'name': '框', 'width': None, 'id': '7201084892128626778', 'barcode': '', 'unit_id': '7201084892128626778', 'pd_id': '5527777407481373046', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '5.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '1.0', 'weight_unit_code': '', 'unit_price': '114', 'unit_name': '中箱', 'is_base': '0', 'in_shopCart': '1', 'name': '中箱', 'width': None, 'id': '5883871079509131722', 'barcode': '', 'unit_id': '5883871079509131722', 'pd_id': '9037644936183609402', 'ratio': '5.00000000', 'height': None}\n", "{'sequ': 3.0, 'unit_ratio': '4.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '91.2', 'unit_name': '箱', 'is_base': '0', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '6023406724340972070', 'barcode': '', 'unit_id': '6023406724340972070', 'pd_id': '9037644936183609402', 'ratio': '4.00000000', 'height': None}\n", "{'sequ': 4.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '22.8', 'unit_name': '小包', 'is_base': '1', 'in_shopCart': '0', 'name': '小包', 'width': None, 'id': '6832153143334871811', 'barcode': '', 'unit_id': '6832153143334871811', 'pd_id': '9037644936183609402', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '5.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '137.5', 'unit_name': '中箱', 'is_base': '0', 'in_shopCart': '0', 'name': '中箱', 'width': None, 'id': '7092553563164546566', 'barcode': '', 'unit_id': '7092553563164546566', 'pd_id': '6526881238650860334', 'ratio': '5.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '4.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '110', 'unit_name': '箱', 'is_base': '0', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8498029582320495970', 'barcode': '', 'unit_id': '8498029582320495970', 'pd_id': '6526881238650860334', 'ratio': '4.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '27.5', 'unit_name': '小包', 'is_base': '1', 'in_shopCart': '0', 'name': '小包', 'width': None, 'id': '5472932358420194987', 'barcode': '', 'unit_id': '5472932358420194987', 'pd_id': '6526881238650860334', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 0.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '2.5', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '5286586338152827013', 'barcode': '', 'unit_id': '5286586338152827013', 'pd_id': '6885309087337275667', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '119', 'unit_name': '件', 'is_base': '1', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '6456443912468907611', 'barcode': '', 'unit_id': '6456443912468907611', 'pd_id': '8564732717168588347', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '10', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '5502014816384555402', 'barcode': '', 'unit_id': '5502014816384555402', 'pd_id': '8971496168123755911', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 0.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '9.9', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '9215982296831656378', 'barcode': '', 'unit_id': '9215982296831656378', 'pd_id': '5989524581893029070', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '8.9', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '4869171496892163852', 'barcode': '', 'unit_id': '4869171496892163852', 'pd_id': '7839952443416436223', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '98', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8525164379939744551', 'barcode': '', 'unit_id': '8525164379939744551', 'pd_id': '4773699854511739273', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '2.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '84', 'unit_name': '中包', 'is_base': '0', 'in_shopCart': '0', 'name': '中包', 'width': None, 'id': '5087204613046891842', 'barcode': '', 'unit_id': '5087204613046891842', 'pd_id': '6422988343214026466', 'ratio': '2.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '42', 'unit_name': '小包', 'is_base': '1', 'in_shopCart': '0', 'name': '小包', 'width': None, 'id': '8292315931740981676', 'barcode': '', 'unit_id': '8292315931740981676', 'pd_id': '6422988343214026466', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '188', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8536629747371252972', 'barcode': '', 'unit_id': '8536629747371252972', 'pd_id': '6219087314290790864', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '199', 'unit_name': '件', 'is_base': '1', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '5251681458645681810', 'barcode': '', 'unit_id': '5251681458645681810', 'pd_id': '6864795464946466323', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '2.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '56', 'unit_name': '中包', 'is_base': '0', 'in_shopCart': '0', 'name': '中包', 'width': None, 'id': '4701043604701094929', 'barcode': '', 'unit_id': '4701043604701094929', 'pd_id': '5832168888017084579', 'ratio': '2.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '28', 'unit_name': '小包', 'is_base': '1', 'in_shopCart': '0', 'name': '小包', 'width': None, 'id': '8867088699265546731', 'barcode': '', 'unit_id': '8867088699265546731', 'pd_id': '5832168888017084579', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '150', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '6276104262965659675', 'barcode': '', 'unit_id': '6276104262965659675', 'pd_id': '7880836457475828024', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '5', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '6994113960263252792', 'barcode': '', 'unit_id': '6994113960263252792', 'pd_id': '7178203688585058931', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '9.5', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '7020347787177226408', 'barcode': '', 'unit_id': '7020347787177226408', 'pd_id': '8910576476441341325', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '12', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '7868907175507964716', 'barcode': '', 'unit_id': '7868907175507964716', 'pd_id': '6369994604861463772', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '5.5', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '7857539788122821193', 'barcode': '', 'unit_id': '7857539788122821193', 'pd_id': '7237193661152546817', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '7', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '8964006197951514462', 'barcode': '', 'unit_id': '8964006197951514462', 'pd_id': '8238918264076119308', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '4.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '192', 'unit_name': '件', 'is_base': '0', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '8467962208840631472', 'barcode': '', 'unit_id': '8467962208840631472', 'pd_id': '6647914237299242875', 'ratio': '4.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '48', 'unit_name': '个', 'is_base': '1', 'in_shopCart': '0', 'name': '个', 'width': None, 'id': '9158174116439301692', 'barcode': '', 'unit_id': '9158174116439301692', 'pd_id': '6647914237299242875', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '2.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '84', 'unit_name': '件', 'is_base': '0', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '5751082007493369639', 'barcode': '', 'unit_id': '5751082007493369639', 'pd_id': '8641732918838690749', 'ratio': '2.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '42', 'unit_name': '个', 'is_base': '1', 'in_shopCart': '0', 'name': '个', 'width': None, 'id': '7588591261232759177', 'barcode': '', 'unit_id': '7588591261232759177', 'pd_id': '8641732918838690749', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '95', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '4956483789593741946', 'barcode': '', 'unit_id': '4956483789593741946', 'pd_id': '5283926514279896305', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '61', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '6419432344852011315', 'barcode': '', 'unit_id': '6419432344852011315', 'pd_id': '7482036890285565336', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '4.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '80', 'unit_name': '箱', 'is_base': '0', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8425556057511180500', 'barcode': '', 'unit_id': '8425556057511180500', 'pd_id': '4760407267285705994', 'ratio': '4.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '20', 'unit_name': '个', 'is_base': '1', 'in_shopCart': '0', 'name': '个', 'width': None, 'id': '4958224240408100069', 'barcode': '', 'unit_id': '4958224240408100069', 'pd_id': '4760407267285705994', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '21', 'unit_name': '件', 'is_base': '1', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '8767699658055097765', 'barcode': '', 'unit_id': '8767699658055097765', 'pd_id': '9168396631883442842', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '3.9', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '8992124664097993955', 'barcode': '', 'unit_id': '8992124664097993955', 'pd_id': '7487475941984157599', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '29', 'unit_name': '框', 'is_base': '1', 'in_shopCart': '0', 'name': '框', 'width': None, 'id': '4858869776044185225', 'barcode': '', 'unit_id': '4858869776044185225', 'pd_id': '5497587370306406374', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 3.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '3.9', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '5595554248322419041', 'barcode': '', 'unit_id': '5595554248322419041', 'pd_id': '4896572522899894350', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '4.8', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '5071329313074762908', 'barcode': '', 'unit_id': '5071329313074762908', 'pd_id': '9213297038515363556', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '2.5', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '4683067703875512527', 'barcode': '', 'unit_id': '4683067703875512527', 'pd_id': '4943662194020173810', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '6.3', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '6075393085652835100', 'barcode': '', 'unit_id': '6075393085652835100', 'pd_id': '7597054420720754969', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '4.2', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '6112227366409861210', 'barcode': '', 'unit_id': '6112227366409861210', 'pd_id': '9144434390506351494', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '89', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8630185683902993987', 'barcode': '', 'unit_id': '8630185683902993987', 'pd_id': '5619063831371973466', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '128', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8959894526252606364', 'barcode': '', 'unit_id': '8959894526252606364', 'pd_id': '5912973654926441440', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '19', 'unit_name': '个', 'is_base': '1', 'in_shopCart': '0', 'name': '个', 'width': None, 'id': '8401561138794341924', 'barcode': '', 'unit_id': '8401561138794341924', 'pd_id': '8165662646920077239', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '17.9', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '5772590496722570789', 'barcode': '', 'unit_id': '5772590496722570789', 'pd_id': '8137609219361584542', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '9.9', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '5607350593151245876', 'barcode': '', 'unit_id': '5607350593151245876', 'pd_id': '5474206093171944530', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '24.8', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '8266429492840806980', 'barcode': '', 'unit_id': '8266429492840806980', 'pd_id': '6669678861706228502', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '163', 'unit_name': '件', 'is_base': '1', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '8959407576472768420', 'barcode': '', 'unit_id': '8959407576472768420', 'pd_id': '5612530592992451387', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '145', 'unit_name': '件', 'is_base': '1', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '5583871144612252615', 'barcode': '', 'unit_id': '5583871144612252615', 'pd_id': '4812597329569687798', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '6.5', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '8897754373588627152', 'barcode': '', 'unit_id': '8897754373588627152', 'pd_id': '6293123658632029565', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '52', 'unit_name': '件', 'is_base': '1', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '8976360103431539218', 'barcode': '', 'unit_id': '8976360103431539218', 'pd_id': '6739780455171973542', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '68', 'unit_name': '件', 'is_base': '1', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '6565865176694674533', 'barcode': '', 'unit_id': '6565865176694674533', 'pd_id': '8516698577384666587', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '8', 'unit_name': '个', 'is_base': '1', 'in_shopCart': '0', 'name': '个', 'width': None, 'id': '8409922563132662345', 'barcode': '', 'unit_id': '8409922563132662345', 'pd_id': '6026741266852683475', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '78', 'unit_name': '小箱', 'is_base': '1', 'in_shopCart': '0', 'name': '小箱', 'width': None, 'id': '8461881381019456559', 'barcode': '', 'unit_id': '8461881381019456559', 'pd_id': '7855361366306172982', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '4.8', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '6670558626978276580', 'barcode': '', 'unit_id': '6670558626978276580', 'pd_id': '6714143451623295086', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '32', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8950136405463135120', 'barcode': '', 'unit_id': '8950136405463135120', 'pd_id': '6975302428209380690', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '4.5', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '9162337422941594264', 'barcode': '', 'unit_id': '9162337422941594264', 'pd_id': '8856513524482330978', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '2.9', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '9130074182808142659', 'barcode': '', 'unit_id': '9130074182808142659', 'pd_id': '6492456299861788706', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '8.9', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '4763885603644624827', 'barcode': '', 'unit_id': '4763885603644624827', 'pd_id': '7390503815327061764', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '289', 'unit_name': '包', 'is_base': '1', 'in_shopCart': '0', 'name': '包', 'width': None, 'id': '6210951445653293535', 'barcode': '', 'unit_id': '6210951445653293535', 'pd_id': '6701973896336637995', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '88', 'unit_name': '件', 'is_base': '1', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '5105819569433267609', 'barcode': '', 'unit_id': '5105819569433267609', 'pd_id': '8102869567233144654', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '168', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8522703765221263669', 'barcode': '', 'unit_id': '8522703765221263669', 'pd_id': '7614485972180215415', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '298', 'unit_name': '件', 'is_base': '1', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '5210948450082447997', 'barcode': '', 'unit_id': '5210948450082447997', 'pd_id': '8142343660315277704', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '369', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '9140818825343647985', 'barcode': '', 'unit_id': '9140818825343647985', 'pd_id': '9137374045470253806', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 0.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '179', 'unit_name': '桶', 'is_base': '1', 'in_shopCart': '0', 'name': '桶', 'width': None, 'id': '5099833430241276528', 'barcode': '', 'unit_id': '5099833430241276528', 'pd_id': '6950581749344340385', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '72', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8809979612236079513', 'barcode': '', 'unit_id': '8809979612236079513', 'pd_id': '5534849891619005183', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '330', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '9183863669882645803', 'barcode': '', 'unit_id': '9183863669882645803', 'pd_id': '8113186708334156838', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '28', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '8663249464734069428', 'barcode': '', 'unit_id': '8663249464734069428', 'pd_id': '4762037098390032531', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '8414373248635601729', 'barcode': '', 'unit_id': '8414373248635601729', 'pd_id': '7412753788802211030', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '7248228571474537672', 'barcode': '', 'unit_id': '7248228571474537672', 'pd_id': '7150850017271837973', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '23', 'unit_name': '袋', 'is_base': '1', 'in_shopCart': '0', 'name': '袋', 'width': None, 'id': '5499321368596236314', 'barcode': '', 'unit_id': '5499321368596236314', 'pd_id': '9048793834027343810', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '23', 'unit_name': '袋', 'is_base': '1', 'in_shopCart': '0', 'name': '袋', 'width': None, 'id': '8770552376762598726', 'barcode': '', 'unit_id': '8770552376762598726', 'pd_id': '7559399870979746667', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '12', 'unit_name': '袋', 'is_base': '1', 'in_shopCart': '0', 'name': '袋', 'width': None, 'id': '8846521293113139984', 'barcode': '', 'unit_id': '8846521293113139984', 'pd_id': '8000485355695888417', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '12', 'unit_name': '袋', 'is_base': '1', 'in_shopCart': '0', 'name': '袋', 'width': None, 'id': '7104793873785697929', 'barcode': '', 'unit_id': '7104793873785697929', 'pd_id': '4675896796830119030', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '11', 'unit_name': '袋', 'is_base': '1', 'in_shopCart': '0', 'name': '袋', 'width': None, 'id': '5890887949731135405', 'barcode': '', 'unit_id': '5890887949731135405', 'pd_id': '8430949958888446093', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '5824373743879061673', 'barcode': '', 'unit_id': '5824373743879061673', 'pd_id': '9137042926121660011', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '4892299209727524980', 'barcode': '', 'unit_id': '4892299209727524980', 'pd_id': '7200621843477876326', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '4.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '220', 'unit_name': '件', 'is_base': '0', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '8128014003381322518', 'barcode': '', 'unit_id': '8128014003381322518', 'pd_id': '6794862191987874250', 'ratio': '4.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '55', 'unit_name': '桶', 'is_base': '1', 'in_shopCart': '0', 'name': '桶', 'width': None, 'id': '8373315113377473188', 'barcode': '', 'unit_id': '8373315113377473188', 'pd_id': '6794862191987874250', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '4.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '168', 'unit_name': '箱', 'is_base': '0', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8270370144205957506', 'barcode': '', 'unit_id': '8270370144205957506', 'pd_id': '8483520766019151596', 'ratio': '4.00000000', 'height': None}\n", "{'sequ': 2.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '42', 'unit_name': '桶', 'is_base': '1', 'in_shopCart': '0', 'name': '桶', 'width': None, 'id': '7777386351623563221', 'barcode': '', 'unit_id': '7777386351623563221', 'pd_id': '8483520766019151596', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '15', 'unit_name': '板', 'is_base': '1', 'in_shopCart': '0', 'name': '板', 'width': None, 'id': '9202269085920691774', 'barcode': '', 'unit_id': '9202269085920691774', 'pd_id': '8671182338067491348', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '320', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8361616348248047788', 'barcode': '', 'unit_id': '8361616348248047788', 'pd_id': '7470657931195223945', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '355', 'unit_name': '袋', 'is_base': '1', 'in_shopCart': '0', 'name': '袋', 'width': None, 'id': '7733478135311959874', 'barcode': '', 'unit_id': '7733478135311959874', 'pd_id': '9198288539561815426', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 0.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '275', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '6333408132081809934', 'barcode': '', 'unit_id': '6333408132081809934', 'pd_id': '5773898656442825754', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '30', 'unit_name': '桶', 'is_base': '1', 'in_shopCart': '0', 'name': '桶', 'width': None, 'id': '7689069978590545733', 'barcode': '', 'unit_id': '7689069978590545733', 'pd_id': '8466807537199093127', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '62', 'unit_name': '罐', 'is_base': '1', 'in_shopCart': '0', 'name': '罐', 'width': None, 'id': '5004212542756298495', 'barcode': '', 'unit_id': '5004212542756298495', 'pd_id': '4760133747272627630', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '28', 'unit_name': '桶', 'is_base': '1', 'in_shopCart': '0', 'name': '桶', 'width': None, 'id': '6953879918288197360', 'barcode': '', 'unit_id': '6953879918288197360', 'pd_id': '6699418925959643045', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '28', 'unit_name': '桶', 'is_base': '1', 'in_shopCart': '0', 'name': '桶', 'width': None, 'id': '4691026558553944709', 'barcode': '', 'unit_id': '4691026558553944709', 'pd_id': '4884573652794625866', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '113', 'unit_name': '袋', 'is_base': '1', 'in_shopCart': '0', 'name': '袋', 'width': None, 'id': '7180118866364516186', 'barcode': '', 'unit_id': '7180118866364516186', 'pd_id': '7939722455944355206', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '125', 'unit_name': '袋', 'is_base': '1', 'in_shopCart': '0', 'name': '袋', 'width': None, 'id': '8706669356491688922', 'barcode': '', 'unit_id': '8706669356491688922', 'pd_id': '6353372858433605813', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '208', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '8037015069022222503', 'barcode': '', 'unit_id': '8037015069022222503', 'pd_id': '6948549653468134561', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '30', 'unit_name': '桶', 'is_base': '1', 'in_shopCart': '0', 'name': '桶', 'width': None, 'id': '5801815319597147331', 'barcode': '', 'unit_id': '5801815319597147331', 'pd_id': '7112862622524338818', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '185', 'unit_name': '箱', 'is_base': '1', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '7975699948231468689', 'barcode': '', 'unit_id': '7975699948231468689', 'pd_id': '8663429430326437990', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '8918117981332753133', 'barcode': '', 'unit_id': '8918117981332753133', 'pd_id': '7039025524718615195', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '5759908916803260320', 'barcode': '', 'unit_id': '5759908916803260320', 'pd_id': '5802925283830277485', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '7348373249020364409', 'barcode': '', 'unit_id': '7348373249020364409', 'pd_id': '7614032511234228034', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '5500182753167130058', 'barcode': '', 'unit_id': '5500182753167130058', 'pd_id': '8529065907897138238', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '8224665931704609088', 'barcode': '', 'unit_id': '8224665931704609088', 'pd_id': '7776055329262794187', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '5436808034127625656', 'barcode': '', 'unit_id': '5436808034127625656', 'pd_id': '6193088869813546153', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '7026336304052054124', 'barcode': '', 'unit_id': '7026336304052054124', 'pd_id': '8575128352738234567', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '20', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '7571256339160822140', 'barcode': '', 'unit_id': '7571256339160822140', 'pd_id': '7976659277963890530', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '8747400936231345984', 'barcode': '', 'unit_id': '8747400936231345984', 'pd_id': '6033488123748750425', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '7694282764932946103', 'barcode': '', 'unit_id': '7694282764932946103', 'pd_id': '8143095426582987698', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '14', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '8899479942363292228', 'barcode': '', 'unit_id': '8899479942363292228', 'pd_id': '8697290008478557407', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '23', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '8406495332658441859', 'barcode': '', 'unit_id': '8406495332658441859', 'pd_id': '7552205791058798726', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '23', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '5757369971725599705', 'barcode': '', 'unit_id': '5757369971725599705', 'pd_id': '4924229234547530436', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '24.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '432', 'unit_name': '箱', 'is_base': '0', 'in_shopCart': '0', 'name': '箱', 'width': None, 'id': '7565767373951161920', 'barcode': '', 'unit_id': '7565767373951161920', 'pd_id': '5955310462495624214', 'ratio': '24.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '18', 'unit_name': '包', 'is_base': '1', 'in_shopCart': '0', 'name': '包', 'width': None, 'id': '7174573155358748305', 'barcode': '', 'unit_id': '7174573155358748305', 'pd_id': '5955310462495624214', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '9', 'unit_name': '盒', 'is_base': '1', 'in_shopCart': '0', 'name': '盒', 'width': None, 'id': '8384635306503742076', 'barcode': '', 'unit_id': '8384635306503742076', 'pd_id': '8715075537938650026', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '115', 'unit_name': '件', 'is_base': '1', 'in_shopCart': '0', 'name': '件', 'width': None, 'id': '4674588666366338373', 'barcode': '', 'unit_id': '4674588666366338373', 'pd_id': '5616519770756144908', 'ratio': '1.00000000', 'height': None}\n", "{'sequ': 1.0, 'unit_ratio': '1.00000000', 'length': None, 'weight': None, 'in_shop_cart_count': '0.0', 'weight_unit_code': '', 'unit_price': '4.8', 'unit_name': '斤', 'is_base': '1', 'in_shopCart': '0', 'name': '斤', 'width': None, 'id': '8345484557609808561', 'barcode': '', 'unit_id': '8345484557609808561', 'pd_id': '8638173504675307107', 'ratio': '1.00000000', 'height': None}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sku_sequ</th>\n", "      <th>sku_unit_ratio</th>\n", "      <th>sku_length</th>\n", "      <th>sku_weight</th>\n", "      <th>sku_in_shop_cart_count</th>\n", "      <th>sku_weight_unit_code</th>\n", "      <th>sku_unit_price</th>\n", "      <th>sku_unit_name</th>\n", "      <th>sku_is_base</th>\n", "      <th>sku_in_shopCart</th>\n", "      <th>...</th>\n", "      <th>brand_id</th>\n", "      <th>unit_name</th>\n", "      <th>big_samll_unit_can_use_num</th>\n", "      <th>name</th>\n", "      <th>prop_values</th>\n", "      <th>short_name</th>\n", "      <th>bind_mz_promotion_id</th>\n", "      <th>quota_in_price</th>\n", "      <th>quota_input_num</th>\n", "      <th>promotion</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>1.00000000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td>169</td>\n", "      <td>箱</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>6489320774649852113</td>\n", "      <td>箱</td>\n", "      <td>0箱</td>\n", "      <td>红壳鸡蛋  大码</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.0</td>\n", "      <td>1.00000000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td>9.5</td>\n", "      <td>盒</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>6489320774649852113</td>\n", "      <td>盒</td>\n", "      <td>0盒</td>\n", "      <td>丹东红颜草莓24颗（盒）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.0</td>\n", "      <td>1.00000000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td>10</td>\n", "      <td>盒</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>6489320774649852113</td>\n", "      <td>盒</td>\n", "      <td>0盒</td>\n", "      <td>丹东红颜草莓 20颗（盒）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.0</td>\n", "      <td>1.00000000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td>54</td>\n", "      <td>板</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>6489320774649852113</td>\n", "      <td>板</td>\n", "      <td>0板</td>\n", "      <td>红颜草莓 中果（板装 ）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.0</td>\n", "      <td>1.00000000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td>215</td>\n", "      <td>框</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>6489320774649852113</td>\n", "      <td>框</td>\n", "      <td>0框</td>\n", "      <td>越南大青芒芒果特价量贩装（框装）约4.5元/斤</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1.0</td>\n", "      <td>5.00000000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1.0</td>\n", "      <td></td>\n", "      <td>114</td>\n", "      <td>中箱</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>6489320774649852113</td>\n", "      <td>小包</td>\n", "      <td>0小包</td>\n", "      <td>越南大青芒芒果 （二级）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3.0</td>\n", "      <td>4.00000000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td>91.2</td>\n", "      <td>箱</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>6489320774649852113</td>\n", "      <td>小包</td>\n", "      <td>0小包</td>\n", "      <td>越南大青芒芒果 （二级）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>4.0</td>\n", "      <td>1.00000000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td>22.8</td>\n", "      <td>小包</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>6489320774649852113</td>\n", "      <td>小包</td>\n", "      <td>0小包</td>\n", "      <td>越南大青芒芒果 （二级）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1.0</td>\n", "      <td>5.00000000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td>137.5</td>\n", "      <td>中箱</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>6489320774649852113</td>\n", "      <td>小包</td>\n", "      <td>0小包</td>\n", "      <td>越南大青芒芒果 （一级）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>1.0</td>\n", "      <td>4.00000000</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0.0</td>\n", "      <td></td>\n", "      <td>110</td>\n", "      <td>箱</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>6489320774649852113</td>\n", "      <td>小包</td>\n", "      <td>0小包</td>\n", "      <td>越南大青芒芒果 （一级）</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 53 columns</p>\n", "</div>"], "text/plain": ["   sku_sequ sku_unit_ratio sku_length sku_weight sku_in_shop_cart_count  \\\n", "0       1.0     1.00000000       None       None                    0.0   \n", "1       0.0     1.00000000       None       None                    0.0   \n", "2       1.0     1.00000000       None       None                    0.0   \n", "3       1.0     1.00000000       None       None                    0.0   \n", "4       1.0     1.00000000       None       None                    0.0   \n", "5       1.0     5.00000000       None       None                    1.0   \n", "6       3.0     4.00000000       None       None                    0.0   \n", "7       4.0     1.00000000       None       None                    0.0   \n", "8       1.0     5.00000000       None       None                    0.0   \n", "9       1.0     4.00000000       None       None                    0.0   \n", "\n", "  sku_weight_unit_code sku_unit_price sku_unit_name sku_is_base  \\\n", "0                                 169             箱           1   \n", "1                                 9.5             盒           1   \n", "2                                  10             盒           1   \n", "3                                  54             板           1   \n", "4                                 215             框           1   \n", "5                                 114            中箱           0   \n", "6                                91.2             箱           0   \n", "7                                22.8            小包           1   \n", "8                               137.5            中箱           0   \n", "9                                 110             箱           0   \n", "\n", "  sku_in_shopCart  ...             brand_id unit_name  \\\n", "0               0  ...  6489320774649852113         箱   \n", "1               0  ...  6489320774649852113         盒   \n", "2               0  ...  6489320774649852113         盒   \n", "3               0  ...  6489320774649852113         板   \n", "4               0  ...  6489320774649852113         框   \n", "5               1  ...  6489320774649852113        小包   \n", "6               0  ...  6489320774649852113        小包   \n", "7               0  ...  6489320774649852113        小包   \n", "8               0  ...  6489320774649852113        小包   \n", "9               0  ...  6489320774649852113        小包   \n", "\n", "  big_samll_unit_can_use_num                     name prop_values short_name  \\\n", "0                         0箱                 红壳鸡蛋  大码                          \n", "1                         0盒             丹东红颜草莓24颗（盒）                          \n", "2                         0盒            丹东红颜草莓 20颗（盒）                          \n", "3                         0板             红颜草莓 中果（板装 ）                          \n", "4                         0框  越南大青芒芒果特价量贩装（框装）约4.5元/斤                          \n", "5                        0小包             越南大青芒芒果 （二级）                          \n", "6                        0小包             越南大青芒芒果 （二级）                          \n", "7                        0小包             越南大青芒芒果 （二级）                          \n", "8                        0小包             越南大青芒芒果 （一级）                          \n", "9                        0小包             越南大青芒芒果 （一级）                          \n", "\n", "  bind_mz_promotion_id quota_in_price quota_input_num promotion  \n", "0                                                   0         0  \n", "1                                                   0         0  \n", "2                                                   0         0  \n", "3                                                   0         0  \n", "4                                                   0         0  \n", "5                                                   0         0  \n", "6                                                   0         0  \n", "7                                                   0         0  \n", "8                                                   0         0  \n", "9                                                   0         0  \n", "\n", "[10 rows x 53 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "\n", "\n", "all_products=[]\n", "def get_products_by_page(page=1):\n", "    url = \"https://weixin.dinghuo365.com/biz/std_mendian/pd/client/v1/queryPds.action\"\n", "    data = {\n", "        \"orderType\": \"MERP\",\n", "        \"page\": page,\n", "        \"rows\": 20,\n", "        \"class_id\": \"-1\",\n", "        \"period\": \"all\",\n", "        \"brand\": \"\",\n", "        \"brandIds\": \"\",\n", "        \"orderByField\": \"\",\n", "        \"sortWay\": \"asc\",\n", "    }\n", "    cookies = {\n", "        \"sourceType\": \"CLIENT\",\n", "        \"WQSESSIONID\": \"27E4A77DA514A0ACFBC79B217CA0A03D.10\",\n", "        \"x-token\": jwtT<PERSON>,\n", "        \"tenantId\": \"6489320774649852103\",\n", "    }\n", "\n", "    products = get_remote_data_with_proxy_json(url=url, json=data, cookies=cookies)[\n", "        \"data\"\n", "    ][\"products\"]\n", "    if products is not None and len(products) >= 20:\n", "        print(f\"未取完:{page},继续...\")\n", "        sub_list = get_products_by_page(page=page + 1)\n", "        if len(sub_list) > 0:\n", "            products.extend(sub_list)\n", "    return products\n", "\n", "all_products=get_products_by_page(1)\n", "all_sku_list=[]\n", "for spu in all_products:\n", "    for unit_info in spu['unit_info']:\n", "        print(unit_info)\n", "        sku_info={}\n", "        for key,value in unit_info.items():\n", "            sku_info[f\"sku_{key}\"]=value\n", "        sku_info.update(spu)\n", "        del sku_info['unit_info']\n", "        all_sku_list.append(sku_info)\n", "\n", "len(all_sku_list)\n", "all_sku_list_df=pd.DataFrame(all_sku_list)\n", "all_sku_list_df.head(10)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'price', 'competitor_name', 'sku_unit_id', 'bind_mz_promotion_id', 'sku_unit_ratio', 'prop_values', 'competitor', 'sku_is_base', 'is_promotion_pd2', 'sku_width', 'rebate', 'quota_out_price', 'sku_weight', 'code', 'picture_full', 'mini_order_unit_name', 'name_spec', 'sku_length', 'unit_name', 'name', 'quota_in_price', 'spec', 'sku_barcode', 'sku_height', 'sku_weight_unit_code', 'ds', 'quota_base_unit_num', 'sku_name', 'sku_unit_price', 'quota_unit_name', 'spider_fetch_time', 'quota_input_num', 'big_samll_unit_can_use_num', 'class_id', 'base_unit_price', 'promotion', 'price_priority', 'quota_unit_ratio', 'unit_ratio', 'picture', 'mini_order_quantity', 'sku_id', 'mini_order_unit_id', 'brand_id', 'short_name', 'pd_id', 'sku_unit_name', 'sku_in_shopcart', 'item_type', 'sku_pd_id', 'sku_sequ', 'quota_unit_id', 'quota_id', 'sku_in_shop_cart_count', 'unit_id', 'quota_can_use_num', 'sku_ratio'}\n", "成功写入odps:summerfarm_ds.spider_lanwei_product_result_df, partition_spec:ds=20240411,competitor_name=lanwei, attemp:0\n", "sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_lanwei_product_result_df\n", "                             where ds>='20240312' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240411</td>\n", "      <td>lanwei</td>\n", "      <td>348</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ds competitor_name  recods\n", "0  20240411          lanwei     348"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "all_sku_list_df['competitor']=brand_name\n", "all_products_df=all_sku_list_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
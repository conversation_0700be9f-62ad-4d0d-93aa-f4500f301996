{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "1708251541235, headers:{'appCode': 'wxc0416f9c9c965355', 'oemId': '100188', 'merchantId': '100001', 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7', 'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}\n"]}], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime,timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS,DataFrame\n", "from odps.accounts import StsAccount\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID']\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']\n", "THREAD_CNT = int(os.environ.get('THREAD_CNT', 20))\n", "\n", "print(f\"Thread count: {THREAD_CNT}\")\n", "\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "hints={'odps.sql.hive.compatible':True,'odps.sql.type.system.odps2':True}\n", "def get_odps_sql_result_as_df(sql):\n", "    instance=odps.execute_sql(sql, hints=hints)\n", "    instance.wait_for_success()\n", "    pd_df=None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "timestamp_of_now=int(datetime.now().timestamp())*1000+235\n", "\n", "headers={'appCode':'wxc0416f9c9c965355',\n", "         'oemId':'100188',\n", "         'merchantId':'100001',\n", "         'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "         'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'}\n", "brand_name='橘祥家'\n", "competitor_name_en='juxiangjia'\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 先登录\n", "\n", "获取token并保存"]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sessionid: dkgoojm9mbldkhvzvt6zn1slumvy7sw1\n", "response cookie:{'sessionid': 'dkgoojm9mbldkhvzvt6zn1slumvy7sw1'}\n", "{'code': 0, 'msg': '登录成功', 'data': {'user_id': 3003009}} 登录成功\n"]}], "source": ["# 登录\n", "from urllib.parse import unquote\n", "\n", "url='https://bshop.guanmai.cn/login'\n", "login_response=requests.post(url, headers=headers, data={'username':'17729941198', 'password':'aa123456'})\n", "\n", "\n", "after_login_cookie={}\n", "# Print all the cookies set by the server\n", "for cookie in login_response.cookies:\n", "    print(f'{cookie.name}: {cookie.value}')\n", "    after_login_cookie[cookie.name]=cookie.value\n", "\n", "\n", "print(f\"response cookie:{after_login_cookie}\")\n", "print(login_response.json(),unquote(login_response.json()['msg']))"]}, {"cell_type": "code", "execution_count": 123, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<!doctype html><html><head><meta charset=\"UTF-8\"/><meta name=\"format-detection\" content=\"telephone=n\n", "new sessionid:dkgoojm9mbldkhvzvt6zn1slumvy7sw1\n"]}], "source": ["after_login_cookie.update({'cms_key':'cygyl','group_id':'3252'})\n", "after_login_cookie\n", "\n", "sessionid=after_login_cookie['sessionid']\n", "\n", "url = 'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp=1706286340876'\n", "\n", "headers = {\n", "    'authority': 'bshop.guanmai.cn',\n", "    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',\n", "    'accept-language': 'en-US,en;q=0.9',\n", "    'cookie': f'cms_key=cygyl; group_id=3252; sessionid={sessionid}; gr_user_id=62c026d8-a829-40c7-823f-d7e38bf255d6; 9beedda875b5420f_gr_session_id=2a97577a-00ae-45a7-8392-4cf0d0fde7cb; 9beedda875b5420f_gr_session_id_sent_vst=2a97577a-00ae-45a7-8392-4cf0d0fde7cb',\n", "    'referer': 'https://bshop.guanmai.cn/v587/?cms_key=cygyl&timestamp=1706286034360',\n", "    'sec-fetch-dest': 'document',\n", "    'sec-fetch-mode': 'navigate',\n", "    'sec-fetch-site': 'same-origin',\n", "    'sec-fetch-user': '?1',\n", "    'upgrade-insecure-requests': '1',\n", "    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'\n", "}\n", "\n", "response = requests.get(url, headers=headers)\n", "\n", "print(response.text[0:100])\n", "\n", "print(f\"new sessionid:{sessionid}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取一级类目列表"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'childList': [{'id': 1300185, 'logo': '', 'name': '鸡肉'}],\n", "  'id': 1300181,\n", "  'logo': '',\n", "  'name': '圣农',\n", "  'type': 2},\n", " {'childList': [{'id': 1294770, 'name': '熊猫'}],\n", "  'id': 1281131,\n", "  'logo': '',\n", "  'name': '奶制品',\n", "  'type': 2},\n", " {'childList': [{'id': 1295450, 'logo': '', 'name': '品名'}],\n", "  'id': 1295448,\n", "  'logo': '',\n", "  'name': '糖霜',\n", "  'type': 2},\n", " {'childList': [{'id': 1295449, 'logo': '', 'name': '新良'},\n", "   {'id': 1296929, 'logo': '', 'name': '啄木鸟'}],\n", "  'id': 1295447,\n", "  'logo': '',\n", "  'name': '面粉',\n", "  'type': 2},\n", " {'childList': [{'id': 1285118, 'name': '1KG 24种口味'}],\n", "  'id': 1281129,\n", "  'name': '果茸',\n", "  'type': 2},\n", " {'childList': [{'id': 1285042, 'logo': '', 'name': '进口洋酒 '}],\n", "  'id': 1281133,\n", "  'name': '酒馆',\n", "  'type': 2},\n", " {'childList': [{'id': 1281161,\n", "    'logo': '',\n", "    'name': '法芙娜可可粉1kg 法国Valrhona100%巧克力粉'}],\n", "  'id': 1280788,\n", "  'logo': '',\n", "  'name': '可可粉 巧克力',\n", "  'type': 2},\n", " {'childList': [{'id': 1280759, 'logo': '', 'name': '轻便型'}],\n", "  'id': 1280757,\n", "  'name': '品质工具',\n", "  'type': 2},\n", " {'childList': [], 'id': 1300184, 'logo': '', 'name': '肉类', 'type': 2},\n", " {'childList': [{'id': 1295870, 'logo': '', 'name': '原料'}],\n", "  'id': 1295869,\n", "  'logo': '',\n", "  'name': '烘焙辅料',\n", "  'type': 2},\n", " {'childList': [{'id': 1280779, 'name': '纯牛奶'}],\n", "  'id': 1280778,\n", "  'name': '<PERSON><PERSON><PERSON>/雀巢',\n", "  'type': 2},\n", " {'childList': [{'id': 1296193, 'logo': '', 'name': '酱料'}],\n", "  'id': 1296192,\n", "  'logo': '',\n", "  'name': '圣兹',\n", "  'type': 2},\n", " {'childList': [{'id': 1295428, 'logo': '', 'name': '配料'}],\n", "  'id': 1295427,\n", "  'logo': '',\n", "  'name': '椰蓉小方',\n", "  'type': 2}]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取一级类目列表\n", "\n", "import urllib.parse\n", "import json\n", "\n", "url='https://gateway.jipaibuy.com/zuul/am-user-center-appweb/api/shop/getCategoryList/3?markingTime=1708251378989'\n", "\n", "categoryList=requests.get(url, headers=headers).json()['REP_BODY']['categoryList']\n", "categoryList"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>img</th>\n", "      <th>isSellOut</th>\n", "      <th>marketPrice</th>\n", "      <th>name</th>\n", "      <th>prodId</th>\n", "      <th>prodProfitMaxPrice</th>\n", "      <th>prodProfitMinPrice</th>\n", "      <th>prodType</th>\n", "      <th>sellPrice</th>\n", "      <th>stock</th>\n", "      <th>usp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/131...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>安佳淡奶油1L*12瓶整箱</td>\n", "      <td>2427174</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>47500</td>\n", "      <td>4681</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/713...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>泰国榴莲肉冷冻无核榴莲3KG*6</td>\n", "      <td>2495752</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>183600</td>\n", "      <td>11</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/093...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>法国爱乐薇淡奶油1L*12 铁塔淡奶油 动物奶油 烘焙原料 裱花奶油</td>\n", "      <td>1665371</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>54200</td>\n", "      <td>1981</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/872...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>太古纯正糖粉蓝标糖霜铁通装 烘焙专用二级糖粉13.62kg</td>\n", "      <td>1667011</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>23000</td>\n", "      <td>96</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/593...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>美玫牌低筋粉 小麦面粉 低筋面粉饼干蛋糕</td>\n", "      <td>1666991</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>18000</td>\n", "      <td>197</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/866...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>韩国细沙砂糖 30KG</td>\n", "      <td>1667003</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>27500</td>\n", "      <td>297</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/108...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>王后低筋高筋面粉25kg 皇后全麦面包蛋糕吐司柔风糕点粉 烘焙原料</td>\n", "      <td>1666984</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>17500</td>\n", "      <td>38</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/700...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>安佳马苏里拉芝士碎12KG*1</td>\n", "      <td>2427080</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>60000</td>\n", "      <td>1999</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/898...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>铁塔淡奶油紫整箱1L*12</td>\n", "      <td>2427181</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>48500</td>\n", "      <td>499</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/066...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>安佳84片芝士橙片再制车打片干酪1040g*10</td>\n", "      <td>1665367</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>63500</td>\n", "      <td>20</td>\n", "      <td>纯正切达干酪风味 熔化时间短，熔化后的质地柔滑均匀 保质期长达12个月，质量稳定 预切片</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/361...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>英国进口蓝米吉动物性稀奶油1l*12蛋糕裱花原料</td>\n", "      <td>1665498</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>59800</td>\n", "      <td>50</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/021...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>啄木鸟披萨专用粉 25kg</td>\n", "      <td>2514348</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>14500</td>\n", "      <td>11</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/865...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>比利时蔻曼(歌文)淡味无盐黄油2.5KG*4 黄油起酥烘焙原料甜品</td>\n", "      <td>1665468</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>98000</td>\n", "      <td>20</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/214...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>百吉福原味独立芝士片216G*24 奶酪芝士三明治披萨汉堡干酪片</td>\n", "      <td>1665372</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>39000</td>\n", "      <td>50</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/588...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>日清 山茶花 紫罗兰 百合花日清山茶花高筋 小麦细粉</td>\n", "      <td>1666973</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>33500</td>\n", "      <td>60</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/912...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>蓝钻杏仁粉  蓝钻杏仁粉片</td>\n", "      <td>1667012</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>52000</td>\n", "      <td>40</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/675...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>金像面粉22.68KG</td>\n", "      <td>1666992</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>20900</td>\n", "      <td>50</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/785...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>侨艺800动植物混合奶油 1L*12</td>\n", "      <td>1667001</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>34000</td>\n", "      <td>60</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/096...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>艾恩摩尔稀奶油 家用动物淡奶油1*12</td>\n", "      <td>2427178</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>46000</td>\n", "      <td>2000</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/234...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>百吉福汉堡干酪片黄片960g*12</td>\n", "      <td>2494979</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>63500</td>\n", "      <td>300</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/424...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>法国百吉福马苏里拉芝士碎3KG*4</td>\n", "      <td>2495684</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>52500</td>\n", "      <td>50</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/706...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>丸菱豆沙馅料5KG*2</td>\n", "      <td>2495735</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>15600</td>\n", "      <td>20</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/399...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>圣农原味鸡块1KG*8一箱炸鸡块上校黄金麦乐黑椒冷冻小吃半成品</td>\n", "      <td>2670531</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>12000</td>\n", "      <td>100</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/030...</td>\n", "      <td>0</td>\n", "      <td>33000</td>\n", "      <td>圣农留香霸王腿大1kg*12一箱冷冻鸡全腿外卖鸡腿饭 油炸半成品</td>\n", "      <td>2670525</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>36000</td>\n", "      <td>100</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/729...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>圣农奥尔良鸡肉丁1kg*10一箱调理鸡胸肉腿肉丁半成品披萨炒饭酒店商家用</td>\n", "      <td>2670533</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>23800</td>\n", "      <td>100</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/558...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>圣农奥尔良烤翅12对翅1kg*12一箱肯德基烤翅烤翅精制烤对翅</td>\n", "      <td>2670532</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>45000</td>\n", "      <td>100</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/261...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>圣农黑椒鸡块1KG*10一箱炸鸡块上校黄金麦乐黑椒冷冻小吃半成品</td>\n", "      <td>2670528</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>14000</td>\n", "      <td>100</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/257...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>圣农金丝鸡排藤椒味1kg*8一箱 冷冻油炸裹粉裹米大鸡排卡兹脆半成品80片</td>\n", "      <td>2670508</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>16000</td>\n", "      <td>100</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/719...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>圣农烤翅根1kg*10一箱冷冻精制鸡翅根烧烤奥尔良烤翅商用油炸小吃半成品</td>\n", "      <td>2670791</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>21000</td>\n", "      <td>100</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/619...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>圣农傲椒风味鸡翅尖1kg*10一箱油炸小吃奥尔良调理腌制鸡尖块商用冷冻半成品</td>\n", "      <td>2670790</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>18000</td>\n", "      <td>100</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/459...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>圣农鸡肉洋葱圈800g*10一箱冷冻半成品家庭装鸡排鸡块鸡米花裹粉油炸小吃</td>\n", "      <td>2670789</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>13000</td>\n", "      <td>100</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/577...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>圣农美厨一口香鲜烤翅中1KG*10一箱</td>\n", "      <td>2677954</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>50000</td>\n", "      <td>20</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/000...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>琪雷萨马斯卡彭尼奶酪500g提拉米苏蛋糕芝士烘焙原料</td>\n", "      <td>1666994</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>24000</td>\n", "      <td>1</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/776...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>安佳奶油干酪 1KG*12</td>\n", "      <td>1665361</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>61000</td>\n", "      <td>2</td>\n", "      <td>醇正的奶香味，风味独特浓郁 乳脂含量可观 良好的烘焙稳定性及紧密的质构 传统工艺，自然生产过...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>https://osscdn.jipaibuy.com/itam/rest/test/022...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>安佳黄油（原味）5KG*4</td>\n", "      <td>1665366</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>132000</td>\n", "      <td>10</td>\n", "      <td>醇正而新鲜的新西兰黄油风味，乳香天然</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                  img  isSellOut  marketPrice  \\\n", "0   https://osscdn.jipaibuy.com/itam/rest/test/131...          0            0   \n", "1   https://osscdn.jipaibuy.com/itam/rest/test/713...          0            0   \n", "2   https://osscdn.jipaibuy.com/itam/rest/test/093...          0            0   \n", "3   https://osscdn.jipaibuy.com/itam/rest/test/872...          0            0   \n", "4   https://osscdn.jipaibuy.com/itam/rest/test/593...          0            0   \n", "5   https://osscdn.jipaibuy.com/itam/rest/test/866...          0            0   \n", "6   https://osscdn.jipaibuy.com/itam/rest/test/108...          0            0   \n", "7   https://osscdn.jipaibuy.com/itam/rest/test/700...          0            0   \n", "8   https://osscdn.jipaibuy.com/itam/rest/test/898...          0            0   \n", "9   https://osscdn.jipaibuy.com/itam/rest/test/066...          0            0   \n", "10  https://osscdn.jipaibuy.com/itam/rest/test/361...          0            0   \n", "11  https://osscdn.jipaibuy.com/itam/rest/test/021...          0            0   \n", "12  https://osscdn.jipaibuy.com/itam/rest/test/865...          0            0   \n", "13  https://osscdn.jipaibuy.com/itam/rest/test/214...          0            0   \n", "14  https://osscdn.jipaibuy.com/itam/rest/test/588...          0            0   \n", "15  https://osscdn.jipaibuy.com/itam/rest/test/912...          0            0   \n", "16  https://osscdn.jipaibuy.com/itam/rest/test/675...          0            0   \n", "17  https://osscdn.jipaibuy.com/itam/rest/test/785...          0            0   \n", "18  https://osscdn.jipaibuy.com/itam/rest/test/096...          0            0   \n", "19  https://osscdn.jipaibuy.com/itam/rest/test/234...          0            0   \n", "20  https://osscdn.jipaibuy.com/itam/rest/test/424...          0            0   \n", "21  https://osscdn.jipaibuy.com/itam/rest/test/706...          0            0   \n", "22  https://osscdn.jipaibuy.com/itam/rest/test/399...          0            0   \n", "23  https://osscdn.jipaibuy.com/itam/rest/test/030...          0        33000   \n", "24  https://osscdn.jipaibuy.com/itam/rest/test/729...          0            0   \n", "25  https://osscdn.jipaibuy.com/itam/rest/test/558...          0            0   \n", "26  https://osscdn.jipaibuy.com/itam/rest/test/261...          0            0   \n", "27  https://osscdn.jipaibuy.com/itam/rest/test/257...          0            0   \n", "28  https://osscdn.jipaibuy.com/itam/rest/test/719...          0            0   \n", "29  https://osscdn.jipaibuy.com/itam/rest/test/619...          0            0   \n", "30  https://osscdn.jipaibuy.com/itam/rest/test/459...          0            0   \n", "31  https://osscdn.jipaibuy.com/itam/rest/test/577...          0            0   \n", "32  https://osscdn.jipaibuy.com/itam/rest/test/000...          0            0   \n", "33  https://osscdn.jipaibuy.com/itam/rest/test/776...          0            0   \n", "34  https://osscdn.jipaibuy.com/itam/rest/test/022...          0            0   \n", "\n", "                                      name   prodId  prodProfitMaxPrice  \\\n", "0                            安佳淡奶油1L*12瓶整箱  2427174                   0   \n", "1                         泰国榴莲肉冷冻无核榴莲3KG*6  2495752                   0   \n", "2       法国爱乐薇淡奶油1L*12 铁塔淡奶油 动物奶油 烘焙原料 裱花奶油  1665371                   0   \n", "3            太古纯正糖粉蓝标糖霜铁通装 烘焙专用二级糖粉13.62kg  1667011                   0   \n", "4                     美玫牌低筋粉 小麦面粉 低筋面粉饼干蛋糕  1666991                   0   \n", "5                              韩国细沙砂糖 30KG  1667003                   0   \n", "6        王后低筋高筋面粉25kg 皇后全麦面包蛋糕吐司柔风糕点粉 烘焙原料  1666984                   0   \n", "7                          安佳马苏里拉芝士碎12KG*1  2427080                   0   \n", "8                            铁塔淡奶油紫整箱1L*12  2427181                   0   \n", "9                 安佳84片芝士橙片再制车打片干酪1040g*10  1665367                   0   \n", "10                英国进口蓝米吉动物性稀奶油1l*12蛋糕裱花原料  1665498                   0   \n", "11                           啄木鸟披萨专用粉 25kg  2514348                   0   \n", "12       比利时蔻曼(歌文)淡味无盐黄油2.5KG*4 黄油起酥烘焙原料甜品  1665468                   0   \n", "13        百吉福原味独立芝士片216G*24 奶酪芝士三明治披萨汉堡干酪片  1665372                   0   \n", "14              日清 山茶花 紫罗兰 百合花日清山茶花高筋 小麦细粉  1666973                   0   \n", "15                           蓝钻杏仁粉  蓝钻杏仁粉片  1667012                   0   \n", "16                             金像面粉22.68KG  1666992                   0   \n", "17                      侨艺800动植物混合奶油 1L*12  1667001                   0   \n", "18                     艾恩摩尔稀奶油 家用动物淡奶油1*12  2427178                   0   \n", "19                       百吉福汉堡干酪片黄片960g*12  2494979                   0   \n", "20                       法国百吉福马苏里拉芝士碎3KG*4  2495684                   0   \n", "21                             丸菱豆沙馅料5KG*2  2495735                   0   \n", "22         圣农原味鸡块1KG*8一箱炸鸡块上校黄金麦乐黑椒冷冻小吃半成品  2670531                   0   \n", "23        圣农留香霸王腿大1kg*12一箱冷冻鸡全腿外卖鸡腿饭 油炸半成品  2670525                   0   \n", "24    圣农奥尔良鸡肉丁1kg*10一箱调理鸡胸肉腿肉丁半成品披萨炒饭酒店商家用  2670533                   0   \n", "25         圣农奥尔良烤翅12对翅1kg*12一箱肯德基烤翅烤翅精制烤对翅  2670532                   0   \n", "26        圣农黑椒鸡块1KG*10一箱炸鸡块上校黄金麦乐黑椒冷冻小吃半成品  2670528                   0   \n", "27   圣农金丝鸡排藤椒味1kg*8一箱 冷冻油炸裹粉裹米大鸡排卡兹脆半成品80片  2670508                   0   \n", "28    圣农烤翅根1kg*10一箱冷冻精制鸡翅根烧烤奥尔良烤翅商用油炸小吃半成品  2670791                   0   \n", "29  圣农傲椒风味鸡翅尖1kg*10一箱油炸小吃奥尔良调理腌制鸡尖块商用冷冻半成品  2670790                   0   \n", "30   圣农鸡肉洋葱圈800g*10一箱冷冻半成品家庭装鸡排鸡块鸡米花裹粉油炸小吃  2670789                   0   \n", "31                     圣农美厨一口香鲜烤翅中1KG*10一箱  2677954                   0   \n", "32              琪雷萨马斯卡彭尼奶酪500g提拉米苏蛋糕芝士烘焙原料  1666994                   0   \n", "33                           安佳奶油干酪 1KG*12  1665361                   0   \n", "34                           安佳黄油（原味）5KG*4  1665366                   0   \n", "\n", "    prodProfitMinPrice  prodType  sellPrice  stock  \\\n", "0                    0         1      47500   4681   \n", "1                    0         1     183600     11   \n", "2                    0         1      54200   1981   \n", "3                    0         1      23000     96   \n", "4                    0         1      18000    197   \n", "5                    0         1      27500    297   \n", "6                    0         1      17500     38   \n", "7                    0         1      60000   1999   \n", "8                    0         1      48500    499   \n", "9                    0         1      63500     20   \n", "10                   0         1      59800     50   \n", "11                   0         1      14500     11   \n", "12                   0         1      98000     20   \n", "13                   0         1      39000     50   \n", "14                   0         1      33500     60   \n", "15                   0         1      52000     40   \n", "16                   0         1      20900     50   \n", "17                   0         1      34000     60   \n", "18                   0         1      46000   2000   \n", "19                   0         1      63500    300   \n", "20                   0         1      52500     50   \n", "21                   0         1      15600     20   \n", "22                   0         1      12000    100   \n", "23                   0         1      36000    100   \n", "24                   0         1      23800    100   \n", "25                   0         1      45000    100   \n", "26                   0         1      14000    100   \n", "27                   0         1      16000    100   \n", "28                   0         1      21000    100   \n", "29                   0         1      18000    100   \n", "30                   0         1      13000    100   \n", "31                   0         1      50000     20   \n", "32                   0         1      24000      1   \n", "33                   0         1      61000      2   \n", "34                   0         1     132000     10   \n", "\n", "                                                  usp  \n", "0                                                      \n", "1                                                      \n", "2                                                      \n", "3                                                      \n", "4                                                      \n", "5                                                      \n", "6                                                      \n", "7                                                      \n", "8                                                      \n", "9        纯正切达干酪风味 熔化时间短，熔化后的质地柔滑均匀 保质期长达12个月，质量稳定 预切片  \n", "10                                                     \n", "11                                                     \n", "12                                                     \n", "13                                                     \n", "14                                                     \n", "15                                                     \n", "16                                                     \n", "17                                                     \n", "18                                                     \n", "19                                                     \n", "20                                                     \n", "21                                                     \n", "22                                                     \n", "23                                                     \n", "24                                                     \n", "25                                                     \n", "26                                                     \n", "27                                                     \n", "28                                                     \n", "29                                                     \n", "30                                                     \n", "31                                                     \n", "32                                                     \n", "33  醇正的奶香味，风味独特浓郁 乳脂含量可观 良好的烘焙稳定性及紧密的质构 传统工艺，自然生产过...  \n", "34                                 醇正而新鲜的新西兰黄油风味，乳香天然  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "pid_list_url='https://gateway.jipaibuy.com/zuul/am-user-center-appweb/api/shop/product/getProductList/-1/1/1000?markingTime=1708250022564'\n", "product_list=requests.get(pid_list_url, headers=headers).json()['REP_BODY']['list']\n", "product_list_df=pd.DataFrame(product_list)\n", "product_list_df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thread count: 20\n", "*************:38656\n", "***************:49659\n", "**************:45514\n", "*************:49059\n", "*************:32722\n", "**************:46603\n", "**************:41691\n", "**************:42281\n", "*************:41368\n", "**************:35309\n", "['*************:38656', '***************:49659', '**************:45514', '*************:49059', '*************:32722', '**************:46603', '**************:41691', '**************:42281', '*************:41368', '**************:35309']\n", "成功写入odps:summerfarm_ds.spider_juxiangjia_product_result_df, partition_spec:ds=20240218,competitor_name=juxiangjia, attemp:0\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps \n", "# 写入odps\n", "product_list_df['competitor']=brand_name\n", "all_products_df=product_list_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = 'summerfarm_ds.spider_juxiangjia_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sql:\n", "select ds,competitor_name,count(*) as recods from summerfarm_ds.spider_juxiangjia_product_result_df where ds>='20240205' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240218</td>\n", "      <td>juxian<PERSON><PERSON>a</td>\n", "      <td>35</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ds competitor_name  recods\n", "0  20240218      juxiangjia      35"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "\n", "\n", "get_odps_sql_result_as_df(\"select ds,competitor_name,count(*) as recods from summerfarm_ds.spider_juxiangjia_product_result_df where ds>='20240205' group by ds,competitor_name order by ds desc limit 50\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
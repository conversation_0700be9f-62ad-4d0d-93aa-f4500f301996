{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 17:16:11, date_of_now:2024-03-06, brand_name:张业食品, headers:{'STOREID': '3821371'}\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/张业食品/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "headers={'STOREID':'3821371',}\n", "brand_name='张业食品'\n", "competitor_name_en='zhang<PERSON>hipin'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, headers:{headers}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**************:32137\n", "**************:36406\n", "**************:35438\n", "***************:44790\n", "*************:34732\n", "**************:37958\n", "***************:45612\n", "***************:49265\n", "**************:36461\n", "***************:34682\n", "['**************:32137', '**************:36406', '**************:35438', '***************:44790', '*************:34732', '**************:37958', '***************:45612', '***************:49265', '**************:36461', '***************:34682']\n"]}], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url, data, headers):\n", "    max_retries=3;\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, data=data, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None\n", "def post_remote_data_with_proxy(url, json, headers):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, json=json, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级、二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: *************************************************\n", "1597040233672712523 797673 面粉类\n", "\n", "1597041880147263124 797679 金味、金鹂油脂\n", "1597041942282843894 797680 糖类\n", "1597041990841463811 797681 辅料系列\n", "1604207038567427787 838368 奶油/淡奶油类\n", "1604207454171135505 838372 维朗系列\n", "1604207514595667727 838376 馅料类\n", "1604208717640366036 838385 冷冻半成品\n", "1604208789603633192 838388 美果树系列\n", "1604209026609355487 838393 沙拉酱系列\n", "1604219358455943195 838415 银豹\n", "1604299355358859039 838620 肉松系列\n", "1634613349159167160 1043625 棒师傅系列\n", "1634613447157966345 1043626 冷冻肉制品\n", "1634613462563426391 1043627 干果类\n", "1634613493453502407 1043631 巧克力类\n", "1634613831047109621 1043633 宝笙系列\n", "1634613840578752857 1043634 妙利系列\n", "1634613851391933491 1043635 新意系列\n", "1634613865177149007 1043636 焙乐道系列\n", "1634613873141305185 1043637 焙之玺系列\n", "1634613948750308849 1043651 科麦系列\n", "1634621993328472055 1043744 早苗系列\n", "\n", "1635227090813828399 1046734 芝士系列\n", "1637714672985136291 1066108 布丁、饼干类\n", "1646467050298565248 1087172 蓝彪系列\n", "1646474539896039488 1087214 玉米粒\n", "1646731836307181340 1090801 奥昆系列\n", "1676685392846137065 1223710 伯乐滋系列\n", "1676692014595241769 1223738 马卡龙系列\n", "1676968276471810557 1224342 贝一系列\n", "1677056727127797968 1224689 安佳系列\n", "1677056977080531577 1224690 妙可蓝多系列\n", "1677057124314651608 1224691 丘比系列\n", "1677056586674609237 1224692 南桥油脂\n", "1677057800439617199 1224693 阿黛尔系列\n", "1677057893392838202 1224694 炼奶系列\n", "1677057987611807503 1224696 味斯美系列\n", "1677058497080127615 1224699 速冻系列\n", "1677062860783749092 1224703 奶粉/牛奶类\n", "1677063065924916098 1224704 其他油脂类\n", "1677063810799306786 1224706 京日系列\n", "1677063959503150147 1224707 立高系列\n", "1677807176220646223 1226501 包装类\n", "1679050406205929192 1231321 雀巢系列\n", "1679110666883419256 1231420 雪媚娘皮类\n", "1679310691910146636 1231638 仟菓匠\n", "1679387389889307205 1231808 双其乐\n", "1679459355929892386 1231875 体验店商品\n", "1680836896910435498 1233994 和福大福系列\n", "1683101112093309047 1241386 荷美尔\n", "1690592098252681756 1257856 大成系列\n", "1690592215548298556 1257857 南顺面粉\n", "1690592276595681689 1257858 海融系列\n", "1690592297426616558 1257859 雷之音系列\n", "1690592327788472027 1257860 麦维客系列\n", "1690592352490781708 1257861 势道系列\n"]}], "source": ["body={\n", "    \"storeId\": 3821371,\n", "    \"includeAttributes\": <PERSON><PERSON><PERSON>,\n", "    \"includeAllProducts\": False,\n", "    \"includeHotSale\": True,\n", "    \"includeCombo\": True,\n", "    \"isMultiCategory\": True,\n", "    \"multiLevel\": \"\",\n", "    \"includePresale\": True,\n", "    \"includeGroupSale\": True,\n", "    \"includeBargainSale\": True,\n", "    \"includeSeckillSale\": True,\n", "    \"includePeriod\": True,\n", "    \"includeNew\": True,\n", "    \"isRefresh\": True,\n", "    \"v\": 2,\n", "    \"hideProductByMode\": True\n", "}\n", "\n", "url='https://wxservice-stg.pospal.cn/wxapi/product/categories'\n", "categories=json.loads(post_remote_data_with_proxy(url, json=body, headers=headers).text)\n", "categories=categories['categories']\n", "for category in categories:\n", "    print(category['CategoryUid'], category['CategoryId'],category['DisplayName'])"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1597040233672712523 797673 面粉类\n", "\n", "Using proxy: *************************************************\n", "面粉类\n", " 商品个数:18\n", "1597041880147263124 797679 金味、金鹂油脂\n", "Using proxy: ***********************************************\n", "金味、金鹂油脂 商品个数:11\n", "1597041942282843894 797680 糖类\n", "Using proxy: *************************************************\n", "糖类 商品个数:3\n", "1597041990841463811 797681 辅料系列\n", "Using proxy: ************************************************\n", "辅料系列 商品个数:26\n", "1604207038567427787 838368 奶油/淡奶油类\n", "Using proxy: ************************************************\n", "奶油/淡奶油类 商品个数:10\n", "1604207454171135505 838372 维朗系列\n", "Using proxy: ************************************************\n", "维朗系列 商品个数:48\n", "1604207514595667727 838376 馅料类\n", "Using proxy: *************************************************\n", "馅料类 商品个数:7\n", "1604208717640366036 838385 冷冻半成品\n", "Using proxy: ***********************************************\n", "冷冻半成品 商品个数:2\n", "1604208789603633192 838388 美果树系列\n", "Using proxy: ************************************************\n", "美果树系列 商品个数:27\n", "1604209026609355487 838393 沙拉酱系列\n", "Using proxy: *************************************************\n", "沙拉酱系列 商品个数:7\n", "1604219358455943195 838415 银豹\n", "Using proxy: *************************************************\n", "银豹 商品个数:1\n", "1604299355358859039 838620 肉松系列\n", "Using proxy: *************************************************\n", "肉松系列 商品个数:3\n", "1634613349159167160 1043625 棒师傅系列\n", "Using proxy: ************************************************\n", "棒师傅系列 商品个数:5\n", "1634613447157966345 1043626 冷冻肉制品\n", "Using proxy: ***********************************************\n", "冷冻肉制品 商品个数:1\n", "1634613462563426391 1043627 干果类\n", "Using proxy: *************************************************\n", "干果类 商品个数:6\n", "1634613493453502407 1043631 巧克力类\n", "Using proxy: ************************************************\n", "巧克力类 商品个数:9\n", "1634613831047109621 1043633 宝笙系列\n", "Using proxy: ***********************************************\n", "宝笙系列 商品个数:1\n", "1634613840578752857 1043634 妙利系列\n", "Using proxy: *************************************************\n", "妙利系列 商品个数:1\n", "1634613851391933491 1043635 新意系列\n", "Using proxy: *************************************************\n", "新意系列 商品个数:3\n", "1634613865177149007 1043636 焙乐道系列\n", "Using proxy: *************************************************\n", "焙乐道系列 商品个数:6\n", "1634613873141305185 1043637 焙之玺系列\n", "Using proxy: ************************************************\n", "焙之玺系列 商品个数:3\n", "1634613948750308849 1043651 科麦系列\n", "Using proxy: ***********************************************\n", "科麦系列 商品个数:3\n", "1634621993328472055 1043744 早苗系列\n", "\n", "Using proxy: ************************************************\n", "早苗系列\n", " 商品个数:4\n", "1635227090813828399 1046734 芝士系列\n", "Using proxy: ************************************************\n", "芝士系列 商品个数:2\n", "1637714672985136291 1066108 布丁、饼干类\n", "Using proxy: ************************************************\n", "布丁、饼干类 商品个数:0\n", "1646467050298565248 1087172 蓝彪系列\n", "Using proxy: ***********************************************\n", "蓝彪系列 商品个数:12\n", "1646474539896039488 1087214 玉米粒\n", "Using proxy: ************************************************\n", "玉米粒 商品个数:1\n", "1646731836307181340 1090801 奥昆系列\n", "Using proxy: ************************************************\n", "奥昆系列 商品个数:10\n", "1676685392846137065 1223710 伯乐滋系列\n", "Using proxy: *************************************************\n", "伯乐滋系列 商品个数:9\n", "1676692014595241769 1223738 马卡龙系列\n", "Using proxy: ************************************************\n", "马卡龙系列 商品个数:3\n", "1676968276471810557 1224342 贝一系列\n", "Using proxy: ***********************************************\n", "贝一系列 商品个数:14\n", "1677056727127797968 1224689 安佳系列\n", "Using proxy: ************************************************\n", "安佳系列 商品个数:4\n", "1677056977080531577 1224690 妙可蓝多系列\n", "Using proxy: ************************************************\n", "妙可蓝多系列 商品个数:6\n", "1677057124314651608 1224691 丘比系列\n", "Using proxy: ************************************************\n", "丘比系列 商品个数:3\n", "1677056586674609237 1224692 南桥油脂\n", "Using proxy: *************************************************\n", "南桥油脂 商品个数:9\n", "1677057800439617199 1224693 阿黛尔系列\n", "Using proxy: *************************************************\n", "阿黛尔系列 商品个数:8\n", "1677057893392838202 1224694 炼奶系列\n", "Using proxy: ************************************************\n", "炼奶系列 商品个数:4\n", "1677057987611807503 1224696 味斯美系列\n", "Using proxy: *************************************************\n", "味斯美系列 商品个数:16\n", "1677058497080127615 1224699 速冻系列\n", "Using proxy: ************************************************\n", "速冻系列 商品个数:2\n", "1677062860783749092 1224703 奶粉/牛奶类\n", "Using proxy: ************************************************\n", "奶粉/牛奶类 商品个数:2\n", "1677063065924916098 1224704 其他油脂类\n", "Using proxy: *************************************************\n", "其他油脂类 商品个数:6\n", "1677063810799306786 1224706 京日系列\n", "Using proxy: ***********************************************\n", "京日系列 商品个数:3\n", "1677063959503150147 1224707 立高系列\n", "Using proxy: ***********************************************\n", "立高系列 商品个数:10\n", "1677807176220646223 1226501 包装类\n", "Using proxy: *************************************************\n", "包装类 商品个数:2\n", "1679050406205929192 1231321 雀巢系列\n", "Using proxy: *************************************************\n", "雀巢系列 商品个数:5\n", "1679110666883419256 1231420 雪媚娘皮类\n", "Using proxy: ************************************************\n", "雪媚娘皮类 商品个数:4\n", "1679310691910146636 1231638 仟菓匠\n", "Using proxy: ************************************************\n", "仟菓匠 商品个数:6\n", "1679387389889307205 1231808 双其乐\n", "Using proxy: ************************************************\n", "双其乐 商品个数:60\n", "1679459355929892386 1231875 体验店商品\n", "Using proxy: *************************************************\n", "体验店商品 商品个数:0\n", "1680836896910435498 1233994 和福大福系列\n", "Using proxy: *************************************************\n", "和福大福系列 商品个数:0\n", "1683101112093309047 1241386 荷美尔\n", "Using proxy: ************************************************\n", "荷美尔 商品个数:10\n", "1690592098252681756 1257856 大成系列\n", "Using proxy: *************************************************\n", "大成系列 商品个数:34\n", "1690592215548298556 1257857 南顺面粉\n", "Using proxy: ************************************************\n", "南顺面粉 商品个数:4\n", "1690592276595681689 1257858 海融系列\n", "Using proxy: ************************************************\n", "海融系列 商品个数:12\n", "1690592297426616558 1257859 雷之音系列\n", "Using proxy: *************************************************\n", "雷之音系列 商品个数:7\n", "1690592327788472027 1257860 麦维客系列\n", "Using proxy: ************************************************\n", "麦维客系列 商品个数:11\n", "1690592352490781708 1257861 势道系列\n", "Using proxy: ************************************************\n", "势道系列 商品个数:3\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>isPromotionProduct</th>\n", "      <th>name</th>\n", "      <th>defaultproductimage</th>\n", "      <th>category</th>\n", "      <th>sellPrice</th>\n", "      <th>sellPrice2</th>\n", "      <th>buyPrice</th>\n", "      <th>stock</th>\n", "      <th>id</th>\n", "      <th>uid</th>\n", "      <th>...</th>\n", "      <th>weightUnit</th>\n", "      <th>virtualStock</th>\n", "      <th>crossBorderProduct</th>\n", "      <th>allowExpress</th>\n", "      <th>attribute5</th>\n", "      <th>attribute7</th>\n", "      <th>brandName</th>\n", "      <th>serviceTime</th>\n", "      <th>productSeries</th>\n", "      <th>specProductOrder</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>编映山红高筋粉25kg/包</td>\n", "      <td>{'id': '11054149', 'imagepath': 'https://img.p...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>107.00</td>\n", "      <td>107.00</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>34607883</td>\n", "      <td>514008276044876898</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>False</td>\n", "      <td>炜源糕粉30kg/包</td>\n", "      <td>{'id': '6642639', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>355.00</td>\n", "      <td>355.00</td>\n", "      <td>0.0</td>\n", "      <td>300.0</td>\n", "      <td>34608061</td>\n", "      <td>497181791760173834</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>False</td>\n", "      <td>三象糯粉500g*20包/箱</td>\n", "      <td>{'id': '8249818', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>140.00</td>\n", "      <td>140.00</td>\n", "      <td>0.0</td>\n", "      <td>299.0</td>\n", "      <td>34608202</td>\n", "      <td>342260936090990116</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>False</td>\n", "      <td>纸袋蓝金山面包粉25kg/包</td>\n", "      <td>{'id': '6685977', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>140.00</td>\n", "      <td>140.00</td>\n", "      <td>0.0</td>\n", "      <td>276.0</td>\n", "      <td>34943228</td>\n", "      <td>694771248476873828</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>益海嘉里</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>False</td>\n", "      <td>纸袋花鼓蛋糕专用粉25kg/包</td>\n", "      <td>{'id': '8223564', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>120.00</td>\n", "      <td>120.00</td>\n", "      <td>0.0</td>\n", "      <td>281.0</td>\n", "      <td>34943242</td>\n", "      <td>1131338457855239687</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>益海嘉里</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>False</td>\n", "      <td>王后低筋粉25kg/包</td>\n", "      <td>{'id': '8914979', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>155.00</td>\n", "      <td>155.00</td>\n", "      <td>0.0</td>\n", "      <td>40.0</td>\n", "      <td>43627180</td>\n", "      <td>1124106227545490496</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>False</td>\n", "      <td>王后高筋面粉25kg/包</td>\n", "      <td>{'id': '8915017', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>164.00</td>\n", "      <td>164.00</td>\n", "      <td>0.0</td>\n", "      <td>84.0</td>\n", "      <td>43628028</td>\n", "      <td>422911358720106330</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>False</td>\n", "      <td>编织袋中粮玉米淀粉25kg/包</td>\n", "      <td>{'id': '8940032', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>105.00</td>\n", "      <td>105.00</td>\n", "      <td>0.0</td>\n", "      <td>20.0</td>\n", "      <td>43632655</td>\n", "      <td>830855309250408659</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>False</td>\n", "      <td>柳叶梅全麦粉5kg/包</td>\n", "      <td>{'id': '8962686', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>60.00</td>\n", "      <td>60.00</td>\n", "      <td>0.0</td>\n", "      <td>20.0</td>\n", "      <td>43632669</td>\n", "      <td>978476689082620055</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>False</td>\n", "      <td>测试</td>\n", "      <td>{'id': '0', 'imagepath': 'https://img.pospal.c...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>0.01</td>\n", "      <td>0.01</td>\n", "      <td>0.0</td>\n", "      <td>-1.0</td>\n", "      <td>45824354</td>\n", "      <td>1089227030782993184</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 51 columns</p>\n", "</div>"], "text/plain": ["   isPromotionProduct             name  \\\n", "0               False    编映山红高筋粉25kg/包   \n", "1               False       炜源糕粉30kg/包   \n", "2               False   三象糯粉500g*20包/箱   \n", "3               False   纸袋蓝金山面包粉25kg/包   \n", "4               False  纸袋花鼓蛋糕专用粉25kg/包   \n", "5               False      王后低筋粉25kg/包   \n", "6               False     王后高筋面粉25kg/包   \n", "7               False  编织袋中粮玉米淀粉25kg/包   \n", "8               False      柳叶梅全麦粉5kg/包   \n", "9               False               测试   \n", "\n", "                                 defaultproductimage  \\\n", "0  {'id': '11054149', 'imagepath': 'https://img.p...   \n", "1  {'id': '6642639', 'imagepath': 'https://img.po...   \n", "2  {'id': '8249818', 'imagepath': 'https://img.po...   \n", "3  {'id': '6685977', 'imagepath': 'https://img.po...   \n", "4  {'id': '8223564', 'imagepath': 'https://img.po...   \n", "5  {'id': '8914979', 'imagepath': 'https://img.po...   \n", "6  {'id': '8915017', 'imagepath': 'https://img.po...   \n", "7  {'id': '8940032', 'imagepath': 'https://img.po...   \n", "8  {'id': '8962686', 'imagepath': 'https://img.po...   \n", "9  {'id': '0', 'imagepath': 'https://img.pospal.c...   \n", "\n", "                                            category  sellPrice  sellPrice2  \\\n", "0  {'id': '797673', 'uid': '1597040233672712523',...     107.00      107.00   \n", "1  {'id': '797673', 'uid': '1597040233672712523',...     355.00      355.00   \n", "2  {'id': '797673', 'uid': '1597040233672712523',...     140.00      140.00   \n", "3  {'id': '797673', 'uid': '1597040233672712523',...     140.00      140.00   \n", "4  {'id': '797673', 'uid': '1597040233672712523',...     120.00      120.00   \n", "5  {'id': '797673', 'uid': '1597040233672712523',...     155.00      155.00   \n", "6  {'id': '797673', 'uid': '1597040233672712523',...     164.00      164.00   \n", "7  {'id': '797673', 'uid': '1597040233672712523',...     105.00      105.00   \n", "8  {'id': '797673', 'uid': '1597040233672712523',...      60.00       60.00   \n", "9  {'id': '797673', 'uid': '1597040233672712523',...       0.01        0.01   \n", "\n", "   buyPrice  stock        id                  uid  ...  weightUnit  \\\n", "0       0.0    4.0  34607883   514008276044876898  ...         1.0   \n", "1       0.0  300.0  34608061   497181791760173834  ...         NaN   \n", "2       0.0  299.0  34608202   342260936090990116  ...         NaN   \n", "3       0.0  276.0  34943228   694771248476873828  ...         1.0   \n", "4       0.0  281.0  34943242  1131338457855239687  ...         NaN   \n", "5       0.0   40.0  43627180  1124106227545490496  ...         1.0   \n", "6       0.0   84.0  43628028   422911358720106330  ...         1.0   \n", "7       0.0   20.0  43632655   830855309250408659  ...         1.0   \n", "8       0.0   20.0  43632669   978476689082620055  ...         1.0   \n", "9       0.0   -1.0  45824354  1089227030782993184  ...         1.0   \n", "\n", "  virtualStock crossBorderProduct  allowExpress attribute5  attribute7  \\\n", "0          NaN                NaN           NaN        NaN         NaN   \n", "1          NaN                NaN           NaN        NaN         NaN   \n", "2          0.0                0.0           1.0        NaN         NaN   \n", "3          NaN                NaN           NaN                          \n", "4          NaN                NaN           NaN        NaN         NaN   \n", "5          NaN                NaN           NaN                          \n", "6          0.0                0.0           1.0        NaN         NaN   \n", "7          NaN                NaN           NaN        NaN         NaN   \n", "8          NaN                NaN           NaN                          \n", "9          NaN                NaN           NaN        NaN         NaN   \n", "\n", "  brandName serviceTime productSeries specProductOrder  \n", "0       NaN         NaN           NaN              NaN  \n", "1       NaN         NaN           NaN              NaN  \n", "2       NaN         NaN           NaN              NaN  \n", "3      益海嘉里         0.0           NaN              NaN  \n", "4      益海嘉里         NaN           NaN              NaN  \n", "5       NaN         0.0           NaN              NaN  \n", "6       NaN         NaN           NaN              NaN  \n", "7       NaN         NaN           NaN              NaN  \n", "8       NaN         0.0           NaN              NaN  \n", "9       NaN         NaN           NaN              NaN  \n", "\n", "[10 rows x 51 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["p_body={\n", "    \"storeId\": 3821371,\n", "    \"tags\": \"\",\n", "    \"brandUid\": \"\",\n", "    \"key\": \"\",\n", "    \"cUids\": \"1597041880147263124\",\n", "    \"pageIdx\": 0,\n", "    \"size\": 60,\n", "    \"includeTag\": True,\n", "     \"includeFuncTag\": True,\n", "    \"ignoreCart\": True,\n", "    \"isSeries\": True,\n", "    \"pUids\": \"\",\n", "    \"orderType\": 0,\n", "    \"minprice\": \"\",\n", "    \"maxprice\": \"\",\n", "    \"times\": <PERSON><PERSON><PERSON>,\n", "    \"includeSale\": 0,\n", "    \"checkCatSaleTime\": True,\n", "    \"hideProductByMode\": True,\n", "    \"nextCustomerCategoryUid\": \"10000\"\n", "}\n", "\n", "\n", "product_list_all=[]\n", "for category in categories:\n", "    CategoryUid=category['CategoryUid']\n", "    print(category['CategoryUid'], category['CategoryId'],category['DisplayName'])\n", "    p_body['cUids']=CategoryUid\n", "    url='https://wxservice-stg.pospal.cn/wxapi/product/listmulti'\n", "    products=json.loads(post_remote_data_with_proxy(url, headers=headers, json=p_body).text)['data']\n", "    product_list_all.extend(products)\n", "    print(f\"{category['DisplayName']} 商品个数:{len(products)}\")\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>isPromotionProduct</th>\n", "      <th>name</th>\n", "      <th>defaultproductimage</th>\n", "      <th>category</th>\n", "      <th>sellPrice</th>\n", "      <th>sellPrice2</th>\n", "      <th>buyPrice</th>\n", "      <th>stock</th>\n", "      <th>id</th>\n", "      <th>uid</th>\n", "      <th>...</th>\n", "      <th>weightUnit</th>\n", "      <th>virtualStock</th>\n", "      <th>crossBorderProduct</th>\n", "      <th>allowExpress</th>\n", "      <th>attribute5</th>\n", "      <th>attribute7</th>\n", "      <th>brandName</th>\n", "      <th>serviceTime</th>\n", "      <th>productSeries</th>\n", "      <th>specProductOrder</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>编映山红高筋粉25kg/包</td>\n", "      <td>{'id': '11054149', 'imagepath': 'https://img.p...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>107.0</td>\n", "      <td>107.0</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>34607883</td>\n", "      <td>514008276044876898</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>False</td>\n", "      <td>炜源糕粉30kg/包</td>\n", "      <td>{'id': '6642639', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>355.0</td>\n", "      <td>355.0</td>\n", "      <td>0.0</td>\n", "      <td>300.0</td>\n", "      <td>34608061</td>\n", "      <td>497181791760173834</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>False</td>\n", "      <td>三象糯粉500g*20包/箱</td>\n", "      <td>{'id': '8249818', 'imagepath': 'https://img.po...</td>\n", "      <td>{'id': '797673', 'uid': '1597040233672712523',...</td>\n", "      <td>140.0</td>\n", "      <td>140.0</td>\n", "      <td>0.0</td>\n", "      <td>299.0</td>\n", "      <td>34608202</td>\n", "      <td>342260936090990116</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 51 columns</p>\n", "</div>"], "text/plain": ["   isPromotionProduct            name  \\\n", "0               False   编映山红高筋粉25kg/包   \n", "1               False      炜源糕粉30kg/包   \n", "2               False  三象糯粉500g*20包/箱   \n", "\n", "                                 defaultproductimage  \\\n", "0  {'id': '11054149', 'imagepath': 'https://img.p...   \n", "1  {'id': '6642639', 'imagepath': 'https://img.po...   \n", "2  {'id': '8249818', 'imagepath': 'https://img.po...   \n", "\n", "                                            category  sellPrice  sellPrice2  \\\n", "0  {'id': '797673', 'uid': '1597040233672712523',...      107.0       107.0   \n", "1  {'id': '797673', 'uid': '1597040233672712523',...      355.0       355.0   \n", "2  {'id': '797673', 'uid': '1597040233672712523',...      140.0       140.0   \n", "\n", "   buyPrice  stock        id                 uid  ...  weightUnit  \\\n", "0       0.0    4.0  34607883  514008276044876898  ...         1.0   \n", "1       0.0  300.0  34608061  497181791760173834  ...         NaN   \n", "2       0.0  299.0  34608202  342260936090990116  ...         NaN   \n", "\n", "  virtualStock crossBorderProduct  allowExpress attribute5  attribute7  \\\n", "0          NaN                NaN           NaN        NaN         NaN   \n", "1          NaN                NaN           NaN        NaN         NaN   \n", "2          0.0                0.0           1.0        NaN         NaN   \n", "\n", "  brandName serviceTime productSeries specProductOrder  \n", "0       NaN         NaN           NaN              NaN  \n", "1       NaN         NaN           NaN              NaN  \n", "2       NaN         NaN           NaN              NaN  \n", "\n", "[3 rows x 51 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(product_list_all_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:编映山红高筋粉25kg/包, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:529.8318862915039ms\n", "inputText:炜源糕粉30kg/包, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:538.0373001098633ms\n", "inputText:三象糯粉500g*20包/箱, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:683.753252029419ms\n", "inputText:纸袋蓝金山面包粉25kg/包, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:699.4531154632568ms\n", "inputText:纸袋花鼓蛋糕专用粉25kg/包, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:685.7049465179443ms\n", "inputText:王后低筋粉25kg/包, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:721.4763164520264ms\n", "inputText:王后高筋面粉25kg/包, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:919.8477268218994ms\n", "inputText:编织袋中粮玉米淀粉25kg/包, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:783.0066680908203ms\n", "inputText:柳叶梅全麦粉5kg/包, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:760.2818012237549ms\n", "inputText:测试, usage:{\"prompt_tokens\": 1, \"total_tokens\": 1}, time cost:542.6337718963623ms\n", "inputText:白玉兰蛋糕粉22.68kg/包, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:674.2255687713623ms\n", "inputText:王后柔风吐司粉25kg/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:745.3734874725342ms\n", "inputText:日清山茶花强力粉25kg/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:637.1150016784668ms\n", "inputText:秒杀测试, usage:{\"prompt_tokens\": 4, \"total_tokens\": 4}, time cost:566.2918090820312ms\n", "inputText:纸袋伯爵T55传统面粉25kg/包, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:657.0451259613037ms\n", "inputText:纸袋伯爵T65传统面粉25kg/包, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:744.255781173706ms\n", "inputText:纸袋金山全麦粉20kg/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:799.4661331176758ms\n", "inputText:纸袋金像牌焙柔面包用小麦粉25kg/包, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:558.5625171661377ms\n", "inputText:铁桶金味奶酥油15kg/桶, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:762.908935546875ms\n", "inputText:金味5L液态油5L*4桶/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:688.7495517730713ms\n", "inputText:金味液态酥油（牛奶味）5L*4桶/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:589.6100997924805ms\n", "inputText:金味超级烤焙奶油10kg/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:698.1525421142578ms\n", "inputText:金鹂精品酥油10kg/箱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:712.3513221740723ms\n", "inputText:金鹂5L液态酥油5L*4桶/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:609.1058254241943ms\n", "inputText:金味日式片状甜奶油10kg/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:731.2438488006592ms\n", "inputText:金味夹心油10kg/箱, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:652.275562286377ms\n", "inputText:金味乳品发酵奶油10kg/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:634.1774463653564ms\n", "inputText:大8猪油15KG/桶, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:549.262285232544ms\n", "inputText:金味02乳品黄油10kg/箱, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:855.5634021759033ms\n", "inputText:TS幼沙糖30kg/包, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:619.7977066040039ms\n", "inputText:太古蓝标糖霜13.62kg/桶, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:631.1023235321045ms\n", "inputText:糖粉25kg/包, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:580.6539058685303ms\n", "inputText:法国酵母500g*20包/箱, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:663.7513637542725ms\n", "inputText:理想粟粉454g*24盒/箱, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:702.9266357421875ms\n", "inputText:百花蜂蜜375g/瓶, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:658.3704948425293ms\n", "inputText:雅圣达高档枧水5kg*4桶/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:557.5830936431885ms\n", "inputText:朱师傅干葱粉100g*12罐/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:855.3774356842041ms\n", "inputText:味林皇冠面包糠1kg*10包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:673.5854148864746ms\n", "inputText:食粉（小苏打粉）400g*24盒/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:664.2026901245117ms\n", "inputText:臭粉1.2kg*8罐/箱, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:587.4807834625244ms\n", "inputText:维朗1加仑食品用香精（牛乳鸡蛋味）3.78L* 4瓶/箱, usage:{\"prompt_tokens\": 42, \"total_tokens\": 42}, time cost:560.4574680328369ms\n", "inputText:奥凯脱氢乙酸钠1kg*10盒/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:762.0866298675537ms\n", "inputText:可可百利薄脆片2.5kg*4盒/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:672.797441482544ms\n", "inputText:三花全脂淡奶410g*48罐/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:659.0712070465088ms\n", "inputText:高达椰酱400g*24罐/箱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:650.1321792602539ms\n", "inputText:彩针550g*12罐/箱, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:600.313663482666ms\n", "inputText:百利吉利丁片1kg*25盒/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:595.5367088317871ms\n", "inputText:百利蕃茄沙司3kg*6罐/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:692.9798126220703ms\n", "inputText:百利沙律酱3.79L*4桶/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:626.3113021850586ms\n", "inputText:日本樱花海苔50片*90小包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:722.4507331848145ms\n", "inputText:立高玉荔牌起司馅3kg*4桶/件, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:608.4251403808594ms\n", "inputText:帝派菠萝片850g*24罐/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:677.7443885803223ms\n", "inputText:椰富椰蓉45kg/包, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:560.7569217681885ms\n", "inputText:朱师傅帕玛森芝士粉500g*12罐/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:926.123857498169ms\n", "inputText:三花植脂淡奶410g*48罐/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:723.1805324554443ms\n", "inputText:蓝黛可可粉（可可脂含量20%-22%）1kg*10包/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:582.8757286071777ms\n", "inputText:美雅士朗姆酒750ml*12瓶/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:667.7045822143555ms\n", "inputText:奥利奥饼干（轻甜夹心）116g*24盒/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:867.5777912139893ms\n", "inputText:侨艺淡奶油(动植脂混合奶油)1L*12盒/箱, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:681.553840637207ms\n", "inputText:1L铁塔淡奶油1L*12盒/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:570.6286430358887ms\n", "inputText:总统淡奶油1L*6盒/箱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:554.1880130767822ms\n", "inputText:格尔巴尼马斯卡彭芝士500g*6杯/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:630.3596496582031ms\n", "inputText:维益和牧牛奶奶油907g*12支/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:665.8751964569092ms\n", "inputText:金钻含乳脂奶油907g*12支/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:744.9197769165039ms\n", "inputText:金钻甜点植脂忌廉1kg*12支/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:607.0981025695801ms\n", "inputText:美蒂雅含乳脂植脂奶油907克*12盒/箱, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:678.4458160400391ms\n", "inputText:亚奇丽蛋挞液907g*12支/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:606.4331531524658ms\n", "inputText:景巢葡挞液907g*12支/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:589.104413986206ms\n", "inputText:维朗美式奶油松饼调配粉2kg*5包/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:585.7534408569336ms\n", "inputText:维朗特级榴莲奶露馅5kg*4桶/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:610.5287075042725ms\n", "inputText:维朗特级香蕉奶露馅5kg*4桶/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:673.1243133544922ms\n", "inputText:维朗特级柚子奶露馅5kg*4桶/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:616.267204284668ms\n", "inputText:维朗特级黑糖红枣馅5kg*4桶/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:553.3983707427979ms\n", "inputText:维朗红枣蓉5kg*4桶/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:630.0592422485352ms\n", "inputText:维朗芝士馅5kg*4桶/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:693.4652328491211ms\n", "inputText:维朗杨枝柑露夹心馅5kg*4桶/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:704.6394348144531ms\n", "inputText:维朗臻选柑桔果馅5kg*4桶/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:742.6166534423828ms\n", "inputText:维朗蓝莓果酱5kg*4桶/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:626.3446807861328ms\n", "inputText:维朗蓝莓果泥5kg*4桶/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:727.5164127349854ms\n", "inputText:维朗卡士达酱(原味）1KG*6条/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:807.0862293243408ms\n", "inputText:维朗柑桔果酱5kg*4桶/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:698.6944675445557ms\n", "inputText:维朗面包改良剂（冷冻面团专用）1kg*6包/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:636.6400718688965ms\n", "inputText:维朗美式巧克力松饼调配粉2kg*5包/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:570.3389644622803ms\n", "inputText:维朗5kg牛油粉末香精（牛油至尊)5kg*4桶/箱, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:662.3835563659668ms\n", "inputText:维朗面包柔软剂2kg*4包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:609.865665435791ms\n", "inputText:维朗蛋白稳定剂(塔塔粉)1kg*12罐/箱, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:538.5904312133789ms\n", "inputText:维朗600G坚亨88面包改良剂600g*20包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:557.3160648345947ms\n", "inputText:维朗紫米预拌粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:559.2284202575684ms\n", "inputText:维朗黑麦烘焙杂粮粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:599.9021530151367ms\n", "inputText:维朗混合种子烘焙杂粮粉2kg*5包/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:519.7269916534424ms\n", "inputText:维朗藜麦烘焙杂粮粉（藜麦碎）2kg*5包/箱, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:569.4448947906494ms\n", "inputText:维朗全麦烘焙调配粉2kg*5包/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:653.2895565032959ms\n", "inputText:维朗麻薯烘焙调配粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:632.1327686309814ms\n", "inputText:维朗胡萝卜烘焙调配粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:629.1592121124268ms\n", "inputText:维朗藜麦烘焙调配粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:566.6916370391846ms\n", "inputText:维朗玉米烘焙调配粉3kg*4包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:546.8344688415527ms\n", "inputText:维朗香芫茜烘焙调配粉80g*12瓶/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:614.6659851074219ms\n", "inputText:维朗马铃薯装饰粉（原味）1kg*5包/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:1703.9821147918701ms\n", "inputText:维朗紫薯装饰粉1kg*5包/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:619.140625ms\n", "inputText:维朗表面装饰防潮糖粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:583.8663578033447ms\n", "inputText:维朗水晶粉（果冻粉）1kg*12罐/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:594.6002006530762ms\n", "inputText:维朗即溶吉士粉1kg*12瓶/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:619.5230484008789ms\n", "inputText:维朗软心馅预拌粉2kg*5包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:676.1312484741211ms\n", "inputText:维朗麻薯烘焙预拌粉（手调）2.5kkg*4包/箱, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:1359.0266704559326ms\n", "inputText:维朗戚风蛋糕预拌粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:615.9403324127197ms\n", "inputText:维朗乳酸风味柔软面包预拌粉2kg*5包/箱, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:689.2004013061523ms\n", "inputText:维朗牛油风味软熟面包预拌粉2kg*5包/箱, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:574.089765548706ms\n", "inputText:维朗紫薯麻薯预拌粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:625.5412101745605ms\n", "inputText:维朗摩奇烘焙预拌粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:649.3997573852539ms\n", "inputText:维朗复合膨松剂（泡打粉）2.7kg*6罐/箱, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:656.1899185180664ms\n", "inputText:维朗红枣松饼调配粉2kg*5包/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:778.5358428955078ms\n", "inputText:维朗软熟麻薯预拌粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:528.0141830444336ms\n", "inputText:维朗冰摩奇烘焙预拌粉2.5kg*4包/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:585.7455730438232ms\n", "inputText:维朗1kg牛油粉末香精（牛油至尊)1kg*12桶/箱, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:558.1438541412354ms\n", "inputText:维朗焙烤食品预拌粉（禾味面包蛋糕专用）2.5kg*4包/箱, usage:{\"prompt_tokens\": 42, \"total_tokens\": 42}, time cost:652.3547172546387ms\n", "inputText:维朗5KG坚亨88面包改良剂5kg*2包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:602.9210090637207ms\n", "inputText:焙皇低糖红豆馅5kg*2包/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:582.2494029998779ms\n", "inputText:盛华奶黄馅2.5kg*4包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:512.2637748718262ms\n", "inputText:碧琪栗子蓉900g*12罐/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:521.6341018676758ms\n", "inputText:维芙西点夹心奶油1kg*6包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:572.9889869689941ms\n", "inputText:蕾芝音卡士达酱（原味）1kg*6包/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:668.8022613525391ms\n", "inputText:纸箱装盛华豆沙4.65kg*3包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:560.366153717041ms\n", "inputText:华琪香芋馅（蛋糕专用）2.5kg*6包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:561.0263347625732ms\n", "inputText:星奇异榴莲千层切件1.1kg*10盒/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:527.8255939483643ms\n", "inputText:星奇异芒果千层切件1.1kg*10盒/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:704.4863700866699ms\n", "inputText:美果树速冻草莓果泥1kg*6盒/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:648.9880084991455ms\n", "inputText:美果树速冻芒果果泥1kg*6盒/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:558.2115650177002ms\n", "inputText:美果树速冻蓝莓果泥1kg*6盒/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:561.7537498474121ms\n", "inputText:美果树速冻白桃果泥1kg*6盒/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:650.8722305297852ms\n", "inputText:美果树牛奶布丁420g*24盒/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:608.5362434387207ms\n", "inputText:美果树蓝莓果泥1.5kg*6桶/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:505.05781173706055ms\n", "inputText:美果树草莓果泥1.5kg*6桶/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:653.1434059143066ms\n", "inputText:美果树芒果果泥1.5kg*6桶/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:600.0523567199707ms\n", "inputText:美果树栗子蓉（罐装）900g*12罐/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:617.9862022399902ms\n", "inputText:美果树杨枝甘露果泥1.5kg*6桶/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:3811.7809295654297ms\n", "inputText:美果树芒果果馅3kg*4桶/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:523.6170291900635ms\n", "inputText:美果树草莓果馅3kg*4桶/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:602.5640964508057ms\n", "inputText:美果树香橙果馅3kg*4桶/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:520.9650993347168ms\n", "inputText:美果树菠萝果馅3kg*4桶/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:541.9833660125732ms\n", "inputText:美果树精品蓝莓果馅3kg*4桶/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:570.5418586730957ms\n", "inputText:美果树白桃果馅3kg*4桶/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:859.1165542602539ms\n", "inputText:美果树杨枝甘露馅3kg*4桶/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:612.9515171051025ms\n", "inputText:美果树红樱桃果馅3kg*4桶/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:532.8545570373535ms\n", "inputText:美果树大颗粒芒果馅1.5kg*6桶/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:518.9158916473389ms\n", "inputText:美果树扁桃仁巧克力果馅3kg*4桶/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:737.044095993042ms\n", "inputText:美果树榛子酱3kg*4桶/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:616.4729595184326ms\n", "inputText:美果树镜面果胶1.5kg*6桶/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:504.90570068359375ms\n", "inputText:美果树冷藏白桃冻420g*24盒/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:558.0663681030273ms\n", "inputText:美果树冷藏芝士酱1.5kg*6桶/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:535.8889102935791ms\n", "inputText:美果树冷藏白桃茉莉果馅3kg*4桶/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:570.9257125854492ms\n", "inputText:美果树冷藏椰奶布丁420g*24盒/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:553.849458694458ms\n", "inputText:美果树冷藏生椰拿铁布丁420g*24盒/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:1246.2413311004639ms\n", "inputText:忆霖甜沙律酱1kg*16包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:539.4432544708252ms\n", "inputText:忆霖蛋黄清爽酱1kg*16包/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:499.3705749511719ms\n", "inputText:妙多千岛酱1kg*12包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:578.2296657562256ms\n", "inputText:百利沙拉酱（香甜型）1kg*12包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:515.9094333648682ms\n", "inputText:浪辰沙律酱900g*12包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:513.8390064239502ms\n", "inputText:百利汉堡沙律酱1kg*12包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:579.8559188842773ms\n", "inputText:美煌茂林1+1沙拉酱（原味）900g*12包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:713.0498886108398ms\n", "inputText:银豹收银系统, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:818.8455104827881ms\n", "inputText:琦轩金牌松2.5kg*6包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:619.8909282684326ms\n", "inputText:琦轩海苔芝麻酥松A级2.5kg*6包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:548.1350421905518ms\n", "inputText:味匠B8肉松 2.5KG*6包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:490.0624752044678ms\n", "inputText:棒师傅酒渍樱桃900g*6罐/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:544.9457168579102ms\n", "inputText:棒师傅黑樱桃馅3kg*2桶/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:530.4205417633057ms\n", "inputText:第二代棒师傅原味有枝黑樱（m级）850g*12罐/箱, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:588.8104438781738ms\n", "inputText:棒师傅第二代原味有枝红樱桃（M级）850g*12罐/箱, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:521.8672752380371ms\n", "inputText:棒师傅黑樱桃850g*12罐/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:524.6672630310059ms\n", "inputText:正大烘焙爆浆鸡排（蜂蜜芥末味）640g*10包/箱, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:546.2331771850586ms\n", "inputText:宝路特级提子干10kg/箱, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:677.7324676513672ms\n", "inputText:苏丹白芝麻25kg/包, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:687.8113746643066ms\n", "inputText:丰焙椰蓉2.5KG*6包/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:3697.6065635681152ms\n", "inputText:中号寿桃12个/箱, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:598.5121726989746ms\n", "inputText:雪白7-8瓜子仁15kg/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:526.5440940856934ms\n", "inputText:太和玫瑰糖5kg/桶, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:623.7015724182129ms\n", "inputText:帝王花白巧克力块1kg*10块/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:551.9037246704102ms\n", "inputText:帝王花黑巧克力块1kg*10块/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:532.081127166748ms\n", "inputText:奥利奥饼干碎400g*24包/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:534.7421169281006ms\n", "inputText:可可琳纳黑巧克力块1kg*10块/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:521.942138671875ms\n", "inputText:朱师傅银牌黑巧克力块1kg*12块/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:707.4778079986572ms\n", "inputText:朱师傅银牌白巧克力块1kg*12块/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:542.4058437347412ms\n", "inputText:可可琳纳白巧克力块1kg*10块/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:596.6789722442627ms\n", "inputText:伯乐滋白巧克力块1kg*10块/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:601.3903617858887ms\n", "inputText:伯乐滋黑巧克力块1KG*10块/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:565.8578872680664ms\n", "inputText:宝笙布丁粉800g*12罐/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:630.9511661529541ms\n", "inputText:妙利戚风蛋糕预拌粉5kg*2包/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:540.463924407959ms\n", "inputText:新意500面包改良剂1kg*10包/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:743.9696788787842ms\n", "inputText:新意牛奶香粉1kg*8罐/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:545.7701683044434ms\n", "inputText:新意即溶吉士粉1kg*10包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:631.0691833496094ms\n", "inputText:焙乐道S500面包改良剂1kg*20包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:619.8091506958008ms\n", "inputText:焙乐道特A面包改良剂1kg*10包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:796.1647510528564ms\n", "inputText:焙乐道泡打粉1.4kg*8罐/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:619.5418834686279ms\n", "inputText:焙乐道塔塔粉1kg*8罐/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:1592.031717300415ms\n", "inputText:焙乐道快味可即溶吉士粉5kg*2盒/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:661.5054607391357ms\n", "inputText:焙乐道麻薯粉5kg*2包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:604.3131351470947ms\n", "inputText:焙之玺泡芙预拌粉1kg*10包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:807.4452877044678ms\n", "inputText:焙之玺红丝绒海绵蛋糕预拌粉5KG*2包/箱, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:691.5688514709473ms\n", "inputText:焙之玺法工速溶卡士达糕点预拌粉（国产）5kg*2包/箱, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:685.9281063079834ms\n", "inputText:科麦卡士达克林姆粉（吉士粉）5kg*4包/箱, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:582.3714733123779ms\n", "inputText:科麦抹茶粉A500g*40包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:612.9302978515625ms\n", "inputText:科麦脱膜油520g*24瓶/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:673.5124588012695ms\n", "inputText:早苗SP蛋糕油复配糕点乳化剂5kg*4罐/箱, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:626.3899803161621ms\n", "inputText:早苗塔塔粉1.35kg*12罐/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:558.1173896789551ms\n", "inputText:早苗泡打粉2.7kg*6罐/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:594.6881771087646ms\n", "inputText:百事达栗子蓉1kg*20包/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:663.4280681610107ms\n", "inputText:安佳忌廉芝士1kg*12条/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:702.8224468231201ms\n", "inputText:罗普马苏里拉奶酪2kg*4包/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:595.4506397247314ms\n", "inputText:蓝彪原味甜甜圈5包*15个/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:524.1990089416504ms\n", "inputText:蓝彪草莓甜甜圈5包*15个/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:525.8581638336182ms\n", "inputText:蓝彪蓝莓甜甜圈5包*15个/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:695.4050064086914ms\n", "inputText:蓝彪蛋黄酥65克*96个/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:661.9999408721924ms\n", "inputText:蓝彪Y1A港式蛋挞皮34个*10包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:695.6820487976074ms\n", "inputText:蓝彪叉烧酥180个/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:585.7522487640381ms\n", "inputText:蓝彪老婆饼30克*260个/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:522.2756862640381ms\n", "inputText:蓝彪榴莲酥24个*8层/192个/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:598.2110500335693ms\n", "inputText:蓝彪葡挞液1KG*12支/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:642.3895359039307ms\n", "inputText:蓝彪熟皮利卡60克*80条/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:996.6154098510742ms\n", "inputText:蓝彪起酥老婆饼 224个/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:570.6179141998291ms\n", "inputText:蓝彪冷冻泡芙150个/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:635.5984210968018ms\n", "inputText:粤来粤好粟米粒420g*24罐/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:536.7543697357178ms\n", "inputText:好禧坊蓝莓慕斯蛋糕10枚*8盒/箱, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:730.0863265991211ms\n", "inputText:奥昆（好禧坊）50g甜甜圈5包*15个/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:557.6105117797852ms\n", "inputText:奥昆207葡挞皮（蓝色包装）30个*10包/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:609.0493202209473ms\n", "inputText:圣口乐彩虹蛋糕10枚*9盒/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:528.6953449249268ms\n", "inputText:奥昆港式2103蛋挞皮（蓝色包装）10包*34个/箱, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:604.6504974365234ms\n", "inputText:奥昆蛋黄酥65g*150个/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:562.0529651641846ms\n", "inputText:奥昆榴莲酥216个/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:600.4147529602051ms\n", "inputText:奥昆蓝莓甜甜圈5包*15个/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:589.801549911499ms\n", "inputText:奥昆草莓甜甜圈5包*15个/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:549.9868392944336ms\n", "inputText:奥昆波斯面种（速冻焙烤预制品）3kg*3包/箱, usage:{\"prompt_tokens\": 30, \"total_tokens\": 30}, time cost:549.2873191833496ms\n", "inputText:伯乐滋（314）黑生日牌110片/盒, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:563.5886192321777ms\n", "inputText:伯乐滋（605)立体十三幺咬牙麻将32片/盒, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:528.6037921905518ms\n", "inputText:伯乐滋黑白烟棒170片/盒, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:613.7368679046631ms\n", "inputText:伯乐滋网格白巧克力长方形110片/盒, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:574.1665363311768ms\n", "inputText:伯乐滋网格黑巧克力长方形110片/盒, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:589.8120403289795ms\n", "inputText:伯乐滋代可可脂巧克力装饰（黑/白碎花）, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:673.5365390777588ms\n", "inputText:伯乐滋4*8纯白长方形110片/盒, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:582.4611186981201ms\n", "inputText:伯乐滋4*8纯黑长方形110片/盒, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:575.0033855438232ms\n", "inputText:伯乐滋吸塑黑白扇120片/盒, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:564.5236968994141ms\n", "inputText:马卡龙小四粒装40g*60盒/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:602.4389266967773ms\n", "inputText:马卡龙迷你40粒装40粒*18板/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:576.3256549835205ms\n", "inputText:马卡龙至尊4粒装48g**70盒/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:629.9934387207031ms\n", "inputText:贝一金牌拉丝面包预拌粉5kg*4包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:639.080286026001ms\n", "inputText:贝一轻松打高品质Q润戚风蛋糕预拌粉25kg/包, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:612.011194229126ms\n", "inputText:贝一麻薯9号面包预拌粉5kg*4包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:614.2804622650146ms\n", "inputText:贝一红丝绒蛋糕预拌粉5kg*4包/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:633.704662322998ms\n", "inputText:贝一金牌QQ原味麻薯预拌粉5kg*4包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:807.2443008422852ms\n", "inputText:贝一海绵蛋糕预拌粉（打发型）5kg*4包/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:562.6251697540283ms\n", "inputText:贝一35可丝达酱（日式乳酸菌风味）1kg*6袋/箱, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:559.563398361206ms\n", "inputText:贝一35蛋胚酱1kg*6袋/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:589.846134185791ms\n", "inputText:贝一戚风蛋糕预拌粉（青柠味）5kg*4袋/箱, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:666.0511493682861ms\n", "inputText:贝一Emulquick蛋糕油4.5kg*4桶/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:691.7097568511963ms\n", "cache matched:贝一Emulquick蛋糕油4.5kg*4桶/箱\n", "cache matched:贝一35蛋胚酱1kg*6袋/箱\n", "cache matched:贝一35可丝达酱（日式乳酸菌风味）1kg*6袋/箱\n", "inputText:贝一食品工业轻松打超人气戚风预拌粉, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:614.9790287017822ms\n", "inputText:安佳芝士片(米白色）84片*10包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:676.6986846923828ms\n", "inputText:牧童牛油227g*40块/箱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:538.7539863586426ms\n", "inputText:安佳奶油芝士20kg/箱, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:614.7763729095459ms\n", "inputText:安佳芝士片（橙色）84片*10包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:647.8714942932129ms\n", "inputText:妙可蓝多芝士碎3kg*4包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:695.5299377441406ms\n", "inputText:妙可蓝多芝士片（白色）80片*8包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:612.6132011413574ms\n", "inputText:妙可蓝多奶油芝士2kg*5条/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:541.3460731506348ms\n", "inputText:妙可蓝多芝士片（黄色）80片*8包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:575.1745700836182ms\n", "inputText:妙可蓝多马苏里拉芝士碎450g*24包/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:546.4575290679932ms\n", "inputText:妙可蓝多原味奶酪棒（低温）20g*5个*24包/箱, usage:{\"prompt_tokens\": 34, \"total_tokens\": 34}, time cost:541.4943695068359ms\n", "inputText:丘比烘焙专用沙拉酱（甜味）1kg*10包/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:644.8009014129639ms\n", "inputText:丘比鸡蛋沙拉风味酱1kg*10包/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:576.3678550720215ms\n", "inputText:丘比土豆沙拉风味酱1kg*10包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:566.4992332458496ms\n", "inputText:南侨酥油16kg/桶, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:689.0668869018555ms\n", "inputText:南侨液态酥油20kg/桶, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:773.4365463256836ms\n", "inputText:南侨日式片状甜奶油10kg/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:513.9987468719482ms\n", "inputText:南侨维佳烘焙奶油10kg/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:665.5151844024658ms\n", "inputText:南侨维佳夹心奶油10kg/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:666.7711734771729ms\n", "inputText:南侨丹麦面包专用油10kg/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:625.220537185669ms\n", "inputText:南侨王牌酥片玛琪琳酥油10kg/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:766.7407989501953ms\n", "inputText:南侨玉峰牌高级雪白乳化油15kg/桶, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:649.6543884277344ms\n", "inputText:纸箱南侨量版专用酥油20kg/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:524.2033004760742ms\n", "inputText:阿黛尔液态酥油5kg*4桶/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:588.1123542785645ms\n", "inputText:阿黛尔和风奶油（烤焙油）10kg/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:562.9832744598389ms\n", "inputText:阿黛尔片状甜奶油10kg/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:522.6619243621826ms\n", "inputText:阿黛尔南纬46度10kg/箱, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:583.4057331085205ms\n", "inputText:阿黛尔乳品发酵奶油10kg/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:666.6700839996338ms\n", "inputText:阿黛尔丹麦面包专用油（加奶粉）10kg/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:733.2792282104492ms\n", "inputText:阿黛尔可丝达-香浓牛奶味（馅料）6KG/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:550.750732421875ms\n", "inputText:鸿信和风御香奶油20kg/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:606.7132949829102ms\n", "inputText:东泰调制加糖炼乳（全拉盖）350g*24罐/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:521.5723514556885ms\n", "inputText:胶罐富乐2.4KG炼奶2.4kg*8罐/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:655.6282043457031ms\n", "inputText:富乐甜炼奶350g*24罐/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:521.9089984893799ms\n", "inputText:熊猫炼奶350g*48罐/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:535.9067916870117ms\n", "inputText:味斯美5cm日式爆汁肠1kg*12包/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:528.8803577423096ms\n", "inputText:味斯美日式12cm爆汁肠1kg*12包/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:1108.4873676300049ms\n", "inputText:味斯美原味火山石地道肠1kg*12包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:519.8116302490234ms\n", "inputText:味斯美装饰原味松松1KG*10包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:547.9297637939453ms\n", "inputText:味斯美大肉块汉堡肉1.3kg*10包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:573.1499195098877ms\n", "inputText:味斯美魔力能量棒192条/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:594.0651893615723ms\n", "inputText:味斯美海苔脆脆松2.5kg*8包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:558.030366897583ms\n", "inputText:味斯美蟹王肉松2KG*8包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:555.6919574737549ms\n", "inputText:味斯美3A海苔肉松2kg*8包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:566.0300254821777ms\n", "inputText:味斯美黄（A）肉松2.5kg*8包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:568.9735412597656ms\n", "inputText:味斯美巧克力坚果肉松2kg*8包/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:587.9383087158203ms\n", "inputText:赢利宝海苔肉松1kg*10包/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:667.0627593994141ms\n", "inputText:味斯美原味酥脆松（绿色）2kg*8包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:952.4447917938232ms\n", "inputText:味斯美原味肉松（华南专供）1kg*10包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:734.382152557373ms\n", "inputText:味斯美牛肉味酥脆松2kg*8包/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:591.6244983673096ms\n", "inputText:味斯美抹茶酥脆松（肉粉松）2kg*8包/箱, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:654.6630859375ms\n", "inputText:薇薇安3A速冻榴莲肉3kg*6包/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:635.8850002288818ms\n", "inputText:2A速冻榴莲肉3kg*6包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:581.287145614624ms\n", "inputText:新西兰全脂奶粉25kg/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:585.1502418518066ms\n", "inputText:澳兰克纯牛奶, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:549.8571395874023ms\n", "inputText:聚美味一级大豆油20L/桶, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:600.816011428833ms\n", "inputText:德宝无盐黄油25kg/箱, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:650.604248046875ms\n", "inputText:铁桶车轮黄奶油16kg/桶, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:608.0431938171387ms\n", "inputText:红玫瑰起酥油15kg/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:730.604887008667ms\n", "inputText:纸箱车轮黄奶油15kg/箱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:653.9895534515381ms\n", "inputText:纸箱车轮白奶油15kg/箱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:663.3098125457764ms\n", "inputText:京日JA18红豆馅5kg*4包/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:553.7445545196533ms\n", "inputText:京日紫薯馅5kg*4包/箱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:653.8221836090088ms\n", "inputText:京日红小豆甘纳豆5kg*2包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:569.7886943817139ms\n", "inputText:立高白桃奶油夹心酱1kg*6包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:566.258430480957ms\n", "inputText:立高北海道牛奶味奶油夹心酱1kg*6条/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:622.2579479217529ms\n", "inputText:立高日式乳酸菌奶油夹心酱1KG*6袋/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:811.1321926116943ms\n", "inputText:立高速冻草莓果溶1kg*8盒/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:673.6481189727783ms\n", "inputText:立高速冻蓝莓果蓉1kg*8盒/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:512.2988224029541ms\n", "inputText:立高速冻芒果果溶1kg*8盒/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:565.0274753570557ms\n", "inputText:立高香草味奶油夹心酱1kg*6包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:628.8585662841797ms\n", "inputText:立高芝士味奶油夹心酱1kg*6包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:523.6549377441406ms\n", "inputText:立高牛乳奶油1L*12支/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:561.5234375ms\n", "inputText:立高速冻树莓果蓉1kg*8盒/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:636.9287967681885ms\n", "inputText:大号裱花袋 100个/包, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:3799.7913360595703ms\n", "inputText:大金生日帽100个*30扎/箱, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:758.354663848877ms\n", "inputText:雀巢奶粉500g*24包/箱, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:558.1223964691162ms\n", "inputText:雀巢炼奶原味185g*6支*4盒/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:595.8411693572998ms\n", "inputText:雀巢淡奶油1L*12盒/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:563.2083415985107ms\n", "inputText:雀巢醇品咖啡粉500g*6罐/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:661.9575023651123ms\n", "inputText:雀巢炼奶草莓味185g*6支*4盒/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:624.3889331817627ms\n", "inputText:9*9米芝品芒果味雪媚娘皮48包/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:788.9981269836426ms\n", "inputText:9*9米芝品草莓味雪媚娘皮48包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:525.5603790283203ms\n", "inputText:米芝品12*12芒果味雪媚娘皮30包/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:742.3171997070312ms\n", "inputText:米芝品12*12原味雪媚娘皮30包/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:663.9366149902344ms\n", "inputText:仟菓匠冷藏紫薯馅（无蔗糖无油）1KG*12袋/箱, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:670.1130867004395ms\n", "inputText:仟菓匠紫米馅1kg*12包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:532.7789783477783ms\n", "inputText:仟菓匠速冻南瓜原浆1KG*12袋/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:586.838960647583ms\n", "inputText:仟菓匠速冻芋泥1KG*12袋/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:577.0726203918457ms\n", "inputText:仟菓匠冷藏南瓜馅1KG*12袋/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:550.3067970275879ms\n", "inputText:仟菓匠冷藏香芋馅（面包专用）1kg*12包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:621.5479373931885ms\n", "inputText:双其乐黑色胶盒600个/箱DT-1032, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:565.6619071960449ms\n", "inputText:双其乐白色胶盒1200套/箱DT-1034, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:578.1314373016357ms\n", "inputText:双其乐白色胶盒+小叉600套/箱DT-1052高, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:574.7694969177246ms\n", "inputText:双其乐白色长形胶盒1200套/箱DT-1055, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:559.0581893920898ms\n", "inputText:双其乐白色胶盒600套/箱DT-1056, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:577.5330066680908ms\n", "inputText:双其乐八角黄白胶盒800个/箱DT-1061, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:813.4803771972656ms\n", "inputText:双其乐白色胶盒1200套/箱DT-1061, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:547.8954315185547ms\n", "inputText:双其乐白色胶盒1200套/箱DT-1062, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:594.327449798584ms\n", "inputText:双其乐透明小笼包胶盒+不干胶800套/箱DT-1064, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:762.951135635376ms\n", "inputText:双其乐白色胶托+袋+酒种红豆不干胶1000套/箱DT-1092, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:634.1147422790527ms\n", "inputText:双其乐白色胶托+袋+健康杂粮不干胶1000套/箱DT-1094, usage:{\"prompt_tokens\": 39, \"total_tokens\": 39}, time cost:647.158145904541ms\n", "inputText:双其乐白色三格胶盒+小叉1000个/箱DT-1096, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:603.9469242095947ms\n", "inputText:双其乐透明胶盒600套/箱DT-1102, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:1007.2071552276611ms\n", "inputText:双其乐白色胶盒900个/箱DT-1103, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:758.3532333374023ms\n", "inputText:双其乐白色胶盒1200个/箱DT-1104, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:623.6839294433594ms\n", "inputText:双其乐白色胶盒1200套/箱DT-1109, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:652.7552604675293ms\n", "inputText:双其乐黄色方形胶盒1200套/箱DT-1212黄白, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:570.9748268127441ms\n", "inputText:双其乐黑色胶盒1000套/箱DT-1811, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:592.9787158966064ms\n", "inputText:双其乐白色胶盒1200/个套DT-167, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:643.7637805938721ms\n", "inputText:双其乐白色六格胶盒800套/箱DT-737, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:577.9054164886475ms\n", "inputText:双其乐白色两格盒400套/箱DT-LK09, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:690.8261775970459ms\n", "inputText:双其乐白色胶盒500套/箱DT-LK199, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:667.0496463775635ms\n", "inputText:双其乐白色胶盒+小叉800套/箱CF-07, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:620.2640533447266ms\n", "inputText:双其乐透明两个装胶盒600个/箱CF-09, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:1331.4383029937744ms\n", "inputText:双其乐单个装透明盒1200个/箱CF-10, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:650.6631374359131ms\n", "inputText:双其乐透明胶盒800个/箱CF-11, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:725.024938583374ms\n", "inputText:双其乐透明胶盒600个/箱CF-12, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:631.0791969299316ms\n", "inputText:双其乐白色长形胶盒600套/箱CF-18, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:683.6428642272949ms\n", "inputText:双其乐白色胶盒1000个/箱H-103, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:630.2134990692139ms\n", "inputText:双其乐白色长形胶盒1200套/箱H-2085, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:1268.1710720062256ms\n", "inputText:双其乐黑色胶盒2000套/箱HY-01, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:550.0857830047607ms\n", "inputText:双其乐大日式卷盒+小叉600套/箱HY-05, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:1338.8900756835938ms\n", "inputText:双其乐透明胶盒900个/箱HY-13, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:1563.9829635620117ms\n", "inputText:双其乐米色胶盒1500个/箱HY-17, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:583.9927196502686ms\n", "inputText:双其乐白色胶盒1800个/箱HY-19, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:543.05100440979ms\n", "inputText:双其乐白色胶盒2000套/箱HT-109, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:586.8747234344482ms\n", "inputText:双其乐啡色小拱形胶盒2400套/箱Z2521啡, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:750.5111694335938ms\n", "inputText:双其乐银色小拱形胶盒2400套/箱Z2522银, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:675.7521629333496ms\n", "inputText:双其乐白色胶盒1200套/箱Z2523, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:637.2597217559814ms\n", "inputText:双其乐黑色方形胶盒1000套/箱Z2526, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:571.497917175293ms\n", "inputText:双其乐黑色胶盒600个/箱Z2538-黑, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:645.4517841339111ms\n", "inputText:双其乐白色胶盒600个/箱Z2538-白, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:580.188512802124ms\n", "inputText:双其乐白色胶盒1600套箱Z2623, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:579.0424346923828ms\n", "inputText:双其乐黑色胶盒1500个/箱Z585, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:516.5467262268066ms\n", "inputText:双其乐透明胶盒1500套/箱Z588, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:753.4675598144531ms\n", "inputText:双其乐啡色长形胶盒1200套/箱Z589, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:567.8665637969971ms\n", "inputText:双其乐白色方形胶盒1200套/箱Z712, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:567.0278072357178ms\n", "inputText:双其乐小蛋黄酥盒3600套/箱Z717, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:723.1698036193848ms\n", "inputText:双其乐透明胶盒1200个/箱Z730, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:1331.8119049072266ms\n", "inputText:双其乐白色胶盒1800个/箱Z739, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:908.7865352630615ms\n", "inputText:双其乐白色胶盒800个/箱Z741, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:2029.3543338775635ms\n", "inputText:双其乐白色胶盒1600个/箱Z743, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:1764.7294998168945ms\n", "inputText:双其乐白色胶盒1000套/箱Z744, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:613.591194152832ms\n", "inputText:双其乐透明胶盒600套/箱B123, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:626.1606216430664ms\n", "inputText:多特白色四格胶盒900套/箱B211, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:643.6042785644531ms\n", "inputText:双其乐白色胶盒（大提篮）1800套/箱B-1019R, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:757.9572200775146ms\n", "inputText:双其乐长形卷边纸杯带孔600个/箱BK-0369, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:942.1851634979248ms\n", "inputText:双其乐透明胶盒2000个/箱K11, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:938.0896091461182ms\n", "inputText:双其乐透明胶盒1000套/箱DT-105, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:849.6417999267578ms\n", "inputText:双其乐7#白盒800套/箱017B, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:780.3254127502441ms\n", "inputText:荷美尔牛肉风味披萨粒（速冻熟制品）2.5kg*4包/箱, usage:{\"prompt_tokens\": 37, \"total_tokens\": 37}, time cost:739.0854358673096ms\n", "inputText:荷美尔惠选烘焙培根2kg*6包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:661.9174480438232ms\n", "inputText:荷美尔精选后腿方火腿片500g*20包/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:771.5868949890137ms\n", "inputText:荷美尔精选烘焙热狗肠1kg*10包/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:766.4060592651367ms\n", "inputText:荷美尔惠选鸡肉脆皮热狗肠1kg*10包/箱, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:843.9042568206787ms\n", "inputText:荷美尔精选美式培根1kg*10包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:649.8093605041504ms\n", "inputText:荷美尔生煎罗勒风味香肠1kg*12包/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:646.2163925170898ms\n", "inputText:荷美尔惠选培根1kg*10包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:712.4531269073486ms\n", "inputText:荷美尔烟熏鸡胸肉2kg*8包/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:588.4661674499512ms\n", "inputText:阿黛尔香浓牛奶可丝达6kg/件, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:766.2234306335449ms\n", "inputText:大成30g黑椒味德式肠1kg*10包/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:613.6062145233154ms\n", "inputText:大成32cm德式香肠1kg*12包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:662.9018783569336ms\n", "inputText:大成超值培根肉1kg*12包/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:728.2395362854004ms\n", "inputText:大成德式迷你香肠（原味）1kg*10包/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:746.166467666626ms\n", "inputText:大成火腿片500g*24包/箱, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:965.2063846588135ms\n", "inputText:大成姐妹厨房脆香猪扒3kg*4包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:561.194658279419ms\n", "inputText:大成12cm美式脆皮肠1kg*12包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:526.2942314147949ms\n", "inputText:大成蜜汁黑椒鸡扒2.2kg*4包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:648.5583782196045ms\n", "inputText:大成琵琶腿（生）1kg*10包/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:638.141393661499ms\n", "inputText:大成85X火腿切片500g*20包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:573.4689235687256ms\n", "inputText:大成香熏鸡胸肉1kg*10包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:623.769998550415ms\n", "inputText:大成咖喱鸡肉馅1kg*10包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:531.8834781646729ms\n", "inputText:大成50g德式香肠1kg*12包/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:616.5945529937744ms\n", "inputText:大成可丽奥尔良味鸡扒1.25kg*8包/箱（120片/箱）, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:615.0069236755371ms\n", "inputText:大成15cm美式脆皮肠1kg*10包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:635.9531879425049ms\n", "inputText:大成叉烧酱1kg*10包/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:626.8906593322754ms\n", "inputText:大成早餐腿扒(餐饮版）1kg*10包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:560.1489543914795ms\n", "inputText:大成30g帕罗德式香肠1kg*10包/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:617.2966957092285ms\n", "inputText:大成香熏鸡肉片1kg*12包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:670.2961921691895ms\n", "inputText:大成黑椒牛肉馅1kg*10包/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:656.4407348632812ms\n", "inputText:大成劲脆大鸡排1kg*10包/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:628.8387775421143ms\n", "inputText:大成台式地道肠600g*12包/箱, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:641.1187648773193ms\n", "inputText:大成鸡腿汉堡2.5kg*4包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:822.8695392608643ms\n", "inputText:大成30g法兰克福香肠1kg*10包/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:596.792459487915ms\n", "inputText:大成汉堡肉50片*4包/箱, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:699.317216873169ms\n", "inputText:大成海苔酥脆松1kg*10包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:715.2841091156006ms\n", "inputText:大成台式脆骨肠1kg*10包/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:773.029088973999ms\n", "cache matched:大成30g黑椒味德式肠1kg*10包/箱\n", "inputText:大成超值切片火腿D（N)1kg*10包/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:833.7759971618652ms\n", "inputText:大成海苔肉松1kg*10包/箱（4包）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:732.816219329834ms\n", "inputText:大成烘焙脆鸡排1kg*10包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:754.9149990081787ms\n", "inputText:大成黑松露风味牛肉馅1kg*10包/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:651.7958641052246ms\n", "inputText:大成山楂叉烧馅1kg*10包/箱, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:596.1124897003174ms\n", "inputText:大成港式山楂叉烧西酥540g*16包/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:595.1454639434814ms\n", "inputText:布袋美玫低筋粉25kg/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:596.0524082183838ms\n", "inputText:樱皇精研日式面包粉专用小麦粉25kg/包, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:658.0848693847656ms\n", "inputText:纸袋金装金像面包粉25kg/包, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:631.6127777099609ms\n", "inputText:布袋金像B面包粉25kg/包, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:1261.904001235962ms\n", "inputText:桑爱植脂忌廉980g*12支/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:792.0141220092773ms\n", "inputText:沁护含乳脂植脂忌廉980g*12支/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:715.0533199310303ms\n", "inputText:丝诺含乳脂植脂忌廉907g*12支/箱, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:933.0894947052002ms\n", "inputText:沁护心选含乳脂植脂奶油980g*12支/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:930.0715923309326ms\n", "inputText:恋乳70淡奶油1L*12支/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:942.9140090942383ms\n", "inputText:恋乳稀奶油1L*12支/箱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:808.0086708068848ms\n", "inputText:海融飞青花奶酪奶油980g*12支/箱, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:790.6115055084229ms\n", "inputText:飞青花季歌乳脂植脂奶油980g*12支/箱, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:772.9835510253906ms\n", "inputText:吉福特芝士预拌酱1kg*12支/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:697.0372200012207ms\n", "inputText:麦香植脂忌廉980g*12支/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:672.8990077972412ms\n", "inputText:季歌乳脂奶油1kg*12支/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:586.0893726348877ms\n", "inputText:蒂娜非氢化奶茶味巧克力酱3kg*4桶/箱, usage:{\"prompt_tokens\": 32, \"total_tokens\": 32}, time cost:739.2251491546631ms\n", "inputText:蕾芝音金沙奶黄流心馅1kg*6包/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:650.6431102752686ms\n", "inputText:蕾芝音岩烧酱1kg*6包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:831.0036659240723ms\n", "inputText:蕾芝音海盐柠檬馅1kg*6包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:581.7239284515381ms\n", "inputText:蕾芝音咖喱馅1kg*6包/箱, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:583.9235782623291ms\n", "inputText:蕾芝音卡士达酱（酸奶）1kg*6包/箱, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:538.0802154541016ms\n", "inputText:蕾芝音耐烘烤咸蛋黄馅1kg*6包/箱, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:646.2271213531494ms\n", "inputText:蕾芝音耐烘咸香芝士馅1kg*6包/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:707.1106433868408ms\n", "inputText:麦维客红豆饼13层*20个/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:721.621036529541ms\n", "inputText:麦维客绿豆饼13层*20个/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:616.5239810943604ms\n", "inputText:麦维客2.0丹麦黄油老婆饼220个/箱, usage:{\"prompt_tokens\": 29, \"total_tokens\": 29}, time cost:534.4667434692383ms\n", "inputText:麦维客速冻榴莲酥160个/箱, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:587.0575904846191ms\n", "inputText:麦维客速冻小酥芙（蛋奶味）440个/箱 20g*440个/箱, usage:{\"prompt_tokens\": 36, \"total_tokens\": 36}, time cost:688.300371170044ms\n", "inputText:麦维客速冻小酥芙（海盐芝士味）440个/箱 20g*440个/箱, usage:{\"prompt_tokens\": 38, \"total_tokens\": 38}, time cost:835.1516723632812ms\n", "inputText:麦维客22g盘挞(复合奶油）165个/箱, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:643.9700126647949ms\n", "inputText:麦维客速冻2.0乳脂手工葡式蛋挞皮20个*10包/箱, usage:{\"prompt_tokens\": 37, \"total_tokens\": 37}, time cost:558.9487552642822ms\n", "inputText:麦维客老婆饼13层*20个/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:585.9367847442627ms\n", "inputText:麦维客速冻5.1S乳脂手工葡式蛋挞皮220个/箱, usage:{\"prompt_tokens\": 35, \"total_tokens\": 35}, time cost:589.9124145507812ms\n", "inputText:麦维客速冻号角酥144个/箱, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:541.4345264434814ms\n", "inputText:势道原味金牌SP8厘米松1kg*10包/箱, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:673.8646030426025ms\n", "inputText:势道咸蛋黄酥松2.5kg*6包/箱, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:735.9981536865234ms\n", "inputText:势道SP1馅料肉松2.5kg*6包/箱, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:3604.0096282958984ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['name'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
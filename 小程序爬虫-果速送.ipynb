{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 写入odps\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import requests\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json, logging\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.48(0x1800302d) NetType/4G Language/zh_CN\",\n", "}\n", "brand_name = \"果速送-杭州\"\n", "competitor_name_en = \"guosusong\"\n", "\n", "logging.info(f\"{timestamp_of_now}, headers:{headers}\")\n", "server_url = \"https://app.guoss.cn/gss_api/server/api.do\"\n", "\n", "\n", "# login first\n", "\n", "data = {\n", "    \"method\": \"user_login3\",\n", "    \"websiteNode\": \"3301\",\n", "    \"mobile\": \"***********\",\n", "    \"password\": \"92f60e437c7d8414b23b14e18024049d\",\n", "    \"version\": \"2.1.19\",\n", "}\n", "\n", "token = requests.post(server_url, headers=headers, data=data).json().get('data')\n", "\n", "logging.info(f\"user token:{token}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["token\n", "import hashlib\n", "\n", "\n", "def generate_md5_string(token=token):\n", "    # Concatenate the required strings\n", "    string_to_hash = (\n", "        \"firmId\" + token.get(\"firmInfo\").get(\"id\") + \"key\" + token.get(\"secretKey\")\n", "    )\n", "\n", "    # Generate MD5 hash\n", "    md5_hash = hashlib.md5(string_to_hash.encode()).hexdigest()\n", "\n", "    return md5_hash.upper()\n", "\n", "\n", "sign_string = generate_md5_string()\n", "logging.info(f\"sign_string:{sign_string}\")\n", "\n", "hashlib.md5(\n", "    \"firmId33941keywr2fqc4af72ch2b6j5dgh3fp1epdepq\".encode()\n", ").hexdigest().upper()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取类目列表;\n", "# 一级：\n", "\n", "\n", "import requests\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json\n", "\n", "headers = {\n", "    \"User-Agent\": \"Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36\",\n", "}\n", "\n", "data = {\n", "    \"method\": \"goods_type_first\",\n", "    \"firmId\": \"33941\",\n", "    \"websiteNode\": \"3301\",\n", "    \"version\": \"2.1.19\",\n", "}\n", "\n", "response = requests.post(url=server_url, headers=headers, data=data).json()\n", "\n", "logging.info(f\"一级类目response:{response}\")\n", "\n", "root_category_list = response[\"data\"]\n", "\n", "# 二级\n", "second_category_list_all = []\n", "for root in root_category_list:\n", "    logging.info(f\"获取一级类目的二级类目列表:{root}\")\n", "    second_category_list = requests.post(\n", "        url=server_url,\n", "        headers=headers,\n", "        data={\n", "            \"method\": \"goods_type_second\",\n", "            \"firmId\": \"33941\",\n", "            \"websiteNode\": \"3301\",\n", "            \"version\": \"2.1.19\",\n", "            \"typeCode\": root[\"typeCode\"],\n", "        },\n", "    ).json()\n", "    logging.info(f\"second_category_list:{second_category_list}\")\n", "    second_category_list = second_category_list[\"data\"]\n", "    second_category_list_all.extend(second_category_list)\n", "\n", "logging.info(f\"全部二级类目:{second_category_list_all}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_products_for_second_category(typeCode=6717, pageNo=1):\n", "    products = requests.post(\n", "        url=server_url,\n", "        headers=headers,\n", "        data={\n", "            \"method\": \"goods_info_list_three\",\n", "            \"firmId\": \"33941\",\n", "            \"websiteNode\": \"3301\",\n", "            \"typeCode\": typeCode,\n", "            \"pageSize\": \"50\",\n", "            \"eyeId\": \"\",\n", "            \"pageNo\": pageNo,\n", "            \"keyWord\": \"\",\n", "            \"source\": \"firmId33941\",\n", "            \"sign\": sign_string,\n", "            \"tokenId\": token.get(\"tokenId\"),\n", "            \"version\": \"2.1.19\",\n", "        },\n", "    ).json()\n", "    logging.info(f\"二级类目:{typeCode} 的商品:{products}\")\n", "    products = products[\"data\"][\"page\"][\"objects\"]\n", "    if products is not None and len(products) > 20:\n", "        sub_list = get_products_for_second_category(\n", "            typeCode=typeCode, pageNo=pageNo + 1\n", "        )\n", "        if sub_list is not None:\n", "            products.extend(sub_list)\n", "    return products\n", "\n", "\n", "all_products = []\n", "product_id_map = {}\n", "for cate in second_category_list_all:\n", "    typeName = cate[\"typeName\"]\n", "    sub_list = get_products_for_second_category(cate[\"typeCode\"])\n", "    for product in sub_list:\n", "        if \"typeName\" not in product:\n", "            product[\"typeName\"] = typeName\n", "        else:\n", "            product[\"typeName\"] = f\"{typeName},{product['typeName']}\"\n", "        if product[\"goodsCode\"] in product_id_map:\n", "            print(f\"重复的goodsCode:{product['goodsCode']}\")\n", "        else:\n", "            product_id_map[product[\"goodsCode\"]] = True\n", "            all_products.append(product)\n", "\n", "print(len(all_products), all_products[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import html2text\n", "import concurrent.futures\n", "\n", "all_product_details = []\n", "\n", "\n", "def get_product_detail(goods={}):\n", "    goods_id = goods[\"id\"]\n", "    global all_product_details\n", "    # url=f\"{server_url}method=goods_details_message_four&goodsId={goods_id}&firmId=33941\"\n", "    goodsDetails = requests.post(\n", "        url=server_url,\n", "        headers=headers,\n", "        data={\n", "            \"method\": \"goods_details_message_four\",\n", "            \"goodsId\": goods_id,\n", "            \"firmId\": \"33941\",\n", "            \"source\": \"firmId33941\",\n", "            \"sign\": sign_string,\n", "            \"tokenId\": token.get(\"tokenId\"),\n", "            \"version\": \"2.1.19\",\n", "        },\n", "    ).json()\n", "    logging.info(f\"goods_id:{goods_id} goodsDetails:{goodsDetails}\")\n", "    goodsDetails = goodsDetails[\"data\"][\"goodsDetails\"]\n", "    goodsDetails[\"goodsContextMarkdown\"] = html2text.html2text(\n", "        goodsDetails[\"goodsContext\"]\n", "    )\n", "    if \"typeName\" in goods:\n", "        print(f'{goods[\"typeName\"]}:{goodsDetails[\"goodsName\"]}')\n", "        goodsDetails[\"typeName\"] = goods[\"typeName\"]\n", "    all_product_details.append(goodsDetails)\n", "    return goodsDetails\n", "\n", "\n", "with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:\n", "    # Submit tasks to the executor\n", "    futures = [\n", "        executor.submit(get_product_detail, goods=goods) for goods in all_products\n", "    ]\n", "    # Wait for all tasks to complete\n", "    concurrent.futures.wait(futures)\n", "\n", "all_product_details_df = pd.DataFrame(all_product_details)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2024-08-13 18:53:00][INFO][proxy_setup.py] - sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_guosusong_product_result_df\n", "                             where ds>='20240714' group by ds,competitor_name order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ds</th>\n", "      <th>competitor_name</th>\n", "      <th>recods</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20240813</td>\n", "      <td>guosusong</td>\n", "      <td>542</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20240808</td>\n", "      <td>guosusong</td>\n", "      <td>262</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20240807</td>\n", "      <td>guosusong</td>\n", "      <td>788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20240806</td>\n", "      <td>guosusong</td>\n", "      <td>796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20240805</td>\n", "      <td>guosusong</td>\n", "      <td>787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>20240804</td>\n", "      <td>guosusong</td>\n", "      <td>788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>20240803</td>\n", "      <td>guosusong</td>\n", "      <td>778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>20240802</td>\n", "      <td>guosusong</td>\n", "      <td>1021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>20240801</td>\n", "      <td>guosusong</td>\n", "      <td>735</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>20240731</td>\n", "      <td>guosusong</td>\n", "      <td>725</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>20240730</td>\n", "      <td>guosusong</td>\n", "      <td>726</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>20240729</td>\n", "      <td>guosusong</td>\n", "      <td>720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>20240728</td>\n", "      <td>guosusong</td>\n", "      <td>739</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>20240727</td>\n", "      <td>guosusong</td>\n", "      <td>749</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>20240726</td>\n", "      <td>guosusong</td>\n", "      <td>720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>20240725</td>\n", "      <td>guosusong</td>\n", "      <td>705</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>20240724</td>\n", "      <td>guosusong</td>\n", "      <td>704</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>20240723</td>\n", "      <td>guosusong</td>\n", "      <td>693</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>20240722</td>\n", "      <td>guosusong</td>\n", "      <td>696</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>20240721</td>\n", "      <td>guosusong</td>\n", "      <td>717</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>20240720</td>\n", "      <td>guosusong</td>\n", "      <td>738</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>20240719</td>\n", "      <td>guosusong</td>\n", "      <td>759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>20240718</td>\n", "      <td>guosusong</td>\n", "      <td>765</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>20240717</td>\n", "      <td>guosusong</td>\n", "      <td>740</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>20240716</td>\n", "      <td>guosusong</td>\n", "      <td>724</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>20240715</td>\n", "      <td>guosusong</td>\n", "      <td>709</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>20240714</td>\n", "      <td>guosusong</td>\n", "      <td>712</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          ds competitor_name  recods\n", "0   20240813       guosusong     542\n", "1   20240808       guosusong     262\n", "2   20240807       guosusong     788\n", "3   20240806       guosusong     796\n", "4   20240805       guosusong     787\n", "5   20240804       guosusong     788\n", "6   20240803       guosusong     778\n", "7   20240802       guosusong    1021\n", "8   20240801       guosusong     735\n", "9   20240731       guosusong     725\n", "10  20240730       guosus<PERSON>     726\n", "11  20240729       guo<PERSON><PERSON>     720\n", "12  20240728       guosus<PERSON>     739\n", "13  20240727       guosusong     749\n", "14  20240726       guo<PERSON><PERSON>     720\n", "15  20240725       guosus<PERSON>     705\n", "16  20240724       guosusong     704\n", "17  20240723       guosusong     693\n", "18  20240722       guosusong     696\n", "19  20240721       guosusong     717\n", "20  20240720       guosusong     738\n", "21  20240719       g<PERSON>susong     759\n", "22  20240718       guosusong     765\n", "23  20240717       guosusong     740\n", "24  20240716       guosusong     724\n", "25  20240715       guosusong     709\n", "26  20240714       guo<PERSON>ong     712"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "\n", "all_sku_list_df=pd.DataFrame(all_product_details)\n", "# 写入odps\n", "all_sku_list_df['competitor']=brand_name\n", "all_products_df=all_sku_list_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "\n", "logging.info(df.to_string())\n", "logging.info(f\"===new_record==={brand_name}, 商品数:{len(all_products_df)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}
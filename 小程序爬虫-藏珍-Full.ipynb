{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 11:38:16, date_of_now:2024-03-06, brand_name:藏珍\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/藏珍/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "brand_name=\"藏珍\"\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**************:49761\n", "************:46074\n", "**************:33909\n", "**************:35454\n", "***************:42561\n", "**************:45261\n", "**************:38749\n", "*************:40884\n", "**************:45060\n", "*************:34022\n", "['**************:49761', '************:46074', '**************:33909', '**************:35454', '***************:42561', '**************:45261', '**************:38749', '*************:40884', '**************:45060', '*************:34022']\n"]}], "source": ["def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "import requests\n", "import random\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url, max_retries=3):\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://***********:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response.text\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None\n", "def post_remote_data_with_proxy(url, data, headers):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://***********:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, data=data, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response.text\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["登录获取token并保存"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ************************************************\n"]}, {"data": {"text/plain": ["'YxxFN7eY2A+nOaJJbJDt+Dwjm/YP4opUSvhF4XHmgbKDBA5XgncbT1TXbVRUIEovYitSIJ95gTw2hXjRhxrctBHqgup0+c1AAYhvIp3KrPRz+1j+20xEHwCdvaIkaEBpRGkvEstAxIPDcsgdjWJabrBG1ruyoNfyxe5ljZCvWtM='"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["data={'accounts_name':'***********',\n", "'accounts_pass':'767776326c6433397474576a456271314146394346504c55304974353045764b7761472f687330775870706c495845303435463048505153394b734a44326d76695a6b732f654b4e4c4a4b5937744f6462302b39667755445263637a72436a65376c483641694a30715a6b33314936497547666b4c5838364f7372345a6c583070493144313937727358504b4a6d71684c5963354e4f474e6d646564314f574330505773616369434d41343d',\n", "'login_type':'xcx',}\n", "login_res = post_remote_data_with_proxy(f'https://passport.dhb168.com/login/mUserLogin',data=data,headers=None)\n", "token = json.loads(login_res)['token']\n", "token"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: ************************************************\n", "{'status': 'T', 'data': {'access_token': 'JowRJXUVWXj5xh0OfoLigAJGDiuMAewGReHNarzB', 'token_type': 'Bearer', 'expires_in': 2592000, 'refresh_token': 'ODpIv6YloGuGziq40z1SauBQGNELpiifSwPETJAv', 'skey': 'af40b4276f1231ac7b20475d9aab4b61', 'auth_token': 'a0efb6d5614d587571cf715f12273efd', 'is_more': 'T', 'isShare': True, 'company_id': 198679, 'component_appid': 'wxd2c2555c5732b86f', 'wid': 'wx61a903d0ed3feccf', 'is_dhb': 0, 'registration_code': '6968571,198679', 'registration_code_signature': '024b4ec21b5fe3714e58d73a9a3c795a', 'is_open_safe_login': False, 'forbidden_bind_multiple_wechat': 'F'}, 'code': 200}\n"]}], "source": ["import urllib.parse\n", "\n", "token_data={'client_id':'3',\n", "'client_secret':'1a164178e7a4c824303318b2b6f4a0df337b68d6',\n", "'need_skey':'1',\n", "'scope':'',\n", "'code':'undefined',\n", "'appid':'wx61a903d0ed3feccf',\n", "'grant_type':'agency_password_sso',\n", "'token':f'{token}',\n", "'company_id':'198679',}\n", "\n", "r=post_remote_data_with_proxy(f'https://admin.dhb168.com/Auth/oauth/token', data=token_data,headers=None)\n", "r=json.loads(r)\n", "print(r)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["JowRJXUVWXj5xh0OfoLigAJGDiuMAewGReHNarzB af40b4276f1231ac7b20475d9aab4b61\n", "Using proxy: *************************************************\n", "{'first_category': [{'category_id': '27817', 'company_id': '198679', 'parent_id': '0', 'order_num': '499', 'category_pnum': '0.27817.', 'category_num': '1121', 'category_name': '牛油果/龙眼', 'level_num': '1', 'category_count': '2', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 1, 'calc_goods_count': '1'}, {'category_id': '21554', 'company_id': '198679', 'parent_id': '0', 'order_num': '498', 'category_pnum': '0.21554.', 'category_num': '1001', 'category_name': '芒果类', 'level_num': '1', 'category_count': '3', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 2, 'calc_goods_count': '1'}, {'category_id': '27450', 'company_id': '198679', 'parent_id': '0', 'order_num': '497', 'category_pnum': '0.27450.', 'category_num': '1065', 'category_name': '瓜类', 'level_num': '1', 'category_count': '4', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 1, 'calc_goods_count': '1'}, {'category_id': '27437', 'company_id': '198679', 'parent_id': '0', 'order_num': '496', 'category_pnum': '0.27437.', 'category_num': '1052', 'category_name': '柑橘橙', 'level_num': '1', 'category_count': '3', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/11/1667446444893_18797388334843856.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/11/1667446444893_18797388334843856.png', 'custom_sub_level': 3, 'calc_goods_count': '1'}, {'category_id': '21626', 'company_id': '198679', 'parent_id': '0', 'order_num': '495', 'category_pnum': '0.21626.', 'category_num': '1006', 'category_name': '凤梨/菠萝', 'level_num': '1', 'category_count': '2', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 2, 'calc_goods_count': '1'}, {'category_id': '27468', 'company_id': '198679', 'parent_id': '0', 'order_num': '494', 'category_pnum': '0.27468.', 'category_num': '1083', 'category_name': '草莓/李子/猕猴桃', 'level_num': '1', 'category_count': '3', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 2, 'calc_goods_count': '1'}, {'category_id': '21627', 'company_id': '198679', 'parent_id': '0', 'order_num': '491', 'category_pnum': '0.21627.', 'category_num': '1007', 'category_name': '葡萄/提子', 'level_num': '1', 'category_count': '5', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 2, 'calc_goods_count': '1'}], 'second_category': {'21554': [{'category_id': '27455', 'company_id': '198679', 'parent_id': '21554', 'order_num': '500', 'category_pnum': '0.21554.27455.', 'category_num': '1070', 'category_name': '台芒', 'level_num': '2', 'category_count': '3', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1673749698080_6429771523409247.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1673749698080_6429771523409247.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}], '21626': [{'category_id': '25486', 'company_id': '198679', 'parent_id': '21626', 'order_num': '500', 'category_pnum': '0.21626.25486.', 'category_num': '1020', 'category_name': '有冠凤梨', 'level_num': '2', 'category_count': '2', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/17/1669714366465_84472187520813.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/17/1669714366465_84472187520813.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}], '21627': [{'category_id': '25485', 'company_id': '198679', 'parent_id': '21627', 'order_num': '499', 'category_pnum': '0.21627.25485.', 'category_num': '1019', 'category_name': '夏黑葡萄', 'level_num': '2', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/9/1667524998894_4642430645843063.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/9/1667524998894_4642430645843063.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}, {'category_id': '26789', 'company_id': '198679', 'parent_id': '21627', 'order_num': '498', 'category_pnum': '0.21627.26789.', 'category_num': '1035', 'category_name': '青提', 'level_num': '2', 'category_count': '2', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/14/1666766580158_664671174206056.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/14/1666766580158_664671174206056.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}], '25507': [], '27400': [], '27437': [{'category_id': '27438', 'company_id': '198679', 'parent_id': '27437', 'order_num': '500', 'category_pnum': '0.27437.27438.', 'category_num': '1053', 'category_name': '柠檬', 'level_num': '2', 'category_count': '5', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 1, 'calc_goods_count': '1'}, {'category_id': '27444', 'company_id': '198679', 'parent_id': '27437', 'order_num': '500', 'category_pnum': '0.27437.27444.', 'category_num': '1059', 'category_name': '柑桔', 'level_num': '2', 'category_count': '3', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/9/1708133570828_8726460989979559.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/9/1708133570828_8726460989979559.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}, {'category_id': '27447', 'company_id': '198679', 'parent_id': '27437', 'order_num': '500', 'category_pnum': '0.27437.27447.', 'category_num': '1062', 'category_name': '橙类', 'level_num': '2', 'category_count': '2', 'goods_count': '0', 'is_default': 'F', 'category_image': '', 'has_child': 'T', 'goods_picture': '', 'custom_sub_level': 2, 'calc_goods_count': '1'}], '27450': [{'category_id': '27451', 'company_id': '198679', 'parent_id': '27450', 'order_num': '500', 'category_pnum': '0.27450.27451.', 'category_num': '1066', 'category_name': '麒麟西瓜', 'level_num': '2', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/17/1669714095153_17892891133953248.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/17/1669714095153_17892891133953248.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27468': [{'category_id': '27820', 'company_id': '198679', 'parent_id': '27468', 'order_num': '500', 'category_pnum': '0.27468.27820.', 'category_num': '1125', 'category_name': '草莓', 'level_num': '2', 'category_count': '5', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/9/1670722916447_31578669828737227.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/9/1670722916447_31578669828737227.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}, {'category_id': '29135', 'company_id': '198679', 'parent_id': '27468', 'order_num': '500', 'category_pnum': '0.27468.29135.', 'category_num': '1157', 'category_name': '猕猴桃', 'level_num': '2', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/17/1670317625960_9209301420663378.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/17/1670317625960_9209301420663378.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27478': [], '27804': [], '27808': [], '27814': [], '27817': [{'category_id': '28730', 'company_id': '198679', 'parent_id': '27817', 'order_num': '500', 'category_pnum': '0.27817.28730.', 'category_num': '1133', 'category_name': '龙眼', 'level_num': '2', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/9/1673745679979_24491040254924368.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/9/1673745679979_24491040254924368.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '29132': []}, 'third_category': {'25486': [{'category_id': '27460', 'company_id': '198679', 'parent_id': '25486', 'order_num': '500', 'category_pnum': '0.21626.25486.27460.', 'category_num': '1075', 'category_name': '佳农凤梨有冠6-9', 'level_num': '3', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/17/1669714400572_6889506306393953.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/17/1669714400572_6889506306393953.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '25487': [], '26789': [{'category_id': '28737', 'company_id': '198679', 'parent_id': '26789', 'order_num': '500', 'category_pnum': '0.21627.26789.28737.', 'category_num': '1139', 'category_name': '阳光玫瑰', 'level_num': '3', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1670552076667_4852683702494722.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1670552076667_4852683702494722.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27438': [{'category_id': '27439', 'company_id': '198679', 'parent_id': '27438', 'order_num': '500', 'category_pnum': '0.27437.27438.27439.', 'category_num': '1054', 'category_name': '香水柠檬一级', 'level_num': '3', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/11/1667531809825_8890380387623855.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/11/1667531809825_8890380387623855.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}, {'category_id': '27442', 'company_id': '198679', 'parent_id': '27438', 'order_num': '500', 'category_pnum': '0.27437.27438.27442.', 'category_num': '1057', 'category_name': '黄柠檬', 'level_num': '3', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/16/1670317030861_8522933714041201.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/16/1670317030861_8522933714041201.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27444': [{'category_id': '27445', 'company_id': '198679', 'parent_id': '27444', 'order_num': '500', 'category_pnum': '0.27437.27444.27445.', 'category_num': '1060', 'category_name': '青金桔', 'level_num': '3', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1672193366225_8108435662280824.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1672193366225_8108435662280824.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}], '27447': [{'category_id': '27449', 'company_id': '198679', 'parent_id': '27447', 'order_num': '500', 'category_pnum': '0.27437.27447.27449.', 'category_num': '1064', 'category_name': '国产橙', 'level_num': '3', 'category_count': '1', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1669776504504_5694896628822617.png', 'has_child': 'T', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1669776504504_5694896628822617.png', 'custom_sub_level': 1, 'calc_goods_count': '1'}], '27455': [], '27479': [], '27820': [], '27823': []}, 'four_category': {'27448': [], '27449': [{'category_id': '27711', 'company_id': '198679', 'parent_id': '27449', 'order_num': '500', 'category_pnum': '0.27437.27447.27449.27711.', 'category_num': '1103', 'category_name': '赣南脐橙', 'level_num': '4', 'category_count': '0', 'goods_count': '0', 'is_default': 'F', 'category_image': '000198/*********/10/1669776545916_7684729528982541.png', 'has_child': 'F', 'goods_picture': 'https://img.dhb168.com/000198/*********/10/1669776545916_7684729528982541.png', 'custom_sub_level': 0, 'calc_goods_count': '1'}]}, 'level_num': 4, 'debug': []}\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>category_id</th>\n", "      <th>company_id</th>\n", "      <th>parent_id</th>\n", "      <th>order_num</th>\n", "      <th>category_pnum</th>\n", "      <th>category_num</th>\n", "      <th>category_name</th>\n", "      <th>level_num</th>\n", "      <th>category_count</th>\n", "      <th>goods_count</th>\n", "      <th>is_default</th>\n", "      <th>category_image</th>\n", "      <th>has_child</th>\n", "      <th>goods_picture</th>\n", "      <th>custom_sub_level</th>\n", "      <th>calc_goods_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>27817</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>499</td>\n", "      <td>0.27817.</td>\n", "      <td>1121</td>\n", "      <td>牛油果/龙眼</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21554</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>498</td>\n", "      <td>0.21554.</td>\n", "      <td>1001</td>\n", "      <td>芒果类</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>27450</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>497</td>\n", "      <td>0.27450.</td>\n", "      <td>1065</td>\n", "      <td>瓜类</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>27437</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>496</td>\n", "      <td>0.27437.</td>\n", "      <td>1052</td>\n", "      <td>柑橘橙</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td>000198/*********/11/1667446444893_187973883348...</td>\n", "      <td>T</td>\n", "      <td>https://img.dhb168.com/000198/*********/11/166...</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21626</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>495</td>\n", "      <td>0.21626.</td>\n", "      <td>1006</td>\n", "      <td>凤梨/菠萝</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>27468</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>494</td>\n", "      <td>0.27468.</td>\n", "      <td>1083</td>\n", "      <td>草莓/李子/猕猴桃</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>21627</td>\n", "      <td>198679</td>\n", "      <td>0</td>\n", "      <td>491</td>\n", "      <td>0.21627.</td>\n", "      <td>1007</td>\n", "      <td>葡萄/提子</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>F</td>\n", "      <td></td>\n", "      <td>T</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  category_id company_id parent_id order_num category_pnum category_num  \\\n", "0       27817     198679         0       499      0.27817.         1121   \n", "1       21554     198679         0       498      0.21554.         1001   \n", "2       27450     198679         0       497      0.27450.         1065   \n", "3       27437     198679         0       496      0.27437.         1052   \n", "4       21626     198679         0       495      0.21626.         1006   \n", "5       27468     198679         0       494      0.27468.         1083   \n", "6       21627     198679         0       491      0.21627.         1007   \n", "\n", "  category_name level_num category_count goods_count is_default  \\\n", "0        牛油果/龙眼         1              2           0          F   \n", "1           芒果类         1              3           0          F   \n", "2            瓜类         1              4           0          F   \n", "3           柑橘橙         1              3           0          F   \n", "4         凤梨/菠萝         1              2           0          F   \n", "5     草莓/李子/猕猴桃         1              3           0          F   \n", "6         葡萄/提子         1              5           0          F   \n", "\n", "                                      category_image has_child  \\\n", "0                                                            T   \n", "1                                                            T   \n", "2                                                            T   \n", "3  000198/*********/11/1667446444893_187973883348...         T   \n", "4                                                            T   \n", "5                                                            T   \n", "6                                                            T   \n", "\n", "                                       goods_picture  custom_sub_level  \\\n", "0                                                                    1   \n", "1                                                                    2   \n", "2                                                                    1   \n", "3  https://img.dhb168.com/000198/*********/11/166...                 3   \n", "4                                                                    2   \n", "5                                                                    2   \n", "6                                                                    2   \n", "\n", "  calc_goods_count  \n", "0                1  \n", "1                1  \n", "2                1  \n", "3                1  \n", "4                1  \n", "5                1  \n", "6                1  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["access_token = r['data']['access_token']\n", "skey = r['data']['skey']\n", "\n", "skey_var=f'{{\"skey\":\"{skey}\",\"version\":\"10.9\"}}'\n", "data={\n", "    'a':'goodsNewCategory',\n", "    'val':f'{skey_var}',\n", "    'c':'Ding<PERSON><PERSON>',\n", "}\n", "print(access_token, skey)\n", "\n", "headers = {\n", "    \"Authorization\": f\"Bearer {access_token}\"\n", "}\n", "\n", "url=f'https://api.dhb168.com/api.php'\n", "\n", "cate_list = json.loads(post_remote_data_with_proxy(url, data=data, headers=headers))['data']\n", "print(cate_list)\n", "first_category_df=pd.DataFrame(cate_list['first_category'])\n", "first_category_df"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["first category id:21554\n", "first category id:21626\n", "first category id:21627\n", "first category id:25507\n", "first category id:27400\n", "first category id:27437\n", "first category id:27450\n", "first category id:27468\n", "first category id:27478\n", "first category id:27804\n", "first category id:27808\n", "first category id:27814\n", "first category id:27817\n", "first category id:29132\n", "Using proxy: **********************************************\n", "category_id:27455, product count:1\n", "Using proxy: ***********************************************\n", "category_id:25486, product count:2\n", "Using proxy: ***********************************************\n", "category_id:25485, product count:1\n", "Using proxy: **********************************************\n", "category_id:26789, product count:1\n", "Using proxy: ************************************************\n", "category_id:27438, product count:2\n", "Using proxy: ***********************************************\n", "category_id:27444, product count:1\n", "Using proxy: ************************************************\n", "category_id:27447, product count:1\n", "Using proxy: *************************************************\n", "category_id:27451, product count:1\n", "Using proxy: ************************************************\n", "category_id:27820, product count:1\n", "Using proxy: ************************************************\n", "category_id:29135, product count:1\n", "Using proxy: ************************************************\n", "category_id:28730, product count:1\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>goods_id</th>\n", "      <th>multi_id</th>\n", "      <th>goods_num</th>\n", "      <th>goods_type</th>\n", "      <th>goods_name</th>\n", "      <th>goods_model</th>\n", "      <th>goods_picture</th>\n", "      <th>selling_price</th>\n", "      <th>whole_price</th>\n", "      <th>base_units</th>\n", "      <th>...</th>\n", "      <th>promotion_limit_msg</th>\n", "      <th>number_price</th>\n", "      <th>price</th>\n", "      <th>options_id</th>\n", "      <th>price_id</th>\n", "      <th>multi_price_id</th>\n", "      <th>options</th>\n", "      <th>units_list</th>\n", "      <th>goods_new_type</th>\n", "      <th>goods_type_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>278399</td>\n", "      <td>0</td>\n", "      <td>cyyk100715</td>\n", "      <td></td>\n", "      <td>台农芒果/中台 净重12斤/份±100g</td>\n", "      <td>净重12斤±100g</td>\n", "      <td>https://img.dhb168.com/000198/*********/10/167...</td>\n", "      <td>0.00</td>\n", "      <td>81.60</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>81.60</td>\n", "      <td>0</td>\n", "      <td>309079</td>\n", "      <td>309079</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>274939</td>\n", "      <td>0</td>\n", "      <td>CSK100510</td>\n", "      <td></td>\n", "      <td>佳农sw有冠凤梨 4粒装</td>\n", "      <td>4粒装</td>\n", "      <td>https://img.dhb168.com/000198/*********/17/166...</td>\n", "      <td>0.00</td>\n", "      <td>72.50</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>72.50</td>\n", "      <td>0</td>\n", "      <td>303990</td>\n", "      <td>303990</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>274688</td>\n", "      <td>0</td>\n", "      <td>CYK100468</td>\n", "      <td></td>\n", "      <td>佳农sw有冠凤梨 6-9粒装</td>\n", "      <td>有冠6-9粒</td>\n", "      <td>https://img.dhb168.com/000198/*********/17/166...</td>\n", "      <td>0.00</td>\n", "      <td>145.00</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>145.00</td>\n", "      <td>0</td>\n", "      <td>303583</td>\n", "      <td>303583</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>274953</td>\n", "      <td>0</td>\n", "      <td>CYS100511</td>\n", "      <td>2,3</td>\n", "      <td>夏黑葡萄 净重10斤/份±100g</td>\n", "      <td>10斤/份±100g</td>\n", "      <td>https://img.dhb168.com/000198/*********/9/1667...</td>\n", "      <td>0.00</td>\n", "      <td>143.00</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>143.00</td>\n", "      <td>0</td>\n", "      <td>304012</td>\n", "      <td>304012</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>[]</td>\n", "      <td>[荐, 热]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>277067</td>\n", "      <td>0</td>\n", "      <td>CYK100675</td>\n", "      <td></td>\n", "      <td>阳光玫瑰青提  净重8斤±50g</td>\n", "      <td>8斤/份±50g</td>\n", "      <td>https://img.dhb168.com/000198/*********/10/167...</td>\n", "      <td>0.00</td>\n", "      <td>213.60</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>213.60</td>\n", "      <td>0</td>\n", "      <td>307429</td>\n", "      <td>307429</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>[{'tags_name': '惠', 'tags_color': ''}]</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>294204</td>\n", "      <td>0</td>\n", "      <td>CSK100768</td>\n", "      <td>2,1</td>\n", "      <td>应季香水柠檬保鲜装  净果10斤±100g</td>\n", "      <td>10斤±100g</td>\n", "      <td>https://img.dhb168.com/000198/*********/11/166...</td>\n", "      <td>0.00</td>\n", "      <td>95.00</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>95.00</td>\n", "      <td>0</td>\n", "      <td>326259</td>\n", "      <td>326259</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>[]</td>\n", "      <td>[荐, 新]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>274695</td>\n", "      <td>0</td>\n", "      <td>SK100474</td>\n", "      <td></td>\n", "      <td>黄柠檬 净果5斤±50g</td>\n", "      <td>5斤±50g</td>\n", "      <td>https://img.dhb168.com/000198/*********/16/167...</td>\n", "      <td>0.00</td>\n", "      <td>15.00</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>15.00</td>\n", "      <td>0</td>\n", "      <td>303590</td>\n", "      <td>303590</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>274690</td>\n", "      <td>0</td>\n", "      <td>SYK100469</td>\n", "      <td></td>\n", "      <td>青金桔 净重3斤/份±30g</td>\n", "      <td>3斤/份±30g</td>\n", "      <td>https://img.dhb168.com/000198/*********/15/166...</td>\n", "      <td>0.00</td>\n", "      <td>34.20</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>34.20</td>\n", "      <td>0</td>\n", "      <td>303585</td>\n", "      <td>303585</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>275933</td>\n", "      <td>0</td>\n", "      <td>YYZK100657</td>\n", "      <td></td>\n", "      <td>脐橙  净果10斤/份±50g</td>\n", "      <td>10斤/份±50g</td>\n", "      <td>https://img.dhb168.com/000198/*********/10/166...</td>\n", "      <td>0.00</td>\n", "      <td>41.00</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>41.00</td>\n", "      <td>0</td>\n", "      <td>305547</td>\n", "      <td>305547</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>283809</td>\n", "      <td>0</td>\n", "      <td>S100734</td>\n", "      <td></td>\n", "      <td>无籽麒麟西瓜 毛重15-17斤±500g（快递）</td>\n", "      <td>15-17斤±500g</td>\n", "      <td>https://img.dhb168.com/000198/*********/15/166...</td>\n", "      <td>0.00</td>\n", "      <td>90.00</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>90.00</td>\n", "      <td>0</td>\n", "      <td>315353</td>\n", "      <td>315353</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>277193</td>\n", "      <td>0</td>\n", "      <td>CYK100680</td>\n", "      <td>2,3</td>\n", "      <td>红颜草莓盒装  14盒/份 250g/盒</td>\n", "      <td>14盒/份  250g/盒</td>\n", "      <td>https://img.dhb168.com/000198/*********/9/1672...</td>\n", "      <td>0.00</td>\n", "      <td>165.20</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>165.20</td>\n", "      <td>0</td>\n", "      <td>307597</td>\n", "      <td>307597</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>[]</td>\n", "      <td>[荐, 热]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>274888</td>\n", "      <td>0</td>\n", "      <td>YK100494</td>\n", "      <td></td>\n", "      <td>猕猴桃 净重8斤±100g（快递）</td>\n", "      <td>8斤±100g</td>\n", "      <td>https://img.dhb168.com/000198/*********/17/167...</td>\n", "      <td>0.00</td>\n", "      <td>52.00</td>\n", "      <td>份</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>52.00</td>\n", "      <td>0</td>\n", "      <td>303926</td>\n", "      <td>303926</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>276999</td>\n", "      <td>0</td>\n", "      <td>YK100670</td>\n", "      <td>1,2</td>\n", "      <td>龙眼  净重5斤/份±50g</td>\n", "      <td>5斤/份±50g</td>\n", "      <td>https://img.dhb168.com/000198/*********/9/1673...</td>\n", "      <td>0.00</td>\n", "      <td>41.00</td>\n", "      <td>盒</td>\n", "      <td>...</td>\n", "      <td></td>\n", "      <td>[]</td>\n", "      <td>41.00</td>\n", "      <td>0</td>\n", "      <td>307351</td>\n", "      <td>307351</td>\n", "      <td>F</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>[]</td>\n", "      <td>[新, 荐]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>13 rows × 57 columns</p>\n", "</div>"], "text/plain": ["   goods_id multi_id   goods_num goods_type                goods_name  \\\n", "0    278399        0  cyyk100715                 台农芒果/中台 净重12斤/份±100g   \n", "1    274939        0   CSK100510                         佳农sw有冠凤梨 4粒装   \n", "2    274688        0   CYK100468                       佳农sw有冠凤梨 6-9粒装   \n", "3    274953        0   CYS100511        2,3         夏黑葡萄 净重10斤/份±100g   \n", "4    277067        0   CYK100675                     阳光玫瑰青提  净重8斤±50g   \n", "5    294204        0   CSK100768        2,1     应季香水柠檬保鲜装  净果10斤±100g   \n", "6    274695        0    SK100474                         黄柠檬 净果5斤±50g   \n", "7    274690        0   SYK100469                       青金桔 净重3斤/份±30g   \n", "8    275933        0  YYZK100657                      脐橙  净果10斤/份±50g   \n", "9    283809        0     S100734             无籽麒麟西瓜 毛重15-17斤±500g（快递）   \n", "10   277193        0   CYK100680        2,3      红颜草莓盒装  14盒/份 250g/盒   \n", "11   274888        0    YK100494                    猕猴桃 净重8斤±100g（快递）   \n", "12   276999        0    YK100670        1,2            龙眼  净重5斤/份±50g   \n", "\n", "      goods_model                                      goods_picture  \\\n", "0      净重12斤±100g  https://img.dhb168.com/000198/*********/10/167...   \n", "1             4粒装  https://img.dhb168.com/000198/*********/17/166...   \n", "2          有冠6-9粒  https://img.dhb168.com/000198/*********/17/166...   \n", "3      10斤/份±100g  https://img.dhb168.com/000198/*********/9/1667...   \n", "4        8斤/份±50g  https://img.dhb168.com/000198/*********/10/167...   \n", "5        10斤±100g  https://img.dhb168.com/000198/*********/11/166...   \n", "6          5斤±50g  https://img.dhb168.com/000198/*********/16/167...   \n", "7        3斤/份±30g  https://img.dhb168.com/000198/*********/15/166...   \n", "8       10斤/份±50g  https://img.dhb168.com/000198/*********/10/166...   \n", "9     15-17斤±500g  https://img.dhb168.com/000198/*********/15/166...   \n", "10  14盒/份  250g/盒  https://img.dhb168.com/000198/*********/9/1672...   \n", "11        8斤±100g  https://img.dhb168.com/000198/*********/17/167...   \n", "12       5斤/份±50g  https://img.dhb168.com/000198/*********/9/1673...   \n", "\n", "   selling_price whole_price base_units  ... promotion_limit_msg number_price  \\\n", "0           0.00       81.60          份  ...                               []   \n", "1           0.00       72.50          份  ...                               []   \n", "2           0.00      145.00          份  ...                               []   \n", "3           0.00      143.00          份  ...                               []   \n", "4           0.00      213.60          份  ...                               []   \n", "5           0.00       95.00          份  ...                               []   \n", "6           0.00       15.00          份  ...                               []   \n", "7           0.00       34.20          份  ...                               []   \n", "8           0.00       41.00          份  ...                               []   \n", "9           0.00       90.00          份  ...                               []   \n", "10          0.00      165.20          份  ...                               []   \n", "11          0.00       52.00          份  ...                               []   \n", "12          0.00       41.00          盒  ...                               []   \n", "\n", "     price options_id price_id multi_price_id options  \\\n", "0    81.60          0   309079         309079       F   \n", "1    72.50          0   303990         303990       F   \n", "2   145.00          0   303583         303583       F   \n", "3   143.00          0   304012         304012       F   \n", "4   213.60          0   307429         307429       F   \n", "5    95.00          0   326259         326259       F   \n", "6    15.00          0   303590         303590       F   \n", "7    34.20          0   303585         303585       F   \n", "8    41.00          0   305547         305547       F   \n", "9    90.00          0   315353         315353       F   \n", "10  165.20          0   307597         307597       F   \n", "11   52.00          0   303926         303926       F   \n", "12   41.00          0   307351         307351       F   \n", "\n", "                                           units_list  \\\n", "0   [{'units_type': 'base_units', 'rate_number': '...   \n", "1   [{'units_type': 'base_units', 'rate_number': '...   \n", "2   [{'units_type': 'base_units', 'rate_number': '...   \n", "3   [{'units_type': 'base_units', 'rate_number': '...   \n", "4   [{'units_type': 'base_units', 'rate_number': '...   \n", "5   [{'units_type': 'base_units', 'rate_number': '...   \n", "6   [{'units_type': 'base_units', 'rate_number': '...   \n", "7   [{'units_type': 'base_units', 'rate_number': '...   \n", "8   [{'units_type': 'base_units', 'rate_number': '...   \n", "9   [{'units_type': 'base_units', 'rate_number': '...   \n", "10  [{'units_type': 'base_units', 'rate_number': '...   \n", "11  [{'units_type': 'base_units', 'rate_number': '...   \n", "12  [{'units_type': 'base_units', 'rate_number': '...   \n", "\n", "                            goods_new_type goods_type_name  \n", "0                                      NaN             NaN  \n", "1                                      NaN             NaN  \n", "2                                      NaN             NaN  \n", "3                                       []          [荐, 热]  \n", "4   [{'tags_name': '惠', 'tags_color': ''}]              []  \n", "5                                       []          [荐, 新]  \n", "6                                      NaN             NaN  \n", "7                                      NaN             NaN  \n", "8                                      NaN             NaN  \n", "9                                      NaN             NaN  \n", "10                                      []          [荐, 热]  \n", "11                                     NaN             NaN  \n", "12                                      []          [新, 荐]  \n", "\n", "[13 rows x 57 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data={'a':'goodsList',\n", "      'c':'Ding<PERSON><PERSON>',\n", "      'val':f'{{\"page\":1,\"step\":30,\"category_id\":\"27460\",\"type\":0,\"skey\":\"{skey}\",\"version\":\"10.9\"}}'}\n", "\n", "second_category=cate_list['second_category']\n", "second_category_list=[]\n", "for category_list in second_category:\n", "    print(f\"first category id:{category_list}\")\n", "    second_category_list.extend(second_category[category_list])\n", "\n", "product_list_all=[]\n", "for cate in second_category_list:\n", "    category_id=cate[\"category_id\"]\n", "    data={'a':'goodsList',\n", "      'c':'Ding<PERSON><PERSON>',\n", "      'val':f'{{\"page\":1,\"step\":30,\"category_id\":\"{category_id}\",\"type\":0,\"skey\":\"{skey}\",\"version\":\"10.9\"}}'}\n", "\n", "    product_list = json.loads(post_remote_data_with_proxy(url, data=data, headers=headers))['data']['list']\n", "    print(f'category_id:{category_id}, product count:{len(product_list)}')\n", "    product_list_all.extend(product_list)\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>goods_id</th>\n", "      <th>goods_num</th>\n", "      <th>goods_type</th>\n", "      <th>goods_name</th>\n", "      <th>goods_model</th>\n", "      <th>goods_picture</th>\n", "      <th>whole_price</th>\n", "      <th>price</th>\n", "      <th>base_units</th>\n", "      <th>stock</th>\n", "      <th>units_list</th>\n", "      <th>field_data</th>\n", "      <th>数据获取时间</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>278399</td>\n", "      <td>cyyk100715</td>\n", "      <td></td>\n", "      <td>台农芒果/中台 净重12斤/份±100g</td>\n", "      <td>净重12斤±100g</td>\n", "      <td>https://img.dhb168.com/000198/*********/10/167...</td>\n", "      <td>81.60</td>\n", "      <td>81.60</td>\n", "      <td>份</td>\n", "      <td>37.00</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>[{'name': '净重', 'value': '单果100克起'}, {'name': ...</td>\n", "      <td>2024-03-06 11:38:16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>274939</td>\n", "      <td>CSK100510</td>\n", "      <td></td>\n", "      <td>佳农sw有冠凤梨 4粒装</td>\n", "      <td>4粒装</td>\n", "      <td>https://img.dhb168.com/000198/*********/17/166...</td>\n", "      <td>72.50</td>\n", "      <td>72.50</td>\n", "      <td>份</td>\n", "      <td>20.00</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>[{'name': '净重', 'value': '单果2.2斤左右'}, {'name':...</td>\n", "      <td>2024-03-06 11:38:16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>274688</td>\n", "      <td>CYK100468</td>\n", "      <td></td>\n", "      <td>佳农sw有冠凤梨 6-9粒装</td>\n", "      <td>有冠6-9粒</td>\n", "      <td>https://img.dhb168.com/000198/*********/17/166...</td>\n", "      <td>145.00</td>\n", "      <td>145.00</td>\n", "      <td>份</td>\n", "      <td>39.00</td>\n", "      <td>[{'units_type': 'base_units', 'rate_number': '...</td>\n", "      <td>[{'name': '净重', 'value': '单果2.2斤起'}, {'name': ...</td>\n", "      <td>2024-03-06 11:38:16</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  goods_id   goods_num goods_type            goods_name goods_model  \\\n", "0   278399  cyyk100715             台农芒果/中台 净重12斤/份±100g  净重12斤±100g   \n", "1   274939   CSK100510                     佳农sw有冠凤梨 4粒装         4粒装   \n", "2   274688   CYK100468                   佳农sw有冠凤梨 6-9粒装      有冠6-9粒   \n", "\n", "                                       goods_picture whole_price   price  \\\n", "0  https://img.dhb168.com/000198/*********/10/167...       81.60   81.60   \n", "1  https://img.dhb168.com/000198/*********/17/166...       72.50   72.50   \n", "2  https://img.dhb168.com/000198/*********/17/166...      145.00  145.00   \n", "\n", "  base_units  stock                                         units_list  \\\n", "0          份  37.00  [{'units_type': 'base_units', 'rate_number': '...   \n", "1          份  20.00  [{'units_type': 'base_units', 'rate_number': '...   \n", "2          份  39.00  [{'units_type': 'base_units', 'rate_number': '...   \n", "\n", "                                          field_data               数据获取时间  \n", "0  [{'name': '净重', 'value': '单果100克起'}, {'name': ...  2024-03-06 11:38:16  \n", "1  [{'name': '净重', 'value': '单果2.2斤左右'}, {'name':...  2024-03-06 11:38:16  \n", "2  [{'name': '净重', 'value': '单果2.2斤起'}, {'name': ...  2024-03-06 11:38:16  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["all_products_clean=[]\n", "for product in product_list_all:\n", "    clean_product={}\n", "    clean_product['goods_id']=product['goods_id']\n", "    clean_product['goods_num']=product['goods_num']\n", "    clean_product['goods_type']=product['goods_type']\n", "    clean_product['goods_name']=product['goods_name']\n", "    clean_product['goods_model']=product['goods_model']\n", "    clean_product['goods_picture']=product['goods_picture']\n", "    clean_product['whole_price']=product['whole_price']\n", "    clean_product['price']=product['price']\n", "    clean_product['base_units']=product['base_units']\n", "    clean_product['stock']=product['stock']\n", "    clean_product['units_list']=product['units_list']\n", "    clean_product['field_data']=product['field_data']\n", "    clean_product['数据获取时间']=time_of_now\n", "    all_products_clean.append(clean_product)\n", "\n", "\n", "date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(product_list_all_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "df_cate_list=pd.DataFrame(all_products_clean)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-清洗后数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:台农芒果/中台 净重12斤/份±100g, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:896.3959217071533ms\n", "inputText:佳农sw有冠凤梨 4粒装, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:657.0849418640137ms\n", "inputText:佳农sw有冠凤梨 6-9粒装, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:544.3177223205566ms\n", "inputText:夏黑葡萄 净重10斤/份±100g, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:503.2036304473877ms\n", "inputText:阳光玫瑰青提  净重8斤±50g, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:614.5648956298828ms\n", "inputText:应季香水柠檬保鲜装  净果10斤±100g, usage:{\"prompt_tokens\": 26, \"total_tokens\": 26}, time cost:635.1039409637451ms\n", "inputText:黄柠檬 净果5斤±50g, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:624.255895614624ms\n", "inputText:青金桔 净重3斤/份±30g, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:526.3080596923828ms\n", "inputText:脐橙  净果10斤/份±50g, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:941.1492347717285ms\n", "inputText:无籽麒麟西瓜 毛重15-17斤±500g（快递）, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:498.7912178039551ms\n", "inputText:红颜草莓盒装  14盒/份 250g/盒, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:485.81504821777344ms\n", "inputText:猕猴桃 净重8斤±100g（快递）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:523.3604907989502ms\n", "inputText:龙眼  净重5斤/份±50g, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:526.5073776245117ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['goods_name'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}
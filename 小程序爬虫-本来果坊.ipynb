{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 写入odps\n", "import requests\n", "import json\n", "import hashlib\n", "import time\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import os\n", "from odps import ODPS, DataFrame\n", "from odps.accounts import StsAccount\n", "from scripts.proxy_setup import get_remote_data_with_proxy_json\n", "import traceback\n", "import concurrent.futures\n", "import threading\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "timestamp_of_now = int(datetime.now().timestamp()) * 1000 + 235\n", "\n", "headers = {\n", "    \"Host\": \"guofangapi.lpq1688.com\",\n", "    \"Connection\": \"keep-alive\",\n", "    \"content-type\": \"application/json\",\n", "    \"Accept-Encoding\": \"gzip,compress,br,deflate\",\n", "    \"User-Agent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.59(0x18003b2c) NetType/4G Language/zh_CN\",\n", "    \"Referer\": \"https://servicewechat.com/wxd6c6696aa2913f8c/43/page-frame.html\",\n", "}\n", "brand_name = \"本来果坊\"\n", "competitor_name_en = \"ben<PERSON><PERSON><PERSON><PERSON>\"\n", "\n", "print(f\"{timestamp_of_now}, headers:{headers}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 用以下代码实现登录\n", "import requests # 确保requests库已导入\n", "\n", "# 创建一个会话对象，它会自动管理cookies\n", "session = requests.Session()\n", "\n", "# 登录API的完整URL\n", "login_url = 'https://guofangapi.lpq1688.com/IAccount/Login?Source=1&Version=3.8.6&Client=Applets&DeviceID=67FF8930-8801-43AC-8C76-0F3116AE1556&LoginType=2&customerID=***********&md5Pwd=e10adc3949ba59abbe56e057f20f883e'\n", "\n", "# 使用会话对象发送GET请求，自动处理cookies\n", "cookies = None\n", "cookies_map = {}\n", "try:\n", "    response = session.get(url=login_url, headers=headers, timeout=10)\n", "    response.raise_for_status() # 检查HTTP状态码，如果不是2xx则抛出异常\n", "    token = response.json()\n", "    cookies = response.cookies\n", "except requests.exceptions.RequestException as e:\n", "    print(f\"登录请求失败: {e}\")\n", "    token = None # 登录失败时将token设为None\n", "\n", "# 打印登录结果\n", "print(f\"登录结果: {token}\")\n", "if cookies:\n", "    for cookie in cookies:\n", "        print(f\"获取到的cookies: {cookie.name}={cookie.value}\")\n", "        cookies_map[cookie.name] = cookie.value\n", "else:\n", "    print(\"未获取到cookies\")\n", "\n", "# 注意：此处的session对象已包含登录API返回的cookies，后续所有需要认证的API调用都应使用此session对象\n", "# 例如，将 get_remote_data_with_proxy_json 替换为 session.get() 或 session.post()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd # 确保pandas库已导入\n", "\n", "# 定义需要获取的madeIn类型：0代表国产，1代表进口\n", "made_in_types = {\n", "    0: \"国产\",\n", "    1: \"进口\"\n", "}\n", "\n", "# 初始化一个空的DataFrame列表，用于存储不同madeIn类型的结果\n", "all_categories_dfs = []\n", "\n", "for made_in_value, made_in_name in made_in_types.items():\n", "    # 根据madeIn值构建分类列表API的完整URL\n", "    category_list_api_url = f'https://guofangapi.lpq1688.com/ICategory/C1ListNew?Source=1&Version=3.8.6&Client=Applets&DeviceID=67FF8930-8801-43AC-8C76-0F3116AE1556&madeIn={made_in_value}&isRefresh=0'\n", "\n", "    print(f\"正在获取 {made_in_name} 分类列表...\")\n", "    try:\n", "        # 使用之前登录成功的session对象发送请求，session会自动携带cookies\n", "        response = session.get(url=category_list_api_url, headers=headers, timeout=10)\n", "        response.raise_for_status() # 检查HTTP状态码，如果不是2xx则抛出异常\n", "        category_data = response.json()\n", "\n", "        # 检查响应数据结构，假设分类数据在 'data' 键下且为列表\n", "        if category_data and 'data' in category_data and isinstance(category_data['data'], list):\n", "            current_category_df = pd.DataFrame(category_data['data'])\n", "            # 添加一个新列来标识是国产还是进口\n", "            current_category_df['madeInType'] = made_in_name\n", "            current_category_df['madeIn'] = made_in_value\n", "            all_categories_dfs.append(current_category_df)\n", "            print(f\"成功获取 {made_in_name} 分类列表并转换为DataFrame。\")\n", "        else:\n", "            print(f\"获取 {made_in_name} 分类列表成功，但数据结构不符合预期或数据为空。\")\n", "            print(f\"原始响应数据: {category_data}\")\n", "\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"获取 {made_in_name} 分类列表请求失败: {e}\")\n", "\n", "# 将所有获取到的DataFrame合并成一个\n", "if all_categories_dfs:\n", "    category_df = pd.concat(all_categories_dfs, ignore_index=True)\n", "    print(\"所有分类列表已合并。\")\n", "else:\n", "    category_df = pd.DataFrame() # 如果没有获取到任何数据，则初始化一个空的DataFrame\n", "    print(\"未能获取任何分类列表。\")\n", "\n", "# 将DataFrame作为该单元格的输出\n", "category_df"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始获取各分类下的商品列表...\n", "正在获取 国产 - 促销品区 (c1SysNo=11518, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 促销品区 第 1 页 8 件商品。\n", "正在获取 国产 - 礼盒专区 - 组合礼盒 (c1SysNo=10767, c2SysNo=11366.0) 第 1 页商品...\n", "成功获取 礼盒专区 - 组合礼盒 第 1 页 3 件商品。\n", "正在获取 国产 - 礼盒专区 - 西瓜 (c1SysNo=10767, c2SysNo=11368.0) 第 1 页商品...\n", "成功获取 礼盒专区 - 西瓜 第 1 页 3 件商品。\n", "正在获取 国产 - 礼盒专区 - 粽子礼盒 (c1SysNo=10767, c2SysNo=11539.0) 第 1 页商品...\n", "成功获取 礼盒专区 - 粽子礼盒 第 1 页 3 件商品。\n", "正在获取 国产 - 礼盒专区 - 甜瓜 (c1SysNo=10767, c2SysNo=11516.0) 第 1 页商品...\n", "成功获取 礼盒专区 - 甜瓜 第 1 页 1 件商品。\n", "正在获取 国产 - 品质好货 - 苹果 (c1SysNo=11375, c2SysNo=11376.0) 第 1 页商品...\n", "成功获取 品质好货 - 苹果 第 1 页 8 件商品。\n", "正在获取 国产 - 品质好货 - 千禧 (c1SysNo=11375, c2SysNo=11530.0) 第 1 页商品...\n", "成功获取 品质好货 - 千禧 第 1 页 4 件商品。\n", "正在获取 国产 - 品质好货 - 瓜类 (c1SysNo=11375, c2SysNo=11535.0) 第 1 页商品...\n", "成功获取 品质好货 - 瓜类 第 1 页 3 件商品。\n", "正在获取 国产 - 品质好货 - 葡提 (c1SysNo=11375, c2SysNo=11541.0) 第 1 页商品...\n", "成功获取 品质好货 - 葡提 第 1 页 3 件商品。\n", "正在获取 国产 - 品质好货 - 梨 (c1SysNo=11375, c2SysNo=11378.0) 第 1 页商品...\n", "成功获取 品质好货 - 梨 第 1 页 4 件商品。\n", "正在获取 国产 - 品质好货 - 芒果 (c1SysNo=11375, c2SysNo=11533.0) 第 1 页商品...\n", "成功获取 品质好货 - 芒果 第 1 页 2 件商品。\n", "正在获取 国产 - 香蕉专区 - 闵盛园 (c1SysNo=9263, c2SysNo=10873.0) 第 1 页商品...\n", "成功获取 香蕉专区 - 闵盛园 第 1 页 1 件商品。\n", "正在获取 国产 - 香蕉专区 - 索菲亚 (c1SysNo=9263, c2SysNo=9272.0) 第 1 页商品...\n", "成功获取 香蕉专区 - 索菲亚 第 1 页 1 件商品。\n", "正在获取 国产 - 香蕉专区 - 德兴好 (c1SysNo=9263, c2SysNo=10871.0) 第 1 页商品...\n", "成功获取 香蕉专区 - 德兴好 第 1 页 1 件商品。\n", "正在获取 国产 - 香蕉专区 - 陆杰 (c1SysNo=9263, c2SysNo=9520.0) 第 1 页商品...\n", "成功获取 香蕉专区 - 陆杰 第 1 页 1 件商品。\n", "正在获取 国产 - 甜瓜/蜜瓜 - 晓蜜 (c1SysNo=11382, c2SysNo=11383.0) 第 1 页商品...\n", "成功获取 甜瓜/蜜瓜 - 晓蜜 第 1 页 4 件商品。\n", "正在获取 国产 - 甜瓜/蜜瓜 - 玉菇 (c1SysNo=11382, c2SysNo=11385.0) 第 1 页商品...\n", "成功获取 甜瓜/蜜瓜 - 玉菇 第 1 页 12 件商品。\n", "正在获取 国产 - 甜瓜/蜜瓜 - 网纹瓜 (c1SysNo=11382, c2SysNo=11388.0) 第 1 页商品...\n", "成功获取 甜瓜/蜜瓜 - 网纹瓜 第 1 页 2 件商品。\n", "正在获取 国产 - 甜瓜/蜜瓜 - 羊角蜜 (c1SysNo=11382, c2SysNo=11389.0) 第 1 页商品...\n", "成功获取 甜瓜/蜜瓜 - 羊角蜜 第 1 页 4 件商品。\n", "正在获取 国产 - 甜瓜/蜜瓜 - 博洋 (c1SysNo=11382, c2SysNo=11390.0) 第 1 页商品...\n", "成功获取 甜瓜/蜜瓜 - 博洋 第 1 页 3 件商品。\n", "正在获取 国产 - 甜瓜/蜜瓜 - 木瓜 (c1SysNo=11382, c2SysNo=11391.0) 第 1 页商品...\n", "成功获取 甜瓜/蜜瓜 - 木瓜 第 1 页 4 件商品。\n", "正在获取 国产 - 甜瓜/蜜瓜 - 其他 (c1SysNo=11382, c2SysNo=11392.0) 第 1 页商品...\n", "成功获取 甜瓜/蜜瓜 - 其他 第 1 页 18 件商品。\n", "正在获取 国产 - 苹果专区 - 陕西苹果 (c1SysNo=9353, c2SysNo=9354.0) 第 1 页商品...\n", "成功获取 苹果专区 - 陕西苹果 第 1 页 13 件商品。\n", "正在获取 国产 - 苹果专区 - 甘肃苹果 (c1SysNo=9353, c2SysNo=9974.0) 第 1 页商品...\n", "成功获取 苹果专区 - 甘肃苹果 第 1 页 12 件商品。\n", "正在获取 国产 - 苹果专区 - 山东苹果 (c1SysNo=9353, c2SysNo=9356.0) 第 1 页商品...\n", "成功获取 苹果专区 - 山东苹果 第 1 页 10 件商品。\n", "正在获取 国产 - 苹果专区 - 辽宁苹果 (c1SysNo=9353, c2SysNo=9360.0) 第 1 页商品...\n", "成功获取 苹果专区 - 辽宁苹果 第 1 页 1 件商品。\n", "正在获取 国产 - 柑橘专区 - 耙耙柑 (c1SysNo=11399, c2SysNo=11402.0) 第 1 页商品...\n", "成功获取 柑橘专区 - 耙耙柑 第 1 页 4 件商品。\n", "正在获取 国产 - 柑橘专区 - 丑八怪 (c1SysNo=11399, c2SysNo=11404.0) 第 1 页商品...\n", "成功获取 柑橘专区 - 丑八怪 第 1 页 3 件商品。\n", "正在获取 国产 - 柑橘专区 - 沃柑 (c1SysNo=11399, c2SysNo=11406.0) 第 1 页商品...\n", "成功获取 柑橘专区 - 沃柑 第 1 页 5 件商品。\n", "正在获取 国产 - 梨类专区 - 其他梨 (c1SysNo=9370, c2SysNo=9371.0) 第 1 页商品...\n", "成功获取 梨类专区 - 其他梨 第 1 页 2 件商品。\n", "正在获取 国产 - 梨类专区 - 香梨 (c1SysNo=9370, c2SysNo=9896.0) 第 1 页商品...\n", "成功获取 梨类专区 - 香梨 第 1 页 7 件商品。\n", "正在获取 国产 - 梨类专区 - 蜜梨 (c1SysNo=9370, c2SysNo=9902.0) 第 1 页商品...\n", "成功获取 梨类专区 - 蜜梨 第 1 页 2 件商品。\n", "正在获取 国产 - 梨类专区 - 皇冠梨 (c1SysNo=9370, c2SysNo=9890.0) 第 1 页商品...\n", "成功获取 梨类专区 - 皇冠梨 第 1 页 3 件商品。\n", "正在获取 国产 - 梨类专区 - 砀山梨 (c1SysNo=9370, c2SysNo=9900.0) 第 1 页商品...\n", "成功获取 梨类专区 - 砀山梨 第 1 页 3 件商品。\n", "正在获取 国产 - 西瓜专区 - 8424 (c1SysNo=9373, c2SysNo=9374.0) 第 1 页商品...\n", "成功获取 西瓜专区 - 8424 第 1 页 18 件商品。\n", "正在获取 国产 - 西瓜专区 - 美都 (c1SysNo=9373, c2SysNo=11484.0) 第 1 页商品...\n", "成功获取 西瓜专区 - 美都 第 1 页 9 件商品。\n", "正在获取 国产 - 西瓜专区 - 麒麟瓜 (c1SysNo=9373, c2SysNo=11490.0) 第 1 页商品...\n", "成功获取 西瓜专区 - 麒麟瓜 第 1 页 6 件商品。\n", "正在获取 国产 - 西瓜专区 - 特小凤 (c1SysNo=9373, c2SysNo=11488.0) 第 1 页商品...\n", "成功获取 西瓜专区 - 特小凤 第 1 页 3 件商品。\n", "正在获取 国产 - 西瓜专区 - 其他 (c1SysNo=9373, c2SysNo=11486.0) 第 1 页商品...\n", "成功获取 西瓜专区 - 其他 第 1 页 6 件商品。\n", "正在获取 国产 - 千禧专区 - 千禧 (c1SysNo=11454, c2SysNo=11455.0) 第 1 页商品...\n", "成功获取 千禧专区 - 千禧 第 1 页 12 件商品。\n", "正在获取 国产 - 千禧专区 - 其他 (c1SysNo=11454, c2SysNo=11459.0) 第 1 页商品...\n", "成功获取 千禧专区 - 其他 第 1 页 2 件商品。\n", "正在获取 国产 - 荔枝专区 - 妃子笑 (c1SysNo=9305, c2SysNo=9310.0) 第 1 页商品...\n", "成功获取 荔枝专区 - 妃子笑 第 1 页 6 件商品。\n", "正在获取 国产 - 荔枝专区 - 白糖罂 (c1SysNo=9305, c2SysNo=9312.0) 第 1 页商品...\n", "成功获取 荔枝专区 - 白糖罂 第 1 页 3 件商品。\n", "正在获取 国产 - 荔枝专区 - 其他荔枝 (c1SysNo=9305, c2SysNo=9318.0) 第 1 页商品...\n", "成功获取 荔枝专区 - 其他荔枝 第 1 页 2 件商品。\n", "正在获取 国产 - 葡提专区 - 巨峰 (c1SysNo=9320, c2SysNo=9321.0) 第 1 页商品...\n", "成功获取 葡提专区 - 巨峰 第 1 页 6 件商品。\n", "正在获取 国产 - 葡提专区 - 红提 (c1SysNo=9320, c2SysNo=9329.0) 第 1 页商品...\n", "成功获取 葡提专区 - 红提 第 1 页 3 件商品。\n", "正在获取 国产 - 葡提专区 - 阳光玫瑰 (c1SysNo=9320, c2SysNo=9323.0) 第 1 页商品...\n", "成功获取 葡提专区 - 阳光玫瑰 第 1 页 5 件商品。\n", "正在获取 国产 - 葡提专区 - 夏黑 (c1SysNo=9320, c2SysNo=9325.0) 第 1 页商品...\n", "成功获取 葡提专区 - 夏黑 第 1 页 7 件商品。\n", "正在获取 国产 - 葡提专区 - 青提 (c1SysNo=9320, c2SysNo=9333.0) 第 1 页商品...\n", "成功获取 葡提专区 - 青提 第 1 页 3 件商品。\n", "正在获取 国产 - 葡提专区 - 茉莉香 (c1SysNo=9320, c2SysNo=9327.0) 第 1 页商品...\n", "成功获取 葡提专区 - 茉莉香 第 1 页 3 件商品。\n", "正在获取 国产 - 葡提专区 - 乒乓 (c1SysNo=9320, c2SysNo=9337.0) 第 1 页商品...\n", "成功获取 葡提专区 - 乒乓 第 1 页 1 件商品。\n", "正在获取 国产 - 桃类专区 - 黄桃 (c1SysNo=9339, c2SysNo=9340.0) 第 1 页商品...\n", "成功获取 桃类专区 - 黄桃 第 1 页 3 件商品。\n", "正在获取 国产 - 桃类专区 - 油桃 (c1SysNo=9339, c2SysNo=9342.0) 第 1 页商品...\n", "成功获取 桃类专区 - 油桃 第 1 页 14 件商品。\n", "正在获取 国产 - 桃类专区 - 水蜜桃 (c1SysNo=9339, c2SysNo=9344.0) 第 1 页商品...\n", "成功获取 桃类专区 - 水蜜桃 第 1 页 20 件商品。\n", "正在获取 国产 - 桃类专区 - 毛桃 (c1SysNo=9339, c2SysNo=9346.0) 第 1 页商品...\n", "成功获取 桃类专区 - 毛桃 第 1 页 8 件商品。\n", "正在获取 国产 - 火龙果区 - 进口白肉 (c1SysNo=9398, c2SysNo=9399.0) 第 1 页商品...\n", "成功获取 火龙果区 - 进口白肉 第 1 页 1 件商品。\n", "正在获取 国产 - 火龙果区 - 国产白肉 (c1SysNo=9398, c2SysNo=9403.0) 第 1 页商品...\n", "成功获取 火龙果区 - 国产白肉 第 1 页 1 件商品。\n", "正在获取 国产 - 火龙果区 - 国产红肉 (c1SysNo=9398, c2SysNo=9405.0) 第 1 页商品...\n", "成功获取 火龙果区 - 国产红肉 第 1 页 3 件商品。\n", "正在获取 国产 - 樱桃专区 (c1SysNo=9291, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 樱桃专区 第 1 页 9 件商品。\n", "正在获取 国产 - 芒果专区 - 台芒 (c1SysNo=9376, c2SysNo=9377.0) 第 1 页商品...\n", "成功获取 芒果专区 - 台芒 第 1 页 3 件商品。\n", "正在获取 国产 - 芒果专区 - 水仙芒 (c1SysNo=9376, c2SysNo=9383.0) 第 1 页商品...\n", "成功获取 芒果专区 - 水仙芒 第 1 页 4 件商品。\n", "正在获取 国产 - 芒果专区 - 青芒 (c1SysNo=9376, c2SysNo=9385.0) 第 1 页商品...\n", "成功获取 芒果专区 - 青芒 第 1 页 1 件商品。\n", "正在获取 国产 - 芒果专区 - 贵妃芒 (c1SysNo=9376, c2SysNo=11452.0) 第 1 页商品...\n", "成功获取 芒果专区 - 贵妃芒 第 1 页 1 件商品。\n", "正在获取 国产 - 蓝莓专区 (c1SysNo=11426, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 蓝莓专区 第 1 页 6 件商品。\n", "正在获取 国产 - 橙柚专区 - 赣南橙 (c1SysNo=11431, c2SysNo=11432.0) 第 1 页商品...\n", "成功获取 橙柚专区 - 赣南橙 第 1 页 3 件商品。\n", "正在获取 国产 - 橙柚专区 - 湖北橙 (c1SysNo=11431, c2SysNo=11434.0) 第 1 页商品...\n", "成功获取 橙柚专区 - 湖北橙 第 1 页 3 件商品。\n", "正在获取 国产 - 菠萝凤梨 - 金钻凤梨 (c1SysNo=11473, c2SysNo=11474.0) 第 1 页商品...\n", "成功获取 菠萝凤梨 - 金钻凤梨 第 1 页 2 件商品。\n", "正在获取 国产 - 菠萝凤梨 - 菠萝 (c1SysNo=11473, c2SysNo=11478.0) 第 1 页商品...\n", "成功获取 菠萝凤梨 - 菠萝 第 1 页 3 件商品。\n", "正在获取 国产 - 枣类专区 (c1SysNo=9350, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 枣类专区 第 1 页 4 件商品。\n", "正在获取 国产 - 小众水果 - 枇杷 (c1SysNo=9483, c2SysNo=9484.0) 第 1 页商品...\n", "成功获取 小众水果 - 枇杷 第 1 页 5 件商品。\n", "正在获取 国产 - 小众水果 - 蔬果 (c1SysNo=9483, c2SysNo=9490.0) 第 1 页商品...\n", "成功获取 小众水果 - 蔬果 第 1 页 3 件商品。\n", "正在获取 国产 - 小众水果 - 柠檬 (c1SysNo=9483, c2SysNo=9492.0) 第 1 页商品...\n", "成功获取 小众水果 - 柠檬 第 1 页 4 件商品。\n", "正在获取 国产 - 小众水果 - 莲雾 (c1SysNo=9483, c2SysNo=11494.0) 第 1 页商品...\n", "成功获取 小众水果 - 莲雾 第 1 页 3 件商品。\n", "正在获取 国产 - 小众水果 - 人参果 (c1SysNo=9483, c2SysNo=9530.0) 第 1 页商品...\n", "成功获取 小众水果 - 人参果 第 1 页 3 件商品。\n", "正在获取 国产 - 小众水果 - 百香果 (c1SysNo=9483, c2SysNo=11500.0) 第 1 页商品...\n", "成功获取 小众水果 - 百香果 第 1 页 2 件商品。\n", "正在获取 国产 - 小众水果 - 其他 (c1SysNo=9483, c2SysNo=9494.0) 第 1 页商品...\n", "成功获取 小众水果 - 其他 第 1 页 4 件商品。\n", "正在获取 国产 - 猕猴桃区 (c1SysNo=9445, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 猕猴桃区 第 1 页 3 件商品。\n", "正在获取 国产 - 李杏梅区 - 李类 (c1SysNo=9469, c2SysNo=9470.0) 第 1 页商品...\n", "成功获取 李杏梅区 - 李类 第 1 页 9 件商品。\n", "正在获取 国产 - 李杏梅区 - 杏类 (c1SysNo=9469, c2SysNo=9472.0) 第 1 页商品...\n", "成功获取 李杏梅区 - 杏类 第 1 页 7 件商品。\n", "正在获取 国产 - 李杏梅区 - 梅类 (c1SysNo=9469, c2SysNo=9474.0) 第 1 页商品...\n", "成功获取 李杏梅区 - 梅类 第 1 页 10 件商品。\n", "正在获取 国产 - 非水果区 - 鸡蛋专区 (c1SysNo=10094, c2SysNo=10577.0) 第 1 页商品...\n", "成功获取 非水果区 - 鸡蛋专区 第 1 页 1 件商品。\n", "正在获取 国产 - 非水果区 - 番薯 (c1SysNo=10094, c2SysNo=10855.0) 第 1 页商品...\n", "成功获取 非水果区 - 番薯 第 1 页 1 件商品。\n", "正在获取 国产 - 包装辅材 (c1SysNo=9501, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 包装辅材 第 1 页 13 件商品。\n", "正在获取 国产 - 日用百货 (c1SysNo=10852, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 日用百货 第 1 页 1 件商品。\n", "正在获取 国产 - 损品专区 (c1SysNo=9504, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 损品专区 第 1 页 11 件商品。\n", "正在获取 进口 - 促销品区 (c1SysNo=11518, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 促销品区 第 1 页 4 件商品。\n", "正在获取 进口 - 佳沛专区 - 金奇异果 (c1SysNo=9194, c2SysNo=9195.0) 第 1 页商品...\n", "成功获取 佳沛专区 - 金奇异果 第 1 页 12 件商品。\n", "正在获取 进口 - 佳沛专区 - 绿奇异果 (c1SysNo=9194, c2SysNo=9197.0) 第 1 页商品...\n", "成功获取 佳沛专区 - 绿奇异果 第 1 页 2 件商品。\n", "正在获取 进口 - 佳沛专区 - 佳沛小包装 (c1SysNo=9194, c2SysNo=9199.0) 第 1 页商品...\n", "成功获取 佳沛专区 - 佳沛小包装 第 1 页 2 件商品。\n", "正在获取 进口 - 礼盒专区 (c1SysNo=10767, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 礼盒专区 第 1 页 2 件商品。\n", "正在获取 进口 - 香蕉专区 - 广裕香蕉 (c1SysNo=9263, c2SysNo=9268.0) 第 1 页商品...\n", "成功获取 香蕉专区 - 广裕香蕉 第 1 页 9 件商品。\n", "正在获取 进口 - 香蕉专区 - 闵盛园 (c1SysNo=9263, c2SysNo=10873.0) 第 1 页商品...\n", "成功获取 香蕉专区 - 闵盛园 第 1 页 6 件商品。\n", "正在获取 进口 - 香蕉专区 - 索菲亚 (c1SysNo=9263, c2SysNo=9272.0) 第 1 页商品...\n", "成功获取 香蕉专区 - 索菲亚 第 1 页 4 件商品。\n", "正在获取 进口 - 香蕉专区 - 德兴好 (c1SysNo=9263, c2SysNo=10871.0) 第 1 页商品...\n", "成功获取 香蕉专区 - 德兴好 第 1 页 2 件商品。\n", "正在获取 进口 - 香蕉专区 - 陆杰 (c1SysNo=9263, c2SysNo=9520.0) 第 1 页商品...\n", "成功获取 香蕉专区 - 陆杰 第 1 页 2 件商品。\n", "正在获取 进口 - 苹果专区 - 进口苹果 (c1SysNo=9353, c2SysNo=9362.0) 第 1 页商品...\n", "成功获取 苹果专区 - 进口苹果 第 1 页 6 件商品。\n", "正在获取 进口 - 苹果专区 - 国产嘎啦 (c1SysNo=9353, c2SysNo=9364.0) 第 1 页商品...\n", "成功获取 苹果专区 - 国产嘎啦 第 1 页 1 件商品。\n", "正在获取 进口 - 荔枝专区 (c1SysNo=9305, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 荔枝专区 第 1 页 1 件商品。\n", "正在获取 进口 - 葡提专区 - 红提 (c1SysNo=9320, c2SysNo=9329.0) 第 1 页商品...\n", "成功获取 葡提专区 - 红提 第 1 页 1 件商品。\n", "正在获取 进口 - 葡提专区 - 黑提 (c1SysNo=9320, c2SysNo=9331.0) 第 1 页商品...\n", "成功获取 葡提专区 - 黑提 第 1 页 1 件商品。\n", "正在获取 进口 - 葡提专区 - 其他 (c1SysNo=9320, c2SysNo=9335.0) 第 1 页商品...\n", "成功获取 葡提专区 - 其他 第 1 页 1 件商品。\n", "正在获取 进口 - 火龙果区 - 进口白肉 (c1SysNo=9398, c2SysNo=9399.0) 第 1 页商品...\n", "成功获取 火龙果区 - 进口白肉 第 1 页 1 件商品。\n", "正在获取 进口 - 火龙果区 - 进口红肉 (c1SysNo=9398, c2SysNo=9401.0) 第 1 页商品...\n", "成功获取 火龙果区 - 进口红肉 第 1 页 3 件商品。\n", "正在获取 进口 - 芒果专区 (c1SysNo=9376, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 芒果专区 第 1 页 1 件商品。\n", "正在获取 进口 - 橙柚专区 - 进口橙 (c1SysNo=11431, c2SysNo=11442.0) 第 1 页商品...\n", "成功获取 橙柚专区 - 进口橙 第 1 页 4 件商品。\n", "正在获取 进口 - 橙柚专区 - 西柚 (c1SysNo=11431, c2SysNo=11446.0) 第 1 页商品...\n", "成功获取 橙柚专区 - 西柚 第 1 页 2 件商品。\n", "正在获取 进口 - 橙柚专区 - 进口柚 (c1SysNo=11431, c2SysNo=11448.0) 第 1 页商品...\n", "成功获取 橙柚专区 - 进口柚 第 1 页 1 件商品。\n", "正在获取 进口 - 菠萝凤梨 (c1SysNo=11473, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 菠萝凤梨 第 1 页 5 件商品。\n", "正在获取 进口 - 椰青专区 (c1SysNo=9430, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 椰青专区 第 1 页 4 件商品。\n", "正在获取 进口 - 榴莲专区 - 金枕榴莲 (c1SysNo=9274, c2SysNo=9279.0) 第 1 页商品...\n", "成功获取 榴莲专区 - 金枕榴莲 第 1 页 5 件商品。\n", "正在获取 进口 - 榴莲专区 - 干尧榴莲 (c1SysNo=9274, c2SysNo=9281.0) 第 1 页商品...\n", "成功获取 榴莲专区 - 干尧榴莲 第 1 页 2 件商品。\n", "正在获取 进口 - 榴莲专区 - 其他榴莲 (c1SysNo=9274, c2SysNo=9289.0) 第 1 页商品...\n", "成功获取 榴莲专区 - 其他榴莲 第 1 页 1 件商品。\n", "正在获取 进口 - 菠萝蜜专区 - 红肉菠萝蜜 (c1SysNo=11463, c2SysNo=11464.0) 第 1 页商品...\n", "成功获取 菠萝蜜专区 - 红肉菠萝蜜 第 1 页 4 件商品。\n", "正在获取 进口 - 菠萝蜜专区 - 黄肉菠萝蜜 (c1SysNo=11463, c2SysNo=11466.0) 第 1 页商品...\n", "成功获取 菠萝蜜专区 - 黄肉菠萝蜜 第 1 页 1 件商品。\n", "正在获取 进口 - 山竹专区 (c1SysNo=11506, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 山竹专区 第 1 页 5 件商品。\n", "正在获取 进口 - 小众水果 - 柠檬 (c1SysNo=9483, c2SysNo=9492.0) 第 1 页商品...\n", "成功获取 小众水果 - 柠檬 第 1 页 1 件商品。\n", "正在获取 进口 - 小众水果 - 牛油果 (c1SysNo=9483, c2SysNo=11502.0) 第 1 页商品...\n", "成功获取 小众水果 - 牛油果 第 1 页 2 件商品。\n", "正在获取 进口 - 损品专区 (c1SysNo=9504, c2SysNo=N/A) 第 1 页商品...\n", "成功获取 损品专区 第 1 页 7 件商品。\n", "所有分类下的商品列表已获取完毕。\n", "商品列表已获取完毕。商品数量: 527\n"]}], "source": ["import pandas as pd\n", "import requests\n", "\n", "# 首先，将c1和c2分类信息扁平化，以便后续遍历\n", "category_with_c2 = []\n", "\n", "# 遍历category_df的每一行，提取c1分类信息和其下的c2子分类信息\n", "for index, row in category_df.iterrows():\n", "    c1_name = row['c1Name']\n", "    c1_sys_no = row['c1SysNo']\n", "    made_in_type = row['madeInType']\n", "    made_in = row['madeIn']\n", "    c2_list = row['c2List'] # 获取当前c1分类下的c2子分类列表\n", "\n", "    # 如果c2List不为空，则遍历c2子分类并添加到结果列表\n", "    if c2_list:\n", "        for c2_item in c2_list:\n", "            category_with_c2.append({\n", "                'category_id': c1_sys_no,\n", "                'category_name': c1_name,\n", "                'c2_id': c2_item.get('c2sysno'),\n", "                'c2_name': c2_item.get('c2name'),\n", "                'madeInType': made_in_type,\n", "                'madeIn': made_in\n", "            })\n", "    else:\n", "        # 如果c2List为空，则只添加c1分类信息，c2信息为None\n", "        category_with_c2.append({\n", "            'category_id': c1_sys_no,\n", "            'category_name': c1_name,\n", "            'c2_id': None,\n", "            'c2_name': None,\n", "            'madeInType': made_in_type,\n", "            'madeIn': made_in\n", "        })\n", "\n", "# 将扁平化的分类信息转换为DataFrame\n", "category_with_c2_df = pd.DataFrame(category_with_c2)\n", "\n", "# 初始化一个空的列表，用于存储所有获取到的商品数据\n", "all_products = []\n", "\n", "# 商品列表API的基础URL和固定参数\n", "product_list_base_url = 'https://guofangapi.lpq1688.com/IProductList/ListNew?Source=1&Version=3.8.6&Client=Applets&DeviceID=67FF8930-8801-43AC-8C76-0F3116AE1556&query=&sort=3&limit=50&extCode=0&filter=&sType=1&isNeedSysNo=true'\n", "page_limit = 50 # 每页商品数量\n", "\n", "print(\"开始获取各分类下的商品列表...\")\n", "\n", "# 遍历扁平化的分类DataFrame的每一行\n", "for index, row in category_with_c2_df.iterrows():\n", "    c1_sys_no = row['category_id']\n", "    c1_name = row['category_name']\n", "    c2_sys_no = row['c2_id']\n", "    c2_name = row['c2_name']\n", "    made_in_type = row['madeInType']\n", "    made_in = row['madeIn']\n", "\n", "    # 根据c2_id是否存在设置isNeedC2SysNo参数\n", "    is_need_c2_sys_no = 'true' if pd.notna(c2_sys_no) else 'false'\n", "\n", "    # 构建当前分类的商品列表API URL前缀\n", "    current_category_product_url = f\"{product_list_base_url}&c1SysNo={c1_sys_no}&madeIn={made_in}&isNeedC2SysNo={is_need_c2_sys_no}\"\n", "    if pd.notna(c2_sys_no):\n", "        current_category_product_url += f\"&c2SysNo={int(c2_sys_no)}\" # c2_sys_no可能为浮点数，转换为整数\n", "\n", "    offset = 0 # 初始化分页偏移量\n", "    while True:\n", "        # 构建带偏移量的完整URL\n", "        full_product_url = f\"{current_category_product_url}&offset={offset}\"\n", "\n", "        category_display_name = f\"{c1_name}\"\n", "        if c2_name:\n", "            category_display_name += f\" - {c2_name}\"\n", "\n", "        print(f\"正在获取 {made_in_type} - {category_display_name} (c1SysNo={c1_sys_no}, c2SysNo={c2_sys_no if pd.notna(c2_sys_no) else 'N/A'}) 第 {offset // page_limit + 1} 页商品...\")\n", "\n", "        try:\n", "            # 使用之前登录成功的session对象发送请求\n", "            response = session.get(url=full_product_url, headers=headers, timeout=10)\n", "            response.raise_for_status() # 检查HTTP状态码\n", "            product_data = response.json()\n", "\n", "            # 检查响应数据结构，根据API返回示例，商品列表在 'data' -> 'productList' 键下\n", "            products_on_page = product_data.get('data', {}).get('productList', [])\n", "\n", "            if not products_on_page:\n", "                print(f\"获取 {category_display_name} 第 {offset // page_limit + 1} 页商品：无更多商品。\")\n", "                break # 没有更多商品，退出分页循环\n", "\n", "            # 构建完整的分类名称\n", "            full_category_name = f\"{made_in_type} - {category_display_name}\"\n", "\n", "            for product in products_on_page:\n", "                # 为每个商品添加分类信息\n", "                product['category_id'] = c1_sys_no\n", "                product['category_name'] = c1_name\n", "                product['c2_id'] = c2_sys_no\n", "                product['c2_name'] = c2_name\n", "                product['madeInType'] = made_in_type\n", "                product['madeIn'] = made_in\n", "                # 添加完整的分类名称\n", "                product['category_full_name'] = full_category_name\n", "                all_products.append(product)\n", "\n", "            print(f\"成功获取 {category_display_name} 第 {offset // page_limit + 1} 页 {len(products_on_page)} 件商品。\")\n", "\n", "            # 如果当前页返回的商品数量小于每页限制，说明已是最后一页\n", "            if len(products_on_page) < page_limit:\n", "                break\n", "            else:\n", "                offset += page_limit # 准备获取下一页\n", "\n", "        except requests.exceptions.RequestException as e:\n", "            print(f\"获取 {category_display_name} 第 {offset // page_limit + 1} 页商品请求失败: {e}\")\n", "            break # 请求失败，跳过当前分类的后续分页\n", "        except KeyError as e:\n", "            print(f\"解析 {category_display_name} 第 {offset // page_limit + 1} 页商品数据失败 (KeyError: {e}). 原始响应: {product_data}\")\n", "            break # 数据结构不符，跳过当前分类的后续分页\n", "        except Exception as e:\n", "            print(f\"获取 {category_display_name} 第 {offset // page_limit + 1} 页商品时发生未知错误: {e}\")\n", "            break\n", "\n", "print(\"所有分类下的商品列表已获取完毕。\")\n", "\n", "# 将所有商品数据转换为DataFrame\n", "all_products_df = pd.DataFrame(all_products)\n", "\n", "print(f\"商品列表已获取完毕。商品数量: {len(all_products_df)}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[2025-06-03 18:26:59][INFO][proxy_setup.py_root] - 表不存在:summerfarm_ds.spider_benlaiguofang_product_result_df\n", "[2025-06-03 18:27:04][INFO][tabletunnel.py_odps.tunnel.tabletunnel] - Tunnel session created: <TableUploadSession id=2025060318270372d8c20b0ebf7a3b project=summerfarm_ds table=spider_benlaiguofang_product_result_df partition_spec=ds=20250603,competitor_name=benlaiguofang>\n", "[2025-06-03 18:27:07][INFO][proxy_setup.py_root] - 成功写入odps:summerfarm_ds.spider_benlaiguofang_product_result_df, partition_spec:ds=20250603,competitor_name=ben<PERSON><PERSON>of<PERSON>, attemp:0\n", "[2025-06-03 18:27:15][INFO][instancetunnel.py_odps.tunnel.instancetunnel] - Tunnel session created: <InstanceDownloadSession id=20250603182715a936f60b0ebed60a project_name=summerfarm_ds_dev instance_id=20250603102707269ge3c44j3y8g>\n", "[2025-06-03 18:27:16][INFO][proxy_setup.py_root] - sql:\n", "select ds,competitor_name,count(*) as recods \n", "                             from summerfarm_ds.spider_benlaiguofang_product_result_df\n", "                             where ds>='20250504' group by ds,competitor_name  order by ds desc limit 50\n", "columns:Index(['ds', 'competitor_name', 'recods'], dtype='object')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["         ds competitor_name  recods\n", "0  20250603   benlaiguofang     527\n"]}], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "all_products_df['competitor']=brand_name\n", "all_products_df=all_products_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name  order by ds desc limit 50\"\"\")\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}
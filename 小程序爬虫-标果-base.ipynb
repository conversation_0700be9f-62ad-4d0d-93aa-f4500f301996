{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 根据一级和二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import pandas as pd\n", "import json\n", "from scripts.proxy_setup import get_odps_sql_result_as_df,get_remote_data_with_proxy_json,logging\n", "\n", "from datetime import datetime\n", "\n", "time_of_now = datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\n", "\n", "headers = {\n", "    \"token\": \"80f45c7cb17a4ecbae516d50f9e53caf\",\n", "    \"sid\": \"8272073\",\n", "    # \"brand_name\": \"标果-广东\",\n", "    # \"token\": \"d962d0ba06514ffa8b80a335d851563f\",\n", "    # \"sid\": \"7731297\",\n", "    # \"time\": \"1702521175012\",\n", "    # \"brand_name\": \"标果-杭州\",\n", "}\n", "\n", "print(time_of_now)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql=\"\"\"\n", "SELECT ds,id,goodsname,competitor,count(1)cnt\n", "FROM summerfarm_ds.spider_biaoguo_with_prop_product_result_df\n", "WHERE ds=MAX_PT('summerfarm_ds.spider_biaoguo_with_prop_product_result_df')\n", "AND competitor like '标果-广东'\n", "GROUP by ds,id,goodsname,competitor;\n", "\"\"\"\n", "\n", "df=get_odps_sql_result_as_df(sql)\n", "df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# after_sale_info = requests.post(\n", "#     \"https://demeter-api.biaoguoworks.com/demeter/api/h5/store/goods/after-sale-standard?goodsId=102168\",\n", "#     headers=headers,\n", "#     proxies={},\n", "#     verify=False,\n", "# ).json()\n", "# after_sale_info\n", "\n", "\n", "def get_products_detail_for_goods(goods={\"id\": 102168}, headers=headers):\n", "    all_goods_prop = []\n", "    goods_id = goods[\"id\"]\n", "    url = f\"https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods/goods-detail?id={goods_id}\"\n", "    result = get_remote_data_with_proxy_json(url, headers=headers, post=True)\n", "    logging.info(f\"{result}\")\n", "    result = result[\"content\"]\n", "    goods[\"goodsDetail\"] = result\n", "\n", "    goodsPropDetailList = result[\"goodsPropDetailList\"]\n", "    for goods_prop in goodsPropDetailList:\n", "        goods_detail_prop = {}\n", "        goods_detail_prop[goods_prop[\"propertyName\"]] = goods_prop[\"propertyValue\"]\n", "        all_goods_prop.append(goods_detail_prop)\n", "    goods[\"goodsPropDetailList\"] = all_goods_prop\n", "    return all_goods_prop\n", "\n", "\n", "get_products_detail_for_goods()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user=requests.post(url='https://demeter-api.biaoguoworks.com/users/api/h5/user/info', headers=headers, verify=False).json()\n", "\n", "print(json.dumps(user, indent=4, ensure_ascii=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url=\"https://demeter-api.biaoguoworks.com/leechee/api/h5/store/front-categorys\"\n", "category_list=json.loads(requests.post(url,headers=headers, verify=False).text)\n", "print(category_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_products_for_category(categoryId=5745, current=1):\n", "    url='https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods'\n", "    data={\n", "        \"size\": 20,\n", "        \"current\": current,\n", "        \"categoryId\": categoryId,\n", "        \"goodsSourceType\": 0,\n", "        \"searchSortType\": \"COMPREHENSIVE\",\n", "        \"goodsSaleTagId\": \"\",\n", "        \"propertyValueIds\": []\n", "    }\n", "    result = json.loads(requests.post(url, headers=headers, json=data, verify=False).text)['content']['records']\n", "    return result\n", "print(get_products_for_category())\n", "\n", "# 根据商品ID查询商品详情页信息：\n", "def get_products_detail_for_goods(goodsId=236955):\n", "    all_goods_prop=[]\n", "    url='https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods/goods-detail?id=' + str(goodsId)\n", "    result = json.loads(requests.post(url, headers=headers, verify=False).text)\n", "    result = result['content']['goodsPropDetailList']\n", "    for goods_prop in result:\n", "            goods_detail_prop={}\n", "            goods_detail_prop[goods_prop['propertyName']]=goods_prop['propertyValue']\n", "            all_goods_prop.append(goods_detail_prop)\n", "    return all_goods_prop\n", "print(get_products_detail_for_goods())\n", "\n", "product_list_all=[]\n", "for category in category_list['content']:\n", "    categoryLevel=category['categoryLevel']\n", "    goodsCount=category[\"goodsCount\"]\n", "    categoryId=category['id']\n", "    categoryName=category[\"categoryName\"]\n", "    print(f'{categoryName}, categoryId:{categoryId}, goodsCount:{goodsCount}')\n", "    if categoryLevel != 3:\n", "        print(f\"非叶子类目:{categoryLevel}, categoryName:{categoryName}\")\n", "        continue\n", "    size=0\n", "    current=1\n", "    while size<goodsCount:\n", "        print(f\"current:{current}, size:{size}, category:{categoryName}-{categoryId}-level:{categoryLevel}\")\n", "        sub_list=get_products_for_category(categoryId=categoryId, current=current)\n", "        current=current+1\n", "        if sub_list is None or len(sub_list)<=0:\n", "            break\n", "        for goods in sub_list:\n", "            goodsPropDetailList = get_products_detail_for_goods(goods[\"id\"]);\n", "            goods['goodsPropDetailList'] = goodsPropDetailList;\n", "            print(goods)\n", "        size=size+len(sub_list)\n", "        print(f\"{categoryName}:{sub_list[0]}\")\n", "        product_list_all.extend(sub_list)\n", "\n", "\n", "product_list_all_df=pd.DataFrame(product_list_all)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from scripts.proxy_setup import write_pandas_df_into_odps,get_odps_sql_result_as_df\n", "# 写入odps\n", "product_list_all_df['competitor']=brand_name\n", "all_products_df=product_list_all_df.astype(str)\n", "\n", "today = datetime.now().strftime('%Y%m%d')\n", "partition_spec = f'ds={today},competitor_name={competitor_name_en}'\n", "table_name = f'summerfarm_ds.spider_{competitor_name_en}_product_result_df'\n", "\n", "write_pandas_df_into_odps(all_products_df, table_name, partition_spec)\n", "\n", "days_30=(datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime('%Y%m%d')\n", "df=get_odps_sql_result_as_df(f\"\"\"select ds,competitor_name,count(*) as recods \n", "                             from {table_name}\n", "                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50\"\"\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}
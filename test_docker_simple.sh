#!/bin/bash

# 简单测试Docker脚本的修复版本

echo "=== 测试Docker脚本修复 ==="

# 设置测试环境
export MAX_JOBS=2
export SCRIPT_DIR="./test_scripts"

# 检查脚本是否存在
if [ ! -f "./run_all_docker.sh" ]; then
    echo "错误: run_all_docker.sh 不存在"
    exit 1
fi

# 检查测试脚本目录
if [ ! -d "$SCRIPT_DIR" ]; then
    echo "错误: 测试脚本目录不存在"
    exit 1
fi

echo "开始测试..."
echo "并发数: $MAX_JOBS"
echo "脚本目录: $SCRIPT_DIR"

# 运行Docker脚本
./run_all_docker.sh

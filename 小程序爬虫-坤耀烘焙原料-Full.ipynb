{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 定义Embedding接口（GPT）"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["time_of_now:2024-03-06 16:15:23, date_of_now:2024-03-06, brand_name:坤耀烘焙原料, headers:{'uniacid': '2595', 'appType': 'mini'}\n"]}], "source": ["import requests\n", "import json\n", "import time\n", "import pandasql\n", "from IPython.core.display import HTML\n", "import pandas as pd\n", "import json\n", "import os\n", "\n", "TEXT_EMBEDDING_CACHE = {}\n", "\n", "USE_CLAUDE=False\n", "\n", "cache_file_path = './data/cache/坤耀烘焙原料/TEXT_EMBEDDING_CACHE.txt'\n", "\n", "if os.path.isfile(cache_file_path):\n", "    with open(cache_file_path, 'r') as f:\n", "        TEXT_EMBEDDING_CACHE = json.load(f)\n", "else:\n", "    print(f\"{cache_file_path} does not exist.\")\n", "\n", "URL='https://xm-ai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2023-07-01-preview'\n", "AZURE_API_KEY=\"********************************\"\n", "\n", "def getEmbeddingsFromAzure(inputText=''):\n", "    if inputText in TEXT_EMBEDDING_CACHE:\n", "        print(f'cache matched:{inputText}')\n", "        return TEXT_EMBEDDING_CACHE[inputText]\n", "\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'api-key': f'{AZURE_API_KEY}'  # replace with your actual Azure API Key\n", "    }\n", "    body = {\n", "        'input': inputText\n", "    }\n", "\n", "    try:\n", "        starting_ts = time.time()\n", "        response = requests.post(URL, headers=headers, data=json.dumps(body))  # replace 'url' with your actual URL\n", "\n", "        if response.status_code == 200:\n", "            data = response.json()\n", "            embedding = data['data'][0]['embedding']\n", "            print(f\"inputText:{inputText}, usage:{json.dumps(data['usage'])}, time cost:{(time.time() - starting_ts) * 1000}ms\")\n", "            TEXT_EMBEDDING_CACHE[inputText] = embedding\n", "            return embedding\n", "        else:\n", "            print(f'Request failed: {response.status_code} {response.text}')\n", "    except Exception as error:\n", "        print(f'An error occurred: {error}')\n", "\n", "if USE_CLAUDE:\n", "    print(getEmbeddingsFromAzure(\"越南大青芒\"))\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "from datetime import datetime \n", "time_of_now=datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "date_of_now=datetime.now().strftime('%Y-%m-%d')\n", "\n", "headers={'uniacid':'2595','appType':'mini',}\n", "brand_name='坤耀烘焙原料'\n", "competitor_name_en='kun<PERSON><PERSON><PERSON><PERSON>'\n", "\n", "print(f\"time_of_now:{time_of_now}, date_of_now:{date_of_now}, brand_name:{brand_name}, headers:{headers}\")\n", "\n", "create_directory_if_not_exists(f'./data/{brand_name}')\n", "create_directory_if_not_exists(f'./data/鲜沐')\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**************:35682\n", "**************:35052\n", "************:40582\n", "*************:48030\n", "*************:33155\n", "***************:47705\n", "************:42172\n", "**************:39080\n", "***************:46912\n", "***************:40780\n", "['**************:35682', '**************:35052', '************:40582', '*************:48030', '*************:33155', '***************:47705', '************:42172', '**************:39080', '***************:46912', '***************:40780']\n"]}], "source": ["import requests\n", "import random\n", "\n", "def get_proxy_list_from_server():\n", "    all_proxies=requests.get(\"http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe\").text\n", "    print(all_proxies)\n", "    proxy_list=all_proxies.split(\"\\r\\n\")\n", "    return proxy_list\n", "\n", "proxy_list=get_proxy_list_from_server()\n", "print(proxy_list)\n", "\n", "def get_remote_data_with_proxy(url, data, headers, cookies):\n", "    max_retries=3;\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.get(url, data=data, headers=headers, proxies=proxies, cookies=cookies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None\n", "def post_remote_data_with_proxy(url, data, headers):\n", "    max_retries=3\n", "    proxies = None\n", "    if len(proxy_list) > 0:\n", "        proxies = {'http': f'http://18258841203:8gTcEKLs@{random.choice(proxy_list)}',}\n", "        print(f\"Using proxy: {proxies['http']}\")\n", "\n", "    for i in range(max_retries):\n", "        try:\n", "            response = requests.post(url, data=data, headers=headers, proxies=proxies, timeout=30)\n", "            if response.status_code == 200:\n", "                return response\n", "            else:\n", "                raise Exception(f\"Error getting data: {response.status_code}\")\n", "        except Exception as e:\n", "            print(f\"Error getting data: {e}\")\n", "            if i == max_retries - 1:\n", "                raise e\n", "\n", "    return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["根据一级、二级类目ID爬取商品信息"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using proxy: **********************************************\n", "牛奶类 6\n", "淡奶油 8\n", "黄油 8\n", "面粉类 8\n", "奶酪芝士马苏 8\n", "馅料 6\n", "糖制品 9\n", "肉松系列产品 3\n", "香肠肉制品 6\n", "慕斯/冰淇淋 9\n", "奶粉 1\n", "沙拉酱调味酱 7\n", "冷冻果茸果品 10\n", "咖啡椰乳奶 3\n", "烘焙配料 7\n", "干果蜜饯 3\n", "果蔬预拌粉 2\n", "食品保鲜脱氧剂 7\n", "咖啡饮品类 9\n", "工具包装 3\n", "运费差价 1\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>typePid</th>\n", "      <th>aloneType</th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>icon</th>\n", "      <th>price</th>\n", "      <th>salesNum</th>\n", "      <th>stock</th>\n", "      <th>minNum</th>\n", "      <th>maxNum</th>\n", "      <th>...</th>\n", "      <th>isSpecs</th>\n", "      <th>isAttr</th>\n", "      <th>isMaterial</th>\n", "      <th>salesType</th>\n", "      <th>startTime</th>\n", "      <th>endTime</th>\n", "      <th>weekDay</th>\n", "      <th>weekSalesType</th>\n", "      <th>activityGoodData</th>\n", "      <th>vipPrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>199646</td>\n", "      <td>国产蒙牛牛奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>50</td>\n", "      <td>0</td>\n", "      <td>999</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>199658</td>\n", "      <td>国产蒙牛牛奶（航天版包装）</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>50</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>199666</td>\n", "      <td>辉山牛奶（烘焙专用）</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>70</td>\n", "      <td>0</td>\n", "      <td>100</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>70.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>216047</td>\n", "      <td>牡纯纯牛奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>78</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1679068800</td>\n", "      <td>1681660800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>78.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>205422</td>\n", "      <td>每日鲜语咖啡大师奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>180</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678579200</td>\n", "      <td>1681171200</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>180.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>205429</td>\n", "      <td>蒙牛现代牧场鲜牛奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>108</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678579200</td>\n", "      <td>1681171200</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>108.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>46513</td>\n", "      <td>2</td>\n", "      <td>199677</td>\n", "      <td>安佳淡奶油</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>560</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>560.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>46513</td>\n", "      <td>2</td>\n", "      <td>199683</td>\n", "      <td>铁塔淡奶油</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>580</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>580.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>46513</td>\n", "      <td>2</td>\n", "      <td>199685</td>\n", "      <td>篮风车蓝米吉稀奶油</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>720</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>720.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>46513</td>\n", "      <td>2</td>\n", "      <td>206570</td>\n", "      <td>佩森淡奶油</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>520</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678636800</td>\n", "      <td>1681228800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>520.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 24 columns</p>\n", "</div>"], "text/plain": ["  typePid aloneType      id           name  \\\n", "0   46469         2  199646         国产蒙牛牛奶   \n", "1   46469         2  199658  国产蒙牛牛奶（航天版包装）   \n", "2   46469         2  199666     辉山牛奶（烘焙专用）   \n", "3   46469         2  216047          牡纯纯牛奶   \n", "4   46469         2  205422      每日鲜语咖啡大师奶   \n", "5   46469         2  205429      蒙牛现代牧场鲜牛奶   \n", "6   46513         2  199677          安佳淡奶油   \n", "7   46513         2  199683          铁塔淡奶油   \n", "8   46513         2  199685      篮风车蓝米吉稀奶油   \n", "9   46513         2  206570          佩森淡奶油   \n", "\n", "                                                icon price salesNum stock  \\\n", "0  https://static.saas.qxepay.com/yb_wm/2595/2023...    50        0   999   \n", "1  https://static.saas.qxepay.com/yb_wm/2595/2023...    50        0     0   \n", "2  https://static.saas.qxepay.com/yb_wm/2595/2023...    70        0   100   \n", "3  https://static.saas.qxepay.com/yb_wm/2595/2023...    78        0     0   \n", "4  https://static.saas.qxepay.com/yb_wm/2595/2023...   180        0     0   \n", "5  https://static.saas.qxepay.com/yb_wm/2595/2023...   108        0     0   \n", "6  https://static.saas.qxepay.com/yb_wm/2595/2023...   560        4     6   \n", "7  https://static.saas.qxepay.com/yb_wm/2595/2023...   580        2     3   \n", "8  https://static.saas.qxepay.com/yb_wm/2595/2023...   720        3     3   \n", "9  https://static.saas.qxepay.com/yb_wm/2595/2023...   520        0    10   \n", "\n", "  minNum maxNum  ... isSpecs isAttr isMaterial salesType   startTime  \\\n", "0     10      0  ...       2      2          2         1  1678204800   \n", "1      1      0  ...       2      2          2         1  1678204800   \n", "2      5      0  ...       2      2          2         1  1678204800   \n", "3      1      0  ...       2      2          2         1  1679068800   \n", "4      1      0  ...       2      2          2         1  1678579200   \n", "5      1      0  ...       2      2          2         1  1678579200   \n", "6      1      0  ...       2      2          2         1  1678204800   \n", "7      1      0  ...       2      2          2         1  1678204800   \n", "8      1      0  ...       2      2          2         1  1678204800   \n", "9      1      0  ...       2      2          2         1  1678636800   \n", "\n", "      endTime weekDay weekSalesType  \\\n", "0  1680796800      []             1   \n", "1  1680796800      []             1   \n", "2  1680796800      []             1   \n", "3  1681660800      []             1   \n", "4  1681171200      []             1   \n", "5  1681171200      []             1   \n", "6  1680796800      []             1   \n", "7  1680796800      []             1   \n", "8  1680796800      []             1   \n", "9  1681228800      []             1   \n", "\n", "                                    activityGoodData vipPrice  \n", "0  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     50.0  \n", "1  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     50.0  \n", "2  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     70.0  \n", "3  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     78.0  \n", "4  {'id': 0, 'type': 0, 'money': 0, 'activityMone...    180.0  \n", "5  {'id': 0, 'type': 0, 'money': 0, 'activityMone...    108.0  \n", "6  {'id': 0, 'type': 0, 'money': 0, 'activityMone...    560.0  \n", "7  {'id': 0, 'type': 0, 'money': 0, 'activityMone...    580.0  \n", "8  {'id': 0, 'type': 0, 'money': 0, 'activityMone...    720.0  \n", "9  {'id': 0, 'type': 0, 'money': 0, 'activityMone...    520.0  \n", "\n", "[10 rows x 24 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["url='https://saas.qxepay.com/index.php/channelApi/good/get-product-list?storeId=2653&lat=26.113972&lng=119.320571'\n", "\n", "category_and_goods=json.loads(get_remote_data_with_proxy(url=url, data=None, headers=headers, cookies=None).text)\n", "category_and_goods=category_and_goods['data']['data']\n", "goods=[]\n", "for data in category_and_goods:\n", "    print(data['name'], len(data['goods']))\n", "    goods.extend(data['goods'])\n", "\n", "print(len(goods))\n", "product_list_all_df=pd.DataFrame(goods)\n", "product_list_all_df.head(10)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>typePid</th>\n", "      <th>aloneType</th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>icon</th>\n", "      <th>price</th>\n", "      <th>salesNum</th>\n", "      <th>stock</th>\n", "      <th>minNum</th>\n", "      <th>maxNum</th>\n", "      <th>...</th>\n", "      <th>isSpecs</th>\n", "      <th>isAttr</th>\n", "      <th>isMaterial</th>\n", "      <th>salesType</th>\n", "      <th>startTime</th>\n", "      <th>endTime</th>\n", "      <th>weekDay</th>\n", "      <th>weekSalesType</th>\n", "      <th>activityGoodData</th>\n", "      <th>vipPrice</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>199646</td>\n", "      <td>国产蒙牛牛奶</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>50</td>\n", "      <td>0</td>\n", "      <td>999</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>199658</td>\n", "      <td>国产蒙牛牛奶（航天版包装）</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>50</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>50.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>46469</td>\n", "      <td>2</td>\n", "      <td>199666</td>\n", "      <td>辉山牛奶（烘焙专用）</td>\n", "      <td>https://static.saas.qxepay.com/yb_wm/2595/2023...</td>\n", "      <td>70</td>\n", "      <td>0</td>\n", "      <td>100</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1678204800</td>\n", "      <td>1680796800</td>\n", "      <td>[]</td>\n", "      <td>1</td>\n", "      <td>{'id': 0, 'type': 0, 'money': 0, 'activityMone...</td>\n", "      <td>70.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 24 columns</p>\n", "</div>"], "text/plain": ["  typePid aloneType      id           name  \\\n", "0   46469         2  199646         国产蒙牛牛奶   \n", "1   46469         2  199658  国产蒙牛牛奶（航天版包装）   \n", "2   46469         2  199666     辉山牛奶（烘焙专用）   \n", "\n", "                                                icon price salesNum stock  \\\n", "0  https://static.saas.qxepay.com/yb_wm/2595/2023...    50        0   999   \n", "1  https://static.saas.qxepay.com/yb_wm/2595/2023...    50        0     0   \n", "2  https://static.saas.qxepay.com/yb_wm/2595/2023...    70        0   100   \n", "\n", "  minNum maxNum  ... isSpecs isAttr isMaterial salesType   startTime  \\\n", "0     10      0  ...       2      2          2         1  1678204800   \n", "1      1      0  ...       2      2          2         1  1678204800   \n", "2      5      0  ...       2      2          2         1  1678204800   \n", "\n", "      endTime weekDay weekSalesType  \\\n", "0  1680796800      []             1   \n", "1  1680796800      []             1   \n", "2  1680796800      []             1   \n", "\n", "                                    activityGoodData vipPrice  \n", "0  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     50.0  \n", "1  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     50.0  \n", "2  {'id': 0, 'type': 0, 'money': 0, 'activityMone...     70.0  \n", "\n", "[3 rows x 24 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["date_to_save_file=time_of_now.split(\" \")[0]\n", "df_cate_list=pd.DataFrame(product_list_all_df)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}--商品列表-原始数据-{date_to_save_file}.csv', index=False, encoding='utf_8_sig')\n", "\n", "df_cate_list.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 到此就结束了，可以将数据写入ODPS了"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inputText:国产蒙牛牛奶, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:2063.7476444244385ms\n", "inputText:国产蒙牛牛奶（航天版包装）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:520.115852355957ms\n", "inputText:辉山牛奶（烘焙专用）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:534.7464084625244ms\n", "inputText:牡纯纯牛奶, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:503.3438205718994ms\n", "inputText:每日鲜语咖啡大师奶, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:519.7312831878662ms\n", "inputText:蒙牛现代牧场鲜牛奶, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:506.6096782684326ms\n", "inputText:安佳淡奶油, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:523.1516361236572ms\n", "inputText:铁塔淡奶油, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:1114.8006916046143ms\n", "inputText:篮风车蓝米吉稀奶油, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:536.8473529815674ms\n", "inputText:佩森淡奶油, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:514.9538516998291ms\n", "inputText:迪比克塑形稀奶油, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:595.2813625335693ms\n", "inputText:蒙牛稀奶油, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:528.7075042724609ms\n", "inputText:爱真喷射奶油, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:509.7336769104004ms\n", "inputText:迪比克喷射奶油, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:513.716459274292ms\n", "inputText:安佳大黄油25kG, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:534.6939563751221ms\n", "inputText:法国伊斯尼AOP黄油片, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:522.2468376159668ms\n", "inputText:柏特兰无盐黄油, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:582.1683406829834ms\n", "inputText:百钻黄油500G*10块/箱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:572.7949142456055ms\n", "inputText:安佳无盐黄油5kG（分装）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:560.9958171844482ms\n", "inputText:百钻发酵黄油, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:672.0099449157715ms\n", "inputText:百钻牌黄油, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:529.2177200317383ms\n", "inputText:法国总统黄油10kG, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:714.4374847412109ms\n", "inputText:日本野赫强力粉, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:664.5092964172363ms\n", "inputText:王后精制低筋粉, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:744.9274063110352ms\n", "inputText:王后精制高筋小麦粉, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:528.1195640563965ms\n", "inputText:贝琪虎皮预拌粉5kG, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:522.1998691558838ms\n", "inputText:三象牌水磨糯米粉, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:547.3771095275879ms\n", "inputText:家樂鹰粟粉（玉米淀粉）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:605.8881282806396ms\n", "inputText:小二哥小酥肉粉（油炸用粉）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:590.9795761108398ms\n", "inputText:小二哥元宵粉（糯米粉）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:571.3939666748047ms\n", "inputText:君皇马斯卡彭500G, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:557.5449466705322ms\n", "inputText:安佳马苏里拉芝士块10kg, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:546.28586769104ms\n", "inputText:安佳碎条状马苏里拉干酪12kG, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:637.6383304595947ms\n", "inputText:爱氏晨曦芝士碎2kG, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:967.240571975708ms\n", "inputText:兰诺斯奶油芝士2KG, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:577.4514675140381ms\n", "inputText:安佳芝士片84片 （黄片）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:862.799882888794ms\n", "inputText:妙多帕玛森干酪粉, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:563.6515617370605ms\n", "inputText:宝宏橙切达迷你再制干酪, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:672.6803779602051ms\n", "inputText:台杰特质绿豆沙（金装）5kG, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:592.695951461792ms\n", "inputText:台杰特级土凤梨馅5KG, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:554.419994354248ms\n", "inputText:台杰金牌红豆粒馅5KG, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:568.9551830291748ms\n", "inputText:台杰牛油果绿豆沙馅5kG, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:677.2844791412354ms\n", "inputText:台锐紫薯馅1kG（冷冻）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:558.2003593444824ms\n", "inputText:华琪香芋馅（蛋糕专用）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:1261.9848251342773ms\n", "inputText:日本林原海藻糖, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:553.8158416748047ms\n", "inputText:海藻糖1kG（分装）, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:535.8526706695557ms\n", "inputText:TS韩国细砂糖, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:579.9582004547119ms\n", "inputText:百钻细砂糖30kG, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:521.1584568023682ms\n", "inputText:舒可曼糖霜（蓝标）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:568.0673122406006ms\n", "inputText:百钻细砂糖5KG, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:546.9858646392822ms\n", "inputText:太古幼滑糖霜（蓝标）, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:613.0673885345459ms\n", "inputText:蜂蜜, usage:{\"prompt_tokens\": 6, \"total_tokens\": 6}, time cost:568.5052871704102ms\n", "cache matched:百钻细砂糖30kG\n", "inputText:黑旗海苔脆松3#, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:565.2408599853516ms\n", "inputText:臣泽原味肉松A, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:622.744083404541ms\n", "inputText:臣泽海苔酥松, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:561.3112449645996ms\n", "inputText:黑旗大肉香肠1kG, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:525.0537395477295ms\n", "inputText:新鲜草莓, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:574.3064880371094ms\n", "inputText:黑旗德式原味超值香肠1kG（长度12cm）, usage:{\"prompt_tokens\": 23, \"total_tokens\": 23}, time cost:499.36461448669434ms\n", "inputText:黑旗超值培根, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:548.3953952789307ms\n", "inputText:黑旗帕斯雀牛肉2#, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:576.340913772583ms\n", "inputText:立信调理鸡排带皮（微辣）, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:624.6521472930908ms\n", "inputText:约瀚丹尼提拉米苏慕斯蛋糕, usage:{\"prompt_tokens\": 24, \"total_tokens\": 24}, time cost:620.1667785644531ms\n", "inputText:约瀚丹尼红丝绒慕斯蛋糕, usage:{\"prompt_tokens\": 25, \"total_tokens\": 25}, time cost:522.4378108978271ms\n", "inputText:约瀚丹尼燕麦乳蛋糕（大盘）, usage:{\"prompt_tokens\": 27, \"total_tokens\": 27}, time cost:651.6139507293701ms\n", "inputText:约瀚丹尼皇家丝滑巧克力, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:560.9986782073975ms\n", "inputText:约瀚丹尼歌剧院蛋糕, usage:{\"prompt_tokens\": 19, \"total_tokens\": 19}, time cost:650.7248878479004ms\n", "inputText:约瀚丹尼生巧熔岩蛋糕, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:579.8368453979492ms\n", "inputText:约瀚丹尼咸奶油风味红丝绒蛋糕, usage:{\"prompt_tokens\": 31, \"total_tokens\": 31}, time cost:506.46185874938965ms\n", "inputText:冰淇淋蛋糕胚8寸, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:672.1289157867432ms\n", "inputText:冰淇淋蛋糕胚6寸, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:626.910924911499ms\n", "inputText:新西兰奶粉25KG, usage:{\"prompt_tokens\": 10, \"total_tokens\": 10}, time cost:577.2871971130371ms\n", "inputText:漫森黑松露调味酱, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:559.1857433319092ms\n", "inputText:鑫隆顺香甜沙拉酱, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:552.4237155914307ms\n", "inputText:鑫隆顺美味沙拉酱, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:545.4127788543701ms\n", "inputText:不二可丝达（奶酪味）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:624.657154083252ms\n", "inputText:不二可丝达（酸奶味）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:488.93237113952637ms\n", "inputText:不二可丝达（蛋奶味）, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:494.6422576904297ms\n", "inputText:凤球唛番茄调味酱3kG, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:508.98122787475586ms\n", "inputText:百香果浆, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:508.94904136657715ms\n", "inputText:泰国金枕榴莲肉（无核）, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:525.8252620697021ms\n", "inputText:D24苏丹王榴莲泥, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:545.8903312683105ms\n", "inputText:乐果芬草莓果泥1kG, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:503.6172866821289ms\n", "inputText:乐果芬芒果果泥1kG, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:520.6353664398193ms\n", "inputText:乐果芬树莓果泥1kG, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:636.0890865325928ms\n", "inputText:乐果芬蓝莓果泥1kG, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:507.7080726623535ms\n", "inputText:乐果芬覆盆子果泥1kG, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:539.6027565002441ms\n", "cache matched:乐果芬草莓果泥1kG\n", "inputText:速冻百香果原浆, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:530.7474136352539ms\n", "inputText:高达椰浆, usage:{\"prompt_tokens\": 7, \"total_tokens\": 7}, time cost:575.3166675567627ms\n", "inputText:菲诺厚椰乳, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:3574.5761394500732ms\n", "inputText:黑白炼乳整箱397g*48罐, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:700.8895874023438ms\n", "inputText:百利牌明胶片, usage:{\"prompt_tokens\": 9, \"total_tokens\": 9}, time cost:636.1467838287354ms\n", "inputText:鑫隆顺海苔片, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:554.5132160186768ms\n", "inputText:迪尔玛伯爵红茶（佛手柑）, usage:{\"prompt_tokens\": 20, \"total_tokens\": 20}, time cost:627.507209777832ms\n", "inputText:咸鸭蛋黄, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:695.6725120544434ms\n", "inputText:法国进口可可百利薄脆片, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:1261.0018253326416ms\n", "inputText:奥利奥饼干碎（中号）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:775.9089469909668ms\n", "inputText:双斧牌食粉（小苏打）, usage:{\"prompt_tokens\": 16, \"total_tokens\": 16}, time cost:714.8723602294922ms\n", "inputText:核桃仁3路2.5kG, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:571.2118148803711ms\n", "inputText:浅二路核桃仁2.5kG, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:619.8139190673828ms\n", "inputText:蔓越莓干11.34kG, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:511.9171142578125ms\n", "inputText:糕点预拌粉XTS-171（Q心麻薯预拌粉）, usage:{\"prompt_tokens\": 28, \"total_tokens\": 28}, time cost:535.912036895752ms\n", "inputText:紫薯全粉, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:572.868824005127ms\n", "inputText:利市30型脱氧剂, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:589.7331237792969ms\n", "inputText:利市50型脱氧剂, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:600.9354591369629ms\n", "inputText:利市200型脱氧剂, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:573.6780166625977ms\n", "inputText:广益复配防腐剂（蛋黄派用）, usage:{\"prompt_tokens\": 22, \"total_tokens\": 22}, time cost:1309.6990585327148ms\n", "inputText:广益复配防腐剂（蛋糕用）, usage:{\"prompt_tokens\": 21, \"total_tokens\": 21}, time cost:647.5727558135986ms\n", "inputText:广益复配面包防腐剂, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:608.8638305664062ms\n", "inputText:广益脱氢乙酸钠, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:614.9063110351562ms\n", "inputText:黄金珍珠粉圆, usage:{\"prompt_tokens\": 11, \"total_tokens\": 11}, time cost:595.9353446960449ms\n", "inputText:贝和红玉岩茶500G, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:699.195384979248ms\n", "inputText:贝和碳焙乌龙茶500G, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:655.8940410614014ms\n", "inputText:饮之励碳焙大红袍300G, usage:{\"prompt_tokens\": 17, \"total_tokens\": 17}, time cost:572.8392601013184ms\n", "inputText:贝和四季春晓茶500G, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:598.3953475952148ms\n", "inputText:贝和茉莉绿妍500G, usage:{\"prompt_tokens\": 15, \"total_tokens\": 15}, time cost:598.0849266052246ms\n", "inputText:贝和燕麦罐头, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:613.8396263122559ms\n", "inputText:贝和红豆罐头920G, usage:{\"prompt_tokens\": 12, \"total_tokens\": 12}, time cost:564.1765594482422ms\n", "inputText:贝和植脂末1kG, usage:{\"prompt_tokens\": 14, \"total_tokens\": 14}, time cost:632.7357292175293ms\n", "inputText:土凤梨模具, usage:{\"prompt_tokens\": 8, \"total_tokens\": 8}, time cost:810.1136684417725ms\n", "inputText:土凤梨酥包装袋, usage:{\"prompt_tokens\": 13, \"total_tokens\": 13}, time cost:3666.5732860565186ms\n", "inputText:绿豆糕椭圆形手压模具, usage:{\"prompt_tokens\": 18, \"total_tokens\": 18}, time cost:568.1750774383545ms\n", "inputText:运费差价, usage:{\"prompt_tokens\": 5, \"total_tokens\": 5}, time cost:594.7043895721436ms\n"]}], "source": ["df_cate_list['title_embedding']=df_cate_list['name'].apply(getEmbeddingsFromAzure)\n", "df_cate_list.to_csv(f'./data/{brand_name}/{brand_name}-商品SKU列表-清洗后数据-with-embedding-{date_of_now}.csv', index=False, encoding='utf_8_sig')\n", "\n", "# 保存EMBEDDING_CACHE到本地文件\n", "with open(cache_file_path, 'w') as f:\n", "    json.dump(TEXT_EMBEDDING_CACHE, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["和鲜沐价格比对的，先放着..."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}